{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms'; // <-- Already imported\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http'; // <-- Import these\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { JwtInterceptor } from './auth/jwt.interceptor'; // Adjust the path as necessary\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: JwtInterceptor,\n        multi: true\n      }],\n      imports: [BrowserModule, FormsModule, HttpClientModule // <-- Add HttpClientModule here\n      ]\n    });\n  }\n}\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, LoginComponent, SignupComponent],\n    imports: [BrowserModule, FormsModule, HttpClientModule // <-- Add HttpClientModule here\n    ]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "AppComponent", "LoginComponent", "SignupComponent", "JwtInterceptor", "AppModule", "bootstrap", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms'; // <-- Already imported\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http'; // <-- Import these\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { JwtInterceptor } from './auth/jwt.interceptor';  // Adjust the path as necessary\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    SignupComponent\n  ],\n  imports: [\n    BrowserModule,\n    FormsModule,\n    HttpClientModule  // <-- Add HttpClientModule here\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: JwtInterceptor,  // Register the interceptor\n      multi: true\n    }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,QAAQ,gBAAgB,CAAC,CAAC;AAC9C,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB,CAAC,CAAC;AAC5E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,cAAc,QAAQ,wBAAwB,CAAC,CAAE;;AAsB1D,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRL,YAAY;IAAA;EAAA;;;iBAPb,CACT;QACEM,OAAO,EAAEP,iBAAiB;QAC1BQ,QAAQ,EAAEJ,cAAc;QACxBK,KAAK,EAAE;OACR,CACF;MAAAC,OAAA,GAVCb,aAAa,EACbC,WAAW,EACXC,gBAAgB,CAAE;MAAA;;;;;;2EAWTM,SAAS;IAAAM,YAAA,GAlBlBV,YAAY,EACZC,cAAc,EACdC,eAAe;IAAAO,OAAA,GAGfb,aAAa,EACbC,WAAW,EACXC,gBAAgB,CAAE;IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}