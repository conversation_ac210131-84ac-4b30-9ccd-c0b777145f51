{"ast": null, "code": "import * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js'; // Import Chart.js and register components\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = [\"myChart\"];\nconst _c1 = function (a1) {\n  return [\"/edit-responsable\", a1];\n};\nfunction UsersComponent_tr_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\")(16, \"a\", 40)(17, \"i\", 41);\n    i0.ɵɵtext(18, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"a\", 42);\n    i0.ɵɵlistener(\"click\", function UsersComponent_tr_73_Template_a_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const responsable_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(responsable_r1.id ? ctx_r2.deleteResponsable(responsable_r1.id) : null);\n    });\n    i0.ɵɵelementStart(20, \"i\", 43);\n    i0.ɵɵtext(21, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const responsable_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.nom);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.prenom);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.telephone);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.role);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.groupe == null ? null : responsable_r1.groupe.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(8, _c1, responsable_r1.id));\n  }\n}\nconst _c2 = function () {\n  return [\"/add-responsable\"];\n};\nexport class UsersComponent {\n  constructor(responsableService, cdr, router) {\n    this.responsableService = responsableService;\n    this.cdr = cdr;\n    this.router = router;\n    this.responsables = [];\n  }\n  ngOnInit() {\n    this.loadResponsables();\n  }\n  loadResponsables() {\n    this.responsableService.getAllUsers().subscribe(data => {\n      this.responsables = data;\n    }, error => {\n      console.error('Error fetching users:', error);\n    });\n  }\n  deleteResponsable(id) {\n    if (id === undefined) {\n      console.error(\"Error: Responsable ID is undefined\");\n      return;\n    }\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: response => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: error => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  updateResponsable(id, updatedResponsable) {\n    if (id && updatedResponsable) {\n      // Ensure that the id and updatedResponsable are valid\n      this.responsableService.updateResponsable(id, updatedResponsable).subscribe(() => {\n        this.loadResponsables();\n      }, error => {\n        console.error('Error updating responsable:', error);\n      });\n    }\n  }\n  trackById(index, item) {\n    return item.id ?? 0; // Return 0 if id is undefined\n  }\n\n  ngAfterViewInit() {\n    feather.replace();\n    Chart.register(...registerables); // Register chart.js components for version 4+\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement;\n      // Ensure the chart has data and a configuration\n      const chart = new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n          datasets: [{\n            data: [15339, 21345, 18483, 24003, 23489, 24092, 12034],\n            tension: 0.4,\n            backgroundColor: 'rgba(0,123,255,0.2)',\n            borderColor: '#007bff',\n            borderWidth: 4,\n            pointBackgroundColor: '#007bff'\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            y: {\n              beginAtZero: true\n            }\n          }\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function UsersComponent_Factory(t) {\n      return new (t || UsersComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UsersComponent,\n      selectors: [[\"app-respon-dashboard\"]],\n      viewQuery: function UsersComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 74,\n      vars: 3,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/css/bootstrap.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"form-control-dark\", \"w-100\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-dark\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"sidebar-header\", \"text-center\"], [\"src\", \"path_to_your_logo.png\", \"alt\", \"Logo\", 1, \"sidebar-logo\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [1, \"fas\", \"fa-tachometer-alt\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [1, \"fas\", \"fa-users\"], [\"href\", \"/groups\", 1, \"nav-link\"], [1, \"fas\", \"fa-cogs\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"text-center\", \"mt-4\", \"mb-4\"], [1, \"mb-3\", \"text-right\"], [1, \"btn\", \"btn-custom\", 3, \"routerLink\"], [1, \"material-icons\", \"align-middle\", 2, \"font-size\", \"20px\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [3, \"routerLink\"], [\"data-toggle\", \"tooltip\", \"title\", \"Modifier\", 1, \"material-icons\", \"edit-icon\"], [\"href\", \"/users\", 3, \"click\"], [\"data-toggle\", \"tooltip\", \"title\", \"Supprimer\", 1, \"material-icons\", \"delete-icon\"]],\n      template: function UsersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"link\", 6)(8, \"link\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10);\n          i0.ɵɵelementStart(12, \"title\");\n          i0.ɵɵtext(13, \"Dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"body\")(15, \"nav\", 11)(16, \"a\", 12);\n          i0.ɵɵtext(17, \"GTI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"input\", 13);\n          i0.ɵɵelementStart(19, \"ul\", 14)(20, \"li\", 15)(21, \"a\", 16);\n          i0.ɵɵlistener(\"click\", function UsersComponent_Template_a_click_21_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(22, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 17)(24, \"div\", 18)(25, \"nav\", 19)(26, \"div\", 20)(27, \"div\", 21);\n          i0.ɵɵelement(28, \"img\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"ul\", 23)(30, \"li\", 24)(31, \"a\", 25);\n          i0.ɵɵelement(32, \"i\", 26);\n          i0.ɵɵtext(33, \" Dashboard \");\n          i0.ɵɵelementStart(34, \"span\", 27);\n          i0.ɵɵtext(35, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"li\", 24)(37, \"a\", 28);\n          i0.ɵɵelement(38, \"i\", 29);\n          i0.ɵɵtext(39, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"li\", 24)(41, \"a\", 30);\n          i0.ɵɵelement(42, \"i\", 31);\n          i0.ɵɵtext(43, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(44, \"main\", 32)(45, \"h2\", 33);\n          i0.ɵɵtext(46, \"Liste des utilisateurs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 34)(48, \"a\", 35)(49, \"i\", 36);\n          i0.ɵɵtext(50, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(51, \" Ajouter un utilisateur \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 37)(53, \"table\", 38)(54, \"thead\")(55, \"tr\")(56, \"th\");\n          i0.ɵɵtext(57, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"th\");\n          i0.ɵɵtext(59, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"th\");\n          i0.ɵɵtext(61, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"th\");\n          i0.ɵɵtext(63, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"th\");\n          i0.ɵɵtext(65, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"th\");\n          i0.ɵɵtext(67, \"R\\u00F4le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"th\");\n          i0.ɵɵtext(69, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"th\");\n          i0.ɵɵtext(71, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(72, \"tbody\");\n          i0.ɵɵtemplate(73, UsersComponent_tr_73_Template, 22, 10, \"tr\", 39);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(48);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c2));\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngForOf\", ctx.responsables);\n        }\n      },\n      dependencies: [i3.NgForOf, i2.RouterLink],\n      styles: [\"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, .25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] { border-top: 1px solid #e5e5e5; }\\n.border-bottom[_ngcontent-%COMP%] { border-bottom: 1px solid #e5e5e5; }\\n\\n\\n.btn[_ngcontent-%COMP%] {\\npadding: 8px 16px;\\nfont-size: 14px;\\nborder: none;\\ncursor: pointer;\\nborder-radius: 4px;\\nfont-weight: bold;\\ntransition: all 0.3s ease;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\nbackground-color: #ff4d4d; \\n\\ncolor: white;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%]:hover {\\nbackground-color: #ff1a1a;\\n}\\n\\n.modify-btn[_ngcontent-%COMP%] {\\nbackground-color: #4d94ff; \\n\\ncolor: white;\\n}\\n\\n.modify-btn[_ngcontent-%COMP%]:hover {\\nbackground-color: #0066cc;\\n}\\n\\n\\n\\n\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n  color: #007bff;  \\n\\n}\\n\\n\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;  \\n\\n}\\n\\n\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;  \\n\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n  color: #c82333;  \\n\\n}\\n\\n.modal[_ngcontent-%COMP%]   .modal-dialog[_ngcontent-%COMP%] {\\nmax-width: 400px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%], .modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%], .modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\\npadding: 20px 30px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\nborder-radius: 3px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\\nbackground: #ecf0f1;\\nborder-radius: 0 0 3px 3px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n    display: inline-block;\\n}\\n.modal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\nborder-radius: 2px;\\nbox-shadow: none;\\nborder-color: #dddddd;\\n}\\n.modal[_ngcontent-%COMP%]   textarea.form-control[_ngcontent-%COMP%] {\\nresize: vertical;\\n}\\n.modal[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\nborder-radius: 2px;\\nmin-width: 100px;\\n}\\t\\n.modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\nfont-weight: normal;\\n}\\t\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"body[_ngcontent-%COMP%] {\\n        font-family: 'Poppins', sans-serif;\\n        margin: 0;\\n        padding: 0;\\n        min-height: 100vh;\\n        background: linear-gradient(-45deg, #1e3c72, #2a5298, #1e3c72, #2a5298);\\n        background-size: 400% 400%;\\n        animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n        color: #fff;\\n      }\\n\\n      @keyframes _ngcontent-%COMP%_gradientMove {\\n        0% { background-position: 0% 50%; }\\n        50% { background-position: 100% 50%; }\\n        100% { background-position: 0% 50%; }\\n      }\\n\\n      .container-fluid[_ngcontent-%COMP%] {\\n        background: rgba(255, 255, 255, 0.06);\\n        border-radius: 15px;\\n        padding: 2rem;\\n        margin-top: 20px;\\n      }\\n\\n      .table-responsive[_ngcontent-%COMP%] {\\n        background: rgba(255, 255, 255, 0.06);\\n        border-radius: 15px;\\n        padding: 1.5rem;\\n        backdrop-filter: blur(12px);\\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n      }\\n\\n      .table[_ngcontent-%COMP%] {\\n        color: #fff;\\n        border-radius: 15px;\\n        overflow: hidden;\\n        border-collapse: separate;\\n        border-spacing: 0 12px;\\n      }\\n\\n      .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n        background-color: rgba(255, 255, 255, 0.15);\\n        color: #ffffff;\\n        font-weight: 600;\\n        text-align: center;\\n        border: none;\\n        padding: 1rem;\\n      }\\n\\n      .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n        background-color: rgba(255, 255, 255, 0.1);\\n        transition: transform 0.2s ease, box-shadow 0.2s ease;\\n        text-align: center;\\n        border-radius: 12px;\\n      }\\n\\n      .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n        transform: scale(1.01);\\n        box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n      }\\n\\n      .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n        vertical-align: middle;\\n        padding: 0.9rem;\\n        font-size: 0.95rem;\\n      }\\n\\n      .btn-custom[_ngcontent-%COMP%] {\\n        background-color: #28a745;\\n        color: white;\\n        border: none;\\n        padding: 12px 25px;\\n        border-radius: 30px;\\n        text-transform: uppercase;\\n        font-weight: bold;\\n        transition: background 0.3s, transform 0.2s;\\n        box-shadow: 0 4px 15px rgba(0,0,0,0.2);\\n      }\\n\\n      .btn-custom[_ngcontent-%COMP%]:hover {\\n        background-color: #218838;\\n        transform: translateY(-2px);\\n      }\\n\\n      .edit-icon[_ngcontent-%COMP%] {\\n        color: #17a2b8;\\n        cursor: pointer;\\n        font-size: 20px;\\n        margin: 0 10px;\\n        transition: color 0.3s;\\n      }\\n\\n      .edit-icon[_ngcontent-%COMP%]:hover {\\n        color: #0d6efd;\\n      }\\n\\n      .delete-icon[_ngcontent-%COMP%] {\\n        color: #dc3545;\\n        cursor: pointer;\\n        font-size: 20px;\\n        transition: color 0.3s;\\n      }\\n\\n      .delete-icon[_ngcontent-%COMP%]:hover {\\n        color: #bd2130;\\n      }\\n\\n      .navbar[_ngcontent-%COMP%] {\\n        background-color: #1c1c1c !important;\\n      }\\n\\n      h2[_ngcontent-%COMP%] {\\n        color: #fff;\\n      }\\n\\n      a.router-link[_ngcontent-%COMP%] {\\n        text-decoration: none;\\n      }\\n\\n      \\n\\n      .sidebar[_ngcontent-%COMP%] {\\n        background-color: #1d1f20;\\n        color: #fff;\\n        padding-top: 30px;\\n        position: fixed;\\n        height: 100%;\\n        top: 0;\\n        left: 0;\\n        bottom: 0;\\n        z-index: 1000;\\n        box-shadow: 2px 0px 15px rgba(0, 0, 0, 0.2);\\n      }\\n\\n      .sidebar-header[_ngcontent-%COMP%] {\\n        padding: 20px;\\n        margin-bottom: 30px;\\n      }\\n\\n      .sidebar-logo[_ngcontent-%COMP%] {\\n        width: 50px;\\n        height: auto;\\n        display: block;\\n        margin: 0 auto;\\n      }\\n\\n      .nav-link[_ngcontent-%COMP%] {\\n        font-size: 16px;\\n        padding: 15px 20px;\\n        color: #bbb;\\n        transition: 0.3s ease;\\n      }\\n\\n      .nav-link[_ngcontent-%COMP%]:hover, .nav-link.active[_ngcontent-%COMP%] {\\n        background-color: #2c3e50;\\n        color: #fff;\\n        border-radius: 5px;\\n      }\\n\\n      .nav-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n        margin-right: 10px;\\n        font-size: 18px;\\n      }\\n\\n      .sidebar[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%] {\\n        margin-bottom: 20px;\\n      }\\n\\n      \\n\\n      @media (max-width: 991px) {\\n        .sidebar[_ngcontent-%COMP%] {\\n          position: absolute;\\n          width: 250px;\\n          top: 0;\\n          bottom: 0;\\n          left: -250px;\\n          transition: all 0.3s ease;\\n        }\\n\\n        .sidebar.active[_ngcontent-%COMP%] {\\n          left: 0;\\n        }\\n\\n        .sidebar-header[_ngcontent-%COMP%] {\\n          display: flex;\\n          justify-content: center;\\n          align-items: center;\\n        }\\n      }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "Chart", "registerables", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "UsersComponent_tr_73_Template_a_click_19_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "responsable_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "id", "deleteResponsable", "ɵɵadvance", "ɵɵtextInterpolate", "nom", "prenom", "email", "telephone", "role", "groupe", "nomGroupe", "ɵɵproperty", "ɵɵpureFunction1", "_c1", "UsersComponent", "constructor", "responsableService", "cdr", "router", "responsables", "ngOnInit", "loadResponsables", "getAllUsers", "subscribe", "data", "error", "console", "undefined", "confirm", "next", "response", "log", "filter", "responsable", "logout", "localStorage", "removeItem", "getItem", "navigate", "updateResponsable", "updatedResponsable", "trackById", "index", "item", "ngAfterViewInit", "replace", "register", "myChartRef", "ctx", "nativeElement", "chart", "type", "labels", "datasets", "tension", "backgroundColor", "borderColor", "borderWidth", "pointBackgroundColor", "options", "responsive", "maintainAspectRatio", "scales", "y", "beginAtZero", "ɵɵdirectiveInject", "i1", "ResponsableService", "ChangeDetectorRef", "i2", "Router", "selectors", "viewQuery", "UsersComponent_Query", "rf", "ɵɵelement", "UsersComponent_Template_a_click_21_listener", "ɵɵtemplate", "UsersComponent_tr_73_Template", "ɵɵpureFunction0", "_c2"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\users\\users.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\users\\users.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, AfterViewInit, ElementRef, ChangeDetectorRef } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js'; // Import Chart.js and register components\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './users.component.html',\n  styleUrls: ['../../dashboard.css']\n})\nexport class UsersComponent implements AfterViewInit, OnInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  responsables: User[] = []; \n\n  constructor(\n    private responsableService: ResponsableService,\n    private cdr: ChangeDetectorRef, \n    private router: Router \n  ) {}\n\n  ngOnInit(): void {\n    this.loadResponsables();\n  }\n\n  loadResponsables() {\n    this.responsableService.getAllUsers().subscribe(\n      (data) => {\n        this.responsables = data;\n      },\n      (error) => {\n        console.error('Error fetching users:', error);\n      }\n    );\n  }\n  \n  deleteResponsable(id?: number) {\n    if (id === undefined) {\n      console.error(\"Error: Responsable ID is undefined\");\n      return;\n    }\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: (response) => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: (error) => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n  \n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n\n  updateResponsable(id: number, updatedResponsable: User): void {\n    if (id && updatedResponsable) {  // Ensure that the id and updatedResponsable are valid\n      this.responsableService.updateResponsable(id, updatedResponsable).subscribe(\n        () => {\n          this.loadResponsables(); \n        },\n        (error) => {\n          console.error('Error updating responsable:', error);\n        }\n      );\n    }\n  }\n  trackById(index: number, item: User): number | undefined {\n    return item.id ?? 0;  // Return 0 if id is undefined\n  }\n  \n\n  ngAfterViewInit() {\n    feather.replace();\n    Chart.register(...registerables);  // Register chart.js components for version 4+\n\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement as HTMLCanvasElement;\n\n      // Ensure the chart has data and a configuration\n      const chart = new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n          datasets: [{\n            data: [15339, 21345, 18483, 24003, 23489, 24092, 12034],\n            tension: 0.4, // Replaces lineTension\n            backgroundColor: 'rgba(0,123,255,0.2)', // Light blue background\n            borderColor: '#007bff',\n            borderWidth: 4,\n            pointBackgroundColor: '#007bff',\n          }]\n        },\n        options: {\n          responsive: true, // Make the chart responsive\n          maintainAspectRatio: false, // Allow the chart to resize with container\n          scales: {\n            y: {\n              beginAtZero: true\n            }\n          }\n        }\n      });\n    }\n  }\n}", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n\n    <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/css/bootstrap.min.css\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n\n    <style>\n      body {\n        font-family: 'Poppins', sans-serif;\n        margin: 0;\n        padding: 0;\n        min-height: 100vh;\n        background: linear-gradient(-45deg, #1e3c72, #2a5298, #1e3c72, #2a5298);\n        background-size: 400% 400%;\n        animation: gradientMove 20s ease infinite;\n        color: #fff;\n      }\n\n      @keyframes gradientMove {\n        0% { background-position: 0% 50%; }\n        50% { background-position: 100% 50%; }\n        100% { background-position: 0% 50%; }\n      }\n\n      .container-fluid {\n        background: rgba(255, 255, 255, 0.06);\n        border-radius: 15px;\n        padding: 2rem;\n        margin-top: 20px;\n      }\n\n      .table-responsive {\n        background: rgba(255, 255, 255, 0.06);\n        border-radius: 15px;\n        padding: 1.5rem;\n        backdrop-filter: blur(12px);\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\n      }\n\n      .table {\n        color: #fff;\n        border-radius: 15px;\n        overflow: hidden;\n        border-collapse: separate;\n        border-spacing: 0 12px;\n      }\n\n      .table thead th {\n        background-color: rgba(255, 255, 255, 0.15);\n        color: #ffffff;\n        font-weight: 600;\n        text-align: center;\n        border: none;\n        padding: 1rem;\n      }\n\n      .table tbody tr {\n        background-color: rgba(255, 255, 255, 0.1);\n        transition: transform 0.2s ease, box-shadow 0.2s ease;\n        text-align: center;\n        border-radius: 12px;\n      }\n\n      .table tbody tr:hover {\n        transform: scale(1.01);\n        box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\n      }\n\n      .table td {\n        vertical-align: middle;\n        padding: 0.9rem;\n        font-size: 0.95rem;\n      }\n\n      .btn-custom {\n        background-color: #28a745;\n        color: white;\n        border: none;\n        padding: 12px 25px;\n        border-radius: 30px;\n        text-transform: uppercase;\n        font-weight: bold;\n        transition: background 0.3s, transform 0.2s;\n        box-shadow: 0 4px 15px rgba(0,0,0,0.2);\n      }\n\n      .btn-custom:hover {\n        background-color: #218838;\n        transform: translateY(-2px);\n      }\n\n      .edit-icon {\n        color: #17a2b8;\n        cursor: pointer;\n        font-size: 20px;\n        margin: 0 10px;\n        transition: color 0.3s;\n      }\n\n      .edit-icon:hover {\n        color: #0d6efd;\n      }\n\n      .delete-icon {\n        color: #dc3545;\n        cursor: pointer;\n        font-size: 20px;\n        transition: color 0.3s;\n      }\n\n      .delete-icon:hover {\n        color: #bd2130;\n      }\n\n      .navbar {\n        background-color: #1c1c1c !important;\n      }\n\n      h2 {\n        color: #fff;\n      }\n\n      a.router-link {\n        text-decoration: none;\n      }\n\n      /* Sidebar Style */\n      .sidebar {\n        background-color: #1d1f20;\n        color: #fff;\n        padding-top: 30px;\n        position: fixed;\n        height: 100%;\n        top: 0;\n        left: 0;\n        bottom: 0;\n        z-index: 1000;\n        box-shadow: 2px 0px 15px rgba(0, 0, 0, 0.2);\n      }\n\n      .sidebar-header {\n        padding: 20px;\n        margin-bottom: 30px;\n      }\n\n      .sidebar-logo {\n        width: 50px;\n        height: auto;\n        display: block;\n        margin: 0 auto;\n      }\n\n      .nav-link {\n        font-size: 16px;\n        padding: 15px 20px;\n        color: #bbb;\n        transition: 0.3s ease;\n      }\n\n      .nav-link:hover, .nav-link.active {\n        background-color: #2c3e50;\n        color: #fff;\n        border-radius: 5px;\n      }\n\n      .nav-link i {\n        margin-right: 10px;\n        font-size: 18px;\n      }\n\n      .sidebar .nav-item {\n        margin-bottom: 20px;\n      }\n\n      /* Responsive Styles */\n      @media (max-width: 991px) {\n        .sidebar {\n          position: absolute;\n          width: 250px;\n          top: 0;\n          bottom: 0;\n          left: -250px;\n          transition: all 0.3s ease;\n        }\n\n        .sidebar.active {\n          left: 0;\n        }\n\n        .sidebar-header {\n          display: flex;\n          justify-content: center;\n          align-items: center;\n        }\n      }\n      \n    </style>\n\n    <title>Dashboard</title>\n  </head>\n\n  <body>\n    <!-- Navbar -->\n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"adminDash\">GTI</a>\n      <input class=\"form-control form-control-dark w-100\" type=\"text\" placeholder=\"Search\" aria-label=\"Search\">\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block bg-dark sidebar\">\n          <div class=\"sidebar-sticky\">\n            <div class=\"sidebar-header text-center\">\n              <img src=\"path_to_your_logo.png\" alt=\"Logo\" class=\"sidebar-logo\">\n            </div>\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <i class=\"fas fa-tachometer-alt\"></i>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <i class=\"fas fa-users\"></i>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <i class=\"fas fa-cogs\"></i>\n                  Gestion des groupes\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <h2 class=\"text-center mt-4 mb-4\">Liste des utilisateurs</h2>\n          <div class=\"mb-3 text-right\">\n            <a [routerLink]=\"['/add-responsable']\" class=\"btn btn-custom\">\n              <i class=\"material-icons align-middle\" style=\"font-size: 20px;\">add</i>\n              Ajouter un utilisateur\n            </a>\n          </div>\n\n          <div class=\"table-responsive\">\n            <table class=\"table table-hover\">\n              <thead>\n                <tr>\n                  <th>ID</th>\n                  <th>Nom</th>\n                  <th>Prénom</th>\n                  <th>Email</th>\n                  <th>Téléphone</th>\n                  <th>Rôle</th>\n                  <th>Groupe</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let responsable of responsables\">\n                  <td>{{ responsable.id }}</td>\n                  <td>{{ responsable.nom }}</td>\n                  <td>{{ responsable.prenom }}</td>\n                  <td>{{ responsable.email }}</td>\n                  <td>{{ responsable.telephone }}</td>\n                  <td>{{ responsable.role }}</td>\n                  <td>{{ responsable.groupe?.nomGroupe }}</td>\n                  <td>\n                    <a [routerLink]=\"['/edit-responsable', responsable.id]\">\n                      <i class=\"material-icons edit-icon\" data-toggle=\"tooltip\" title=\"Modifier\">edit</i>\n                    </a>\n                    <a href=\"/users\" (click)=\"responsable.id ? deleteResponsable(responsable.id) : null\">\n                      <i class=\"material-icons delete-icon\" data-toggle=\"tooltip\" title=\"Supprimer\">delete</i>\n                    </a>\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <!-- Scripts -->\n    <script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js\"></script>\n    <script src=\"https://unpkg.com/feather-icons\"></script>\n    <script>feather.replace()</script>\n  </body>\n</html>\n"], "mappings": "AAGA,OAAO,KAAKA,OAAO,MAAM,eAAe;AACxC,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU,CAAC,CAAC;;;;;;;;;;;;ICgRjCC,EAAA,CAAAC,cAAA,SAA6C;IACvCD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,UAAI;IAE2ED,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAErFH,EAAA,CAAAC,cAAA,aAAqF;IAApED,EAAA,CAAAI,UAAA,mBAAAC,kDAAA;MAAA,MAAAC,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,cAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAJ,cAAA,CAAAK,EAAA,GAAiBH,MAAA,CAAAI,iBAAA,CAAAN,cAAA,CAAAK,EAAA,CAAiC,GAAG,IAAI;IAAA,EAAC;IAClFd,EAAA,CAAAC,cAAA,aAA8E;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAZxFH,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAK,EAAA,CAAoB;IACpBd,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAS,GAAA,CAAqB;IACrBlB,EAAA,CAAAgB,SAAA,GAAwB;IAAxBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAU,MAAA,CAAwB;IACxBnB,EAAA,CAAAgB,SAAA,GAAuB;IAAvBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAW,KAAA,CAAuB;IACvBpB,EAAA,CAAAgB,SAAA,GAA2B;IAA3BhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAY,SAAA,CAA2B;IAC3BrB,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAa,IAAA,CAAsB;IACtBtB,EAAA,CAAAgB,SAAA,GAAmC;IAAnChB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAc,MAAA,kBAAAd,cAAA,CAAAc,MAAA,CAAAC,SAAA,CAAmC;IAElCxB,EAAA,CAAAgB,SAAA,GAAoD;IAApDhB,EAAA,CAAAyB,UAAA,eAAAzB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAAlB,cAAA,CAAAK,EAAA,EAAoD;;;;;;ADjR3E,OAAM,MAAOc,cAAc;EAIzBC,YACUC,kBAAsC,EACtCC,GAAsB,EACtBC,MAAc;IAFd,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,YAAY,GAAW,EAAE;EAMtB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACL,kBAAkB,CAACM,WAAW,EAAE,CAACC,SAAS,CAC5CC,IAAI,IAAI;MACP,IAAI,CAACL,YAAY,GAAGK,IAAI;IAC1B,CAAC,EACAC,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,CACF;EACH;EAEAxB,iBAAiBA,CAACD,EAAW;IAC3B,IAAIA,EAAE,KAAK2B,SAAS,EAAE;MACpBD,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAC;MACnD;;IAEF,IAAIG,OAAO,CAAC,mDAAmD,CAAC,EAAE;MAChE,IAAI,CAACZ,kBAAkB,CAACf,iBAAiB,CAACD,EAAE,CAAC,CAACuB,SAAS,CAAC;QACtDM,IAAI,EAAGC,QAAQ,IAAI;UACjBJ,OAAO,CAACK,GAAG,CAAC,kBAAkB,EAAED,QAAQ,CAAC;UACzC,IAAI,CAACX,YAAY,GAAG,IAAI,CAACA,YAAY,CAACa,MAAM,CAACC,WAAW,IAAIA,WAAW,CAACjC,EAAE,KAAKA,EAAE,CAAC;QACpF,CAAC;QACDyB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAGAS,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCV,OAAO,CAACK,GAAG,CAACI,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,iBAAiBA,CAACvC,EAAU,EAAEwC,kBAAwB;IACpD,IAAIxC,EAAE,IAAIwC,kBAAkB,EAAE;MAAG;MAC/B,IAAI,CAACxB,kBAAkB,CAACuB,iBAAiB,CAACvC,EAAE,EAAEwC,kBAAkB,CAAC,CAACjB,SAAS,CACzE,MAAK;QACH,IAAI,CAACF,gBAAgB,EAAE;MACzB,CAAC,EACAI,KAAK,IAAI;QACRC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD,CAAC,CACF;;EAEL;EACAgB,SAASA,CAACC,KAAa,EAAEC,IAAU;IACjC,OAAOA,IAAI,CAAC3C,EAAE,IAAI,CAAC,CAAC,CAAE;EACxB;;EAGA4C,eAAeA,CAAA;IACb7D,OAAO,CAAC8D,OAAO,EAAE;IACjB7D,KAAK,CAAC8D,QAAQ,CAAC,GAAG7D,aAAa,CAAC,CAAC,CAAE;IAEnC,IAAI,IAAI,CAAC8D,UAAU,EAAE;MACnB,MAAMC,GAAG,GAAG,IAAI,CAACD,UAAU,CAACE,aAAkC;MAE9D;MACA,MAAMC,KAAK,GAAG,IAAIlE,KAAK,CAACgE,GAAG,EAAE;QAC3BG,IAAI,EAAE,MAAM;QACZ3B,IAAI,EAAE;UACJ4B,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;UACtFC,QAAQ,EAAE,CAAC;YACT7B,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YACvD8B,OAAO,EAAE,GAAG;YACZC,eAAe,EAAE,qBAAqB;YACtCC,WAAW,EAAE,SAAS;YACtBC,WAAW,EAAE,CAAC;YACdC,oBAAoB,EAAE;WACvB;SACF;QACDC,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE,KAAK;UAC1BC,MAAM,EAAE;YACNC,CAAC,EAAE;cACDC,WAAW,EAAE;;;;OAIpB,CAAC;;EAEN;;;uBArGWlD,cAAc,EAAA5B,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAjF,EAAA,CAAA+E,iBAAA,CAAA/E,EAAA,CAAAkF,iBAAA,GAAAlF,EAAA,CAAA+E,iBAAA,CAAAI,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdxD,cAAc;MAAAyD,SAAA;MAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAA1B,GAAA;QAAA,IAAA0B,EAAA;;;;;;;;;;;;;UCX3BxF,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAyF,SAAA,cAAsB;UA6MtBzF,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAG1BH,EAAA,CAAAC,cAAA,YAAM;UAG8DD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAyF,SAAA,iBAAyG;UACzGzF,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAI,UAAA,mBAAAsF,4CAAA;YAAA,OAAS5B,GAAA,CAAAd,MAAA,EAAQ;UAAA,EAAC;UAAChD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKzDH,EAAA,CAAAC,cAAA,eAA6B;UAKnBD,EAAA,CAAAyF,SAAA,eAAiE;UACnEzF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAA4B;UAGtBD,EAAA,CAAAyF,SAAA,aAAqC;UACrCzF,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGpDH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAyF,SAAA,aAA4B;UAC5BzF,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAyF,SAAA,aAA2B;UAC3BzF,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKZH,EAAA,CAAAC,cAAA,gBAAkE;UAC9BD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7DH,EAAA,CAAAC,cAAA,eAA6B;UAEuCD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAE,MAAA,gCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACXH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACZH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACfH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,iBAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACbH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACfH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpBH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA2F,UAAA,KAAAC,6BAAA,mBAgBK;UACP5F,EAAA,CAAAG,YAAA,EAAQ;;;UAtCPH,EAAA,CAAAgB,SAAA,IAAmC;UAAnChB,EAAA,CAAAyB,UAAA,eAAAzB,EAAA,CAAA6F,eAAA,IAAAC,GAAA,EAAmC;UAqBN9F,EAAA,CAAAgB,SAAA,IAAe;UAAfhB,EAAA,CAAAyB,UAAA,YAAAqC,GAAA,CAAA7B,YAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}