{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../authentication.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction LoginComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.loginError, \" \");\n  }\n}\nexport class LoginComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.matricule = ''; // Treating matricule as a string in the UI\n    this.password = '';\n    this.loginError = '';\n    this.prenom = '';\n    this.nom = '';\n  }\n  onLogin() {\n    // Convert matricule (string) to number before passing to the login function\n    const matriculeNumber = this.matricule ? Number(this.matricule) : 0; // Default to 0 if it's empty\n    this.authService.login(matriculeNumber, this.password).subscribe(response => {\n      this.authService.storeToken(response.token);\n      const {\n        role,\n        groupe,\n        prenom,\n        nom\n      } = this.authService.getUserDetailsFromToken(response.token);\n      console.log(\"Login successful, User role:\", role, \"Group:\", groupe);\n      if (role === 'ADMIN') {\n        const adminData = {\n          prenom: prenom || 'Admin',\n          nom: nom || ''\n        };\n        localStorage.setItem('admin', JSON.stringify(adminData));\n        this.router.navigate(['/adminDash']);\n      } else if (role === 'RESPONSABLE') {\n        if (groupe) {\n          this.redirectToGroupDashboard(groupe);\n        } else {\n          console.error(\"RESPONSABLE must have a group.\");\n          this.loginError = \"RESPONSABLE must have a group.\";\n        }\n      } else {\n        this.loginError = \"Matricule ou mot de passe incorrect!\";\n      }\n    }, error => {\n      console.error(\"Login failed:\", error);\n      this.loginError = \"Matricule ou mot de passe incorrect!\";\n    });\n  }\n  redirectToGroupDashboard(groupe) {\n    const allowedGroups = {\n      'TRANSACTION': '/transactions',\n      'ACTION': '/actions',\n      'ACTIONNAIRE': '/actionnaires',\n      'PORTEFEUILLE': '/port',\n      'NOTIFICATION': '/reports'\n    };\n    const redirectUrl = allowedGroups[groupe];\n    if (redirectUrl) {\n      this.router.navigate([redirectUrl]);\n    } else {\n      console.error(\"Invalid group:\", groupe);\n      this.router.navigate(['/not-authorized']);\n    }\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 34,\n      vars: 3,\n      consts: [[1, \"full-page-bg\"], [1, \"container\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"vh-100\"], [1, \"card\", \"p-4\", \"shadow-lg\", \"rounded\", 2, \"width\", \"400px\"], [1, \"text-center\"], [1, \"mb-3\"], [1, \"text-muted\"], [3, \"ngSubmit\"], [\"for\", \"matricule\", 1, \"form-label\", \"fw-bold\"], [1, \"input-group\"], [1, \"input-group-text\"], [1, \"bi\", \"bi-person-badge\"], [\"type\", \"number\", \"id\", \"matricule\", \"name\", \"matricule\", \"required\", \"\", \"placeholder\", \"Entrez votre matricule\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\", 1, \"form-label\", \"fw-bold\"], [1, \"bi\", \"bi-lock\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"placeholder\", \"Entrez votre mot de passe\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\", \"fw-bold\"], [1, \"bi\", \"bi-box-arrow-in-right\"], [\"class\", \"alert alert-danger text-center mt-3\", 4, \"ngIf\"], [1, \"text-center\", \"mt-3\"], [\"href\", \"#\", 1, \"text-decoration-none\"], [\"routerLink\", \"/signup\", 1, \"btn\", \"btn-outline-secondary\", \"w-100\"], [1, \"bi\", \"bi-person-plus\"], [1, \"alert\", \"alert-danger\", \"text-center\", \"mt-3\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n          i0.ɵɵtext(5, \"Se connecter\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Connectez-vous \\u00E0 votre compte\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_8_listener() {\n            return ctx.onLogin();\n          });\n          i0.ɵɵelementStart(9, \"div\", 4)(10, \"label\", 7);\n          i0.ɵɵtext(11, \"Matricule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"span\", 9);\n          i0.ɵɵelement(14, \"i\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.matricule = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 4)(17, \"label\", 12);\n          i0.ɵɵtext(18, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 8)(20, \"span\", 9);\n          i0.ɵɵelement(21, \"i\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_22_listener($event) {\n            return ctx.password = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"button\", 15);\n          i0.ɵɵelement(24, \"i\", 16);\n          i0.ɵɵtext(25, \" Se connecter \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(26, LoginComponent_div_26_Template, 2, 1, \"div\", 17);\n          i0.ɵɵelementStart(27, \"div\", 18)(28, \"a\", 19);\n          i0.ɵɵtext(29, \"Mot de passe oubli\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 18)(31, \"a\", 20);\n          i0.ɵɵelement(32, \"i\", 21);\n          i0.ɵɵtext(33, \" S'inscrire \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngModel\", ctx.matricule);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.password);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.loginError);\n        }\n      },\n      dependencies: [i3.NgIf, i2.RouterLink, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NumberValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n      styles: [\"\\n\\n.full-page-bg[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  width: 100%;\\n  background-image: url('/assets/images/test.jpg');\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n  background-attachment: fixed;\\n}\\n\\n\\n\\n.card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9); \\n\\n  padding: 40px;\\n  border-radius: 15px; \\n\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); \\n\\n}\\n\\n\\n\\nh2[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\np.text-muted[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\n\\n\\n.input-group[_ngcontent-%COMP%] {\\n  border-radius: 10px; \\n\\n  overflow: hidden; \\n\\n}\\n\\n.input-group-text[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  border-radius: 10px 0 0 10px; \\n\\n}\\n\\n.input-group-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  border: 1px solid #ddd;\\n  padding: 12px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);\\n}\\n\\n\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border: none;\\n  padding: 12px 30px;\\n  font-size: 1rem;\\n  border-radius: 25px;\\n  transition: background-color 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n}\\n\\n\\n\\n\\n\\n.btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  color: #007bff;\\n  padding: 12px 30px;\\n  border-radius: 25px;\\n  transition: background-color 0.3s ease, color 0.3s ease;\\n}\\n\\n.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #007bff;\\n  color: white !important; \\n\\n}\\n\\n\\n\\n.alert-danger[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  font-size: 0.9rem;\\n  color: #e74c3c;\\n  background-color: rgba(231, 76, 60, 0.1); \\n\\n  border-color: #e74c3c;\\n  padding: 10px;\\n  border-radius: 5px;\\n}\\n\\n\\n\\n.text-center[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n}\\n\\n.text-center[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.btn-outline-secondary[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXV0aC9sb2dpbi9sb2dpbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLGlDQUFpQztBQUNqQztFQUNFLGFBQWE7RUFDYixXQUFXO0VBQ1gsZ0RBQWdEO0VBQ2hELHNCQUFzQjtFQUN0QiwyQkFBMkI7RUFDM0IsNEJBQTRCO0VBQzVCLDRCQUE0QjtBQUM5Qjs7QUFFQSw2QkFBNkI7QUFDN0I7RUFDRSxvQ0FBb0MsRUFBRSxzQ0FBc0M7RUFDNUUsYUFBYTtFQUNiLG1CQUFtQixFQUFFLG9CQUFvQjtFQUN6QywwQ0FBMEMsRUFBRSx5QkFBeUI7QUFDdkU7O0FBRUEsb0JBQW9CO0FBQ3BCO0VBQ0UsZUFBZTtFQUNmLGdCQUFnQjtFQUNoQixXQUFXO0VBQ1gsbUJBQW1CO0FBQ3JCOztBQUVBLGtCQUFrQjtBQUNsQjtFQUNFLFdBQVc7QUFDYjs7QUFFQSxrQkFBa0I7QUFDbEI7RUFDRSxtQkFBbUIsRUFBRSxxQ0FBcUM7RUFDMUQsZ0JBQWdCLEVBQUUsNERBQTREO0FBQ2hGOztBQUVBO0VBQ0UseUJBQXlCO0VBQ3pCLFlBQVk7RUFDWiw0QkFBNEIsRUFBRSxpQ0FBaUM7QUFDakU7O0FBRUE7RUFDRSxZQUFZO0FBQ2Q7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsc0JBQXNCO0VBQ3RCLGFBQWE7RUFDYixlQUFlO0VBQ2YseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UscUJBQXFCO0VBQ3JCLDBDQUEwQztBQUM1Qzs7QUFFQSxtQkFBbUI7QUFDbkI7RUFDRSx5QkFBeUI7RUFDekIsWUFBWTtFQUNaLGtCQUFrQjtFQUNsQixlQUFlO0VBQ2YsbUJBQW1CO0VBQ25CLHNDQUFzQztBQUN4Qzs7QUFFQTtFQUNFLHlCQUF5QjtBQUMzQjs7QUFFQSxpQ0FBaUM7QUFDakMsaUNBQWlDO0FBQ2pDO0VBQ0UscUJBQXFCO0VBQ3JCLGNBQWM7RUFDZCxrQkFBa0I7RUFDbEIsbUJBQW1CO0VBQ25CLHVEQUF1RDtBQUN6RDs7QUFFQTtFQUNFLHlCQUF5QjtFQUN6Qix1QkFBdUIsRUFBRSxtREFBbUQ7QUFDOUU7O0FBRUEsa0JBQWtCO0FBQ2xCO0VBQ0UsZ0JBQWdCO0VBQ2hCLGlCQUFpQjtFQUNqQixjQUFjO0VBQ2Qsd0NBQXdDLEVBQUUseUJBQXlCO0VBQ25FLHFCQUFxQjtFQUNyQixhQUFhO0VBQ2Isa0JBQWtCO0FBQ3BCOztBQUVBLHFCQUFxQjtBQUNyQjtFQUNFLGNBQWM7RUFDZCxxQkFBcUI7RUFDckIsaUJBQWlCO0FBQ25COztBQUVBO0VBQ0UsMEJBQTBCO0FBQzVCOztBQUVBLGlDQUFpQztBQUNqQztFQUNFLGdCQUFnQjtBQUNsQiIsInNvdXJjZXNDb250ZW50IjpbIi8qIEZ1bGwgUGFnZSBCYWNrZ3JvdW5kIFN0eWxpbmcgKi9cclxuLmZ1bGwtcGFnZS1iZyB7XHJcbiAgaGVpZ2h0OiAxMDB2aDtcclxuICB3aWR0aDogMTAwJTtcclxuICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoJy9hc3NldHMvaW1hZ2VzL3Rlc3QuanBnJyk7XHJcbiAgYmFja2dyb3VuZC1zaXplOiBjb3ZlcjtcclxuICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBjZW50ZXI7XHJcbiAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDtcclxuICBiYWNrZ3JvdW5kLWF0dGFjaG1lbnQ6IGZpeGVkO1xyXG59XHJcblxyXG4vKiBDYXJkIFN0eWxpbmcgZm9yIFNpZ24gSW4gKi9cclxuLmNhcmQge1xyXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsgLyogU2VtaS10cmFuc3BhcmVudCB3aGl0ZSBiYWNrZ3JvdW5kICovXHJcbiAgcGFkZGluZzogNDBweDtcclxuICBib3JkZXItcmFkaXVzOiAxNXB4OyAvKiBSb3VuZGVkIGNvcm5lcnMgKi9cclxuICBib3gtc2hhZG93OiAwIDEwcHggMzBweCByZ2JhKDAsIDAsIDAsIDAuMik7IC8qIEJveCBzaGFkb3cgZm9yIGRlcHRoICovXHJcbn1cclxuXHJcbi8qIEhlYWRpbmcgU3R5bGluZyAqL1xyXG5oMiB7XHJcbiAgZm9udC1zaXplOiAycmVtO1xyXG4gIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgY29sb3I6ICMzMzM7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxufVxyXG5cclxuLyogU3VidGl0bGUgVGV4dCAqL1xyXG5wLnRleHQtbXV0ZWQge1xyXG4gIGNvbG9yOiAjNjY2O1xyXG59XHJcblxyXG4vKiBJbnB1dCBTdHlsaW5nICovXHJcbi5pbnB1dC1ncm91cCB7XHJcbiAgYm9yZGVyLXJhZGl1czogMTBweDsgLyogUm91bmRlZCBjb3JuZXJzIGZvciBpbnB1dCBncm91cHMgKi9cclxuICBvdmVyZmxvdzogaGlkZGVuOyAvKiBFbnN1cmVzIHRoZSBpbnB1dCBmaWVsZHMgc3RheSB3aXRoaW4gdGhlIHJvdW5kZWQgYm9yZGVyICovXHJcbn1cclxuXHJcbi5pbnB1dC1ncm91cC10ZXh0IHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDA3YmZmO1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxuICBib3JkZXItcmFkaXVzOiAxMHB4IDAgMCAxMHB4OyAvKiBSb3VuZGVkIGNvcm5lcnMgb24gbGVmdCBzaWRlICovXHJcbn1cclxuXHJcbi5pbnB1dC1ncm91cC10ZXh0IGkge1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLmZvcm0tY29udHJvbCB7XHJcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcclxuICBib3JkZXI6IDFweCBzb2xpZCAjZGRkO1xyXG4gIHBhZGRpbmc6IDEycHg7XHJcbiAgZm9udC1zaXplOiAxcmVtO1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbn1cclxuXHJcbi5mb3JtLWNvbnRyb2w6Zm9jdXMge1xyXG4gIGJvcmRlci1jb2xvcjogIzAwN2JmZjtcclxuICBib3gtc2hhZG93OiAwIDAgNXB4IHJnYmEoMCwgMTIzLCAyNTUsIDAuNSk7XHJcbn1cclxuXHJcbi8qIEJ1dHRvbiBTdHlsaW5nICovXHJcbi5idG4tcHJpbWFyeSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwN2JmZjtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgcGFkZGluZzogMTJweCAzMHB4O1xyXG4gIGZvbnQtc2l6ZTogMXJlbTtcclxuICBib3JkZXItcmFkaXVzOiAyNXB4O1xyXG4gIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4zcyBlYXNlO1xyXG59XHJcblxyXG4uYnRuLXByaW1hcnk6aG92ZXIge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICMwMDU2YjM7XHJcbn1cclxuXHJcbi8qIEFkanVzdGluZyB0aGUgU2lnbiBVcCBCdXR0b24gKi9cclxuLyogQWRqdXN0aW5nIHRoZSBTaWduIFVwIEJ1dHRvbiAqL1xyXG4uYnRuLW91dGxpbmUtc2Vjb25kYXJ5IHtcclxuICBib3JkZXItY29sb3I6ICMwMDdiZmY7XHJcbiAgY29sb3I6ICMwMDdiZmY7XHJcbiAgcGFkZGluZzogMTJweCAzMHB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDI1cHg7XHJcbiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjNzIGVhc2UsIGNvbG9yIDAuM3MgZWFzZTtcclxufVxyXG5cclxuLmJ0bi1vdXRsaW5lLXNlY29uZGFyeTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwN2JmZjtcclxuICBjb2xvcjogd2hpdGUgIWltcG9ydGFudDsgLyogRW5zdXJlIHRoZSB0ZXh0IGNvbG9yIGlzIGFsd2F5cyB3aGl0ZSBvbiBob3ZlciAqL1xyXG59XHJcblxyXG4vKiBFcnJvciBNZXNzYWdlICovXHJcbi5hbGVydC1kYW5nZXIge1xyXG4gIG1hcmdpbi10b3A6IDIwcHg7XHJcbiAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgY29sb3I6ICNlNzRjM2M7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyMzEsIDc2LCA2MCwgMC4xKTsgLyogTGlnaHQgcmVkIGJhY2tncm91bmQgKi9cclxuICBib3JkZXItY29sb3I6ICNlNzRjM2M7XHJcbiAgcGFkZGluZzogMTBweDtcclxuICBib3JkZXItcmFkaXVzOiA1cHg7XHJcbn1cclxuXHJcbi8qIEFkZGl0aW9uYWwgTGlua3MgKi9cclxuLnRleHQtY2VudGVyIGEge1xyXG4gIGNvbG9yOiAjMDA3YmZmO1xyXG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICBmb250LXNpemU6IDAuOXJlbTtcclxufVxyXG5cclxuLnRleHQtY2VudGVyIGE6aG92ZXIge1xyXG4gIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG59XHJcblxyXG4vKiBBZGp1c3RpbmcgdGhlIFNpZ24gVXAgQnV0dG9uICovXHJcbi5idG4tb3V0bGluZS1zZWNvbmRhcnkge1xyXG4gIG1hcmdpbi10b3A6IDIwcHg7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "loginError", "LoginComponent", "constructor", "authService", "router", "matricule", "password", "prenom", "nom", "onLogin", "matricule<PERSON><PERSON>ber", "Number", "login", "subscribe", "response", "storeToken", "token", "role", "groupe", "getUserDetailsFromToken", "console", "log", "adminData", "localStorage", "setItem", "JSON", "stringify", "navigate", "redirectToGroupDashboard", "error", "allowedGroups", "redirectUrl", "ɵɵdirectiveInject", "i1", "AuthenticationService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_8_listener", "ɵɵelement", "LoginComponent_Template_input_ngModelChange_15_listener", "$event", "LoginComponent_Template_input_ngModelChange_22_listener", "ɵɵtemplate", "LoginComponent_div_26_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\auth\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { AuthenticationService } from '../authentication.service';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent {\n  matricule: string = '';  // Treating matricule as a string in the UI\n  password: string = '';\n  loginError: string = '';\n  prenom: string = '';\n  nom: string = '';\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  onLogin() {\n    // Convert matricule (string) to number before passing to the login function\n    const matriculeNumber = this.matricule ? Number(this.matricule) : 0; // Default to 0 if it's empty\n\n    this.authService.login(matriculeNumber, this.password).subscribe(\n      response => {\n        this.authService.storeToken(response.token);\n        \n        const { role, groupe, prenom, nom } = this.authService.getUserDetailsFromToken(response.token) as any;\n\n        console.log(\"Login successful, User role:\", role, \"Group:\", groupe);\n\n        if (role === 'ADMIN') {\n          const adminData = {\n            prenom: prenom || 'Admin',\n            nom: nom || ''\n          };\n          localStorage.setItem('admin', JSON.stringify(adminData));\n          this.router.navigate(['/adminDash']);\n        } \n        else if (role === 'RESPONSABLE') {\n          if (groupe) {\n            this.redirectToGroupDashboard(groupe);\n          } else {\n            console.error(\"RESPONSABLE must have a group.\");\n            this.loginError = \"RESPONSABLE must have a group.\";\n          }\n        } \n        else {\n          this.loginError = \"Matricule ou mot de passe incorrect!\";\n        }\n      },\n      error => {\n        console.error(\"Login failed:\", error);\n        this.loginError = \"Matricule ou mot de passe incorrect!\";\n      }\n    );\n  }\n\n  private redirectToGroupDashboard(groupe: string) {\n    const allowedGroups: Record<string, string> = {\n      'TRANSACTION': '/transactions',\n      'ACTION': '/actions',\n      'ACTIONNAIRE': '/actionnaires',\n      'PORTEFEUILLE': '/port',\n      'NOTIFICATION': '/reports'\n    };\n\n    const redirectUrl = allowedGroups[groupe];\n\n    if (redirectUrl) {\n      this.router.navigate([redirectUrl]);\n    } else {\n      console.error(\"Invalid group:\", groupe);\n      this.router.navigate(['/not-authorized']);\n    }\n  }\n}", "<!-- Wrapper for background -->\n<div class=\"full-page-bg\">\n  <div class=\"container d-flex justify-content-center align-items-center vh-100\">\n    <div class=\"card p-4 shadow-lg rounded\" style=\"width: 400px;\">\n      <div class=\"text-center\">\n        <h2 class=\"mb-3\">Se connecter</h2>\n        <p class=\"text-muted\">Connectez-vous à votre compte</p>\n      </div>\n\n      <form (ngSubmit)=\"onLogin()\">\n        <!-- Matricule Field -->\n        <div class=\"mb-3\">\n          <label for=\"matricule\" class=\"form-label fw-bold\">Matricule</label>\n          <div class=\"input-group\">\n            <span class=\"input-group-text\"><i class=\"bi bi-person-badge\"></i></span>\n            <input \n              type=\"number\" \n              id=\"matricule\" \n              class=\"form-control\" \n              [(ngModel)]=\"matricule\" \n              name=\"matricule\" \n              required \n              placeholder=\"Entrez votre matricule\" />\n          </div>\n        </div>\n\n        <!-- Password Field -->\n        <div class=\"mb-3\">\n          <label for=\"password\" class=\"form-label fw-bold\">Mot de passe</label>\n          <div class=\"input-group\">\n            <span class=\"input-group-text\"><i class=\"bi bi-lock\"></i></span>\n            <input \n              type=\"password\" \n              id=\"password\" \n              class=\"form-control\" \n              [(ngModel)]=\"password\" \n              name=\"password\" \n              required \n              placeholder=\"Entrez votre mot de passe\" />\n          </div>\n        </div>\n\n        <!-- Login Button -->\n        <button type=\"submit\" class=\"btn btn-primary w-100 fw-bold\">\n          <i class=\"bi bi-box-arrow-in-right\"></i> Se connecter\n        </button>\n\n        <!-- Error Message -->\n        <div *ngIf=\"loginError\" class=\"alert alert-danger text-center mt-3\">\n          {{ loginError }}\n        </div>\n\n        <!-- Additional Links -->\n        <div class=\"text-center mt-3\">\n          <a href=\"#\" class=\"text-decoration-none\">Mot de passe oublié</a>\n        </div>\n\n        <!-- Sign Up Button -->\n        <div class=\"text-center mt-3\">\n          <a routerLink=\"/signup\" class=\"btn btn-outline-secondary w-100\">\n            <i class=\"bi bi-person-plus\"></i> S'inscrire\n          </a>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;ICgDQA,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,UAAA,MACF;;;ADzCR,OAAM,MAAOC,cAAc;EAOzBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;IANtE,KAAAC,SAAS,GAAW,EAAE,CAAC,CAAE;IACzB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAN,UAAU,GAAW,EAAE;IACvB,KAAAO,MAAM,GAAW,EAAE;IACnB,KAAAC,GAAG,GAAW,EAAE;EAEiE;EAEjFC,OAAOA,CAAA;IACL;IACA,MAAMC,eAAe,GAAG,IAAI,CAACL,SAAS,GAAGM,MAAM,CAAC,IAAI,CAACN,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;IAErE,IAAI,CAACF,WAAW,CAACS,KAAK,CAACF,eAAe,EAAE,IAAI,CAACJ,QAAQ,CAAC,CAACO,SAAS,CAC9DC,QAAQ,IAAG;MACT,IAAI,CAACX,WAAW,CAACY,UAAU,CAACD,QAAQ,CAACE,KAAK,CAAC;MAE3C,MAAM;QAAEC,IAAI;QAAEC,MAAM;QAAEX,MAAM;QAAEC;MAAG,CAAE,GAAG,IAAI,CAACL,WAAW,CAACgB,uBAAuB,CAACL,QAAQ,CAACE,KAAK,CAAQ;MAErGI,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEJ,IAAI,EAAE,QAAQ,EAAEC,MAAM,CAAC;MAEnE,IAAID,IAAI,KAAK,OAAO,EAAE;QACpB,MAAMK,SAAS,GAAG;UAChBf,MAAM,EAAEA,MAAM,IAAI,OAAO;UACzBC,GAAG,EAAEA,GAAG,IAAI;SACb;QACDe,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEC,IAAI,CAACC,SAAS,CAACJ,SAAS,CAAC,CAAC;QACxD,IAAI,CAAClB,MAAM,CAACuB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;OACrC,MACI,IAAIV,IAAI,KAAK,aAAa,EAAE;QAC/B,IAAIC,MAAM,EAAE;UACV,IAAI,CAACU,wBAAwB,CAACV,MAAM,CAAC;SACtC,MAAM;UACLE,OAAO,CAACS,KAAK,CAAC,gCAAgC,CAAC;UAC/C,IAAI,CAAC7B,UAAU,GAAG,gCAAgC;;OAErD,MACI;QACH,IAAI,CAACA,UAAU,GAAG,sCAAsC;;IAE5D,CAAC,EACD6B,KAAK,IAAG;MACNT,OAAO,CAACS,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,IAAI,CAAC7B,UAAU,GAAG,sCAAsC;IAC1D,CAAC,CACF;EACH;EAEQ4B,wBAAwBA,CAACV,MAAc;IAC7C,MAAMY,aAAa,GAA2B;MAC5C,aAAa,EAAE,eAAe;MAC9B,QAAQ,EAAE,UAAU;MACpB,aAAa,EAAE,eAAe;MAC9B,cAAc,EAAE,OAAO;MACvB,cAAc,EAAE;KACjB;IAED,MAAMC,WAAW,GAAGD,aAAa,CAACZ,MAAM,CAAC;IAEzC,IAAIa,WAAW,EAAE;MACf,IAAI,CAAC3B,MAAM,CAACuB,QAAQ,CAAC,CAACI,WAAW,CAAC,CAAC;KACpC,MAAM;MACLX,OAAO,CAACS,KAAK,CAAC,gBAAgB,EAAEX,MAAM,CAAC;MACvC,IAAI,CAACd,MAAM,CAACuB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;;EAE7C;;;uBAjEW1B,cAAc,EAAAR,EAAA,CAAAuC,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAzC,EAAA,CAAAuC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdnC,cAAc;MAAAoC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR3BlD,EAAA,CAAAC,cAAA,aAA0B;UAIDD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,WAAsB;UAAAD,EAAA,CAAAE,MAAA,yCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGzDH,EAAA,CAAAC,cAAA,cAA6B;UAAvBD,EAAA,CAAAoD,UAAA,sBAAAC,iDAAA;YAAA,OAAYF,GAAA,CAAAnC,OAAA,EAAS;UAAA,EAAC;UAE1BhB,EAAA,CAAAC,cAAA,aAAkB;UACkCD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnEH,EAAA,CAAAC,cAAA,cAAyB;UACQD,EAAA,CAAAsD,SAAA,aAAkC;UAAAtD,EAAA,CAAAG,YAAA,EAAO;UACxEH,EAAA,CAAAC,cAAA,iBAOyC;UAHvCD,EAAA,CAAAoD,UAAA,2BAAAG,wDAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAvC,SAAA,GAAA4C,MAAA;UAAA,EAAuB;UAJzBxD,EAAA,CAAAG,YAAA,EAOyC;UAK7CH,EAAA,CAAAC,cAAA,cAAkB;UACiCD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrEH,EAAA,CAAAC,cAAA,cAAyB;UACQD,EAAA,CAAAsD,SAAA,aAA0B;UAAAtD,EAAA,CAAAG,YAAA,EAAO;UAChEH,EAAA,CAAAC,cAAA,iBAO4C;UAH1CD,EAAA,CAAAoD,UAAA,2BAAAK,wDAAAD,MAAA;YAAA,OAAAL,GAAA,CAAAtC,QAAA,GAAA2C,MAAA;UAAA,EAAsB;UAJxBxD,EAAA,CAAAG,YAAA,EAO4C;UAKhDH,EAAA,CAAAC,cAAA,kBAA4D;UAC1DD,EAAA,CAAAsD,SAAA,aAAwC;UAACtD,EAAA,CAAAE,MAAA,sBAC3C;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAA0D,UAAA,KAAAC,8BAAA,kBAEM;UAGN3D,EAAA,CAAAC,cAAA,eAA8B;UACaD,EAAA,CAAAE,MAAA,gCAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIlEH,EAAA,CAAAC,cAAA,eAA8B;UAE1BD,EAAA,CAAAsD,SAAA,aAAiC;UAACtD,EAAA,CAAAE,MAAA,oBACpC;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UA1CAH,EAAA,CAAAI,SAAA,IAAuB;UAAvBJ,EAAA,CAAA4D,UAAA,YAAAT,GAAA,CAAAvC,SAAA,CAAuB;UAgBvBZ,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAA4D,UAAA,YAAAT,GAAA,CAAAtC,QAAA,CAAsB;UAatBb,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAA4D,UAAA,SAAAT,GAAA,CAAA5C,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}