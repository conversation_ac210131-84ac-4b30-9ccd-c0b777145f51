{"ast": null, "code": "/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\nmodule.exports = preferredLanguages;\nmodule.exports.preferredLanguages = preferredLanguages;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleLanguageRegExp = /^\\s*([^\\s\\-;]+)(?:-([^\\s;]+))?\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Language header.\n * @private\n */\n\nfunction parseAcceptLanguage(accept) {\n  var accepts = accept.split(',');\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var language = parseLanguage(accepts[i].trim(), i);\n    if (language) {\n      accepts[j++] = language;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n  return accepts;\n}\n\n/**\n * Parse a language from the Accept-Language header.\n * @private\n */\n\nfunction parseLanguage(str, i) {\n  var match = simpleLanguageRegExp.exec(str);\n  if (!match) return null;\n  var prefix = match[1];\n  var suffix = match[2];\n  var full = prefix;\n  if (suffix) full += \"-\" + suffix;\n  var q = 1;\n  if (match[3]) {\n    var params = match[3].split(';');\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].split('=');\n      if (p[0] === 'q') q = parseFloat(p[1]);\n    }\n  }\n  return {\n    prefix: prefix,\n    suffix: suffix,\n    q: q,\n    i: i,\n    full: full\n  };\n}\n\n/**\n * Get the priority of a language.\n * @private\n */\n\nfunction getLanguagePriority(language, accepted, index) {\n  var priority = {\n    o: -1,\n    q: 0,\n    s: 0\n  };\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(language, accepted[i], index);\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n  return priority;\n}\n\n/**\n * Get the specificity of the language.\n * @private\n */\n\nfunction specify(language, spec, index) {\n  var p = parseLanguage(language);\n  if (!p) return null;\n  var s = 0;\n  if (spec.full.toLowerCase() === p.full.toLowerCase()) {\n    s |= 4;\n  } else if (spec.prefix.toLowerCase() === p.full.toLowerCase()) {\n    s |= 2;\n  } else if (spec.full.toLowerCase() === p.prefix.toLowerCase()) {\n    s |= 1;\n  } else if (spec.full !== '*') {\n    return null;\n  }\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  };\n}\n;\n\n/**\n * Get the preferred languages from an Accept-Language header.\n * @public\n */\n\nfunction preferredLanguages(accept, provided) {\n  // RFC 2616 sec 14.4: no header = *\n  var accepts = parseAcceptLanguage(accept === undefined ? '*' : accept || '');\n  if (!provided) {\n    // sorted list of all languages\n    return accepts.filter(isQuality).sort(compareSpecs).map(getFullLanguage);\n  }\n  var priorities = provided.map(function getPriority(type, index) {\n    return getLanguagePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted languages\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getLanguage(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return b.q - a.q || b.s - a.s || a.o - b.o || a.i - b.i || 0;\n}\n\n/**\n * Get full language string.\n * @private\n */\n\nfunction getFullLanguage(spec) {\n  return spec.full;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}", "map": {"version": 3, "names": ["module", "exports", "preferredLanguages", "simpleLanguageRegExp", "parseAcceptLanguage", "accept", "accepts", "split", "i", "j", "length", "language", "parseLanguage", "trim", "str", "match", "exec", "prefix", "suffix", "full", "q", "params", "p", "parseFloat", "getLanguagePriority", "accepted", "index", "priority", "o", "s", "spec", "specify", "toLowerCase", "provided", "undefined", "filter", "isQuality", "sort", "compareSpecs", "map", "getFullLanguage", "priorities", "getPriority", "type", "getLanguage", "indexOf", "a", "b"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/accepts/node_modules/negotiator/lib/language.js"], "sourcesContent": ["/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredLanguages;\nmodule.exports.preferredLanguages = preferredLanguages;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleLanguageRegExp = /^\\s*([^\\s\\-;]+)(?:-([^\\s;]+))?\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Language header.\n * @private\n */\n\nfunction parseAcceptLanguage(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var language = parseLanguage(accepts[i].trim(), i);\n\n    if (language) {\n      accepts[j++] = language;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a language from the Accept-Language header.\n * @private\n */\n\nfunction parseLanguage(str, i) {\n  var match = simpleLanguageRegExp.exec(str);\n  if (!match) return null;\n\n  var prefix = match[1]\n  var suffix = match[2]\n  var full = prefix\n\n  if (suffix) full += \"-\" + suffix;\n\n  var q = 1;\n  if (match[3]) {\n    var params = match[3].split(';')\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].split('=');\n      if (p[0] === 'q') q = parseFloat(p[1]);\n    }\n  }\n\n  return {\n    prefix: prefix,\n    suffix: suffix,\n    q: q,\n    i: i,\n    full: full\n  };\n}\n\n/**\n * Get the priority of a language.\n * @private\n */\n\nfunction getLanguagePriority(language, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(language, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the language.\n * @private\n */\n\nfunction specify(language, spec, index) {\n  var p = parseLanguage(language)\n  if (!p) return null;\n  var s = 0;\n  if(spec.full.toLowerCase() === p.full.toLowerCase()){\n    s |= 4;\n  } else if (spec.prefix.toLowerCase() === p.full.toLowerCase()) {\n    s |= 2;\n  } else if (spec.full.toLowerCase() === p.prefix.toLowerCase()) {\n    s |= 1;\n  } else if (spec.full !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n};\n\n/**\n * Get the preferred languages from an Accept-Language header.\n * @public\n */\n\nfunction preferredLanguages(accept, provided) {\n  // RFC 2616 sec 14.4: no header = *\n  var accepts = parseAcceptLanguage(accept === undefined ? '*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all languages\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullLanguage);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getLanguagePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted languages\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getLanguage(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full language string.\n * @private\n */\n\nfunction getFullLanguage(spec) {\n  return spec.full;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AAEAA,MAAM,CAACC,OAAO,GAAGC,kBAAkB;AACnCF,MAAM,CAACC,OAAO,CAACC,kBAAkB,GAAGA,kBAAkB;;AAEtD;AACA;AACA;AACA;;AAEA,IAAIC,oBAAoB,GAAG,8CAA8C;;AAEzE;AACA;AACA;AACA;;AAEA,SAASC,mBAAmBA,CAACC,MAAM,EAAE;EACnC,IAAIC,OAAO,GAAGD,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;EAE/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGF,OAAO,CAACI,MAAM,EAAEF,CAAC,EAAE,EAAE;IAC9C,IAAIG,QAAQ,GAAGC,aAAa,CAACN,OAAO,CAACE,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC,EAAEL,CAAC,CAAC;IAElD,IAAIG,QAAQ,EAAE;MACZL,OAAO,CAACG,CAAC,EAAE,CAAC,GAAGE,QAAQ;IACzB;EACF;;EAEA;EACAL,OAAO,CAACI,MAAM,GAAGD,CAAC;EAElB,OAAOH,OAAO;AAChB;;AAEA;AACA;AACA;AACA;;AAEA,SAASM,aAAaA,CAACE,GAAG,EAAEN,CAAC,EAAE;EAC7B,IAAIO,KAAK,GAAGZ,oBAAoB,CAACa,IAAI,CAACF,GAAG,CAAC;EAC1C,IAAI,CAACC,KAAK,EAAE,OAAO,IAAI;EAEvB,IAAIE,MAAM,GAAGF,KAAK,CAAC,CAAC,CAAC;EACrB,IAAIG,MAAM,GAAGH,KAAK,CAAC,CAAC,CAAC;EACrB,IAAII,IAAI,GAAGF,MAAM;EAEjB,IAAIC,MAAM,EAAEC,IAAI,IAAI,GAAG,GAAGD,MAAM;EAEhC,IAAIE,CAAC,GAAG,CAAC;EACT,IAAIL,KAAK,CAAC,CAAC,CAAC,EAAE;IACZ,IAAIM,MAAM,GAAGN,KAAK,CAAC,CAAC,CAAC,CAACR,KAAK,CAAC,GAAG,CAAC;IAChC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,MAAM,CAACX,MAAM,EAAED,CAAC,EAAE,EAAE;MACtC,IAAIa,CAAC,GAAGD,MAAM,CAACZ,CAAC,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC;MAC5B,IAAIe,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEF,CAAC,GAAGG,UAAU,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC;EACF;EAEA,OAAO;IACLL,MAAM,EAAEA,MAAM;IACdC,MAAM,EAAEA,MAAM;IACdE,CAAC,EAAEA,CAAC;IACJZ,CAAC,EAAEA,CAAC;IACJW,IAAI,EAAEA;EACR,CAAC;AACH;;AAEA;AACA;AACA;AACA;;AAEA,SAASK,mBAAmBA,CAACb,QAAQ,EAAEc,QAAQ,EAAEC,KAAK,EAAE;EACtD,IAAIC,QAAQ,GAAG;IAACC,CAAC,EAAE,CAAC,CAAC;IAAER,CAAC,EAAE,CAAC;IAAES,CAAC,EAAE;EAAC,CAAC;EAElC,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,QAAQ,CAACf,MAAM,EAAEF,CAAC,EAAE,EAAE;IACxC,IAAIsB,IAAI,GAAGC,OAAO,CAACpB,QAAQ,EAAEc,QAAQ,CAACjB,CAAC,CAAC,EAAEkB,KAAK,CAAC;IAEhD,IAAII,IAAI,IAAI,CAACH,QAAQ,CAACE,CAAC,GAAGC,IAAI,CAACD,CAAC,IAAIF,QAAQ,CAACP,CAAC,GAAGU,IAAI,CAACV,CAAC,IAAIO,QAAQ,CAACC,CAAC,GAAGE,IAAI,CAACF,CAAC,IAAI,CAAC,EAAE;MACnFD,QAAQ,GAAGG,IAAI;IACjB;EACF;EAEA,OAAOH,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;;AAEA,SAASI,OAAOA,CAACpB,QAAQ,EAAEmB,IAAI,EAAEJ,KAAK,EAAE;EACtC,IAAIJ,CAAC,GAAGV,aAAa,CAACD,QAAQ,CAAC;EAC/B,IAAI,CAACW,CAAC,EAAE,OAAO,IAAI;EACnB,IAAIO,CAAC,GAAG,CAAC;EACT,IAAGC,IAAI,CAACX,IAAI,CAACa,WAAW,CAAC,CAAC,KAAKV,CAAC,CAACH,IAAI,CAACa,WAAW,CAAC,CAAC,EAAC;IAClDH,CAAC,IAAI,CAAC;EACR,CAAC,MAAM,IAAIC,IAAI,CAACb,MAAM,CAACe,WAAW,CAAC,CAAC,KAAKV,CAAC,CAACH,IAAI,CAACa,WAAW,CAAC,CAAC,EAAE;IAC7DH,CAAC,IAAI,CAAC;EACR,CAAC,MAAM,IAAIC,IAAI,CAACX,IAAI,CAACa,WAAW,CAAC,CAAC,KAAKV,CAAC,CAACL,MAAM,CAACe,WAAW,CAAC,CAAC,EAAE;IAC7DH,CAAC,IAAI,CAAC;EACR,CAAC,MAAM,IAAIC,IAAI,CAACX,IAAI,KAAK,GAAG,EAAG;IAC7B,OAAO,IAAI;EACb;EAEA,OAAO;IACLX,CAAC,EAAEkB,KAAK;IACRE,CAAC,EAAEE,IAAI,CAACtB,CAAC;IACTY,CAAC,EAAEU,IAAI,CAACV,CAAC;IACTS,CAAC,EAAEA;EACL,CAAC;AACH;AAAC;;AAED;AACA;AACA;AACA;;AAEA,SAAS3B,kBAAkBA,CAACG,MAAM,EAAE4B,QAAQ,EAAE;EAC5C;EACA,IAAI3B,OAAO,GAAGF,mBAAmB,CAACC,MAAM,KAAK6B,SAAS,GAAG,GAAG,GAAG7B,MAAM,IAAI,EAAE,CAAC;EAE5E,IAAI,CAAC4B,QAAQ,EAAE;IACb;IACA,OAAO3B,OAAO,CACX6B,MAAM,CAACC,SAAS,CAAC,CACjBC,IAAI,CAACC,YAAY,CAAC,CAClBC,GAAG,CAACC,eAAe,CAAC;EACzB;EAEA,IAAIC,UAAU,GAAGR,QAAQ,CAACM,GAAG,CAAC,SAASG,WAAWA,CAACC,IAAI,EAAEjB,KAAK,EAAE;IAC9D,OAAOF,mBAAmB,CAACmB,IAAI,EAAErC,OAAO,EAAEoB,KAAK,CAAC;EAClD,CAAC,CAAC;;EAEF;EACA,OAAOe,UAAU,CAACN,MAAM,CAACC,SAAS,CAAC,CAACC,IAAI,CAACC,YAAY,CAAC,CAACC,GAAG,CAAC,SAASK,WAAWA,CAACjB,QAAQ,EAAE;IACxF,OAAOM,QAAQ,CAACQ,UAAU,CAACI,OAAO,CAAClB,QAAQ,CAAC,CAAC;EAC/C,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;;AAEA,SAASW,YAAYA,CAACQ,CAAC,EAAEC,CAAC,EAAE;EAC1B,OAAQA,CAAC,CAAC3B,CAAC,GAAG0B,CAAC,CAAC1B,CAAC,IAAM2B,CAAC,CAAClB,CAAC,GAAGiB,CAAC,CAACjB,CAAE,IAAKiB,CAAC,CAAClB,CAAC,GAAGmB,CAAC,CAACnB,CAAE,IAAKkB,CAAC,CAACtC,CAAC,GAAGuC,CAAC,CAACvC,CAAE,IAAI,CAAC;AACtE;;AAEA;AACA;AACA;AACA;;AAEA,SAASgC,eAAeA,CAACV,IAAI,EAAE;EAC7B,OAAOA,IAAI,CAACX,IAAI;AAClB;;AAEA;AACA;AACA;AACA;;AAEA,SAASiB,SAASA,CAACN,IAAI,EAAE;EACvB,OAAOA,IAAI,CAACV,CAAC,GAAG,CAAC;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}