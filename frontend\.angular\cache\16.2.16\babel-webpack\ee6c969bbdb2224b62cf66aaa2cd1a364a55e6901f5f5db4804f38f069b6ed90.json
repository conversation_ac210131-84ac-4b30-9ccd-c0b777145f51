{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"myChart\"];\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n  }\\n  \\n  .feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);\\n  }\\n  \\n  .sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #333;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n  }\\n  \\n  .sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, .25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .border-top[_ngcontent-%COMP%] { border-top: 1px solid #e5e5e5; }\\n  .border-bottom[_ngcontent-%COMP%] { border-bottom: 1px solid #e5e5e5; }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class PortefeuillesComponent {\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  static {\n    this.ɵfac = function PortefeuillesComponent_Factory(t) {\n      return new (t || PortefeuillesComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PortefeuillesComponent,\n      selectors: [[\"app-actions\"]],\n      viewQuery: function PortefeuillesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 84,\n      vars: 0,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [\"width\", \"900\", \"height\", \"380\", 2, \"background-color\", \"lightgrey\"], [\"myChart\", \"\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"ResDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"form-control-dark\", \"w-100\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [\"href\", \"/login\", 1, \"nav-link\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-light\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/reports\", 1, \"nav-link\"], [\"data-feather\", \"bar-chart-2\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"layers\"], [1, \"sidebar-heading\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"px-3\", \"mt-4\", \"mb-1\", \"text-muted\"], [\"href\", \"#\", 1, \"d-flex\", \"align-items-center\", \"text-muted\"], [\"data-feather\", \"plus-circle\"], [1, \"nav\", \"flex-column\", \"mb-2\"], [\"href\", \"#\", 1, \"nav-link\"], [\"data-feather\", \"file-text\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"dropdown-toggle\"], [\"data-feather\", \"calendar\"], [\"id\", \"myChart\", \"width\", \"900\", \"height\", \"380\", 1, \"my-4\"]],\n      template: function PortefeuillesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"link\", 5);\n          i0.ɵɵelementStart(7, \"title\");\n          i0.ɵɵtext(8, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"link\", 6)(10, \"link\", 7)(11, \"canvas\", 8, 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"body\")(14, \"nav\", 10)(15, \"a\", 11);\n          i0.ɵɵtext(16, \"GTI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 12);\n          i0.ɵɵelementStart(18, \"ul\", 13)(19, \"li\", 14)(20, \"a\", 15);\n          i0.ɵɵtext(21, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"nav\", 18)(25, \"div\", 19)(26, \"ul\", 20);\n          i0.ɵɵelement(27, \"li\", 21);\n          i0.ɵɵelementStart(28, \"li\", 21)(29, \"a\", 22);\n          i0.ɵɵelement(30, \"span\", 23);\n          i0.ɵɵtext(31, \" Transactions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"li\", 21)(33, \"a\", 24);\n          i0.ɵɵelement(34, \"span\", 25);\n          i0.ɵɵtext(35, \" Actions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"li\", 21)(37, \"a\", 26);\n          i0.ɵɵelement(38, \"span\", 27);\n          i0.ɵɵtext(39, \" Actionnaires \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"li\", 21)(41, \"a\", 28);\n          i0.ɵɵelement(42, \"span\", 29);\n          i0.ɵɵtext(43, \" Reports \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 21)(45, \"a\", 30);\n          i0.ɵɵelement(46, \"span\", 31);\n          i0.ɵɵtext(47, \" Portefeuilles \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"h6\", 32)(49, \"span\");\n          i0.ɵɵtext(50, \"Saved reports\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"a\", 33);\n          i0.ɵɵelement(52, \"span\", 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"ul\", 35)(54, \"li\", 21)(55, \"a\", 36);\n          i0.ɵɵelement(56, \"span\", 37);\n          i0.ɵɵtext(57, \" Current month \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"li\", 21)(59, \"a\", 36);\n          i0.ɵɵelement(60, \"span\", 37);\n          i0.ɵɵtext(61, \" Last quarter \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"li\", 21)(63, \"a\", 36);\n          i0.ɵɵelement(64, \"span\", 37);\n          i0.ɵɵtext(65, \" Social engagement \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"li\", 21)(67, \"a\", 36);\n          i0.ɵɵelement(68, \"span\", 37);\n          i0.ɵɵtext(69, \" Year-end sale \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(70, \"main\", 38)(71, \"div\", 39)(72, \"h1\", 40);\n          i0.ɵɵtext(73, \"Portefeuilles\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 41)(75, \"div\", 42)(76, \"button\", 43);\n          i0.ɵɵtext(77, \"Share\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"button\", 43);\n          i0.ɵɵtext(79, \"Export\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"button\", 44);\n          i0.ɵɵelement(81, \"span\", 45);\n          i0.ɵɵtext(82, \" This week \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(83, \"canvas\", 46);\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "PortefeuillesComponent", "ngAfterViewInit", "replace", "selectors", "viewQuery", "PortefeuillesComponent_Query", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\portefeuilles\\portefeuilles.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\portefeuilles\\portefeuilles.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, ViewChild } from '@angular/core';\nimport { Chart } from 'chart.js';\nimport * as feather from 'feather-icons';\n\n\n\n@Component({\n  selector: 'app-actions',\n  templateUrl: './portefeuilles.component.html',\n  styleUrls: ['../../dashboard.css']\n})\nexport class PortefeuillesComponent implements AfterViewInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  \n  }\n}", "\n<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n\n    <!-- Custom styles for this template -->\n    <link href=\"../../dashboard.css\" rel=\"stylesheet\">\n    <canvas #myChart width=\"900\" height=\"380\" style=\"background-color: lightgrey;\"></canvas>\n\n  </head>\n\n  <body>\n    \n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"ResDash\">GTI</a>\n      <input class=\"form-control form-control-dark w-100\" type=\"text\" placeholder=\"Search\" aria-label=\"Search\">\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" href=\"/login\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block bg-light sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n               \n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\"  href=\"/transactions\">\n                  <span data-feather=\"dollar-sign\"></span>\n                  Transactions\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actions\">\n                  <span data-feather=\"trending-up\"></span>\n                  Actions\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\">\n                  <span data-feather=\"users\"></span>\n                  Actionnaires\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/reports\">\n                  <span data-feather=\"bar-chart-2\"></span>\n                  Reports\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/port\">\n                  <span data-feather=\"layers\"></span>\n                  Portefeuilles\n                </a>\n              </li>\n            </ul>\n\n            <h6 class=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted\">\n              <span>Saved reports</span>\n              <a class=\"d-flex align-items-center text-muted\" href=\"#\">\n                <span data-feather=\"plus-circle\"></span>\n              </a>\n            </h6>\n            <ul class=\"nav flex-column mb-2\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"#\">\n                  <span data-feather=\"file-text\"></span>\n                  Current month\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"#\">\n                  <span data-feather=\"file-text\"></span>\n                  Last quarter\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"#\">\n                  <span data-feather=\"file-text\"></span>\n                  Social engagement\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"#\">\n                  <span data-feather=\"file-text\"></span>\n                  Year-end sale\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Portefeuilles</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n                <button class=\"btn btn-sm btn-outline-secondary\">Share</button>\n                <button class=\"btn btn-sm btn-outline-secondary\">Export</button>\n              </div>\n              <button class=\"btn btn-sm btn-outline-secondary dropdown-toggle\">\n                <span data-feather=\"calendar\"></span>\n                This week\n              </button>\n            </div>\n          </div>\n\n          <canvas class=\"my-4\" id=\"myChart\" width=\"900\" height=\"380\"></canvas>\n          \n        \n        </main>\n      </div>\n    </div>\n\n    <!-- Bootstrap core JavaScript\n    ================================================== -->\n    <!-- Placed at the end of the document so the pages load faster -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>    \n\n    </script>\n  </body>\n</html>\n"], "mappings": "AAEA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;AASxC,OAAM,MAAOC,sBAAsB;EAGjCC,eAAeA,CAAA;IACbF,OAAO,CAACG,OAAO,EAAE,CAAC,CAAC;EAErB;;;;uBANWF,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAG,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCTnCE,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAE,SAAA,cAAsB;UAMtBF,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAG,MAAA,uCAAgC;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAE/CJ,EAAA,CAAAE,SAAA,cAAmF;UASrFF,EAAA,CAAAI,YAAA,EAAO;UAEPJ,EAAA,CAAAC,cAAA,YAAM;UAG4DD,EAAA,CAAAG,MAAA,WAAG;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACrEJ,EAAA,CAAAE,SAAA,iBAAyG;UACzGF,EAAA,CAAAC,cAAA,cAA4B;UAEUD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAKpDJ,EAAA,CAAAC,cAAA,eAA6B;UAKnBD,EAAA,CAAAE,SAAA,cAEK;UACLF,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAwC;UACxCF,EAAA,CAAAG,MAAA,sBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAwC;UACxCF,EAAA,CAAAG,MAAA,iBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAkC;UAClCF,EAAA,CAAAG,MAAA,sBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAwC;UACxCF,EAAA,CAAAG,MAAA,iBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAmC;UACnCF,EAAA,CAAAG,MAAA,uBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAIRJ,EAAA,CAAAC,cAAA,cAAwG;UAChGD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAC1BJ,EAAA,CAAAC,cAAA,aAAyD;UACvDD,EAAA,CAAAE,SAAA,gBAAwC;UAC1CF,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAiC;UAG3BD,EAAA,CAAAE,SAAA,gBAAsC;UACtCF,EAAA,CAAAG,MAAA,uBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAsC;UACtCF,EAAA,CAAAG,MAAA,sBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAsC;UACtCF,EAAA,CAAAG,MAAA,2BACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAsC;UACtCF,EAAA,CAAAG,MAAA,uBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAMZJ,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjCJ,EAAA,CAAAC,cAAA,eAAsC;UAEeD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAC/DJ,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAElEJ,EAAA,CAAAC,cAAA,kBAAiE;UAC/DD,EAAA,CAAAE,SAAA,gBAAqC;UACrCF,EAAA,CAAAG,MAAA,mBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAIbJ,EAAA,CAAAE,SAAA,kBAAoE;UAGtEF,EAAA,CAAAI,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}