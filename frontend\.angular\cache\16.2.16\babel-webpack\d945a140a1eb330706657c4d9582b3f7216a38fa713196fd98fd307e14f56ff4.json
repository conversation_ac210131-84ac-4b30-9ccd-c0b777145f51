{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let ModifyGroupComponent = class ModifyGroupComponent {\n  constructor(route, groupeService, ressourceService,\n  // Inject ressource service\n  router) {\n    this.route = route;\n    this.groupeService = groupeService;\n    this.ressourceService = ressourceService;\n    this.router = router;\n    this.groupeId = 0;\n    this.groupe = {\n      idGroupe: 0,\n      nomGroupe: '',\n      ressources: []\n    }; // Include ressources in group\n    this.allRessources = []; // Store all ressources\n    this.selectedRessources = []; // Store selected ressources' ids\n    this.loading = true;\n  }\n  // In your ngOnInit method, make sure you are correctly initializing the selectedRessources array.\n  ngOnInit() {\n    this.groupeId = Number(this.route.snapshot.paramMap.get('id'));\n    this.getGroupe(this.groupeId);\n    this.getAllRessources(); // Fetch all ressources\n  }\n\n  getGroupe(id) {\n    this.groupeService.getGroupeById(id).subscribe(data => {\n      this.groupe = data;\n      // Assuming ressources is an array of numbers (IDs)\n      this.selectedRessources = data.ressources ? [...data.ressources] : [];\n      this.loading = false;\n    }, err => {\n      console.error('Error loading group data', err);\n      this.loading = false;\n    });\n  }\n  // Fetch all ressources\n  getAllRessources() {\n    this.ressourceService.getAllRessources().subscribe(data => {\n      this.allRessources = data;\n    }, err => {\n      console.error('Error loading ressources', err);\n    });\n  }\n  // Update the group data\n  updateGroupe() {\n    // Ensure selectedRessources is correctly set as an array of resource IDs\n    const updatedGroupe = {\n      ...this.groupe,\n      ressources: this.selectedRessources // This should be an array of IDs\n    };\n\n    this.groupeService.updateGroupe(this.groupeId, updatedGroupe).subscribe(response => {\n      console.log(\"Response:\", response); // Log the valid response\n      this.router.navigate(['/groups']).then(() => {\n        console.log('Navigated to /groups successfully.');\n      }).catch(error => {\n        console.error('Navigation error:', error);\n      });\n    }, error => {\n      console.error('Error updating group:', error);\n      // Handle other errors gracefully\n    });\n  }\n  // Handle cancel action\n  cancel() {\n    this.router.navigate(['/groups']);\n  }\n};\nModifyGroupComponent = __decorate([Component({\n  selector: 'app-modify-group',\n  templateUrl: './modify-group.component.html',\n  styleUrls: ['./modify-group.component.css']\n})], ModifyGroupComponent);", "map": {"version": 3, "names": ["Component", "ModifyGroupComponent", "constructor", "route", "groupeService", "ressourceService", "router", "groupeId", "groupe", "idGroupe", "nomGroupe", "ressources", "allRessources", "selectedRessources", "loading", "ngOnInit", "Number", "snapshot", "paramMap", "get", "getGroupe", "getAllRessources", "id", "getGroupeById", "subscribe", "data", "err", "console", "error", "updateGroupe", "updatedGroupe", "response", "log", "navigate", "then", "catch", "cancel", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\modify-group\\modify-group.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { GroupeService } from '../services/groupe.service';\nimport { RessourceService } from '../services/ressource.service';  // Service to fetch ressources\nimport { Groupe } from '../model/groupe.model';\nimport { Ressource } from '../model/ressource.model';  // Assuming you have this model\n\n@Component({\n  selector: 'app-modify-group',\n  templateUrl: './modify-group.component.html',\n  styleUrls: ['./modify-group.component.css']\n})\nexport class ModifyGroupComponent implements OnInit {\n  groupeId: number = 0; \n  groupe: Groupe = { idGroupe: 0, nomGroupe: '', ressources: [] }; // Include ressources in group\n  allRessources: Ressource[] = [];  // Store all ressources\n  selectedRessources: number[] = [];  // Store selected ressources' ids\n  loading: boolean = true;\n\n  constructor(\n    private route: ActivatedRoute,\n    private groupeService: GroupeService,\n    private ressourceService: RessourceService,  // Inject ressource service\n    private router: Router\n  ) {}\n\n// In your ngOnInit method, make sure you are correctly initializing the selectedRessources array.\nngOnInit(): void {\n  this.groupeId = Number(this.route.snapshot.paramMap.get('id'));\n  this.getGroupe(this.groupeId);\n  this.getAllRessources();  // Fetch all ressources\n}\n\ngetGroupe(id: number): void {\n  this.groupeService.getGroupeById(id).subscribe(\n    (data: Groupe) => {\n      this.groupe = data;\n      // Assuming ressources is an array of numbers (IDs)\n      this.selectedRessources = data.ressources ? [...data.ressources] : [];\n      this.loading = false;\n    },\n    (err) => {\n      console.error('Error loading group data', err);\n      this.loading = false;\n    }\n  );\n}\n\n  \n  // Fetch all ressources\n  getAllRessources(): void {\n    this.ressourceService.getAllRessources().subscribe(\n      (data: Ressource[]) => {\n        this.allRessources = data;\n      },\n      (err) => {\n        console.error('Error loading ressources', err);\n      }\n    );\n  }\n\n  // Update the group data\n  updateGroupe(): void {\n    // Ensure selectedRessources is correctly set as an array of resource IDs\n    const updatedGroupe = {\n      ...this.groupe,\n      ressources: this.selectedRessources // This should be an array of IDs\n    };\n  \n    this.groupeService.updateGroupe(this.groupeId, updatedGroupe).subscribe(\n      (response) => {\n        console.log(\"Response:\", response); // Log the valid response\n        this.router.navigate(['/groups']).then(() => {\n          console.log('Navigated to /groups successfully.');\n        }).catch((error) => {\n          console.error('Navigation error:', error);\n        });\n      },\n      (error) => {\n        console.error('Error updating group:', error);\n        // Handle other errors gracefully\n      }\n    );\n  }\n  \n  // Handle cancel action\n  cancel(): void {\n    this.router.navigate(['/groups']);\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAY1C,WAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAO/BC,YACUC,KAAqB,EACrBC,aAA4B,EAC5BC,gBAAkC;EAAG;EACrCC,MAAc;IAHd,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,MAAM,GAANA,MAAM;IAVhB,KAAAC,QAAQ,GAAW,CAAC;IACpB,KAAAC,MAAM,GAAW;MAAEC,QAAQ,EAAE,CAAC;MAAEC,SAAS,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAE,CAAE,CAAC,CAAC;IACjE,KAAAC,aAAa,GAAgB,EAAE,CAAC,CAAE;IAClC,KAAAC,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAC,OAAO,GAAY,IAAI;EAOpB;EAEL;EACAC,QAAQA,CAAA;IACN,IAAI,CAACR,QAAQ,GAAGS,MAAM,CAAC,IAAI,CAACb,KAAK,CAACc,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9D,IAAI,CAACC,SAAS,CAAC,IAAI,CAACb,QAAQ,CAAC;IAC7B,IAAI,CAACc,gBAAgB,EAAE,CAAC,CAAE;EAC5B;;EAEAD,SAASA,CAACE,EAAU;IAClB,IAAI,CAAClB,aAAa,CAACmB,aAAa,CAACD,EAAE,CAAC,CAACE,SAAS,CAC3CC,IAAY,IAAI;MACf,IAAI,CAACjB,MAAM,GAAGiB,IAAI;MAClB;MACA,IAAI,CAACZ,kBAAkB,GAAGY,IAAI,CAACd,UAAU,GAAG,CAAC,GAAGc,IAAI,CAACd,UAAU,CAAC,GAAG,EAAE;MACrE,IAAI,CAACG,OAAO,GAAG,KAAK;IACtB,CAAC,EACAY,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;MAC9C,IAAI,CAACZ,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAGE;EACAO,gBAAgBA,CAAA;IACd,IAAI,CAAChB,gBAAgB,CAACgB,gBAAgB,EAAE,CAACG,SAAS,CAC/CC,IAAiB,IAAI;MACpB,IAAI,CAACb,aAAa,GAAGa,IAAI;IAC3B,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;IAChD,CAAC,CACF;EACH;EAEA;EACAG,YAAYA,CAAA;IACV;IACA,MAAMC,aAAa,GAAG;MACpB,GAAG,IAAI,CAACtB,MAAM;MACdG,UAAU,EAAE,IAAI,CAACE,kBAAkB,CAAC;KACrC;;IAED,IAAI,CAACT,aAAa,CAACyB,YAAY,CAAC,IAAI,CAACtB,QAAQ,EAAEuB,aAAa,CAAC,CAACN,SAAS,CACpEO,QAAQ,IAAI;MACXJ,OAAO,CAACK,GAAG,CAAC,WAAW,EAAED,QAAQ,CAAC,CAAC,CAAC;MACpC,IAAI,CAACzB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,CAACC,IAAI,CAAC,MAAK;QAC1CP,OAAO,CAACK,GAAG,CAAC,oCAAoC,CAAC;MACnD,CAAC,CAAC,CAACG,KAAK,CAAEP,KAAK,IAAI;QACjBD,OAAO,CAACC,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC,EACAA,KAAK,IAAI;MACRD,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C;IACF,CAAC,CACF;EACH;EAEA;EACAQ,MAAMA,CAAA;IACJ,IAAI,CAAC9B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;CACD;AA7EYhC,oBAAoB,GAAAoC,UAAA,EALhCrC,SAAS,CAAC;EACTsC,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,8BAA8B;CAC3C,CAAC,C,EACWvC,oBAAoB,CA6EhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}