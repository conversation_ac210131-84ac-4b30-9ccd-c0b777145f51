{"ast": null, "code": "import * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js';\nimport { Modal } from 'bootstrap';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../auth/authentication.service\";\nimport * as i4 from \"../services/transaction.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"myChart\"];\nconst _c1 = [\"feedbackModal\"];\nfunction AdminDashComponent_a_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 40);\n    i0.ɵɵelement(1, \"span\", 41);\n    i0.ɵɵtext(2, \" Dashboard \");\n    i0.ɵɵelementStart(3, \"span\", 42);\n    i0.ɵɵtext(4, \"(current)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminDashComponent_a_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 43);\n    i0.ɵɵelement(1, \"span\", 44);\n    i0.ɵɵtext(2, \" Gestion des utilisateurs \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 45);\n    i0.ɵɵelement(1, \"span\", 46);\n    i0.ɵɵtext(2, \" Gestion des groupes \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 47);\n    i0.ɵɵelement(1, \"span\", 48);\n    i0.ɵɵtext(2, \" Gestion des transactions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 49);\n    i0.ɵɵelement(1, \"span\", 50);\n    i0.ɵɵtext(2, \" Gestion des actions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 51);\n    i0.ɵɵelement(1, \"span\", 52);\n    i0.ɵɵtext(2, \" Gestion des actionnaires \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 53);\n    i0.ɵɵelement(1, \"span\", 54);\n    i0.ɵɵtext(2, \" Gestion des Portefeuilles \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = \"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, 0.25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: 'Poppins', sans-serif;\\n  margin: 0;\\n  padding: 0;\\n  min-height: 100vh;\\n  background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n  background-size: 400% 400%;\\n  animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n  color: #fff;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n  0% {\\n      background-position: 0% 50%;\\n  }\\n\\n  50% {\\n      background-position: 100% 50%;\\n  }\\n\\n  100% {\\n      background-position: 0% 50%;\\n  }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  margin-top: 20px;\\n}\\n\\n\\n\\n\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n  background-color: #218838;\\n  transform: translateY(-2px);\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 1.5rem;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n  background-color: #f1f1f1;\\n  padding: 1rem;\\n  border-top: 1px solid #ddd;\\n  display: flex;\\n  justify-content: center; \\n\\n  gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  font-size: 1rem;\\n  border-radius: 0.3rem;\\n  transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n  transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column; \\n\\n  align-items: flex-start; \\n\\n}\\n\\n\\n\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n  background: rgba(20, 33, 59, 0.9); \\n\\n  color: #fff;\\n  min-height: 100vh;\\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #ccc;\\n  transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n  background-color: #000 !important;\\n  color: #fff;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n  position: relative;\\n  padding: 0.5rem 1rem;\\n  transition: color 0.3s ease;\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  width: 0;\\n  background: #ff4c60;\\n  transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 30px;\\n  object-fit: cover;\\n  border-radius: 50%; \\n\\n  margin-right: 8px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class AdminDashComponent {\n  constructor(responsableService, cdr, router, authService, transactionService) {\n    this.responsableService = responsableService;\n    this.cdr = cdr;\n    this.router = router;\n    this.authService = authService;\n    this.transactionService = transactionService;\n    this.responsables = [];\n    this.adminName = '';\n    this.transactions = [];\n    this.modalMessage = '';\n    // Registering chart.js components\n    Chart.register(...registerables);\n  }\n  // Function to load transactions from the server\n  loadTransactions() {\n    this.transactionService.getAllTransactions().subscribe({\n      next: data => {\n        this.transactions = data;\n        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded\n        // Now render the chart\n        this.renderChart();\n      },\n      error: err => {\n        console.error('Error fetching transactions', err);\n      }\n    });\n  }\n  // Function to render the chart using the transaction data\n  renderChart() {\n    const ctx = this.myChartRef.nativeElement.getContext('2d');\n    // Destroy previous chart if exists\n    if (this.chartInstance) {\n      this.chartInstance.destroy();\n    }\n    const labels = this.transactions.map(t => t.type);\n    const data = this.transactions.map(t => t.montatnt);\n    // Create a new chart instance\n    this.chartInstance = new Chart(ctx, {\n      type: 'line',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Montant par Type de Transaction',\n          data: data,\n          backgroundColor: 'rgba(54, 162, 235, 0.6)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n  ngOnInit() {\n    const adminData = localStorage.getItem('admin');\n    if (adminData) {\n      const admin = JSON.parse(adminData);\n      this.adminName = `${admin.prenom} ${admin.nom}`;\n    } else {\n      this.adminName = 'Admin';\n    }\n    this.loadResponsables();\n  }\n  loadResponsables() {\n    this.responsableService.getResponsables().subscribe(data => {\n      this.responsables = data;\n    }, error => {\n      console.error('Error fetching responsables:', error);\n    });\n  }\n  deleteResponsable(id) {\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: response => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: error => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    localStorage.removeItem('admin'); // ✅ correct key now\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  updateResponsable(id, updatedResponsable) {\n    this.responsableService.updateResponsable(id, updatedResponsable).subscribe(() => {\n      this.loadResponsables();\n    }, error => {\n      console.error('Error updating responsable:', error);\n    });\n  }\n  trackById(index, item) {\n    return item.id ?? 0;\n  }\n  ngAfterViewInit() {\n    this.loadTransactions();\n    feather.replace();\n    if (this.feedbackModalRef) {\n      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);\n    }\n  }\n  static {\n    this.ɵfac = function AdminDashComponent_Factory(t) {\n      return new (t || AdminDashComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthenticationService), i0.ɵɵdirectiveInject(i4.TransactionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashComponent,\n      selectors: [[\"app-respon-dashboard\"]],\n      viewQuery: function AdminDashComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedbackModalRef = _t.first);\n        }\n      },\n      decls: 53,\n      vars: 8,\n      consts: [[\"lang\", \"en\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-store, no-cache, must-revalidate, max-age=0\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"class\", \"nav-link\", \"href\", \"/adminDash\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/users\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/groups\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/transactions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actionnaires\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/port\", 4, \"ngIf\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [\"width\", \"400\", \"height\", \"200\", 2, \"background-color\", \"lightgrey\"], [\"myChart\", \"\"], [\"href\", \"/adminDash\", 1, \"nav-link\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"book\"]],\n      template: function AdminDashComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"meta\", 6)(8, \"meta\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10)(12, \"link\", 11);\n          i0.ɵɵelementStart(13, \"title\");\n          i0.ɵɵtext(14, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"link\", 12)(16, \"link\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"body\")(18, \"nav\", 14)(19, \"a\", 15);\n          i0.ɵɵelement(20, \"img\", 16);\n          i0.ɵɵtext(21, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"ul\", 17)(23, \"li\", 18)(24, \"a\", 19);\n          i0.ɵɵlistener(\"click\", function AdminDashComponent_Template_a_click_24_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(25, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21)(28, \"nav\", 22)(29, \"div\", 23)(30, \"ul\", 24)(31, \"li\", 25);\n          i0.ɵɵtemplate(32, AdminDashComponent_a_32_Template, 5, 0, \"a\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"li\", 25);\n          i0.ɵɵtemplate(34, AdminDashComponent_a_34_Template, 3, 0, \"a\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"li\", 25);\n          i0.ɵɵtemplate(36, AdminDashComponent_a_36_Template, 3, 0, \"a\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"li\", 25);\n          i0.ɵɵtemplate(38, AdminDashComponent_a_38_Template, 3, 0, \"a\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"li\", 25);\n          i0.ɵɵtemplate(40, AdminDashComponent_a_40_Template, 3, 0, \"a\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"li\", 25);\n          i0.ɵɵtemplate(42, AdminDashComponent_a_42_Template, 3, 0, \"a\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"li\", 25);\n          i0.ɵɵtemplate(44, AdminDashComponent_a_44_Template, 3, 0, \"a\", 32);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(45, \"main\", 33)(46, \"div\", 34)(47, \"h1\", 35);\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 36);\n          i0.ɵɵelement(50, \"div\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(51, \"canvas\", 38, 39);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(32);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/adminDash\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/users\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/groups\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/transactions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actionnaires\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/port\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"Welcome, \", ctx.adminName, \"\");\n        }\n      },\n      dependencies: [i5.NgIf],\n      styles: [_c2, _c2]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "Chart", "registerables", "Modal", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "AdminDashComponent", "constructor", "responsableService", "cdr", "router", "authService", "transactionService", "responsables", "admin<PERSON>ame", "transactions", "modalMessage", "register", "loadTransactions", "getAllTransactions", "subscribe", "next", "data", "console", "log", "<PERSON><PERSON><PERSON>", "error", "err", "ctx", "myChartRef", "nativeElement", "getContext", "chartInstance", "destroy", "labels", "map", "t", "type", "montatnt", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "options", "responsive", "scales", "y", "beginAtZero", "ngOnInit", "adminData", "localStorage", "getItem", "admin", "JSON", "parse", "prenom", "nom", "loadResponsables", "getResponsables", "deleteResponsable", "id", "confirm", "response", "filter", "responsable", "logout", "removeItem", "navigate", "updateResponsable", "updatedResponsable", "trackById", "index", "item", "ngAfterViewInit", "replace", "feedbackModalRef", "modalInstance", "ɵɵdirectiveInject", "i1", "ResponsableService", "ChangeDetectorRef", "i2", "Router", "i3", "AuthenticationService", "i4", "TransactionService", "selectors", "viewQuery", "AdminDashComponent_Query", "rf", "ɵɵlistener", "AdminDashComponent_Template_a_click_24_listener", "ɵɵtemplate", "AdminDashComponent_a_32_Template", "AdminDashComponent_a_34_Template", "AdminDashComponent_a_36_Template", "AdminDashComponent_a_38_Template", "AdminDashComponent_a_40_Template", "AdminDashComponent_a_42_Template", "AdminDashComponent_a_44_Template", "ɵɵadvance", "ɵɵproperty", "isRouteAllowed", "ɵɵtextInterpolate1"], "sources": ["C:\\Users\\<USER>\\Documents\\Final\\frontend\\src\\app\\admin-dash\\admin-dash.component.ts", "C:\\Users\\<USER>\\Documents\\Final\\frontend\\src\\app\\admin-dash\\admin-dash.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, AfterViewInit, ElementRef, ChangeDetectorRef } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js';\nimport { Router } from '@angular/router';\nimport { AuthenticationService } from '../auth/authentication.service';\nimport { Transaction } from '../model/transaction.model';\nimport { TransactionService } from '../services/transaction.service';\nimport { Modal } from 'bootstrap';\n\n@Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './admin-dash.component.html',\n  styleUrls: ['./admin-dash.component.css']\n})\nexport class AdminDashComponent implements AfterViewInit, OnInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  @ViewChild('feedbackModal') feedbackModalRef!: ElementRef;\n  responsables: User[] = [];\n  adminName: string = '';\n  private chartInstance: any;\n  transactions: Transaction[] = [];\n  modalMessage = '';\n  private modalInstance!: Modal;\n\n  constructor(\n    private responsableService: ResponsableService,\n    private cdr: ChangeDetectorRef,\n    private router: Router,\n    public authService: AuthenticationService,\n    private transactionService: TransactionService,\n\n    \n  ) {\n    // Registering chart.js components\n    Chart.register(...registerables);\n  }\n\n  \n\n  // Function to load transactions from the server\n  loadTransactions(): void {\n    this.transactionService.getAllTransactions().subscribe({\n      next: (data) => {\n        this.transactions = data;\n        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded\n\n        // Now render the chart\n        this.renderChart();\n      },\n      error: (err) => {\n        console.error('Error fetching transactions', err);\n      }\n    });\n  }\n\n\n  // Function to render the chart using the transaction data\n  renderChart(): void {\n    const ctx = this.myChartRef.nativeElement.getContext('2d');\n\n    // Destroy previous chart if exists\n    if (this.chartInstance) {\n      this.chartInstance.destroy();\n    }\n\n    const labels = this.transactions.map(t => t.type);\n    const data = this.transactions.map(t => t.montatnt);\n\n    // Create a new chart instance\n    this.chartInstance = new Chart(ctx, {\n      type: 'line',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Montant par Type de Transaction',\n          data: data,\n          backgroundColor: 'rgba(54, 162, 235, 0.6)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n\n\n  ngOnInit(): void {\n    const adminData = localStorage.getItem('admin');\n    if (adminData) {\n      const admin = JSON.parse(adminData);\n      this.adminName = `${admin.prenom} ${admin.nom}`;\n    } else {\n      this.adminName = 'Admin';\n    }\n\n    this.loadResponsables();\n  }\n\n  loadResponsables() {\n    this.responsableService.getResponsables().subscribe(\n      (data) => {\n        this.responsables = data;\n      },\n      (error) => {\n        console.error('Error fetching responsables:', error);\n      }\n    );\n  }\n\n  deleteResponsable(id: number) {\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: (response) => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: (error) => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    localStorage.removeItem('admin'); // ✅ correct key now\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n\n  updateResponsable(id: number, updatedResponsable: User) {\n    this.responsableService.updateResponsable(id, updatedResponsable).subscribe(\n      () => {\n        this.loadResponsables();\n      },\n      (error) => {\n        console.error('Error updating responsable:', error);\n      }\n    );\n  }\n\n  trackById(index: number, item: User): number | undefined {\n    return item.id ?? 0;\n  }\n\n  ngAfterViewInit() {\n    this.loadTransactions();\n    feather.replace();\n    if (this.feedbackModalRef) {\n      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);\n    }\n  }\n\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta http-equiv=\"Cache-Control\" content=\"no-store, no-cache, must-revalidate, max-age=0\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n\n    <!-- Custom styles for this template -->\n    <link href=\"./admin-dash.component.css\" rel=\"stylesheet\">\n  </head>\n\n  <body>\n     <!-- Navbar -->\n     <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/adminDash\" *ngIf=\"authService.isRouteAllowed('/adminDash')\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\" *ngIf=\"authService.isRouteAllowed('/users')\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\" *ngIf=\"authService.isRouteAllowed('/groups')\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            \n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/transactions\" *ngIf=\"authService.isRouteAllowed('/transactions')\">\n                <span data-feather=\"dollar-sign\"></span>\n                Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/actions\" *ngIf=\"authService.isRouteAllowed('/actions')\">\n                <span data-feather=\"trending-up\"></span>\n               Gestion des actions\n              </a>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\" *ngIf=\"authService.isRouteAllowed('/actionnaires')\">\n                  <span data-feather=\"users\"></span>\n                 Gestion des actionnaires\n                </a>\n                     <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/port\" *ngIf=\"authService.isRouteAllowed('/port')\">\n                <span data-feather=\"book\"></span>\n               Gestion des Portefeuilles\n              </a>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Welcome, {{ adminName }}</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n             \n              </div>\n            </div>\n          </div>\n          <canvas #myChart width=\"400\" height=\"200\" style=\"background-color: lightgrey;\"></canvas>\n\n\n        </main>\n      </div>\n    </div>\n\n    <!-- Bootstrap core JavaScript -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n\n    <!-- Chart.js CDN -->\n<script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n\n<script>\n  document.addEventListener('DOMContentLoaded', function() {\n    const ctx = document.querySelector('canvas#myChart').getContext('2d');\n\n    const myChart = new Chart(ctx, {\n      type: 'bar',  // نوع الرسم (bar, line, pie, doughnut, ...)\n      data: {\n        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],  // تسميات المحور الأفقي\n        datasets: [{\n          label: 'مبيعات (DT)',\n          data: [1200, 1900, 3000, 5000, 2300, 3400],  // البيانات\n          backgroundColor: 'rgba(54, 162, 235, 0.5)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 1\n        }]\n      },\n      options: {\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  });\n</script>\n\n  </body>\n</html>"], "mappings": "AAGA,OAAO,KAAKA,OAAO,MAAM,eAAe;AACxC,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAK/C,SAASC,KAAK,QAAQ,WAAW;;;;;;;;;;;ICoCjBC,EAAA,CAAAC,cAAA,YAAuF;IACrFD,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIlDJ,EAAA,CAAAC,cAAA,YAA+E;IAC7ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAiF;IAC/ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAINJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAwC;IACxCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAmF;IACjFD,EAAA,CAAAE,SAAA,eAAwC;IACzCF,EAAA,CAAAG,MAAA,4BACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAEFJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAkC;IACnCF,EAAA,CAAAG,MAAA,iCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,YAA6E;IAC3ED,EAAA,CAAAE,SAAA,eAAiC;IAClCF,EAAA,CAAAG,MAAA,kCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;ADnElB,OAAM,MAAOC,kBAAkB;EAU7BC,YACUC,kBAAsC,EACtCC,GAAsB,EACtBC,MAAc,EACfC,WAAkC,EACjCC,kBAAsC;IAJtC,KAAAJ,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAZ5B,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,SAAS,GAAW,EAAE;IAEtB,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,YAAY,GAAG,EAAE;IAYf;IACAlB,KAAK,CAACmB,QAAQ,CAAC,GAAGlB,aAAa,CAAC;EAClC;EAIA;EACAmB,gBAAgBA,CAAA;IACd,IAAI,CAACN,kBAAkB,CAACO,kBAAkB,EAAE,CAACC,SAAS,CAAC;MACrDC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACP,YAAY,GAAGO,IAAI;QACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACT,YAAY,CAAC,CAAC,CAAC;QAEtD;QACA,IAAI,CAACU,WAAW,EAAE;MACpB,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACbJ,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;MACnD;KACD,CAAC;EACJ;EAGA;EACAF,WAAWA,CAAA;IACT,MAAMG,GAAG,GAAG,IAAI,CAACC,UAAU,CAACC,aAAa,CAACC,UAAU,CAAC,IAAI,CAAC;IAE1D;IACA,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;;IAG9B,MAAMC,MAAM,GAAG,IAAI,CAACnB,YAAY,CAACoB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC;IACjD,MAAMf,IAAI,GAAG,IAAI,CAACP,YAAY,CAACoB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACE,QAAQ,CAAC;IAEnD;IACA,IAAI,CAACN,aAAa,GAAG,IAAIlC,KAAK,CAAC8B,GAAG,EAAE;MAClCS,IAAI,EAAE,MAAM;MACZf,IAAI,EAAE;QACJY,MAAM,EAAEA,MAAM;QACdK,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,iCAAiC;UACxClB,IAAI,EAAEA,IAAI;UACVmB,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;SACd;OACF;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNC,CAAC,EAAE;YACDC,WAAW,EAAE;;;;KAIpB,CAAC;EACJ;EAGAC,QAAQA,CAAA;IACN,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC/C,IAAIF,SAAS,EAAE;MACb,MAAMG,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC;MACnC,IAAI,CAACpC,SAAS,GAAG,GAAGuC,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACI,GAAG,EAAE;KAChD,MAAM;MACL,IAAI,CAAC3C,SAAS,GAAG,OAAO;;IAG1B,IAAI,CAAC4C,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAClD,kBAAkB,CAACmD,eAAe,EAAE,CAACvC,SAAS,CAChDE,IAAI,IAAI;MACP,IAAI,CAACT,YAAY,GAAGS,IAAI;IAC1B,CAAC,EACAI,KAAK,IAAI;MACRH,OAAO,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,CACF;EACH;EAEAkC,iBAAiBA,CAACC,EAAU;IAC1B,IAAIC,OAAO,CAAC,mDAAmD,CAAC,EAAE;MAChE,IAAI,CAACtD,kBAAkB,CAACoD,iBAAiB,CAACC,EAAE,CAAC,CAACzC,SAAS,CAAC;QACtDC,IAAI,EAAG0C,QAAQ,IAAI;UACjBxC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEuC,QAAQ,CAAC;UACzC,IAAI,CAAClD,YAAY,GAAG,IAAI,CAACA,YAAY,CAACmD,MAAM,CAACC,WAAW,IAAIA,WAAW,CAACJ,EAAE,KAAKA,EAAE,CAAC;QACpF,CAAC;QACDnC,KAAK,EAAGA,KAAK,IAAI;UACfH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAEAwC,MAAMA,CAAA;IACJf,YAAY,CAACgB,UAAU,CAAC,WAAW,CAAC;IACpChB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;IAC/BhB,YAAY,CAACgB,UAAU,CAAC,OAAO,CAAC;IAChChB,YAAY,CAACgB,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;IAClC5C,OAAO,CAACC,GAAG,CAAC2B,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC1C,MAAM,CAAC0D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,iBAAiBA,CAACR,EAAU,EAAES,kBAAwB;IACpD,IAAI,CAAC9D,kBAAkB,CAAC6D,iBAAiB,CAACR,EAAE,EAAES,kBAAkB,CAAC,CAAClD,SAAS,CACzE,MAAK;MACH,IAAI,CAACsC,gBAAgB,EAAE;IACzB,CAAC,EACAhC,KAAK,IAAI;MACRH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CACF;EACH;EAEA6C,SAASA,CAACC,KAAa,EAAEC,IAAU;IACjC,OAAOA,IAAI,CAACZ,EAAE,IAAI,CAAC;EACrB;EAEAa,eAAeA,CAAA;IACb,IAAI,CAACxD,gBAAgB,EAAE;IACvBrB,OAAO,CAAC8E,OAAO,EAAE;IACjB,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzB,IAAI,CAACC,aAAa,GAAG,IAAI7E,KAAK,CAAC,IAAI,CAAC4E,gBAAgB,CAAC9C,aAAa,CAAC;;EAEvE;;;uBAlJWxB,kBAAkB,EAAAL,EAAA,CAAA6E,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA/E,EAAA,CAAA6E,iBAAA,CAAA7E,EAAA,CAAAgF,iBAAA,GAAAhF,EAAA,CAAA6E,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAAlF,EAAA,CAAA6E,iBAAA,CAAAM,EAAA,CAAAC,qBAAA,GAAApF,EAAA,CAAA6E,iBAAA,CAAAQ,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAlBjF,kBAAkB;MAAAkF,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAA/D,GAAA;QAAA,IAAA+D,EAAA;;;;;;;;;;;;;;;UCf/B1F,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAE,SAAA,cAA0F;UAY1FF,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAG,MAAA,wCAAgC;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAE/CJ,EAAA,CAAAE,SAAA,gBAAmF;UAOrFF,EAAA,CAAAI,YAAA,EAAO;UAEPJ,EAAA,CAAAC,cAAA,YAAM;UAIAD,EAAA,CAAAE,SAAA,eAAwE;UACxEF,EAAA,CAAAG,MAAA,aACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAA2F,UAAA,mBAAAC,gDAAA;YAAA,OAASjE,GAAA,CAAAsC,MAAA,EAAQ;UAAA,EAAC;UAACjE,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAIzDJ,EAAA,CAAAC,cAAA,eAA6B;UAMjBD,EAAA,CAAA6F,UAAA,KAAAC,gCAAA,gBAGI;UACN9F,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA6F,UAAA,KAAAE,gCAAA,gBAGI;UACN/F,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA6F,UAAA,KAAAG,gCAAA,gBAGI;UACNhG,EAAA,CAAAI,YAAA,EAAK;UAEPJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA6F,UAAA,KAAAI,gCAAA,gBAGI;UACNjG,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA6F,UAAA,KAAAK,gCAAA,gBAGI;UAJNlG,EAAA,CAAAI,YAAA,EAAqB;UAKnBJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA6F,UAAA,KAAAM,gCAAA,gBAGI;UAJNnG,EAAA,CAAAI,YAAA,EAAqB;UAKdJ,EAAA,CAAAC,cAAA,cAAqB;UAC5BD,EAAA,CAAA6F,UAAA,KAAAO,gCAAA,gBAGI;UAJGpG,EAAA,CAAAI,YAAA,EAAqB;UASlCJ,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAG,MAAA,IAAwB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC5CJ,EAAA,CAAAC,cAAA,eAAsC;UACpCD,EAAA,CAAAE,SAAA,eAEM;UACRF,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAE,SAAA,sBAAwF;UAG1FF,EAAA,CAAAI,YAAA,EAAO;;;UAvDwCJ,EAAA,CAAAqG,SAAA,IAA8C;UAA9CrG,EAAA,CAAAsG,UAAA,SAAA3E,GAAA,CAAAjB,WAAA,CAAA6F,cAAA,eAA8C;UAMlDvG,EAAA,CAAAqG,SAAA,GAA0C;UAA1CrG,EAAA,CAAAsG,UAAA,SAAA3E,GAAA,CAAAjB,WAAA,CAAA6F,cAAA,WAA0C;UAMzCvG,EAAA,CAAAqG,SAAA,GAA2C;UAA3CrG,EAAA,CAAAsG,UAAA,SAAA3E,GAAA,CAAAjB,WAAA,CAAA6F,cAAA,YAA2C;UAOvCvG,EAAA,CAAAqG,SAAA,GAAiD;UAAjDrG,EAAA,CAAAsG,UAAA,SAAA3E,GAAA,CAAAjB,WAAA,CAAA6F,cAAA,kBAAiD;UAMtDvG,EAAA,CAAAqG,SAAA,GAA4C;UAA5CrG,EAAA,CAAAsG,UAAA,SAAA3E,GAAA,CAAAjB,WAAA,CAAA6F,cAAA,aAA4C;UAKrCvG,EAAA,CAAAqG,SAAA,GAAiD;UAAjDrG,EAAA,CAAAsG,UAAA,SAAA3E,GAAA,CAAAjB,WAAA,CAAA6F,cAAA,kBAAiD;UAK3DvG,EAAA,CAAAqG,SAAA,GAAyC;UAAzCrG,EAAA,CAAAsG,UAAA,SAAA3E,GAAA,CAAAjB,WAAA,CAAA6F,cAAA,UAAyC;UAU9DvG,EAAA,CAAAqG,SAAA,GAAwB;UAAxBrG,EAAA,CAAAwG,kBAAA,cAAA7E,GAAA,CAAAd,SAAA,KAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}