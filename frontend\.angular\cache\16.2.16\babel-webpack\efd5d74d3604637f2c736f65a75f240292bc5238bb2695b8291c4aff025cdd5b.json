{"ast": null, "code": "/*!\n * mime-types\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module dependencies.\n * @private\n */\nvar db = require('mime-db');\nvar extname = require('path').extname;\n\n/**\n * Module variables.\n * @private\n */\n\nvar EXTRACT_TYPE_REGEXP = /^\\s*([^;\\s]*)(?:;|\\s|$)/;\nvar TEXT_TYPE_REGEXP = /^text\\//i;\n\n/**\n * Module exports.\n * @public\n */\n\nexports.charset = charset;\nexports.charsets = {\n  lookup: charset\n};\nexports.contentType = contentType;\nexports.extension = extension;\nexports.extensions = Object.create(null);\nexports.lookup = lookup;\nexports.types = Object.create(null);\n\n// Populate the extensions/types maps\npopulateMaps(exports.extensions, exports.types);\n\n/**\n * Get the default charset for a MIME type.\n *\n * @param {string} type\n * @return {boolean|string}\n */\n\nfunction charset(type) {\n  if (!type || typeof type !== 'string') {\n    return false;\n  }\n\n  // TODO: use media-typer\n  var match = EXTRACT_TYPE_REGEXP.exec(type);\n  var mime = match && db[match[1].toLowerCase()];\n  if (mime && mime.charset) {\n    return mime.charset;\n  }\n\n  // default text/* to utf-8\n  if (match && TEXT_TYPE_REGEXP.test(match[1])) {\n    return 'UTF-8';\n  }\n  return false;\n}\n\n/**\n * Create a full Content-Type header given a MIME type or extension.\n *\n * @param {string} str\n * @return {boolean|string}\n */\n\nfunction contentType(str) {\n  // TODO: should this even be in this module?\n  if (!str || typeof str !== 'string') {\n    return false;\n  }\n  var mime = str.indexOf('/') === -1 ? exports.lookup(str) : str;\n  if (!mime) {\n    return false;\n  }\n\n  // TODO: use content-type or other module\n  if (mime.indexOf('charset') === -1) {\n    var charset = exports.charset(mime);\n    if (charset) mime += '; charset=' + charset.toLowerCase();\n  }\n  return mime;\n}\n\n/**\n * Get the default extension for a MIME type.\n *\n * @param {string} type\n * @return {boolean|string}\n */\n\nfunction extension(type) {\n  if (!type || typeof type !== 'string') {\n    return false;\n  }\n\n  // TODO: use media-typer\n  var match = EXTRACT_TYPE_REGEXP.exec(type);\n\n  // get extensions\n  var exts = match && exports.extensions[match[1].toLowerCase()];\n  if (!exts || !exts.length) {\n    return false;\n  }\n  return exts[0];\n}\n\n/**\n * Lookup the MIME type for a file path/extension.\n *\n * @param {string} path\n * @return {boolean|string}\n */\n\nfunction lookup(path) {\n  if (!path || typeof path !== 'string') {\n    return false;\n  }\n\n  // get the extension (\"ext\" or \".ext\" or full path)\n  var extension = extname('x.' + path).toLowerCase().substr(1);\n  if (!extension) {\n    return false;\n  }\n  return exports.types[extension] || false;\n}\n\n/**\n * Populate the extensions and types maps.\n * @private\n */\n\nfunction populateMaps(extensions, types) {\n  // source preference (least -> most)\n  var preference = ['nginx', 'apache', undefined, 'iana'];\n  Object.keys(db).forEach(function forEachMimeType(type) {\n    var mime = db[type];\n    var exts = mime.extensions;\n    if (!exts || !exts.length) {\n      return;\n    }\n\n    // mime -> extensions\n    extensions[type] = exts;\n\n    // extension -> mime\n    for (var i = 0; i < exts.length; i++) {\n      var extension = exts[i];\n      if (types[extension]) {\n        var from = preference.indexOf(db[types[extension]].source);\n        var to = preference.indexOf(mime.source);\n        if (types[extension] !== 'application/octet-stream' && (from > to || from === to && types[extension].substr(0, 12) === 'application/')) {\n          // skip the remapping\n          continue;\n        }\n      }\n\n      // set the extension -> mime\n      types[extension] = type;\n    }\n  });\n}", "map": {"version": 3, "names": ["db", "require", "extname", "EXTRACT_TYPE_REGEXP", "TEXT_TYPE_REGEXP", "exports", "charset", "charsets", "lookup", "contentType", "extension", "extensions", "Object", "create", "types", "populateMaps", "type", "match", "exec", "mime", "toLowerCase", "test", "str", "indexOf", "exts", "length", "path", "substr", "preference", "undefined", "keys", "for<PERSON>ach", "forEachMimeType", "i", "from", "source", "to"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/mime-types/index.js"], "sourcesContent": ["/*!\n * mime-types\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict'\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar db = require('mime-db')\nvar extname = require('path').extname\n\n/**\n * Module variables.\n * @private\n */\n\nvar EXTRACT_TYPE_REGEXP = /^\\s*([^;\\s]*)(?:;|\\s|$)/\nvar TEXT_TYPE_REGEXP = /^text\\//i\n\n/**\n * Module exports.\n * @public\n */\n\nexports.charset = charset\nexports.charsets = { lookup: charset }\nexports.contentType = contentType\nexports.extension = extension\nexports.extensions = Object.create(null)\nexports.lookup = lookup\nexports.types = Object.create(null)\n\n// Populate the extensions/types maps\npopulateMaps(exports.extensions, exports.types)\n\n/**\n * Get the default charset for a MIME type.\n *\n * @param {string} type\n * @return {boolean|string}\n */\n\nfunction charset (type) {\n  if (!type || typeof type !== 'string') {\n    return false\n  }\n\n  // TODO: use media-typer\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\n  var mime = match && db[match[1].toLowerCase()]\n\n  if (mime && mime.charset) {\n    return mime.charset\n  }\n\n  // default text/* to utf-8\n  if (match && TEXT_TYPE_REGEXP.test(match[1])) {\n    return 'UTF-8'\n  }\n\n  return false\n}\n\n/**\n * Create a full Content-Type header given a MIME type or extension.\n *\n * @param {string} str\n * @return {boolean|string}\n */\n\nfunction contentType (str) {\n  // TODO: should this even be in this module?\n  if (!str || typeof str !== 'string') {\n    return false\n  }\n\n  var mime = str.indexOf('/') === -1\n    ? exports.lookup(str)\n    : str\n\n  if (!mime) {\n    return false\n  }\n\n  // TODO: use content-type or other module\n  if (mime.indexOf('charset') === -1) {\n    var charset = exports.charset(mime)\n    if (charset) mime += '; charset=' + charset.toLowerCase()\n  }\n\n  return mime\n}\n\n/**\n * Get the default extension for a MIME type.\n *\n * @param {string} type\n * @return {boolean|string}\n */\n\nfunction extension (type) {\n  if (!type || typeof type !== 'string') {\n    return false\n  }\n\n  // TODO: use media-typer\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\n\n  // get extensions\n  var exts = match && exports.extensions[match[1].toLowerCase()]\n\n  if (!exts || !exts.length) {\n    return false\n  }\n\n  return exts[0]\n}\n\n/**\n * Lookup the MIME type for a file path/extension.\n *\n * @param {string} path\n * @return {boolean|string}\n */\n\nfunction lookup (path) {\n  if (!path || typeof path !== 'string') {\n    return false\n  }\n\n  // get the extension (\"ext\" or \".ext\" or full path)\n  var extension = extname('x.' + path)\n    .toLowerCase()\n    .substr(1)\n\n  if (!extension) {\n    return false\n  }\n\n  return exports.types[extension] || false\n}\n\n/**\n * Populate the extensions and types maps.\n * @private\n */\n\nfunction populateMaps (extensions, types) {\n  // source preference (least -> most)\n  var preference = ['nginx', 'apache', undefined, 'iana']\n\n  Object.keys(db).forEach(function forEachMimeType (type) {\n    var mime = db[type]\n    var exts = mime.extensions\n\n    if (!exts || !exts.length) {\n      return\n    }\n\n    // mime -> extensions\n    extensions[type] = exts\n\n    // extension -> mime\n    for (var i = 0; i < exts.length; i++) {\n      var extension = exts[i]\n\n      if (types[extension]) {\n        var from = preference.indexOf(db[types[extension]].source)\n        var to = preference.indexOf(mime.source)\n\n        if (types[extension] !== 'application/octet-stream' &&\n          (from > to || (from === to && types[extension].substr(0, 12) === 'application/'))) {\n          // skip the remapping\n          continue\n        }\n      }\n\n      // set the extension -> mime\n      types[extension] = type\n    }\n  })\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AAEA,IAAIA,EAAE,GAAGC,OAAO,CAAC,SAAS,CAAC;AAC3B,IAAIC,OAAO,GAAGD,OAAO,CAAC,MAAM,CAAC,CAACC,OAAO;;AAErC;AACA;AACA;AACA;;AAEA,IAAIC,mBAAmB,GAAG,yBAAyB;AACnD,IAAIC,gBAAgB,GAAG,UAAU;;AAEjC;AACA;AACA;AACA;;AAEAC,OAAO,CAACC,OAAO,GAAGA,OAAO;AACzBD,OAAO,CAACE,QAAQ,GAAG;EAAEC,MAAM,EAAEF;AAAQ,CAAC;AACtCD,OAAO,CAACI,WAAW,GAAGA,WAAW;AACjCJ,OAAO,CAACK,SAAS,GAAGA,SAAS;AAC7BL,OAAO,CAACM,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AACxCR,OAAO,CAACG,MAAM,GAAGA,MAAM;AACvBH,OAAO,CAACS,KAAK,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;;AAEnC;AACAE,YAAY,CAACV,OAAO,CAACM,UAAU,EAAEN,OAAO,CAACS,KAAK,CAAC;;AAE/C;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASR,OAAOA,CAAEU,IAAI,EAAE;EACtB,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,OAAO,KAAK;EACd;;EAEA;EACA,IAAIC,KAAK,GAAGd,mBAAmB,CAACe,IAAI,CAACF,IAAI,CAAC;EAC1C,IAAIG,IAAI,GAAGF,KAAK,IAAIjB,EAAE,CAACiB,KAAK,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC;EAE9C,IAAID,IAAI,IAAIA,IAAI,CAACb,OAAO,EAAE;IACxB,OAAOa,IAAI,CAACb,OAAO;EACrB;;EAEA;EACA,IAAIW,KAAK,IAAIb,gBAAgB,CAACiB,IAAI,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5C,OAAO,OAAO;EAChB;EAEA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASR,WAAWA,CAAEa,GAAG,EAAE;EACzB;EACA,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACnC,OAAO,KAAK;EACd;EAEA,IAAIH,IAAI,GAAGG,GAAG,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAC9BlB,OAAO,CAACG,MAAM,CAACc,GAAG,CAAC,GACnBA,GAAG;EAEP,IAAI,CAACH,IAAI,EAAE;IACT,OAAO,KAAK;EACd;;EAEA;EACA,IAAIA,IAAI,CAACI,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;IAClC,IAAIjB,OAAO,GAAGD,OAAO,CAACC,OAAO,CAACa,IAAI,CAAC;IACnC,IAAIb,OAAO,EAAEa,IAAI,IAAI,YAAY,GAAGb,OAAO,CAACc,WAAW,CAAC,CAAC;EAC3D;EAEA,OAAOD,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAST,SAASA,CAAEM,IAAI,EAAE;EACxB,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,OAAO,KAAK;EACd;;EAEA;EACA,IAAIC,KAAK,GAAGd,mBAAmB,CAACe,IAAI,CAACF,IAAI,CAAC;;EAE1C;EACA,IAAIQ,IAAI,GAAGP,KAAK,IAAIZ,OAAO,CAACM,UAAU,CAACM,KAAK,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAAC;EAE9D,IAAI,CAACI,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,EAAE;IACzB,OAAO,KAAK;EACd;EAEA,OAAOD,IAAI,CAAC,CAAC,CAAC;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAShB,MAAMA,CAAEkB,IAAI,EAAE;EACrB,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,OAAO,KAAK;EACd;;EAEA;EACA,IAAIhB,SAAS,GAAGR,OAAO,CAAC,IAAI,GAAGwB,IAAI,CAAC,CACjCN,WAAW,CAAC,CAAC,CACbO,MAAM,CAAC,CAAC,CAAC;EAEZ,IAAI,CAACjB,SAAS,EAAE;IACd,OAAO,KAAK;EACd;EAEA,OAAOL,OAAO,CAACS,KAAK,CAACJ,SAAS,CAAC,IAAI,KAAK;AAC1C;;AAEA;AACA;AACA;AACA;;AAEA,SAASK,YAAYA,CAAEJ,UAAU,EAAEG,KAAK,EAAE;EACxC;EACA,IAAIc,UAAU,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAEC,SAAS,EAAE,MAAM,CAAC;EAEvDjB,MAAM,CAACkB,IAAI,CAAC9B,EAAE,CAAC,CAAC+B,OAAO,CAAC,SAASC,eAAeA,CAAEhB,IAAI,EAAE;IACtD,IAAIG,IAAI,GAAGnB,EAAE,CAACgB,IAAI,CAAC;IACnB,IAAIQ,IAAI,GAAGL,IAAI,CAACR,UAAU;IAE1B,IAAI,CAACa,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,EAAE;MACzB;IACF;;IAEA;IACAd,UAAU,CAACK,IAAI,CAAC,GAAGQ,IAAI;;IAEvB;IACA,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,IAAI,CAACC,MAAM,EAAEQ,CAAC,EAAE,EAAE;MACpC,IAAIvB,SAAS,GAAGc,IAAI,CAACS,CAAC,CAAC;MAEvB,IAAInB,KAAK,CAACJ,SAAS,CAAC,EAAE;QACpB,IAAIwB,IAAI,GAAGN,UAAU,CAACL,OAAO,CAACvB,EAAE,CAACc,KAAK,CAACJ,SAAS,CAAC,CAAC,CAACyB,MAAM,CAAC;QAC1D,IAAIC,EAAE,GAAGR,UAAU,CAACL,OAAO,CAACJ,IAAI,CAACgB,MAAM,CAAC;QAExC,IAAIrB,KAAK,CAACJ,SAAS,CAAC,KAAK,0BAA0B,KAChDwB,IAAI,GAAGE,EAAE,IAAKF,IAAI,KAAKE,EAAE,IAAItB,KAAK,CAACJ,SAAS,CAAC,CAACiB,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,cAAe,CAAC,EAAE;UACnF;UACA;QACF;MACF;;MAEA;MACAb,KAAK,CAACJ,SAAS,CAAC,GAAGM,IAAI;IACzB;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}