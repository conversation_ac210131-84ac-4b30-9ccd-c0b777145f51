spring.application.name=Administratif


spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.hibernate.ddl-auto=update


spring.jpa.show-sql=true



server.port=8000

spring.config.import=optional:configserver:http://localhost:8888


eureka.client.service-url.defaultZone=http://localhost:8761/eureka/


management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always



spring.datasource.url=*************************************
spring.datasource.username=C##aziz
spring.datasource.password=aziz123
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver




spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB



application.config.actionnaire-url=http://localhost:8020/GRA/Actionnaire


application.config.client-url=http://localhost:8010/GRA/Client






spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=azizgrami31@@gmail.com
spring.mail.password=fbbm mfxu vojw ghkl
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

java.awt.headless=true



twilio.account.sid=**********************************
twilio.auth.token=f2926005c778d47b4e32579e03d06975
twilio.phone.number=whatsapp:+***********  # Twilio sandbox WhatsApp number


