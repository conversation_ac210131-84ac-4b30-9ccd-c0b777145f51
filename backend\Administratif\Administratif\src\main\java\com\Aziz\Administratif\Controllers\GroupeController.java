package com.Aziz.Administratif.Controllers;


import com.Aziz.Administratif.Entity.Groupe;
import com.Aziz.Administratif.Services.GroupeService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("GRA/Groupes")
@RequiredArgsConstructor
public class GroupeController {

    private final GroupeService GroupeService;


    @GetMapping
    public ResponseEntity<List<Groupe>>findAllUsers(){
        return ResponseEntity.ok(GroupeService.findAllGroupes());
    }



    @PostMapping("/ajout")

    public ResponseEntity<?> save(@RequestBody Groupe Groupe) {




        GroupeService.saveGroupe(Groupe);
      //  return ResponseEntity.status(HttpStatus.CREATED);
        return null;
    }


















}
