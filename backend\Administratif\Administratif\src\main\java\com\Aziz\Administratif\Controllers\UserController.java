package com.Aziz.Administratif.Controllers;


import com.Aziz.Administratif.Entity.User;
import com.Aziz.Administratif.Enum.Role;
import com.Aziz.Administratif.Services.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("GRA/Users")
@RequiredArgsConstructor
public class UserController {

    private final UserService service;


    @GetMapping
    public ResponseEntity<List<User>>findAllUsers(){
        return ResponseEntity.ok(service.findAllUsers());
    }



    @PostMapping("/ajout")

    public ResponseEntity<?> save(@RequestBody User user, @RequestParam Long idGroupe, @RequestParam Long idRessource) {
        // Vérifier si un ADMIN a un type (ce qui est interdit)
        if (user.getRole() == Role.ADMIN && user.getGroupe() != null) {
            return ResponseEntity.badRequest().body("Un ADMIN ne peut pas avoir de groupe, houwa el m3alem !");
        }

        // Vérifier si un RESPONSABLE n'a pas de type (ce qui est obligatoire)
        if (user.getRole() == Role.RESPONSABLE && user.getGroupe() == null) {
            return ResponseEntity.badRequest().body("Un RESPONSABLE doit avoir un groupe, mela fech bch yekhdm !");
        }

        // Enregistrer l'utilisateur après validation
        service.saveUser(user,idGroupe,idRessource);
      //  return ResponseEntity.status(HttpStatus.CREATED);
        return null;
    }





    //relation avec actionnaire

//    @GetMapping("with-actionnaire/{user-id}")
//    public ResponseEntity<FullUserResponse>findAllUsers(
//            @PathVariable("user-id") Long userId
//    ){
//        return ResponseEntity.ok(service.findUserswithActionnaire(userId));
//    }








}
