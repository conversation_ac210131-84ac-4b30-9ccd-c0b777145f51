{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js';\nimport { Modal } from 'bootstrap';\nexport let AdminDashComponent = class AdminDashComponent {\n  constructor(responsableService, cdr, router, authService, transactionService) {\n    this.responsableService = responsableService;\n    this.cdr = cdr;\n    this.router = router;\n    this.authService = authService;\n    this.transactionService = transactionService;\n    this.responsables = [];\n    this.adminName = '';\n    this.transactions = [];\n    this.modalMessage = '';\n    // Registering chart.js components\n    Chart.register(...registerables);\n  }\n  // Function to load transactions from the server\n  loadTransactions() {\n    this.transactionService.getAllTransactions().subscribe({\n      next: data => {\n        this.transactions = data;\n        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded\n        // Now render the chart\n        this.renderChart();\n      },\n      error: err => {\n        console.error('Error fetching transactions', err);\n      }\n    });\n  }\n  // Function to render the chart using the transaction data\n  renderChart() {\n    const ctx = this.myChartRef.nativeElement.getContext('2d');\n    // Destroy previous chart if exists\n    if (this.chartInstance) {\n      this.chartInstance.destroy();\n    }\n    const labels = this.transactions.map(t => t.type);\n    const data = this.transactions.map(t => t.montatnt);\n    // Create a new chart instance\n    this.chartInstance = new Chart(ctx, {\n      type: 'line',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Montant par Type de Transaction',\n          data: data,\n          backgroundColor: 'rgba(54, 162, 235, 0.6)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n  ngOnInit() {\n    const adminData = localStorage.getItem('admin');\n    if (adminData) {\n      const admin = JSON.parse(adminData);\n      this.adminName = `${admin.prenom} ${admin.nom}`;\n    } else {\n      this.adminName = 'Admin';\n    }\n    this.loadResponsables();\n  }\n  loadResponsables() {\n    this.responsableService.getResponsables().subscribe(data => {\n      this.responsables = data;\n    }, error => {\n      console.error('Error fetching responsables:', error);\n    });\n  }\n  deleteResponsable(id) {\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: response => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: error => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    localStorage.removeItem('admin'); // ✅ correct key now\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  updateResponsable(id, updatedResponsable) {\n    this.responsableService.updateResponsable(id, updatedResponsable).subscribe(() => {\n      this.loadResponsables();\n    }, error => {\n      console.error('Error updating responsable:', error);\n    });\n  }\n  trackById(index, item) {\n    return item.id ?? 0;\n  }\n  ngAfterViewInit() {\n    this.loadTransactions();\n    feather.replace();\n    if (this.feedbackModalRef) {\n      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);\n    }\n  }\n};\n__decorate([ViewChild('myChart')], AdminDashComponent.prototype, \"myChartRef\", void 0);\n__decorate([ViewChild('feedbackModal')], AdminDashComponent.prototype, \"feedbackModalRef\", void 0);\nAdminDashComponent = __decorate([Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './admin-dash.component.html',\n  styleUrls: ['./admin-dash.component.css']\n})], AdminDashComponent);", "map": {"version": 3, "names": ["Component", "ViewChild", "feather", "Chart", "registerables", "Modal", "AdminDashComponent", "constructor", "responsableService", "cdr", "router", "authService", "transactionService", "responsables", "admin<PERSON>ame", "transactions", "modalMessage", "register", "loadTransactions", "getAllTransactions", "subscribe", "next", "data", "console", "log", "<PERSON><PERSON><PERSON>", "error", "err", "ctx", "myChartRef", "nativeElement", "getContext", "chartInstance", "destroy", "labels", "map", "t", "type", "montatnt", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "options", "responsive", "scales", "y", "beginAtZero", "ngOnInit", "adminData", "localStorage", "getItem", "admin", "JSON", "parse", "prenom", "nom", "loadResponsables", "getResponsables", "deleteResponsable", "id", "confirm", "response", "filter", "responsable", "logout", "removeItem", "navigate", "updateResponsable", "updatedResponsable", "trackById", "index", "item", "ngAfterViewInit", "replace", "feedbackModalRef", "modalInstance", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\admin-dash\\admin-dash.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild, AfterViewInit, ElementRef, ChangeDetectorRef } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js';\nimport { Router } from '@angular/router';\nimport { AuthenticationService } from '../auth/authentication.service';\nimport { Transaction } from '../model/transaction.model';\nimport { TransactionService } from '../services/transaction.service';\nimport { Modal } from 'bootstrap';\n\n@Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './admin-dash.component.html',\n  styleUrls: ['./admin-dash.component.css']\n})\nexport class AdminDashComponent implements AfterViewInit, OnInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  @ViewChild('feedbackModal') feedbackModalRef!: ElementRef;\n  responsables: User[] = [];\n  adminName: string = '';\n  private chartInstance: any;\n  transactions: Transaction[] = [];\n  modalMessage = '';\n  private modalInstance!: Modal;\n\n  constructor(\n    private responsableService: ResponsableService,\n    private cdr: ChangeDetectorRef,\n    private router: Router,\n    public authService: AuthenticationService,\n    private transactionService: TransactionService,\n\n    \n  ) {\n    // Registering chart.js components\n    Chart.register(...registerables);\n  }\n\n  \n\n  // Function to load transactions from the server\n  loadTransactions(): void {\n    this.transactionService.getAllTransactions().subscribe({\n      next: (data) => {\n        this.transactions = data;\n        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded\n\n        // Now render the chart\n        this.renderChart();\n      },\n      error: (err) => {\n        console.error('Error fetching transactions', err);\n      }\n    });\n  }\n\n\n  // Function to render the chart using the transaction data\n  renderChart(): void {\n    const ctx = this.myChartRef.nativeElement.getContext('2d');\n\n    // Destroy previous chart if exists\n    if (this.chartInstance) {\n      this.chartInstance.destroy();\n    }\n\n    const labels = this.transactions.map(t => t.type);\n    const data = this.transactions.map(t => t.montatnt);\n\n    // Create a new chart instance\n    this.chartInstance = new Chart(ctx, {\n      type: 'line',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Montant par Type de Transaction',\n          data: data,\n          backgroundColor: 'rgba(54, 162, 235, 0.6)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n\n\n  ngOnInit(): void {\n    const adminData = localStorage.getItem('admin');\n    if (adminData) {\n      const admin = JSON.parse(adminData);\n      this.adminName = `${admin.prenom} ${admin.nom}`;\n    } else {\n      this.adminName = 'Admin';\n    }\n\n    this.loadResponsables();\n  }\n\n  loadResponsables() {\n    this.responsableService.getResponsables().subscribe(\n      (data) => {\n        this.responsables = data;\n      },\n      (error) => {\n        console.error('Error fetching responsables:', error);\n      }\n    );\n  }\n\n  deleteResponsable(id: number) {\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: (response) => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: (error) => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    localStorage.removeItem('admin'); // ✅ correct key now\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n\n  updateResponsable(id: number, updatedResponsable: User) {\n    this.responsableService.updateResponsable(id, updatedResponsable).subscribe(\n      () => {\n        this.loadResponsables();\n      },\n      (error) => {\n        console.error('Error updating responsable:', error);\n      }\n    );\n  }\n\n  trackById(index: number, item: User): number | undefined {\n    return item.id ?? 0;\n  }\n\n  ngAfterViewInit() {\n    this.loadTransactions();\n    feather.replace();\n    if (this.feedbackModalRef) {\n      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);\n    }\n  }\n\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,SAAS,QAAsD,eAAe;AAG1G,OAAO,KAAKC,OAAO,MAAM,eAAe;AACxC,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAK/C,SAASC,KAAK,QAAQ,WAAW;AAO1B,WAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAU7BC,YACUC,kBAAsC,EACtCC,GAAsB,EACtBC,MAAc,EACfC,WAAkC,EACjCC,kBAAsC;IAJtC,KAAAJ,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAZ5B,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,SAAS,GAAW,EAAE;IAEtB,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,YAAY,GAAG,EAAE;IAYf;IACAb,KAAK,CAACc,QAAQ,CAAC,GAAGb,aAAa,CAAC;EAClC;EAIA;EACAc,gBAAgBA,CAAA;IACd,IAAI,CAACN,kBAAkB,CAACO,kBAAkB,EAAE,CAACC,SAAS,CAAC;MACrDC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACP,YAAY,GAAGO,IAAI;QACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACT,YAAY,CAAC,CAAC,CAAC;QAEtD;QACA,IAAI,CAACU,WAAW,EAAE;MACpB,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACbJ,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;MACnD;KACD,CAAC;EACJ;EAGA;EACAF,WAAWA,CAAA;IACT,MAAMG,GAAG,GAAG,IAAI,CAACC,UAAU,CAACC,aAAa,CAACC,UAAU,CAAC,IAAI,CAAC;IAE1D;IACA,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;;IAG9B,MAAMC,MAAM,GAAG,IAAI,CAACnB,YAAY,CAACoB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC;IACjD,MAAMf,IAAI,GAAG,IAAI,CAACP,YAAY,CAACoB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACE,QAAQ,CAAC;IAEnD;IACA,IAAI,CAACN,aAAa,GAAG,IAAI7B,KAAK,CAACyB,GAAG,EAAE;MAClCS,IAAI,EAAE,MAAM;MACZf,IAAI,EAAE;QACJY,MAAM,EAAEA,MAAM;QACdK,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,iCAAiC;UACxClB,IAAI,EAAEA,IAAI;UACVmB,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;SACd;OACF;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNC,CAAC,EAAE;YACDC,WAAW,EAAE;;;;KAIpB,CAAC;EACJ;EAGAC,QAAQA,CAAA;IACN,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC/C,IAAIF,SAAS,EAAE;MACb,MAAMG,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC;MACnC,IAAI,CAACpC,SAAS,GAAG,GAAGuC,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACI,GAAG,EAAE;KAChD,MAAM;MACL,IAAI,CAAC3C,SAAS,GAAG,OAAO;;IAG1B,IAAI,CAAC4C,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAClD,kBAAkB,CAACmD,eAAe,EAAE,CAACvC,SAAS,CAChDE,IAAI,IAAI;MACP,IAAI,CAACT,YAAY,GAAGS,IAAI;IAC1B,CAAC,EACAI,KAAK,IAAI;MACRH,OAAO,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,CACF;EACH;EAEAkC,iBAAiBA,CAACC,EAAU;IAC1B,IAAIC,OAAO,CAAC,mDAAmD,CAAC,EAAE;MAChE,IAAI,CAACtD,kBAAkB,CAACoD,iBAAiB,CAACC,EAAE,CAAC,CAACzC,SAAS,CAAC;QACtDC,IAAI,EAAG0C,QAAQ,IAAI;UACjBxC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEuC,QAAQ,CAAC;UACzC,IAAI,CAAClD,YAAY,GAAG,IAAI,CAACA,YAAY,CAACmD,MAAM,CAACC,WAAW,IAAIA,WAAW,CAACJ,EAAE,KAAKA,EAAE,CAAC;QACpF,CAAC;QACDnC,KAAK,EAAGA,KAAK,IAAI;UACfH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAEAwC,MAAMA,CAAA;IACJf,YAAY,CAACgB,UAAU,CAAC,WAAW,CAAC;IACpChB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;IAC/BhB,YAAY,CAACgB,UAAU,CAAC,OAAO,CAAC;IAChChB,YAAY,CAACgB,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;IAClC5C,OAAO,CAACC,GAAG,CAAC2B,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC1C,MAAM,CAAC0D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,iBAAiBA,CAACR,EAAU,EAAES,kBAAwB;IACpD,IAAI,CAAC9D,kBAAkB,CAAC6D,iBAAiB,CAACR,EAAE,EAAES,kBAAkB,CAAC,CAAClD,SAAS,CACzE,MAAK;MACH,IAAI,CAACsC,gBAAgB,EAAE;IACzB,CAAC,EACAhC,KAAK,IAAI;MACRH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CACF;EACH;EAEA6C,SAASA,CAACC,KAAa,EAAEC,IAAU;IACjC,OAAOA,IAAI,CAACZ,EAAE,IAAI,CAAC;EACrB;EAEAa,eAAeA,CAAA;IACb,IAAI,CAACxD,gBAAgB,EAAE;IACvBhB,OAAO,CAACyE,OAAO,EAAE;IACjB,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzB,IAAI,CAACC,aAAa,GAAG,IAAIxE,KAAK,CAAC,IAAI,CAACuE,gBAAgB,CAAC9C,aAAa,CAAC;;EAEvE;CAED;AAnJuBgD,UAAA,EAArB7E,SAAS,CAAC,SAAS,CAAC,C,qDAAyB;AAClB6E,UAAA,EAA3B7E,SAAS,CAAC,eAAe,CAAC,C,2DAA+B;AAF/CK,kBAAkB,GAAAwE,UAAA,EAL9B9E,SAAS,CAAC;EACT+E,QAAQ,EAAE,sBAAsB;EAChCC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,CAAC,4BAA4B;CACzC,CAAC,C,EACW3E,kBAAkB,CAoJ9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}