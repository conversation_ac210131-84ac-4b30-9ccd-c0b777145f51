{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nexport class AddResponsableComponent {\n  constructor(responsableService, router) {\n    this.responsableService = responsableService;\n    this.router = router;\n    this.newResponsable = {\n      id: 0,\n      nom: '',\n      prenom: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE' // Role is fixed as RESPONSABLE\n    };\n  }\n  // Add the responsable when form is submitted\n  addResponsable() {\n    this.responsableService.addResponsable(this.newResponsable).subscribe(response => {\n      console.log('Responsable added successfully:', response);\n      this.router.navigate(['/adminDash']); // Navigate to dashboard after successful addition\n    }, error => {\n      console.error('Error adding responsable:', error);\n    });\n  }\n  static {\n    this.ɵfac = function AddResponsableComponent_Factory(t) {\n      return new (t || AddResponsableComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddResponsableComponent,\n      selectors: [[\"app-add-responsable\"]],\n      decls: 2,\n      vars: 0,\n      template: function AddResponsableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"add-responsable works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AddResponsableComponent", "constructor", "responsableService", "router", "newResponsable", "id", "nom", "prenom", "email", "telephone", "role", "addResponsable", "subscribe", "response", "console", "log", "navigate", "error", "i0", "ɵɵdirectiveInject", "i1", "ResponsableService", "i2", "Router", "selectors", "decls", "vars", "template", "AddResponsableComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\n\n@Component({\n  selector: 'app-add-responsable',\n  templateUrl: './add-responsable.component.html',\n  styleUrls: ['./add-responsable.component.css']\n})\nexport class AddResponsableComponent {\n  newResponsable: User = {\n    id: 0,  // Let the backend handle the ID\n    nom: '',\n    prenom: '',\n    email: '',\n    telephone: '',\n    role: 'RESPONSABLE'  // Role is fixed as RESPONSABLE\n  };\n\n  constructor(\n    private responsableService: ResponsableService,\n    private router: Router\n  ) {}\n\n  // Add the responsable when form is submitted\n  addResponsable(): void {\n    this.responsableService.addResponsable(this.newResponsable).subscribe(\n      (response) => {\n        console.log('Responsable added successfully:', response);\n        this.router.navigate(['/adminDash']);  // Navigate to dashboard after successful addition\n      },\n      (error) => {\n        console.error('Error adding responsable:', error);\n      }\n    );\n  }\n}\n", "<p>add-responsable works!</p>\n"], "mappings": ";;;AAUA,OAAM,MAAOA,uBAAuB;EAUlCC,YACUC,kBAAsC,EACtCC,MAAc;IADd,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IAXhB,KAAAC,cAAc,GAAS;MACrBC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa,CAAE;KACtB;EAKE;EAEH;EACAC,cAAcA,CAAA;IACZ,IAAI,CAACT,kBAAkB,CAACS,cAAc,CAAC,IAAI,CAACP,cAAc,CAAC,CAACQ,SAAS,CAClEC,QAAQ,IAAI;MACXC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,QAAQ,CAAC;MACxD,IAAI,CAACV,MAAM,CAACa,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAE;IACzC,CAAC,EACAC,KAAK,IAAI;MACRH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,CACF;EACH;;;uBA1BWjB,uBAAuB,EAAAkB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvBvB,uBAAuB;MAAAwB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVpCX,EAAA,CAAAa,cAAA,QAAG;UAAAb,EAAA,CAAAc,MAAA,6BAAsB;UAAAd,EAAA,CAAAe,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}