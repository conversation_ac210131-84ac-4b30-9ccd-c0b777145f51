{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/groupe.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction ModifyGroupComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"p\");\n    i0.ɵɵtext(2, \"Loading...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModifyGroupComponent_div_5_form_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtext(1, \" Group name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModifyGroupComponent_div_5_form_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 8);\n    i0.ɵɵlistener(\"ngSubmit\", function ModifyGroupComponent_div_5_form_1_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.updateGroupe());\n    });\n    i0.ɵɵelementStart(1, \"div\", 9)(2, \"label\", 10);\n    i0.ɵɵtext(3, \"Nom du groupe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 11, 12);\n    i0.ɵɵlistener(\"ngModelChange\", function ModifyGroupComponent_div_5_form_1_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.groupe.nomGroupe = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ModifyGroupComponent_div_5_form_1_div_6_Template, 2, 0, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14)(8, \"button\", 15);\n    i0.ɵɵtext(9, \"Mettre \\u00E0 jour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ModifyGroupComponent_div_5_form_1_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.cancel());\n    });\n    i0.ɵɵtext(11, \"Annuler\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r4 = i0.ɵɵreference(5);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.groupe.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", _r4.invalid && _r4.touched);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", _r4.invalid);\n  }\n}\nfunction ModifyGroupComponent_div_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.errorMessage, \" \");\n  }\n}\nfunction ModifyGroupComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ModifyGroupComponent_div_5_form_1_Template, 12, 3, \"form\", 6);\n    i0.ɵɵtemplate(2, ModifyGroupComponent_div_5_div_2_Template, 2, 1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.groupe);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.errorMessage);\n  }\n}\nexport class ModifyGroupComponent {\n  constructor(route, groupeService, router) {\n    this.route = route;\n    this.groupeService = groupeService;\n    this.router = router;\n    this.groupeId = 0;\n    this.groupe = {\n      idGroupe: 0,\n      nomGroupe: ''\n    };\n    this.loading = true;\n    this.errorMessage = null;\n  }\n  ngOnInit() {\n    this.groupeId = Number(this.route.snapshot.paramMap.get('id'));\n    // Only fetch group data if ID is valid\n    if (this.groupeId > 0) {\n      console.log('Group ID from URL:', this.groupeId);\n      this.getGroupe(this.groupeId);\n    } else {\n      this.loading = false;\n      console.warn('Invalid group ID');\n    }\n  }\n  // Cleanup check (optional debug)\n  ngOnDestroy() {\n    console.log('ModifyGroupComponent destroyed');\n  }\n  getGroupe(id) {\n    this.groupeService.getGroupeById(id).subscribe(data => {\n      console.log(\"Group data:\", data);\n      this.groupe = data;\n      this.loading = false;\n    }, err => {\n      console.error('Error loading group data', err);\n      this.loading = false;\n      this.errorMessage = 'There was an error loading the group data. Please try again.';\n    });\n  }\n  updateGroupe() {\n    console.log('Updating Group with payload:', this.groupe);\n    this.groupeService.updateGroupe(this.groupeId, this.groupe).subscribe(response => {\n      console.log(\"Group updated successfully\", response);\n      this.errorMessage = null;\n      // Store a flag in localStorage or pass data via NavigationExtras\n      localStorage.setItem('groupUpdated', 'true');\n      // Navigate cleanly\n      this.router.navigate(['/groups'], {\n        replaceUrl: true\n      });\n    }, error => {\n      console.error('Error updating group', error);\n      this.errorMessage = 'There was an error updating the group. Please try again.';\n    });\n  }\n  cancel() {\n    this.router.navigate(['/groups']);\n  }\n  static {\n    this.ɵfac = function ModifyGroupComponent_Factory(t) {\n      return new (t || ModifyGroupComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.GroupeService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModifyGroupComponent,\n      selectors: [[\"app-modify-group\"]],\n      decls: 6,\n      vars: 2,\n      consts: [[1, \"container\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"100vh\"], [1, \"card\", \"p-4\", 2, \"width\", \"100%\", \"max-width\", \"500px\"], [1, \"text-center\", \"mb-4\"], [\"class\", \"text-center\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\"], [3, \"ngSubmit\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [3, \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"nomGroupe\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", \"placeholder\", \"Enter group name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"nomGroupe\", \"ngModel\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"mb-3\", \"d-flex\", \"justify-content-between\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"text-danger\"], [1, \"alert\", \"alert-danger\"]],\n      template: function ModifyGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n          i0.ɵɵtext(3, \"Modifier le groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ModifyGroupComponent_div_4_Template, 3, 0, \"div\", 3);\n          i0.ɵɵtemplate(5, ModifyGroupComponent_div_5_Template, 3, 2, \"div\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ModifyGroupComponent_div_5_form_1_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "updateGroupe", "ModifyGroupComponent_div_5_form_1_Template_input_ngModelChange_4_listener", "$event", "ctx_r8", "groupe", "nomGroupe", "ɵɵtemplate", "ModifyGroupComponent_div_5_form_1_div_6_Template", "ModifyGroupComponent_div_5_form_1_Template_button_click_10_listener", "ctx_r9", "cancel", "ɵɵadvance", "ɵɵproperty", "ctx_r2", "_r4", "invalid", "touched", "ɵɵtextInterpolate1", "ctx_r3", "errorMessage", "ModifyGroupComponent_div_5_form_1_Template", "ModifyGroupComponent_div_5_div_2_Template", "ctx_r1", "ModifyGroupComponent", "constructor", "route", "groupeService", "router", "groupeId", "idGroupe", "loading", "ngOnInit", "Number", "snapshot", "paramMap", "get", "console", "log", "getGroupe", "warn", "ngOnDestroy", "id", "getGroupeById", "subscribe", "data", "err", "error", "response", "localStorage", "setItem", "navigate", "replaceUrl", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "GroupeService", "Router", "selectors", "decls", "vars", "consts", "template", "ModifyGroupComponent_Template", "rf", "ctx", "ModifyGroupComponent_div_4_Template", "ModifyGroupComponent_div_5_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\modify-group\\modify-group.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\modify-group\\modify-group.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\n\n@Component({\n  selector: 'app-modify-group',\n  templateUrl: './modify-group.component.html',\n  styleUrls: ['./modify-group.component.css']\n})\nexport class ModifyGroupComponent implements OnInit, OnDestroy {\n  groupeId: number = 0;\n  groupe: Groupe = { idGroupe: 0, nomGroupe: '' };\n  loading: boolean = true;\n  errorMessage: string | null = null;\n\n  constructor(\n    private route: ActivatedRoute,\n    private groupeService: GroupeService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.groupeId = Number(this.route.snapshot.paramMap.get('id'));\n\n    // Only fetch group data if ID is valid\n    if (this.groupeId > 0) {\n      console.log('Group ID from URL:', this.groupeId);\n      this.getGroupe(this.groupeId);\n    } else {\n      this.loading = false;\n      console.warn('Invalid group ID');\n    }\n  }\n\n  // Cleanup check (optional debug)\n  ngOnDestroy(): void {\n    console.log('ModifyGroupComponent destroyed');\n  }\n\n  getGroupe(id: number): void {\n    this.groupeService.getGroupeById(id).subscribe(\n      (data: Groupe) => {\n        console.log(\"Group data:\", data);\n        this.groupe = data;\n        this.loading = false;\n      },\n      (err) => {\n        console.error('Error loading group data', err);\n        this.loading = false;\n        this.errorMessage = 'There was an error loading the group data. Please try again.';\n      }\n    );\n  }\n\n  updateGroupe(): void {\n    console.log('Updating Group with payload:', this.groupe);\n    this.groupeService.updateGroupe(this.groupeId, this.groupe).subscribe(\n      (response) => {\n        console.log(\"Group updated successfully\", response);\n        this.errorMessage = null;\n  \n        // Store a flag in localStorage or pass data via NavigationExtras\n        localStorage.setItem('groupUpdated', 'true');\n  \n        // Navigate cleanly\n        this.router.navigate(['/groups'], { replaceUrl: true });\n      },\n      (error) => {\n        console.error('Error updating group', error);\n        this.errorMessage = 'There was an error updating the group. Please try again.';\n      }\n    );\n  }\n  \n\n  cancel(): void {\n    this.router.navigate(['/groups']);\n  }\n}\n", "<div class=\"container d-flex justify-content-center align-items-center\" style=\"height: 100vh;\">\n  <div class=\"card p-4\" style=\"width: 100%; max-width: 500px;\">\n    <h2 class=\"text-center mb-4\">Modifier le groupe</h2>\n\n    <!-- Loading Indicator -->\n    <div *ngIf=\"loading\" class=\"text-center\">\n      <p>Loading...</p> <!-- You can add a spinner here if you prefer -->\n    </div>\n\n    <div *ngIf=\"!loading\">\n      <!-- Group Update Form -->\n      <form (ngSubmit)=\"updateGroupe()\" *ngIf=\"groupe\">\n        <!-- Group Name -->\n        <div class=\"mb-3\">\n          <label for=\"nomGroupe\" class=\"form-label\">Nom du groupe</label>\n          <input\n            type=\"text\"\n            id=\"nomGroupe\"\n            [(ngModel)]=\"groupe.nomGroupe\"\n            name=\"nomGroupe\"\n            class=\"form-control\"\n            required\n            placeholder=\"Enter group name\"\n            #nomGroupe=\"ngModel\"\n          />\n          <div *ngIf=\"nomGroupe.invalid && nomGroupe.touched\" class=\"text-danger\">\n            Group name is required.\n          </div>\n        </div>\n\n        <!-- Submit and Cancel buttons -->\n        <div class=\"mb-3 d-flex justify-content-between\">\n          <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"nomGroupe.invalid\">Mettre à jour</button>\n          <button type=\"button\" class=\"btn btn-secondary\" (click)=\"cancel()\">Annuler</button>\n        </div>\n      </form>\n\n      <!-- Error Message -->\n      <div *ngIf=\"errorMessage\" class=\"alert alert-danger\">\n        {{ errorMessage }}\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;ICKIA,EAAA,CAAAC,cAAA,aAAyC;IACpCD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAmBbH,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAhBVH,EAAA,CAAAC,cAAA,cAAiD;IAA3CD,EAAA,CAAAI,UAAA,sBAAAC,oEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAYT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAE/BX,EAAA,CAAAC,cAAA,aAAkB;IAC0BD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,oBASE;IANAD,EAAA,CAAAI,UAAA,2BAAAQ,0EAAAC,MAAA;MAAAb,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAd,EAAA,CAAAS,aAAA;MAAA,OAAaT,EAAA,CAAAU,WAAA,CAAAI,MAAA,CAAAC,MAAA,CAAAC,SAAA,GAAAH,MAAA,CACnB;IAAA,EADoC;IAHhCb,EAAA,CAAAG,YAAA,EASE;IACFH,EAAA,CAAAiB,UAAA,IAAAC,gDAAA,kBAEM;IACRlB,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAiD;IAC8BD,EAAA,CAAAE,MAAA,yBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnGH,EAAA,CAAAC,cAAA,kBAAmE;IAAnBD,EAAA,CAAAI,UAAA,mBAAAe,oEAAA;MAAAnB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAa,MAAA,GAAApB,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAU,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAACrB,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAfjFH,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAuB,UAAA,YAAAC,MAAA,CAAAT,MAAA,CAAAC,SAAA,CAA8B;IAO1BhB,EAAA,CAAAsB,SAAA,GAA4C;IAA5CtB,EAAA,CAAAuB,UAAA,SAAAE,GAAA,CAAAC,OAAA,IAAAD,GAAA,CAAAE,OAAA,CAA4C;IAOJ3B,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAuB,UAAA,aAAAE,GAAA,CAAAC,OAAA,CAA8B;;;;;IAMhF1B,EAAA,CAAAC,cAAA,cAAqD;IACnDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAA4B,kBAAA,MAAAC,MAAA,CAAAC,YAAA,MACF;;;;;IA/BF9B,EAAA,CAAAC,cAAA,UAAsB;IAEpBD,EAAA,CAAAiB,UAAA,IAAAc,0CAAA,mBAwBO;IAGP/B,EAAA,CAAAiB,UAAA,IAAAe,yCAAA,iBAEM;IACRhC,EAAA,CAAAG,YAAA,EAAM;;;;IA9B+BH,EAAA,CAAAsB,SAAA,GAAY;IAAZtB,EAAA,CAAAuB,UAAA,SAAAU,MAAA,CAAAlB,MAAA,CAAY;IA2BzCf,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAAuB,UAAA,SAAAU,MAAA,CAAAH,YAAA,CAAkB;;;AD5B9B,OAAM,MAAOI,oBAAoB;EAM/BC,YACUC,KAAqB,EACrBC,aAA4B,EAC5BC,MAAc;IAFd,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAC,QAAQ,GAAW,CAAC;IACpB,KAAAxB,MAAM,GAAW;MAAEyB,QAAQ,EAAE,CAAC;MAAExB,SAAS,EAAE;IAAE,CAAE;IAC/C,KAAAyB,OAAO,GAAY,IAAI;IACvB,KAAAX,YAAY,GAAkB,IAAI;EAM/B;EAEHY,QAAQA,CAAA;IACN,IAAI,CAACH,QAAQ,GAAGI,MAAM,CAAC,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;IAE9D;IACA,IAAI,IAAI,CAACP,QAAQ,GAAG,CAAC,EAAE;MACrBQ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACT,QAAQ,CAAC;MAChD,IAAI,CAACU,SAAS,CAAC,IAAI,CAACV,QAAQ,CAAC;KAC9B,MAAM;MACL,IAAI,CAACE,OAAO,GAAG,KAAK;MACpBM,OAAO,CAACG,IAAI,CAAC,kBAAkB,CAAC;;EAEpC;EAEA;EACAC,WAAWA,CAAA;IACTJ,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;EAC/C;EAEAC,SAASA,CAACG,EAAU;IAClB,IAAI,CAACf,aAAa,CAACgB,aAAa,CAACD,EAAE,CAAC,CAACE,SAAS,CAC3CC,IAAY,IAAI;MACfR,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEO,IAAI,CAAC;MAChC,IAAI,CAACxC,MAAM,GAAGwC,IAAI;MAClB,IAAI,CAACd,OAAO,GAAG,KAAK;IACtB,CAAC,EACAe,GAAG,IAAI;MACNT,OAAO,CAACU,KAAK,CAAC,0BAA0B,EAAED,GAAG,CAAC;MAC9C,IAAI,CAACf,OAAO,GAAG,KAAK;MACpB,IAAI,CAACX,YAAY,GAAG,8DAA8D;IACpF,CAAC,CACF;EACH;EAEAnB,YAAYA,CAAA;IACVoC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACjC,MAAM,CAAC;IACxD,IAAI,CAACsB,aAAa,CAAC1B,YAAY,CAAC,IAAI,CAAC4B,QAAQ,EAAE,IAAI,CAACxB,MAAM,CAAC,CAACuC,SAAS,CAClEI,QAAQ,IAAI;MACXX,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEU,QAAQ,CAAC;MACnD,IAAI,CAAC5B,YAAY,GAAG,IAAI;MAExB;MACA6B,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC;MAE5C;MACA,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;QAAEC,UAAU,EAAE;MAAI,CAAE,CAAC;IACzD,CAAC,EACAL,KAAK,IAAI;MACRV,OAAO,CAACU,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,IAAI,CAAC3B,YAAY,GAAG,0DAA0D;IAChF,CAAC,CACF;EACH;EAGAT,MAAMA,CAAA;IACJ,IAAI,CAACiB,MAAM,CAACuB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;;;uBApEW3B,oBAAoB,EAAAlC,EAAA,CAAA+D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjE,EAAA,CAAA+D,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAnE,EAAA,CAAA+D,iBAAA,CAAAC,EAAA,CAAAI,MAAA;IAAA;EAAA;;;YAApBlC,oBAAoB;MAAAmC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVjC3E,EAAA,CAAAC,cAAA,aAA+F;UAE9DD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpDH,EAAA,CAAAiB,UAAA,IAAA4D,mCAAA,iBAEM;UAEN7E,EAAA,CAAAiB,UAAA,IAAA6D,mCAAA,iBAgCM;UACR9E,EAAA,CAAAG,YAAA,EAAM;;;UArCEH,EAAA,CAAAsB,SAAA,GAAa;UAAbtB,EAAA,CAAAuB,UAAA,SAAAqD,GAAA,CAAAnC,OAAA,CAAa;UAIbzC,EAAA,CAAAsB,SAAA,GAAc;UAAdtB,EAAA,CAAAuB,UAAA,UAAAqD,GAAA,CAAAnC,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}