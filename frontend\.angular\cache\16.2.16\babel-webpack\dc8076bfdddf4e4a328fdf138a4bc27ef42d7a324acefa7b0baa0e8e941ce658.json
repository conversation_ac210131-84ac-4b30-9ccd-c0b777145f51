{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../authentication.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nexport class LoginComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.email = '';\n    this.password = '';\n  }\n  onLogin() {\n    this.authService.login(this.email, this.password).subscribe(response => {\n      this.authService.storeToken(response.token);\n      const userRole = this.getUserRoleFromToken(response.token);\n      console.log(\"Login successful, User role:\", userRole);\n      if (userRole === 'RESPONSABLE') {\n        this.router.navigate(['/ResDash']);\n      } else {\n        this.router.navigate(['/not-authorized']);\n      }\n    }, error => {\n      console.error(\" Login failed:\", error);\n    });\n  }\n  getUserRoleFromToken(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return payload?.role || '';\n    } catch (e) {\n      console.error(\"Invalid token:\", e);\n      return '';\n    }\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 28,\n      vars: 2,\n      consts: [[1, \"container\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"vh-100\"], [1, \"card\", \"p-4\", \"shadow-lg\", \"rounded\", 2, \"width\", \"400px\"], [1, \"text-center\"], [1, \"mb-3\"], [1, \"text-muted\"], [3, \"ngSubmit\"], [\"for\", \"email\", 1, \"form-label\", \"fw-bold\"], [1, \"input-group\"], [1, \"input-group-text\"], [1, \"bi\", \"bi-envelope\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"placeholder\", \"Enter your email\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\", 1, \"form-label\", \"fw-bold\"], [1, \"bi\", \"bi-lock\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\", \"fw-bold\"], [1, \"bi\", \"bi-box-arrow-in-right\"], [1, \"text-center\", \"mt-3\"], [\"href\", \"#\", 1, \"text-decoration-none\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵtext(4, \"Login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Sign in to your account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onLogin();\n          });\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"label\", 6);\n          i0.ɵɵtext(10, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"span\", 8);\n          i0.ɵɵelement(13, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_14_listener($event) {\n            return ctx.email = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 3)(16, \"label\", 11);\n          i0.ɵɵtext(17, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 7)(19, \"span\", 8);\n          i0.ɵɵelement(20, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.password = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"button\", 14);\n          i0.ɵɵelement(23, \"i\", 15);\n          i0.ɵɵtext(24, \" Login \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 16)(26, \"a\", 17);\n          i0.ɵɵtext(27, \"Forgot password?\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.email);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.password);\n        }\n      },\n      dependencies: [i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["LoginComponent", "constructor", "authService", "router", "email", "password", "onLogin", "login", "subscribe", "response", "storeToken", "token", "userRole", "getUserRoleFromToken", "console", "log", "navigate", "error", "payload", "JSON", "parse", "atob", "split", "role", "e", "i0", "ɵɵdirectiveInject", "i1", "AuthenticationService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_7_listener", "ɵɵelement", "LoginComponent_Template_input_ngModelChange_14_listener", "$event", "LoginComponent_Template_input_ngModelChange_21_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { AuthenticationService } from '../authentication.service';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent {\n  email = '';\n  password = '';\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  onLogin() {\n    this.authService.login(this.email, this.password).subscribe(response => {\n      this.authService.storeToken(response.token);\n      \n      const userRole = this.getUserRoleFromToken(response.token);\n      console.log(\"Login successful, User role:\", userRole);\n\n      if (userRole === 'RESPONSABLE') {\n        this.router.navigate(['/ResDash']);\n      } else {\n        this.router.navigate(['/not-authorized']);\n      }\n    }, error => {\n      console.error(\" Login failed:\", error);\n    });\n  }\n\n  private getUserRoleFromToken(token: string): string {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return payload?.role || '';\n    } catch (e) {\n      console.error(\"Invalid token:\", e);\n      return '';\n    }\n  }\n}\n", "<div class=\"container d-flex justify-content-center align-items-center vh-100\">\n  <div class=\"card p-4 shadow-lg rounded\" style=\"width: 400px;\">\n    <div class=\"text-center\">\n      <h2 class=\"mb-3\">Login</h2>\n      <p class=\"text-muted\">Sign in to your account</p>\n    </div>\n\n    <form (ngSubmit)=\"onLogin()\">\n      <!-- Email Field -->\n      <div class=\"mb-3\">\n        <label for=\"email\" class=\"form-label fw-bold\">Email</label>\n        <div class=\"input-group\">\n          <span class=\"input-group-text\"><i class=\"bi bi-envelope\"></i></span>\n          <input type=\"email\" id=\"email\" class=\"form-control\" [(ngModel)]=\"email\" name=\"email\" required placeholder=\"Enter your email\" />\n        </div>\n      </div>\n\n      <!-- Password Field -->\n      <div class=\"mb-3\">\n        <label for=\"password\" class=\"form-label fw-bold\">Password</label>\n        <div class=\"input-group\">\n          <span class=\"input-group-text\"><i class=\"bi bi-lock\"></i></span>\n          <input type=\"password\" id=\"password\" class=\"form-control\" [(ngModel)]=\"password\" name=\"password\" required placeholder=\"Enter your password\" />\n        </div>\n      </div>\n\n      <!-- Login Button -->\n      <button type=\"submit\" class=\"btn btn-primary w-100 fw-bold\">\n        <i class=\"bi bi-box-arrow-in-right\"></i> Login\n      </button>\n\n      <!-- Additional Links -->\n      <div class=\"text-center mt-3\">\n        <a href=\"#\" class=\"text-decoration-none\">Forgot password?</a>\n      </div>\n    </form>\n  </div>\n</div>\n"], "mappings": ";;;;AASA,OAAM,MAAOA,cAAc;EAIzBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;IAHtE,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,QAAQ,GAAG,EAAE;EAEoE;EAEjFC,OAAOA,CAAA;IACL,IAAI,CAACJ,WAAW,CAACK,KAAK,CAAC,IAAI,CAACH,KAAK,EAAE,IAAI,CAACC,QAAQ,CAAC,CAACG,SAAS,CAACC,QAAQ,IAAG;MACrE,IAAI,CAACP,WAAW,CAACQ,UAAU,CAACD,QAAQ,CAACE,KAAK,CAAC;MAE3C,MAAMC,QAAQ,GAAG,IAAI,CAACC,oBAAoB,CAACJ,QAAQ,CAACE,KAAK,CAAC;MAC1DG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEH,QAAQ,CAAC;MAErD,IAAIA,QAAQ,KAAK,aAAa,EAAE;QAC9B,IAAI,CAACT,MAAM,CAACa,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;OACnC,MAAM;QACL,IAAI,CAACb,MAAM,CAACa,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;;IAE7C,CAAC,EAAEC,KAAK,IAAG;MACTH,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC,CAAC,CAAC;EACJ;EAEQJ,oBAAoBA,CAACF,KAAa;IACxC,IAAI;MACF,MAAMO,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,OAAOJ,OAAO,EAAEK,IAAI,IAAI,EAAE;KAC3B,CAAC,OAAOC,CAAC,EAAE;MACVV,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAEO,CAAC,CAAC;MAClC,OAAO,EAAE;;EAEb;;;uBA/BWxB,cAAc,EAAAyB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAd9B,cAAc;MAAA+B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT3BZ,EAAA,CAAAc,cAAA,aAA+E;UAGxDd,EAAA,CAAAe,MAAA,YAAK;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UAC3BhB,EAAA,CAAAc,cAAA,WAAsB;UAAAd,EAAA,CAAAe,MAAA,8BAAuB;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UAGnDhB,EAAA,CAAAc,cAAA,cAA6B;UAAvBd,EAAA,CAAAiB,UAAA,sBAAAC,iDAAA;YAAA,OAAYL,GAAA,CAAAhC,OAAA,EAAS;UAAA,EAAC;UAE1BmB,EAAA,CAAAc,cAAA,aAAkB;UAC8Bd,EAAA,CAAAe,MAAA,aAAK;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UAC3DhB,EAAA,CAAAc,cAAA,cAAyB;UACQd,EAAA,CAAAmB,SAAA,YAA8B;UAAAnB,EAAA,CAAAgB,YAAA,EAAO;UACpEhB,EAAA,CAAAc,cAAA,iBAA+H;UAA3Ed,EAAA,CAAAiB,UAAA,2BAAAG,wDAAAC,MAAA;YAAA,OAAAR,GAAA,CAAAlC,KAAA,GAAA0C,MAAA;UAAA,EAAmB;UAAvErB,EAAA,CAAAgB,YAAA,EAA+H;UAKnIhB,EAAA,CAAAc,cAAA,cAAkB;UACiCd,EAAA,CAAAe,MAAA,gBAAQ;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UACjEhB,EAAA,CAAAc,cAAA,cAAyB;UACQd,EAAA,CAAAmB,SAAA,aAA0B;UAAAnB,EAAA,CAAAgB,YAAA,EAAO;UAChEhB,EAAA,CAAAc,cAAA,iBAA8I;UAApFd,EAAA,CAAAiB,UAAA,2BAAAK,wDAAAD,MAAA;YAAA,OAAAR,GAAA,CAAAjC,QAAA,GAAAyC,MAAA;UAAA,EAAsB;UAAhFrB,EAAA,CAAAgB,YAAA,EAA8I;UAKlJhB,EAAA,CAAAc,cAAA,kBAA4D;UAC1Dd,EAAA,CAAAmB,SAAA,aAAwC;UAACnB,EAAA,CAAAe,MAAA,eAC3C;UAAAf,EAAA,CAAAgB,YAAA,EAAS;UAGThB,EAAA,CAAAc,cAAA,eAA8B;UACad,EAAA,CAAAe,MAAA,wBAAgB;UAAAf,EAAA,CAAAgB,YAAA,EAAI;;;UApBPhB,EAAA,CAAAuB,SAAA,IAAmB;UAAnBvB,EAAA,CAAAwB,UAAA,YAAAX,GAAA,CAAAlC,KAAA,CAAmB;UASbqB,EAAA,CAAAuB,SAAA,GAAsB;UAAtBvB,EAAA,CAAAwB,UAAA,YAAAX,GAAA,CAAAjC,QAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}