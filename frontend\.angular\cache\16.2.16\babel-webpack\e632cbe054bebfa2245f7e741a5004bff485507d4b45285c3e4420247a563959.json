{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthenticationService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8080/api/v1/auth'; // Change this to your Spring Boot API URL\n  }\n  // Register User\n  register(firstName, lastName, email, password) {\n    return this.http.post(`${this.apiUrl}/register`, {\n      firstName,\n      lastName,\n      email,\n      password\n    });\n  }\n  // Authenticate User\n  login(email, password) {\n    return this.http.post(`${this.apiUrl}/authenticate`, {\n      email,\n      password\n    });\n  }\n  // Store JWT Token\n  storeToken(token) {\n    localStorage.setItem('jwt_token', token); // Store JWT in localStorage\n  }\n  // Get Token\n  getToken() {\n    return localStorage.getItem('jwt_token');\n  }\n  // Remove Token\n  removeToken() {\n    localStorage.removeItem('jwt_token');\n  }\n  // Check if the user is logged in\n  isAuthenticated() {\n    return !!this.getToken();\n  }\n  // Decode JWT and extract role and group\n  getUserDetailsFromToken(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT\n      return {\n        role: payload?.role || '',\n        groupe: payload?.groupe || ''\n      };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return {\n        role: '',\n        groupe: ''\n      };\n    }\n  }\n  static {\n    this.ɵfac = function AuthenticationService_Factory(t) {\n      return new (t || AuthenticationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthenticationService,\n      factory: AuthenticationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AuthenticationService", "constructor", "http", "apiUrl", "register", "firstName", "lastName", "email", "password", "post", "login", "storeToken", "token", "localStorage", "setItem", "getToken", "getItem", "removeToken", "removeItem", "isAuthenticated", "getUserDetailsFromToken", "payload", "JSON", "parse", "atob", "split", "role", "groupe", "e", "console", "error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\authentication.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\n\ninterface AuthResponse {\n  token: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthenticationService {\n  private apiUrl = 'http://localhost:8080/api/v1/auth';  // Change this to your Spring Boot API URL\n\n  constructor(private http: HttpClient) {}\n\n  // Register User\n  register(firstName: string, lastName: string, email: string, password: string): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/register`, { firstName, lastName, email, password });\n  }\n\n  // Authenticate User\n  login(email: string, password: string): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/authenticate`, { email, password });\n  }\n\n  // Store JWT Token\n  storeToken(token: string) {\n    localStorage.setItem('jwt_token', token);  // Store JWT in localStorage\n  }\n\n  // Get Token\n  getToken(): string | null {\n    return localStorage.getItem('jwt_token');\n  }\n\n  // Remove Token\n  removeToken() {\n    localStorage.removeItem('jwt_token');\n  }\n\n  // Check if the user is logged in\n  isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n\n  // Decode JWT and extract role and group\n  getUserDetailsFromToken(token: string): { role: string, groupe: string } {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT\n      return { role: payload?.role || '', groupe: payload?.groupe || '' };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return { role: '', groupe: '' };\n    }\n  }\n}\n"], "mappings": ";;AAWA,OAAM,MAAOA,qBAAqB;EAGhCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,mCAAmC,CAAC,CAAE;EAEhB;EAEvC;EACAC,QAAQA,CAACC,SAAiB,EAAEC,QAAgB,EAAEC,KAAa,EAAEC,QAAgB;IAC3E,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAe,GAAG,IAAI,CAACN,MAAM,WAAW,EAAE;MAAEE,SAAS;MAAEC,QAAQ;MAAEC,KAAK;MAAEC;IAAQ,CAAE,CAAC;EAC1G;EAEA;EACAE,KAAKA,CAACH,KAAa,EAAEC,QAAgB;IACnC,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAe,GAAG,IAAI,CAACN,MAAM,eAAe,EAAE;MAAEI,KAAK;MAAEC;IAAQ,CAAE,CAAC;EACzF;EAEA;EACAG,UAAUA,CAACC,KAAa;IACtBC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEF,KAAK,CAAC,CAAC,CAAE;EAC7C;EAEA;EACAG,QAAQA,CAAA;IACN,OAAOF,YAAY,CAACG,OAAO,CAAC,WAAW,CAAC;EAC1C;EAEA;EACAC,WAAWA,CAAA;IACTJ,YAAY,CAACK,UAAU,CAAC,WAAW,CAAC;EACtC;EAEA;EACAC,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAACJ,QAAQ,EAAE;EAC1B;EAEA;EACAK,uBAAuBA,CAACR,KAAa;IACnC,IAAI;MACF,MAAMS,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACZ,KAAK,CAACa,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,OAAO;QAAEC,IAAI,EAAEL,OAAO,EAAEK,IAAI,IAAI,EAAE;QAAEC,MAAM,EAAEN,OAAO,EAAEM,MAAM,IAAI;MAAE,CAAE;KACpE,CAAC,OAAOC,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,CAAC,CAAC;MACzC,OAAO;QAAEF,IAAI,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAE;;EAEnC;;;uBA5CW3B,qBAAqB,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArBlC,qBAAqB;MAAAmC,OAAA,EAArBnC,qBAAqB,CAAAoC,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}