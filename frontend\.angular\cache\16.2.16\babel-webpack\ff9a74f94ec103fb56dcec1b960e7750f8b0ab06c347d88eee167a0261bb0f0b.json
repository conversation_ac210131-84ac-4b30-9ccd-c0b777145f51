{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class NotAuthorizedComponent {\n  static {\n    this.ɵfac = function NotAuthorizedComponent_Factory(t) {\n      return new (t || NotAuthorizedComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotAuthorizedComponent,\n      selectors: [[\"app-not-authorized\"]],\n      decls: 20,\n      vars: 0,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"UTF-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1.0\"], [\"onclick\", \"window.location.href='/'\", 1, \"button\"], [1, \"abstract-shapes\"], [1, \"shape\", \"circle1\"], [1, \"shape\", \"circle2\"], [1, \"shape\", \"circle3\"], [1, \"footer\"]],\n      template: function NotAuthorizedComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"403 - Non autoris\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"h1\");\n          i0.ɵɵtext(8, \"403 - Non autoris\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\");\n          i0.ɵɵtext(10, \"Vous n'avez pas la permission d'acc\\u00E9der \\u00E0 cette page.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 3);\n          i0.ɵɵtext(12, \"Retourner\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 4);\n          i0.ɵɵelement(14, \"div\", 5)(15, \"div\", 6)(16, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 8)(18, \"p\");\n          i0.ɵɵtext(19, \"\\u00A9 2025 GTI. All Rights Reserved.\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\", \"body[_ngcontent-%COMP%] {\\n      background: linear-gradient(135deg, #1e1e2f, #14141f);\\n      color: white;\\n      font-family: 'Arial', sans-serif;\\n      text-align: center;\\n      height: 100vh;\\n      margin: 0;\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      flex-direction: column;\\n    }\\n\\n    h1[_ngcontent-%COMP%] {\\n      font-size: 80px;\\n      margin-bottom: 20px;\\n      color: #00c6ff;\\n    }\\n\\n    p[_ngcontent-%COMP%] {\\n      font-size: 20px;\\n      margin-bottom: 40px;\\n      color: #b0b0b0;\\n    }\\n\\n\\n\\n    .button[_ngcontent-%COMP%] {\\n      background-color: #00c6ff;\\n      color: white;\\n      padding: 15px 30px;\\n      font-size: 18px;\\n      border: none;\\n      border-radius: 30px;\\n      cursor: pointer;\\n      transition: background-color 0.3s ease;\\n    }\\n\\n    .button[_ngcontent-%COMP%]:hover {\\n      background-color: #009bb6;\\n    }\\n\\n    .footer[_ngcontent-%COMP%] {\\n      position: absolute;\\n      bottom: 20px;\\n      font-size: 14px;\\n      color: #b0b0b0;\\n    }\\n\\n    .abstract-shapes[_ngcontent-%COMP%] {\\n      position: absolute;\\n      top: 50%;\\n      left: 50%;\\n      transform: translate(-50%, -50%);\\n      width: 100%;\\n      height: 100%;\\n      pointer-events: none;\\n      z-index: -1;\\n    }\\n\\n    .shape[_ngcontent-%COMP%] {\\n      position: absolute;\\n      background: rgba(255, 255, 255, 0.1);\\n      border-radius: 50%;\\n      animation: _ngcontent-%COMP%_move 8s infinite linear;\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_move {\\n      0% {\\n        transform: translate(-50%, -50%) scale(1);\\n      }\\n      50% {\\n        transform: translate(-50%, -50%) scale(1.2);\\n      }\\n      100% {\\n        transform: translate(-50%, -50%) scale(1);\\n      }\\n    }\\n\\n    .circle1[_ngcontent-%COMP%] {\\n      width: 200px;\\n      height: 200px;\\n      top: 20%;\\n      left: 30%;\\n    }\\n\\n    .circle2[_ngcontent-%COMP%] {\\n      width: 150px;\\n      height: 150px;\\n      top: 40%;\\n      left: 60%;\\n    }\\n\\n    .circle3[_ngcontent-%COMP%] {\\n      width: 100px;\\n      height: 100px;\\n      top: 60%;\\n      left: 20%;\\n    }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NotAuthorizedComponent", "selectors", "decls", "vars", "consts", "template", "NotAuthorizedComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\not-authorized\\not-authorized.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\not-authorized\\not-authorized.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-not-authorized',\n  templateUrl: './not-authorized.component.html',\n  styleUrls: ['./not-authorized.component.css']\n})\nexport class NotAuthorizedComponent {\n\n}\n", "<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n  <meta charset=\"UTF-8\">\r\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n  <title>403 - Non autorisé</title>\r\n  <style>\r\n    body {\r\n      background: linear-gradient(135deg, #1e1e2f, #14141f);\r\n      color: white;\r\n      font-family: 'Arial', sans-serif;\r\n      text-align: center;\r\n      height: 100vh;\r\n      margin: 0;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      flex-direction: column;\r\n    }\r\n\r\n    h1 {\r\n      font-size: 80px;\r\n      margin-bottom: 20px;\r\n      color: #00c6ff;\r\n    }\r\n\r\n    p {\r\n      font-size: 20px;\r\n      margin-bottom: 40px;\r\n      color: #b0b0b0;\r\n    }\r\n\r\n\r\n\r\n    .button {\r\n      background-color: #00c6ff;\r\n      color: white;\r\n      padding: 15px 30px;\r\n      font-size: 18px;\r\n      border: none;\r\n      border-radius: 30px;\r\n      cursor: pointer;\r\n      transition: background-color 0.3s ease;\r\n    }\r\n\r\n    .button:hover {\r\n      background-color: #009bb6;\r\n    }\r\n\r\n    .footer {\r\n      position: absolute;\r\n      bottom: 20px;\r\n      font-size: 14px;\r\n      color: #b0b0b0;\r\n    }\r\n\r\n    .abstract-shapes {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 50%;\r\n      transform: translate(-50%, -50%);\r\n      width: 100%;\r\n      height: 100%;\r\n      pointer-events: none;\r\n      z-index: -1;\r\n    }\r\n\r\n    .shape {\r\n      position: absolute;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      border-radius: 50%;\r\n      animation: move 8s infinite linear;\r\n    }\r\n\r\n    @keyframes move {\r\n      0% {\r\n        transform: translate(-50%, -50%) scale(1);\r\n      }\r\n      50% {\r\n        transform: translate(-50%, -50%) scale(1.2);\r\n      }\r\n      100% {\r\n        transform: translate(-50%, -50%) scale(1);\r\n      }\r\n    }\r\n\r\n    .circle1 {\r\n      width: 200px;\r\n      height: 200px;\r\n      top: 20%;\r\n      left: 30%;\r\n    }\r\n\r\n    .circle2 {\r\n      width: 150px;\r\n      height: 150px;\r\n      top: 40%;\r\n      left: 60%;\r\n    }\r\n\r\n    .circle3 {\r\n      width: 100px;\r\n      height: 100px;\r\n      top: 60%;\r\n      left: 20%;\r\n    }\r\n  </style>\r\n</head>\r\n<body>\r\n\r\n  <h1>403 - Non autorisé</h1>\r\n  <p>Vous n'avez pas la permission d'accéder à cette page.</p>\r\n  <button class=\"button\" onclick=\"window.location.href='/'\">Retourner</button>\r\n  \r\n  <div class=\"abstract-shapes\">\r\n    <div class=\"shape circle1\"></div>\r\n    <div class=\"shape circle2\"></div>\r\n    <div class=\"shape circle3\"></div>\r\n  </div>\r\n\r\n  <div class=\"footer\">\r\n    <p>&copy; 2025 GTI. All Rights Reserved.</p>\r\n  </div>\r\n  \r\n</body>\r\n</html>\r\n"], "mappings": ";AAOA,OAAM,MAAOA,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNnCE,EAAA,CAAAC,cAAA,cAAgB;UAEdD,EAAA,CAAAE,SAAA,cAAsB;UAEtBF,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAG,MAAA,8BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAuGnCJ,EAAA,CAAAC,cAAA,WAAM;UAEAD,EAAA,CAAAG,MAAA,8BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC3BJ,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAG,MAAA,uEAAqD;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAC5DJ,EAAA,CAAAC,cAAA,iBAA0D;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAE5EJ,EAAA,CAAAC,cAAA,cAA6B;UAC3BD,EAAA,CAAAE,SAAA,cAAiC;UAGnCF,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAC,cAAA,cAAoB;UACfD,EAAA,CAAAG,MAAA,6CAAqC;UAAAH,EAAA,CAAAI,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}