package com.Rayen.Action.Entity;


import jakarta.persistence.*;

import java.util.Date;

@Entity
@Table(name="Action")
public class Action {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idAction;
    private String nomSociete;
    private Double prix;
    private String proprietaire;
    private String UserCreation;
    private Date DateCreation;
    private Date DateModification;
    private String UserModicfication;

    public Long getIdAction() {
        return idAction;
    }

    public void setIdAction(Long idAction) {
        this.idAction = idAction;
    }

    public String getNomSociete() {
        return nomSociete;
    }

    public void setNomSociete(String nomSociete) {
        this.nomSociete = nomSociete;
    }

    public Double getPrix() {
        return prix;
    }

    public void setPrix(Double prix) {
        this.prix = prix;
    }

    public String getProprietaire() {
        return proprietaire;
    }

    public void setProprietaire(String proprietaire) {
        this.proprietaire = proprietaire;
    }

    public String getUserCreation() {
        return UserCreation;
    }

    public void setUserCreation(String userCreation) {
        UserCreation = userCreation;
    }

    public Date getDateCreation() {
        return DateCreation;
    }

    public void setDateCreation(Date dateCreation) {
        DateCreation = dateCreation;
    }

    public Date getDateModification() {
        return DateModification;
    }

    public void setDateModification(Date dateModification) {
        DateModification = dateModification;
    }

    public String getUserModicfication() {
        return UserModicfication;
    }

    public void setUserModicfication(String userModicfication) {
        UserModicfication = userModicfication;
    }
}
