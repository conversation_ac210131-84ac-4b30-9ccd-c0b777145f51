{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./authentication.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(next, state) {\n    const token = this.authService.getToken();\n    console.log(\"Token retrieved from storage:\", token);\n    if (!token || this.isTokenExpired(token)) {\n      console.warn(\"No token found or token expired. Logging out...\");\n      this.authService.logout();\n      return false;\n    }\n    const {\n      role,\n      groupe\n    } = this.authService.getUserDetailsFromToken(token);\n    console.log(\"User role from token:\", role);\n    console.log(\"Group from token:\", groupe);\n    // Check if groupe is an object and has the expected properties\n    if (!groupe || !groupe.ressources || groupe.ressources.length === 0) {\n      console.error(\"Invalid groupe or ressources:\", groupe);\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n    // Extract allowed paths from groupe.ressources\n    const allowedPaths = groupe.ressources.map(r => this.getPath(r.link_path));\n    console.log(\"Allowed paths from groupe:\", allowedPaths);\n    const currentPath = state.url.split('?')[0]; // Trim any query params\n    console.log(\"Current path (trimmed):\", currentPath);\n    // Check if the current path is allowed for the user's group\n    if (allowedPaths.includes(currentPath)) {\n      console.log(\"Access granted to path:\", currentPath);\n      return true;\n    }\n    // Additional role-based access control for Admin\n    if (role.trim().toUpperCase() === 'ADMIN') {\n      console.log(\"Admin user detected, access granted.\");\n      return true;\n    }\n    console.warn(\"Access denied: Path not allowed for the current group/role.\");\n    this.router.navigate(['/not-authorized']);\n    return false;\n  }\n  // Helper method to check token expiration\n  isTokenExpired(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const expiryTime = payload.exp * 1000;\n      return Date.now() > expiryTime;\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return true;\n    }\n  }\n  // Helper method to extract the path from the URL (remove base URL if present)\n  getPath(url) {\n    const baseUrl = 'http://localhost:4200'; // Base URL to remove\n    if (url.startsWith(baseUrl)) {\n      return url.substring(baseUrl.length); // Return the relative path\n    }\n\n    return url; // Return as is if it doesn't start with the base URL\n  }\n\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthenticationService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "next", "state", "token", "getToken", "console", "log", "isTokenExpired", "warn", "logout", "role", "groupe", "getUserDetailsFromToken", "ressources", "length", "error", "navigate", "allowedPaths", "map", "r", "<PERSON><PERSON><PERSON>", "link_path", "currentPath", "url", "split", "includes", "trim", "toUpperCase", "payload", "JSON", "parse", "atob", "expiryTime", "exp", "Date", "now", "e", "baseUrl", "startsWith", "substring", "i0", "ɵɵinject", "i1", "AuthenticationService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Final\\frontend\\src\\app\\auth\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { AuthenticationService } from './authentication.service';\nimport { Ressource } from '../model/ressource.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {\n    const token = this.authService.getToken();\n    console.log(\"Token retrieved from storage:\", token);\n  \n    if (!token || this.isTokenExpired(token)) {\n      console.warn(\"No token found or token expired. Logging out...\");\n      this.authService.logout();\n      return false;\n    }\n  \n    const { role, groupe } = this.authService.getUserDetailsFromToken(token);\n    console.log(\"User role from token:\", role);\n    console.log(\"Group from token:\", groupe);\n  \n    // Check if groupe is an object and has the expected properties\n    if (!groupe || !groupe.ressources || groupe.ressources.length === 0) {\n      console.error(\"Invalid groupe or ressources:\", groupe);\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n  \n    // Extract allowed paths from groupe.ressources\n    const allowedPaths = groupe.ressources.map((r: Ressource) => this.getPath(r.link_path));\n    console.log(\"Allowed paths from groupe:\", allowedPaths);\n  \n    const currentPath = state.url.split('?')[0];  // Trim any query params\n    console.log(\"Current path (trimmed):\", currentPath);\n  \n    // Check if the current path is allowed for the user's group\n    if (allowedPaths.includes(currentPath)) {\n      console.log(\"Access granted to path:\", currentPath);\n      return true;\n    }\n  \n    // Additional role-based access control for Admin\n    if (role.trim().toUpperCase() === 'ADMIN') {\n      console.log(\"Admin user detected, access granted.\");\n      return true;\n    }\n  \n    console.warn(\"Access denied: Path not allowed for the current group/role.\");\n    this.router.navigate(['/not-authorized']);\n    return false;\n  }\n\n  // Helper method to check token expiration\n  private isTokenExpired(token: string): boolean {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const expiryTime = payload.exp * 1000;\n      return Date.now() > expiryTime;\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return true;\n    }\n  }\n\n  // Helper method to extract the path from the URL (remove base URL if present)\n  private getPath(url: string): string {\n    const baseUrl = 'http://localhost:4200';  // Base URL to remove\n    if (url.startsWith(baseUrl)) {\n      return url.substring(baseUrl.length);  // Return the relative path\n    }\n    return url;  // Return as is if it doesn't start with the base URL\n  }\n}\n"], "mappings": ";;;AAQA,OAAM,MAAOA,SAAS;EAEpBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;EAAW;EAEjFC,WAAWA,CAACC,IAA4B,EAAEC,KAA0B;IAClE,MAAMC,KAAK,GAAG,IAAI,CAACL,WAAW,CAACM,QAAQ,EAAE;IACzCC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,KAAK,CAAC;IAEnD,IAAI,CAACA,KAAK,IAAI,IAAI,CAACI,cAAc,CAACJ,KAAK,CAAC,EAAE;MACxCE,OAAO,CAACG,IAAI,CAAC,iDAAiD,CAAC;MAC/D,IAAI,CAACV,WAAW,CAACW,MAAM,EAAE;MACzB,OAAO,KAAK;;IAGd,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAE,GAAG,IAAI,CAACb,WAAW,CAACc,uBAAuB,CAACT,KAAK,CAAC;IACxEE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,IAAI,CAAC;IAC1CL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEK,MAAM,CAAC;IAExC;IACA,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACE,UAAU,IAAIF,MAAM,CAACE,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MACnET,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAEJ,MAAM,CAAC;MACtD,IAAI,CAACZ,MAAM,CAACiB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;MACzC,OAAO,KAAK;;IAGd;IACA,MAAMC,YAAY,GAAGN,MAAM,CAACE,UAAU,CAACK,GAAG,CAAEC,CAAY,IAAK,IAAI,CAACC,OAAO,CAACD,CAAC,CAACE,SAAS,CAAC,CAAC;IACvFhB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,YAAY,CAAC;IAEvD,MAAMK,WAAW,GAAGpB,KAAK,CAACqB,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE;IAC9CnB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgB,WAAW,CAAC;IAEnD;IACA,IAAIL,YAAY,CAACQ,QAAQ,CAACH,WAAW,CAAC,EAAE;MACtCjB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgB,WAAW,CAAC;MACnD,OAAO,IAAI;;IAGb;IACA,IAAIZ,IAAI,CAACgB,IAAI,EAAE,CAACC,WAAW,EAAE,KAAK,OAAO,EAAE;MACzCtB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,OAAO,IAAI;;IAGbD,OAAO,CAACG,IAAI,CAAC,6DAA6D,CAAC;IAC3E,IAAI,CAACT,MAAM,CAACiB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;IACzC,OAAO,KAAK;EACd;EAEA;EACQT,cAAcA,CAACJ,KAAa;IAClC,IAAI;MACF,MAAMyB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC5B,KAAK,CAACqB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMQ,UAAU,GAAGJ,OAAO,CAACK,GAAG,GAAG,IAAI;MACrC,OAAOC,IAAI,CAACC,GAAG,EAAE,GAAGH,UAAU;KAC/B,CAAC,OAAOI,CAAC,EAAE;MACV/B,OAAO,CAACU,KAAK,CAAC,uBAAuB,EAAEqB,CAAC,CAAC;MACzC,OAAO,IAAI;;EAEf;EAEA;EACQhB,OAAOA,CAACG,GAAW;IACzB,MAAMc,OAAO,GAAG,uBAAuB,CAAC,CAAE;IAC1C,IAAId,GAAG,CAACe,UAAU,CAACD,OAAO,CAAC,EAAE;MAC3B,OAAOd,GAAG,CAACgB,SAAS,CAACF,OAAO,CAACvB,MAAM,CAAC,CAAC,CAAE;;;IAEzC,OAAOS,GAAG,CAAC,CAAE;EACf;;;;uBApEW3B,SAAS,EAAA4C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATjD,SAAS;MAAAkD,OAAA,EAATlD,SAAS,CAAAmD,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}