package com.Aziz.Administratif.Security.auth;

import com.Aziz.Administratif.Services.*;
import com.Aziz.Administratif.Entity.Groupe;
import com.Aziz.Administratif.Entity.Ressource;
import com.Aziz.Administratif.Security.Config.JwtService;
import com.Aziz.Administratif.Entity.User;
import com.Aziz.Administratif.Repositories.UserRepository;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AuthenticationService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final AuthenticationManager authenticationManager;
    private final com.Aziz.Administratif.Services.GroupeService GroupeService;
    private final com.Aziz.Administratif.Repositories.GroupeRepository GroupeRepository;
    private final EmailService emailService;




    public AuthenticationResponse register(RegisterRequest request) {
        final SecureRandom random = new SecureRandom();
        String rawPassword1 = PasswordGenerator.generateRandomPassword(8); // you can choose any length here
        String encodedPassword = passwordEncoder.encode(rawPassword1);

        // Create JWT claims
        Map<String, Object> extraClaims = new HashMap<>();
        extraClaims.put("role", request.getRole().name());
        String mail = request.getEmail();

        User existingUser = userRepository.findByMatricule(request.getMatricule());
        Optional<User> existingUserwithEmail = userRepository.findByEmail(mail);

        if ((existingUser != null) || (existingUserwithEmail.isPresent())) {
            throw new RuntimeException("User with matricule " + existingUser.getMatricule() + " already exists. Please enter a new matricule.");
        } else {
            // Fetch the Groupe
            Groupe groupe = null;
            if (request.getGroupeId() != null) {
                groupe = GroupeRepository.findById(request.getGroupeId())
                        .orElseThrow(() -> new RuntimeException("Groupe not found"));
            }
            // Build and save the user
            User tempUser = User.builder()
                    .nom(request.getFirstName())
                    .prenom(request.getLastName())
                    .email(request.getEmail())
                    .password(encodedPassword)
                    .telephone(request.getTelephone())
                    .role(request.getRole())
                    .groupe(groupe)
                    .State(Boolean.TRUE)
                    .matricule(request.getMatricule())
//                   .habilitation(habilitation) // Assign the fetched Habilitation to the user
                    .canCreate(Boolean.TRUE.equals(request.getCanCreate()))
                    .canRead(Boolean.TRUE.equals(request.getCanRead()))
                    .canUpdate(Boolean.TRUE.equals(request.getCanUpdate()))
                    .canDelete(Boolean.TRUE.equals(request.getCanDelete()))
                    .Flag(Boolean.FALSE.equals(request.getFlag()))
                    .build();

            userRepository.save(tempUser);

            // Send registration email with the password
            String mailPassword = rawPassword1;
            String mat = String.valueOf(request.getMatricule());
            emailService.sendRegistrationEmail(tempUser.getEmail(), tempUser.getNom(), mailPassword, mat);

            // Generate JWT token and return
            String jwtToken = jwtService.generateToken(extraClaims, tempUser);
            return AuthenticationResponse.builder()
                    .token(jwtToken)
                    .build();
        }
    }


    public AuthenticationResponse authenticate(AuthenticationRequest request, HttpServletRequest request1) {
        try {
            // Get IP and date time for logging or auditing
            String ipAddress = request1.getRemoteAddr();
            String location = "N/A";
            String dateTime = java.time.LocalDateTime.now().toString();

            // Authenticate the user
            authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            String.valueOf(request.getMatricule()),
                            request.getPassword()
                    )
            );

            User user = userRepository.findByMatricule(request.getMatricule());
            UsernamePasswordAuthenticationToken authToken =
                    new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());

            SecurityContextHolder.getContext().setAuthentication(authToken);


        } catch (Exception e) {
            return AuthenticationResponse.builder()
                    .token("Authentication failed")
                    .build();
        }

        // Find the user from the database using matricule
        User user = userRepository.findByMatricule(request.getMatricule());
        if (user == null) {
            throw new RuntimeException("Utilisateur non trouvé !");
        }

        // Generate JWT token for the authenticated user
        String jwtToken = jwtService.generateToken(new HashMap<>(), user);

        // Get the group of the user and associated resources
        Groupe groupe = user.getGroupe();
        List<Ressource> ressources = groupe.getRessources();  // Assuming a method to get resources linked to the group

        // Map the resources to their link paths
        List<String> allowedPaths = ressources.stream()
                .map(Ressource::getLink_path)  // Assuming Ressource has a method to get its link path
                .collect(Collectors.toList());

        // Return the response with the JWT token, allowed paths, and group information
        return AuthenticationResponse.builder()
                .token(jwtToken)
                .allowedPaths(allowedPaths)  // Send allowedPaths to frontend
                .groupe(groupe.getNomGroupe())  // Send group name or identifier
                .state(user.getState())  // <- Stage True of flase
                .build();
    }


    public AuthenticationResponse GroupeAjout(Groupe Groupe) {

        Map<String, Object> extraClaims = new HashMap<>();
        extraClaims.put("groupe", Groupe.getNomGroupe());

        // Creating user instance
        Groupe tempGroupe = Groupe.builder()
                .NomGroupe(Groupe.getNomGroupe())
                .build();
        GroupeService.saveGroupe(tempGroupe);

        // Generating JWT token
        String jwtToken = null;
        if (jwtToken == null || jwtToken.isEmpty()) {
            return AuthenticationResponse.builder().token("Token generation failed").build();
        }
        return AuthenticationResponse.builder().token(jwtToken).build();
    }







    public Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof User) {
            User userDetails = (User) authentication.getPrincipal();
            return userDetails.getId();
        }
        return null;
    }
}