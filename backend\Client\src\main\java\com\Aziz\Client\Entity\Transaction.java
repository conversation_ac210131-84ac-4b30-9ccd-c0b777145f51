package com.Aziz.Client.Entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name="Transaction")
public class Transaction {


    @Id
    private Long idTransaction;
    private String type;


    private Integer qantite;
    private Double montatnt;
    private String observations;


    @CreationTimestamp
    private LocalDateTime DateCreation;


    private LocalDateTime DateTransaction;

    @ManyToOne
//    @JsonBackReference
    @JoinColumn(name = "idPortefeuille")
    private Portefeuille portefeuille;



    private Long userId;




}
