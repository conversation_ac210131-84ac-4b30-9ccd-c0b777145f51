{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/habilitation.service\";\nexport class HabilitationComponent {\n  constructor(habilitationService) {\n    this.habilitationService = habilitationService;\n    this.users = [];\n    this.groups = [];\n    this.resources = [];\n    this.selectedUserId = null;\n    this.selectedGroupId = null;\n    this.selectedResourceId = null;\n    this.responsables = [];\n  }\n  ngOnInit() {\n    this.loadUsers();\n    this.loadGroups();\n    this.loadResources();\n  }\n  loadUsers() {\n    this.habilitationService.getAllUsers().subscribe(data => {\n      this.users = data;\n    });\n  }\n  loadGroups() {\n    this.habilitationService.getAllGroups().subscribe(data => {\n      this.groups = data;\n    });\n  }\n  loadResources() {\n    this.habilitationService.getAllResources().subscribe(data => {\n      this.resources = data;\n    });\n  }\n  assignHabilitation() {\n    if (this.selectedUserId && this.selectedGroupId && this.selectedResourceId) {\n      this.habilitationService.assignHabilitation(this.selectedUserId, this.selectedGroupId, this.selectedResourceId).subscribe(() => {\n        alert('Habilitation assigned successfully!');\n        this.loadUsers(); // reload updated users\n      }, error => {\n        console.error('Error assigning habilitation:', error);\n        alert('Error assigning habilitation.');\n      });\n    } else {\n      alert('Please select a user, group, and resource.');\n    }\n  }\n  static {\n    this.ɵfac = function HabilitationComponent_Factory(t) {\n      return new (t || HabilitationComponent)(i0.ɵɵdirectiveInject(i1.HabilitationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HabilitationComponent,\n      selectors: [[\"app-habilitation\"]],\n      decls: 0,\n      vars: 0,\n      template: function HabilitationComponent_Template(rf, ctx) {},\n      styles: [\".habilitation-container[_ngcontent-%COMP%] {\\n    max-width: 600px;\\n    margin: 20px auto;\\n    padding: 20px;\\n    background-color: #f9f9f9;\\n    border-radius: 8px;\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n  }\\n  \\n  h2[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 20px;\\n    color: #333;\\n    font-size: 1.5rem;\\n  }\\n  \\n  .form-group[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n  \\n  label[_ngcontent-%COMP%] {\\n    display: block;\\n    margin-bottom: 5px;\\n    font-weight: bold;\\n    color: #555;\\n  }\\n  \\n  select[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 10px;\\n    border: 1px solid #ddd;\\n    border-radius: 4px;\\n    font-size: 1rem;\\n    background-color: #fff;\\n    transition: border 0.3s ease;\\n  }\\n  \\n  select[_ngcontent-%COMP%]:focus {\\n    border-color: #007bff;\\n    outline: none;\\n  }\\n  \\n  button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 12px;\\n    background-color: #007bff;\\n    color: white;\\n    font-size: 1.1rem;\\n    border: none;\\n    border-radius: 4px;\\n    cursor: pointer;\\n    transition: background-color 0.3s ease;\\n  }\\n  \\n  button[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n  }\\n  \\n  button[_ngcontent-%COMP%]:focus {\\n    outline: none;\\n  }\\n  \\n  @media (max-width: 600px) {\\n    .habilitation-container[_ngcontent-%COMP%] {\\n      padding: 15px;\\n    }\\n  \\n    h2[_ngcontent-%COMP%] {\\n      font-size: 1.25rem;\\n    }\\n  \\n    select[_ngcontent-%COMP%] {\\n      font-size: 0.9rem;\\n    }\\n  \\n    button[_ngcontent-%COMP%] {\\n      font-size: 1rem;\\n    }\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["HabilitationComponent", "constructor", "habilitationService", "users", "groups", "resources", "selectedUserId", "selectedGroupId", "selectedResourceId", "responsables", "ngOnInit", "loadUsers", "loadGroups", "loadResources", "getAllUsers", "subscribe", "data", "getAllGroups", "getAllResources", "assignHabilitation", "alert", "error", "console", "i0", "ɵɵdirectiveInject", "i1", "HabilitationService", "selectors", "decls", "vars", "template", "HabilitationComponent_Template", "rf", "ctx", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\habilitation\\habilitation.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { HabilitationService } from '../services/habilitation.service';  // Import the service\nimport { User } from '../model/user.model'; // Import User model\nimport { Groupe } from '../model/groupe.model'; // Import Group model\nimport { Ressource } from '../model/ressource.model'; // Import Ressource model\nimport { ResponsableService } from '../responsable.service';\n\n@Component({\n  selector: 'app-habilitation',\n  templateUrl: './habilitation.component.html',\n  styleUrls: ['./habilitation.component.css'],\n})\nexport class HabilitationComponent implements OnInit {\n  users: User[] = [];\n  groups: Groupe[] = [];\n  resources: Ressource[] = [];\n  selectedUserId: number | null = null;\n  selectedGroupId: number | null = null;\n  selectedResourceId: number | null = null;\n  responsables: User[] = []; \n\n  constructor(private habilitationService: HabilitationService) {}\n\n  ngOnInit(): void {\n    this.loadUsers();\n    this.loadGroups();\n    this.loadResources();\n  }\n\n  loadUsers() {\n    this.habilitationService.getAllUsers().subscribe((data) => {\n      this.users = data;\n    });\n  }\n\n  loadGroups() {\n    this.habilitationService.getAllGroups().subscribe((data) => {\n      this.groups = data;\n    });\n  }\n\n  loadResources() {\n    this.habilitationService.getAllResources().subscribe((data) => {\n      this.resources = data;\n    });\n  }\n\n  assignHabilitation() {\n    if (this.selectedUserId && this.selectedGroupId && this.selectedResourceId) {\n      this.habilitationService.assignHabilitation(\n        this.selectedUserId,\n        this.selectedGroupId,\n        this.selectedResourceId\n      ).subscribe(\n        () => {\n          alert('Habilitation assigned successfully!');\n          this.loadUsers(); // reload updated users\n        },\n        (error) => {\n          console.error('Error assigning habilitation:', error);\n          alert('Error assigning habilitation.');\n        }\n      );\n    } else {\n      alert('Please select a user, group, and resource.');\n    }\n  }\n}\n"], "mappings": ";;AAYA,OAAM,MAAOA,qBAAqB;EAShCC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;IARvC,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,MAAM,GAAa,EAAE;IACrB,KAAAC,SAAS,GAAgB,EAAE;IAC3B,KAAAC,cAAc,GAAkB,IAAI;IACpC,KAAAC,eAAe,GAAkB,IAAI;IACrC,KAAAC,kBAAkB,GAAkB,IAAI;IACxC,KAAAC,YAAY,GAAW,EAAE;EAEsC;EAE/DC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAF,SAASA,CAAA;IACP,IAAI,CAACT,mBAAmB,CAACY,WAAW,EAAE,CAACC,SAAS,CAAEC,IAAI,IAAI;MACxD,IAAI,CAACb,KAAK,GAAGa,IAAI;IACnB,CAAC,CAAC;EACJ;EAEAJ,UAAUA,CAAA;IACR,IAAI,CAACV,mBAAmB,CAACe,YAAY,EAAE,CAACF,SAAS,CAAEC,IAAI,IAAI;MACzD,IAAI,CAACZ,MAAM,GAAGY,IAAI;IACpB,CAAC,CAAC;EACJ;EAEAH,aAAaA,CAAA;IACX,IAAI,CAACX,mBAAmB,CAACgB,eAAe,EAAE,CAACH,SAAS,CAAEC,IAAI,IAAI;MAC5D,IAAI,CAACX,SAAS,GAAGW,IAAI;IACvB,CAAC,CAAC;EACJ;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACb,cAAc,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACC,kBAAkB,EAAE;MAC1E,IAAI,CAACN,mBAAmB,CAACiB,kBAAkB,CACzC,IAAI,CAACb,cAAc,EACnB,IAAI,CAACC,eAAe,EACpB,IAAI,CAACC,kBAAkB,CACxB,CAACO,SAAS,CACT,MAAK;QACHK,KAAK,CAAC,qCAAqC,CAAC;QAC5C,IAAI,CAACT,SAAS,EAAE,CAAC,CAAC;MACpB,CAAC,EACAU,KAAK,IAAI;QACRC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDD,KAAK,CAAC,+BAA+B,CAAC;MACxC,CAAC,CACF;KACF,MAAM;MACLA,KAAK,CAAC,4CAA4C,CAAC;;EAEvD;;;uBAtDWpB,qBAAqB,EAAAuB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAArB1B,qBAAqB;MAAA2B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}