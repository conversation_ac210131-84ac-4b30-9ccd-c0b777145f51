{"ast": null, "code": "/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\nexports.parse = parse;\nexports.serialize = serialize;\n\n/**\n * Module variables.\n * @private\n */\n\nvar __toString = Object.prototype.toString;\n\n/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n */\n\nvar cookieNameRegExp = /^[!#$%&'*+\\-.^_`|~0-9A-Za-z]+$/;\n\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n */\n\nvar cookieValueRegExp = /^(\"?)[\\u0021\\u0023-\\u002B\\u002D-\\u003A\\u003C-\\u005B\\u005D-\\u007E]*\\1$/;\n\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\n\nvar domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\n\nvar pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n *\n * @param {string} str\n * @param {object} [opt]\n * @return {object}\n * @public\n */\n\nfunction parse(str, opt) {\n  if (typeof str !== 'string') {\n    throw new TypeError('argument str must be a string');\n  }\n  var obj = {};\n  var len = str.length;\n  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n  if (len < 2) return obj;\n  var dec = opt && opt.decode || decode;\n  var index = 0;\n  var eqIdx = 0;\n  var endIdx = 0;\n  do {\n    eqIdx = str.indexOf('=', index);\n    if (eqIdx === -1) break; // No more cookie pairs.\n\n    endIdx = str.indexOf(';', index);\n    if (endIdx === -1) {\n      endIdx = len;\n    } else if (eqIdx > endIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(';', eqIdx - 1) + 1;\n      continue;\n    }\n    var keyStartIdx = startIndex(str, index, eqIdx);\n    var keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n    var key = str.slice(keyStartIdx, keyEndIdx);\n\n    // only assign once\n    if (!obj.hasOwnProperty(key)) {\n      var valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n      var valEndIdx = endIndex(str, endIdx, valStartIdx);\n      if (str.charCodeAt(valStartIdx) === 0x22 /* \" */ && str.charCodeAt(valEndIdx - 1) === 0x22 /* \" */) {\n        valStartIdx++;\n        valEndIdx--;\n      }\n      var val = str.slice(valStartIdx, valEndIdx);\n      obj[key] = tryDecode(val, dec);\n    }\n    index = endIdx + 1;\n  } while (index < len);\n  return obj;\n}\nfunction startIndex(str, index, max) {\n  do {\n    var code = str.charCodeAt(index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n  } while (++index < max);\n  return max;\n}\nfunction endIndex(str, index, min) {\n  while (index > min) {\n    var code = str.charCodeAt(--index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n  }\n  return min;\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n *\n * @param {string} name\n * @param {string} val\n * @param {object} [opt]\n * @return {string}\n * @public\n */\n\nfunction serialize(name, val, opt) {\n  var enc = opt && opt.encode || encodeURIComponent;\n  if (typeof enc !== 'function') {\n    throw new TypeError('option encode is invalid');\n  }\n  if (!cookieNameRegExp.test(name)) {\n    throw new TypeError('argument name is invalid');\n  }\n  var value = enc(val);\n  if (!cookieValueRegExp.test(value)) {\n    throw new TypeError('argument val is invalid');\n  }\n  var str = name + '=' + value;\n  if (!opt) return str;\n  if (null != opt.maxAge) {\n    var maxAge = Math.floor(opt.maxAge);\n    if (!isFinite(maxAge)) {\n      throw new TypeError('option maxAge is invalid');\n    }\n    str += '; Max-Age=' + maxAge;\n  }\n  if (opt.domain) {\n    if (!domainValueRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n    str += '; Domain=' + opt.domain;\n  }\n  if (opt.path) {\n    if (!pathValueRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n    str += '; Path=' + opt.path;\n  }\n  if (opt.expires) {\n    var expires = opt.expires;\n    if (!isDate(expires) || isNaN(expires.valueOf())) {\n      throw new TypeError('option expires is invalid');\n    }\n    str += '; Expires=' + expires.toUTCString();\n  }\n  if (opt.httpOnly) {\n    str += '; HttpOnly';\n  }\n  if (opt.secure) {\n    str += '; Secure';\n  }\n  if (opt.partitioned) {\n    str += '; Partitioned';\n  }\n  if (opt.priority) {\n    var priority = typeof opt.priority === 'string' ? opt.priority.toLowerCase() : opt.priority;\n    switch (priority) {\n      case 'low':\n        str += '; Priority=Low';\n        break;\n      case 'medium':\n        str += '; Priority=Medium';\n        break;\n      case 'high':\n        str += '; Priority=High';\n        break;\n      default:\n        throw new TypeError('option priority is invalid');\n    }\n  }\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string' ? opt.sameSite.toLowerCase() : opt.sameSite;\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n  return str;\n}\n\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n *\n * @param {string} str\n * @returns {string}\n */\n\nfunction decode(str) {\n  return str.indexOf('%') !== -1 ? decodeURIComponent(str) : str;\n}\n\n/**\n * Determine if value is a Date.\n *\n * @param {*} val\n * @private\n */\n\nfunction isDate(val) {\n  return __toString.call(val) === '[object Date]';\n}\n\n/**\n * Try decoding a string using a decoding function.\n *\n * @param {string} str\n * @param {function} decode\n * @private\n */\n\nfunction tryDecode(str, decode) {\n  try {\n    return decode(str);\n  } catch (e) {\n    return str;\n  }\n}", "map": {"version": 3, "names": ["exports", "parse", "serialize", "__toString", "Object", "prototype", "toString", "cookieNameRegExp", "cookieValueRegExp", "domainValueRegExp", "pathValueRegExp", "str", "opt", "TypeError", "obj", "len", "length", "dec", "decode", "index", "eqIdx", "endIdx", "indexOf", "lastIndexOf", "keyStartIdx", "startIndex", "keyEndIdx", "endIndex", "key", "slice", "hasOwnProperty", "valStartIdx", "valEndIdx", "charCodeAt", "val", "tryDecode", "max", "code", "min", "name", "enc", "encode", "encodeURIComponent", "test", "value", "maxAge", "Math", "floor", "isFinite", "domain", "path", "expires", "isDate", "isNaN", "valueOf", "toUTCString", "httpOnly", "secure", "partitioned", "priority", "toLowerCase", "sameSite", "decodeURIComponent", "call", "e"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/express/node_modules/cookie/index.js"], "sourcesContent": ["/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nexports.parse = parse;\nexports.serialize = serialize;\n\n/**\n * Module variables.\n * @private\n */\n\nvar __toString = Object.prototype.toString\n\n/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n */\n\nvar cookieNameRegExp = /^[!#$%&'*+\\-.^_`|~0-9A-Za-z]+$/;\n\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n */\n\nvar cookieValueRegExp = /^(\"?)[\\u0021\\u0023-\\u002B\\u002D-\\u003A\\u003C-\\u005B\\u005D-\\u007E]*\\1$/;\n\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\n\nvar domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\n\nvar pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n *\n * @param {string} str\n * @param {object} [opt]\n * @return {object}\n * @public\n */\n\nfunction parse(str, opt) {\n  if (typeof str !== 'string') {\n    throw new TypeError('argument str must be a string');\n  }\n\n  var obj = {};\n  var len = str.length;\n  // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n  if (len < 2) return obj;\n\n  var dec = (opt && opt.decode) || decode;\n  var index = 0;\n  var eqIdx = 0;\n  var endIdx = 0;\n\n  do {\n    eqIdx = str.indexOf('=', index);\n    if (eqIdx === -1) break; // No more cookie pairs.\n\n    endIdx = str.indexOf(';', index);\n\n    if (endIdx === -1) {\n      endIdx = len;\n    } else if (eqIdx > endIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(';', eqIdx - 1) + 1;\n      continue;\n    }\n\n    var keyStartIdx = startIndex(str, index, eqIdx);\n    var keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n    var key = str.slice(keyStartIdx, keyEndIdx);\n\n    // only assign once\n    if (!obj.hasOwnProperty(key)) {\n      var valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n      var valEndIdx = endIndex(str, endIdx, valStartIdx);\n\n      if (str.charCodeAt(valStartIdx) === 0x22 /* \" */ && str.charCodeAt(valEndIdx - 1) === 0x22 /* \" */) {\n        valStartIdx++;\n        valEndIdx--;\n      }\n\n      var val = str.slice(valStartIdx, valEndIdx);\n      obj[key] = tryDecode(val, dec);\n    }\n\n    index = endIdx + 1\n  } while (index < len);\n\n  return obj;\n}\n\nfunction startIndex(str, index, max) {\n  do {\n    var code = str.charCodeAt(index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index;\n  } while (++index < max);\n  return max;\n}\n\nfunction endIndex(str, index, min) {\n  while (index > min) {\n    var code = str.charCodeAt(--index);\n    if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */) return index + 1;\n  }\n  return min;\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n *\n * @param {string} name\n * @param {string} val\n * @param {object} [opt]\n * @return {string}\n * @public\n */\n\nfunction serialize(name, val, opt) {\n  var enc = (opt && opt.encode) || encodeURIComponent;\n\n  if (typeof enc !== 'function') {\n    throw new TypeError('option encode is invalid');\n  }\n\n  if (!cookieNameRegExp.test(name)) {\n    throw new TypeError('argument name is invalid');\n  }\n\n  var value = enc(val);\n\n  if (!cookieValueRegExp.test(value)) {\n    throw new TypeError('argument val is invalid');\n  }\n\n  var str = name + '=' + value;\n  if (!opt) return str;\n\n  if (null != opt.maxAge) {\n    var maxAge = Math.floor(opt.maxAge);\n\n    if (!isFinite(maxAge)) {\n      throw new TypeError('option maxAge is invalid')\n    }\n\n    str += '; Max-Age=' + maxAge;\n  }\n\n  if (opt.domain) {\n    if (!domainValueRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n\n    str += '; Domain=' + opt.domain;\n  }\n\n  if (opt.path) {\n    if (!pathValueRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n\n    str += '; Path=' + opt.path;\n  }\n\n  if (opt.expires) {\n    var expires = opt.expires\n\n    if (!isDate(expires) || isNaN(expires.valueOf())) {\n      throw new TypeError('option expires is invalid');\n    }\n\n    str += '; Expires=' + expires.toUTCString()\n  }\n\n  if (opt.httpOnly) {\n    str += '; HttpOnly';\n  }\n\n  if (opt.secure) {\n    str += '; Secure';\n  }\n\n  if (opt.partitioned) {\n    str += '; Partitioned'\n  }\n\n  if (opt.priority) {\n    var priority = typeof opt.priority === 'string'\n      ? opt.priority.toLowerCase() : opt.priority;\n\n    switch (priority) {\n      case 'low':\n        str += '; Priority=Low'\n        break\n      case 'medium':\n        str += '; Priority=Medium'\n        break\n      case 'high':\n        str += '; Priority=High'\n        break\n      default:\n        throw new TypeError('option priority is invalid')\n    }\n  }\n\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string'\n      ? opt.sameSite.toLowerCase() : opt.sameSite;\n\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      case 'none':\n        str += '; SameSite=None';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n\n  return str;\n}\n\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n *\n * @param {string} str\n * @returns {string}\n */\n\nfunction decode (str) {\n  return str.indexOf('%') !== -1\n    ? decodeURIComponent(str)\n    : str\n}\n\n/**\n * Determine if value is a Date.\n *\n * @param {*} val\n * @private\n */\n\nfunction isDate (val) {\n  return __toString.call(val) === '[object Date]';\n}\n\n/**\n * Try decoding a string using a decoding function.\n *\n * @param {string} str\n * @param {function} decode\n * @private\n */\n\nfunction tryDecode(str, decode) {\n  try {\n    return decode(str);\n  } catch (e) {\n    return str;\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AAEAA,OAAO,CAACC,KAAK,GAAGA,KAAK;AACrBD,OAAO,CAACE,SAAS,GAAGA,SAAS;;AAE7B;AACA;AACA;AACA;;AAEA,IAAIC,UAAU,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,gBAAgB,GAAG,gCAAgC;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,iBAAiB,GAAG,uEAAuE;;AAE/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,iBAAiB,GAAG,qFAAqF;;AAE7G;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,eAAe,GAAG,iCAAiC;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAST,KAAKA,CAACU,GAAG,EAAEC,GAAG,EAAE;EACvB,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAIE,SAAS,CAAC,+BAA+B,CAAC;EACtD;EAEA,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,GAAG,GAAGJ,GAAG,CAACK,MAAM;EACpB;EACA,IAAID,GAAG,GAAG,CAAC,EAAE,OAAOD,GAAG;EAEvB,IAAIG,GAAG,GAAIL,GAAG,IAAIA,GAAG,CAACM,MAAM,IAAKA,MAAM;EACvC,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,MAAM,GAAG,CAAC;EAEd,GAAG;IACDD,KAAK,GAAGT,GAAG,CAACW,OAAO,CAAC,GAAG,EAAEH,KAAK,CAAC;IAC/B,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC;;IAEzBC,MAAM,GAAGV,GAAG,CAACW,OAAO,CAAC,GAAG,EAAEH,KAAK,CAAC;IAEhC,IAAIE,MAAM,KAAK,CAAC,CAAC,EAAE;MACjBA,MAAM,GAAGN,GAAG;IACd,CAAC,MAAM,IAAIK,KAAK,GAAGC,MAAM,EAAE;MACzB;MACAF,KAAK,GAAGR,GAAG,CAACY,WAAW,CAAC,GAAG,EAAEH,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC3C;IACF;IAEA,IAAII,WAAW,GAAGC,UAAU,CAACd,GAAG,EAAEQ,KAAK,EAAEC,KAAK,CAAC;IAC/C,IAAIM,SAAS,GAAGC,QAAQ,CAAChB,GAAG,EAAES,KAAK,EAAEI,WAAW,CAAC;IACjD,IAAII,GAAG,GAAGjB,GAAG,CAACkB,KAAK,CAACL,WAAW,EAAEE,SAAS,CAAC;;IAE3C;IACA,IAAI,CAACZ,GAAG,CAACgB,cAAc,CAACF,GAAG,CAAC,EAAE;MAC5B,IAAIG,WAAW,GAAGN,UAAU,CAACd,GAAG,EAAES,KAAK,GAAG,CAAC,EAAEC,MAAM,CAAC;MACpD,IAAIW,SAAS,GAAGL,QAAQ,CAAChB,GAAG,EAAEU,MAAM,EAAEU,WAAW,CAAC;MAElD,IAAIpB,GAAG,CAACsB,UAAU,CAACF,WAAW,CAAC,KAAK,IAAI,CAAC,WAAWpB,GAAG,CAACsB,UAAU,CAACD,SAAS,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS;QAClGD,WAAW,EAAE;QACbC,SAAS,EAAE;MACb;MAEA,IAAIE,GAAG,GAAGvB,GAAG,CAACkB,KAAK,CAACE,WAAW,EAAEC,SAAS,CAAC;MAC3ClB,GAAG,CAACc,GAAG,CAAC,GAAGO,SAAS,CAACD,GAAG,EAAEjB,GAAG,CAAC;IAChC;IAEAE,KAAK,GAAGE,MAAM,GAAG,CAAC;EACpB,CAAC,QAAQF,KAAK,GAAGJ,GAAG;EAEpB,OAAOD,GAAG;AACZ;AAEA,SAASW,UAAUA,CAACd,GAAG,EAAEQ,KAAK,EAAEiB,GAAG,EAAE;EACnC,GAAG;IACD,IAAIC,IAAI,GAAG1B,GAAG,CAACsB,UAAU,CAACd,KAAK,CAAC;IAChC,IAAIkB,IAAI,KAAK,IAAI,CAAC,WAAWA,IAAI,KAAK,IAAI,CAAC,UAAU,OAAOlB,KAAK;EACnE,CAAC,QAAQ,EAAEA,KAAK,GAAGiB,GAAG;EACtB,OAAOA,GAAG;AACZ;AAEA,SAAST,QAAQA,CAAChB,GAAG,EAAEQ,KAAK,EAAEmB,GAAG,EAAE;EACjC,OAAOnB,KAAK,GAAGmB,GAAG,EAAE;IAClB,IAAID,IAAI,GAAG1B,GAAG,CAACsB,UAAU,CAAC,EAAEd,KAAK,CAAC;IAClC,IAAIkB,IAAI,KAAK,IAAI,CAAC,WAAWA,IAAI,KAAK,IAAI,CAAC,UAAU,OAAOlB,KAAK,GAAG,CAAC;EACvE;EACA,OAAOmB,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASpC,SAASA,CAACqC,IAAI,EAAEL,GAAG,EAAEtB,GAAG,EAAE;EACjC,IAAI4B,GAAG,GAAI5B,GAAG,IAAIA,GAAG,CAAC6B,MAAM,IAAKC,kBAAkB;EAEnD,IAAI,OAAOF,GAAG,KAAK,UAAU,EAAE;IAC7B,MAAM,IAAI3B,SAAS,CAAC,0BAA0B,CAAC;EACjD;EAEA,IAAI,CAACN,gBAAgB,CAACoC,IAAI,CAACJ,IAAI,CAAC,EAAE;IAChC,MAAM,IAAI1B,SAAS,CAAC,0BAA0B,CAAC;EACjD;EAEA,IAAI+B,KAAK,GAAGJ,GAAG,CAACN,GAAG,CAAC;EAEpB,IAAI,CAAC1B,iBAAiB,CAACmC,IAAI,CAACC,KAAK,CAAC,EAAE;IAClC,MAAM,IAAI/B,SAAS,CAAC,yBAAyB,CAAC;EAChD;EAEA,IAAIF,GAAG,GAAG4B,IAAI,GAAG,GAAG,GAAGK,KAAK;EAC5B,IAAI,CAAChC,GAAG,EAAE,OAAOD,GAAG;EAEpB,IAAI,IAAI,IAAIC,GAAG,CAACiC,MAAM,EAAE;IACtB,IAAIA,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACnC,GAAG,CAACiC,MAAM,CAAC;IAEnC,IAAI,CAACG,QAAQ,CAACH,MAAM,CAAC,EAAE;MACrB,MAAM,IAAIhC,SAAS,CAAC,0BAA0B,CAAC;IACjD;IAEAF,GAAG,IAAI,YAAY,GAAGkC,MAAM;EAC9B;EAEA,IAAIjC,GAAG,CAACqC,MAAM,EAAE;IACd,IAAI,CAACxC,iBAAiB,CAACkC,IAAI,CAAC/B,GAAG,CAACqC,MAAM,CAAC,EAAE;MACvC,MAAM,IAAIpC,SAAS,CAAC,0BAA0B,CAAC;IACjD;IAEAF,GAAG,IAAI,WAAW,GAAGC,GAAG,CAACqC,MAAM;EACjC;EAEA,IAAIrC,GAAG,CAACsC,IAAI,EAAE;IACZ,IAAI,CAACxC,eAAe,CAACiC,IAAI,CAAC/B,GAAG,CAACsC,IAAI,CAAC,EAAE;MACnC,MAAM,IAAIrC,SAAS,CAAC,wBAAwB,CAAC;IAC/C;IAEAF,GAAG,IAAI,SAAS,GAAGC,GAAG,CAACsC,IAAI;EAC7B;EAEA,IAAItC,GAAG,CAACuC,OAAO,EAAE;IACf,IAAIA,OAAO,GAAGvC,GAAG,CAACuC,OAAO;IAEzB,IAAI,CAACC,MAAM,CAACD,OAAO,CAAC,IAAIE,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MAChD,MAAM,IAAIzC,SAAS,CAAC,2BAA2B,CAAC;IAClD;IAEAF,GAAG,IAAI,YAAY,GAAGwC,OAAO,CAACI,WAAW,CAAC,CAAC;EAC7C;EAEA,IAAI3C,GAAG,CAAC4C,QAAQ,EAAE;IAChB7C,GAAG,IAAI,YAAY;EACrB;EAEA,IAAIC,GAAG,CAAC6C,MAAM,EAAE;IACd9C,GAAG,IAAI,UAAU;EACnB;EAEA,IAAIC,GAAG,CAAC8C,WAAW,EAAE;IACnB/C,GAAG,IAAI,eAAe;EACxB;EAEA,IAAIC,GAAG,CAAC+C,QAAQ,EAAE;IAChB,IAAIA,QAAQ,GAAG,OAAO/C,GAAG,CAAC+C,QAAQ,KAAK,QAAQ,GAC3C/C,GAAG,CAAC+C,QAAQ,CAACC,WAAW,CAAC,CAAC,GAAGhD,GAAG,CAAC+C,QAAQ;IAE7C,QAAQA,QAAQ;MACd,KAAK,KAAK;QACRhD,GAAG,IAAI,gBAAgB;QACvB;MACF,KAAK,QAAQ;QACXA,GAAG,IAAI,mBAAmB;QAC1B;MACF,KAAK,MAAM;QACTA,GAAG,IAAI,iBAAiB;QACxB;MACF;QACE,MAAM,IAAIE,SAAS,CAAC,4BAA4B,CAAC;IACrD;EACF;EAEA,IAAID,GAAG,CAACiD,QAAQ,EAAE;IAChB,IAAIA,QAAQ,GAAG,OAAOjD,GAAG,CAACiD,QAAQ,KAAK,QAAQ,GAC3CjD,GAAG,CAACiD,QAAQ,CAACD,WAAW,CAAC,CAAC,GAAGhD,GAAG,CAACiD,QAAQ;IAE7C,QAAQA,QAAQ;MACd,KAAK,IAAI;QACPlD,GAAG,IAAI,mBAAmB;QAC1B;MACF,KAAK,KAAK;QACRA,GAAG,IAAI,gBAAgB;QACvB;MACF,KAAK,QAAQ;QACXA,GAAG,IAAI,mBAAmB;QAC1B;MACF,KAAK,MAAM;QACTA,GAAG,IAAI,iBAAiB;QACxB;MACF;QACE,MAAM,IAAIE,SAAS,CAAC,4BAA4B,CAAC;IACrD;EACF;EAEA,OAAOF,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASO,MAAMA,CAAEP,GAAG,EAAE;EACpB,OAAOA,GAAG,CAACW,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAC1BwC,kBAAkB,CAACnD,GAAG,CAAC,GACvBA,GAAG;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASyC,MAAMA,CAAElB,GAAG,EAAE;EACpB,OAAO/B,UAAU,CAAC4D,IAAI,CAAC7B,GAAG,CAAC,KAAK,eAAe;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,SAASA,CAACxB,GAAG,EAAEO,MAAM,EAAE;EAC9B,IAAI;IACF,OAAOA,MAAM,CAACP,GAAG,CAAC;EACpB,CAAC,CAAC,OAAOqD,CAAC,EAAE;IACV,OAAOrD,GAAG;EACZ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}