{"ast": null, "code": "import { dateTimestampProvider } from '../scheduler/dateTimestampProvider';\nimport { map } from './map';\nexport function timestamp(timestampProvider = dateTimestampProvider) {\n  return map(value => ({\n    value,\n    timestamp: timestampProvider.now()\n  }));\n}", "map": {"version": 3, "names": ["dateTimestampProvider", "map", "timestamp", "timestampProvider", "value", "now"], "sources": ["C:/Users/<USER>/Documents/Final/frontend/node_modules/rxjs/dist/esm/internal/operators/timestamp.js"], "sourcesContent": ["import { dateTimestampProvider } from '../scheduler/dateTimestampProvider';\nimport { map } from './map';\nexport function timestamp(timestampProvider = dateTimestampProvider) {\n    return map((value) => ({ value, timestamp: timestampProvider.now() }));\n}\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,GAAG,QAAQ,OAAO;AAC3B,OAAO,SAASC,SAASA,CAACC,iBAAiB,GAAGH,qBAAqB,EAAE;EACjE,OAAOC,GAAG,CAAEG,KAAK,KAAM;IAAEA,KAAK;IAAEF,SAAS,EAAEC,iBAAiB,CAACE,GAAG,CAAC;EAAE,CAAC,CAAC,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}