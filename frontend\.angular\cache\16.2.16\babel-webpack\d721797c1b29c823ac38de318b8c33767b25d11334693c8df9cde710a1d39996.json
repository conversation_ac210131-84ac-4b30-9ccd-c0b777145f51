{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { JwtInterceptor } from './auth/jwt.interceptor';\nimport { AppRoutingModule } from './app-routing.module';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component'; // Import the AppRoutingModule\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: JwtInterceptor,\n        multi: true\n      }],\n      imports: [BrowserModule, FormsModule, HttpClientModule, AppRoutingModule // Include it here\n      ]\n    });\n  }\n}\n\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, LoginComponent, SignupComponent, ResponDashboardComponent, NotAuthorizedComponent],\n    imports: [BrowserModule, FormsModule, HttpClientModule, AppRoutingModule // Include it here\n    ]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "AppComponent", "LoginComponent", "SignupComponent", "JwtInterceptor", "AppRoutingModule", "ResponDashboardComponent", "NotAuthorizedComponent", "AppModule", "bootstrap", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { JwtInterceptor } from './auth/jwt.interceptor'; \nimport { AppRoutingModule } from './app-routing.module';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';  // Import the AppRoutingModule\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    SignupComponent,\n    ResponDashboardComponent,\n    NotAuthorizedComponent\n  ],\n  imports: [\n    BrowserModule,\n    FormsModule,\n    HttpClientModule,\n    AppRoutingModule  // Include it here\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: JwtInterceptor,\n      multi: true\n    }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,sBAAsB,QAAQ,2CAA2C,CAAC,CAAE;;AAyBrF,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRR,YAAY;IAAA;EAAA;;;iBAPb,CACT;QACES,OAAO,EAAEV,iBAAiB;QAC1BW,QAAQ,EAAEP,cAAc;QACxBQ,KAAK,EAAE;OACR,CACF;MAAAC,OAAA,GAXChB,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChBM,gBAAgB,CAAE;MAAA;;;;;;2EAWTG,SAAS;IAAAM,YAAA,GArBlBb,YAAY,EACZC,cAAc,EACdC,eAAe,EACfG,wBAAwB,EACxBC,sBAAsB;IAAAM,OAAA,GAGtBhB,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChBM,gBAAgB,CAAE;IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}