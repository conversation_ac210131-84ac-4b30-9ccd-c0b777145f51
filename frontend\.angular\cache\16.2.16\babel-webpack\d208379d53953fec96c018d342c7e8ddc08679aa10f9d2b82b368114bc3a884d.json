{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TransactionService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth/Transaction';\n  }\n  getAllTransactions() {\n    return this.http.get(`${this.apiUrl}/All_Transactions`);\n  }\n  static {\n    this.ɵfac = function TransactionService_Factory(t) {\n      return new (t || TransactionService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TransactionService,\n      factory: TransactionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["TransactionService", "constructor", "http", "apiUrl", "getAllTransactions", "get", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Final - Copie\\frontend\\src\\app\\services\\transaction.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { Transaction } from '../model/transaction.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class TransactionService {\n  private apiUrl = 'http://localhost:8000/api/v1/auth/Transaction';\n\n  constructor(private http: HttpClient) {}\n\n  getAllTransactions(): Observable<Transaction[]> {\n    return this.http.get<Transaction[]>(`${this.apiUrl}/All_Transactions`);\n  }\n}\n"], "mappings": ";;AAQA,OAAM,MAAOA,kBAAkB;EAG7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,+CAA+C;EAEzB;EAEvCC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAgB,GAAG,IAAI,CAACF,MAAM,mBAAmB,CAAC;EACxE;;;uBAPWH,kBAAkB,EAAAM,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBT,kBAAkB;MAAAU,OAAA,EAAlBV,kBAAkB,CAAAW,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}