package com.Aziz.Administratif.Services;

import com.Aziz.Administratif.Entity.Habilitation;
import com.Aziz.Administratif.Entity.Ressource;
import com.Aziz.Administratif.Repositories.HabilitationRepository;
import com.Aziz.Administratif.Repositories.RessourceRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
@Service
@RequiredArgsConstructor
public class HabilitationService {

    private final HabilitationRepository habilitationRepository;
    private final RessourceRepository ressourceRepository; // Assuming you have a RessourceRepository to handle Ressource entities.

    // Save new habilitation using groupe and ressource ids
    public Habilitation saveHabilitation(Long idGroupe, Long idRessource) {
        // Find the Ressource by ID
        Ressource ressource = ressourceRepository.findById(idRessource)
                .orElseThrow(() -> new RuntimeException("Ressource not found"));

        // Create a new habilitation and set the groupe and ressource
        Habilitation habilitation = new Habilitation();
        habilitation.setIdGroupe(idGroupe);
        habilitation.setRessource(ressource);

        // Save and return the habilitation entity
        return habilitationRepository.save(habilitation);
    }

    // Get all habilitations
    public List<Habilitation> findAllHabilitations() {
        return habilitationRepository.findAll();
    }

    // Find habilitation by ID
    public Optional<Habilitation> findHabilitationById(Long idHabilitation) {
        return habilitationRepository.findById(idHabilitation);
    }

    // Delete habilitation by ID
    public void deleteHabilitation(Long idHabilitation) {
        habilitationRepository.deleteById(idHabilitation);
    }

    // Update habilitation
    public Habilitation updateHabilitation(Long idHabilitation, Habilitation updatedHabilitation) {
        return habilitationRepository.findById(idHabilitation).map(existingHabilitation -> {
            // Update group
            existingHabilitation.setIdGroupe(updatedHabilitation.getIdGroupe());

            // Update ressource if not null and exists in the database
            if (updatedHabilitation.getRessource() != null && updatedHabilitation.getRessource().getIdRessource() != null) {
                // Fetch the existing ressource from the database
                Ressource ressource = ressourceRepository.findById(updatedHabilitation.getRessource().getIdRessource())
                        .orElseThrow(() -> new RuntimeException("Ressource not found"));
                existingHabilitation.setRessource(ressource);
            }

            // Save and return the updated habilitation
            return habilitationRepository.save(existingHabilitation);
        }).orElseThrow(() -> new EntityNotFoundException("Habilitation with ID " + idHabilitation + " not found"));
    }

    // Find habilitations by groupeId
    public List<Habilitation> findHabilitationsByGroupeId(Long idGroupe) {
        return habilitationRepository.findAllByIdGroupe(idGroupe);
    }
}
