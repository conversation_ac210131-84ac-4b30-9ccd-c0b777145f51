{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport * as feather from 'feather-icons';\nimport * as $ from 'jquery';\nexport let TransactionsComponent = class TransactionsComponent {\n  constructor(authService, router, http) {\n    this.authService = authService;\n    this.router = router;\n    this.http = http;\n  }\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  onFileSelected(event) {\n    this.selectedFile = event.target.files[0];\n    if (this.selectedFile) {\n      this.uploadExcel();\n    }\n  }\n  uploadExcel() {\n    if (!this.selectedFile) {\n      this.showModal(\"Veuillez sélectionner un fichier Excel.\", false);\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"file\", this.selectedFile);\n    this.http.post('http://localhost:8000/api/v1/auth/upload-excel', formData, {\n      responseType: 'text'\n    }).subscribe({\n      next: response => {\n        this.showModal(\"Fichier envoyé avec succès : \" + response, true);\n      },\n      error: error => {\n        this.showModal(\"Erreur lors de l'envoi du fichier : \" + error.error, false);\n      }\n    });\n  }\n  showModal(message, success) {\n    const modalBody = document.getElementById(\"uploadModalBody\");\n    const modalHeader = document.querySelector(\".modal-header\");\n    if (modalBody && modalHeader) {\n      modalBody.innerText = message;\n      if (success) {\n        modalHeader.classList.remove(\"bg-danger\");\n        modalHeader.classList.add(\"bg-success\");\n      } else {\n        modalHeader.classList.remove(\"bg-success\");\n        modalHeader.classList.add(\"bg-danger\");\n      }\n      $('#uploadModal').modal('show');\n    }\n  }\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  onFileDropped(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    if (event.dataTransfer && event.dataTransfer.files.length > 0) {\n      this.selectedFile = event.dataTransfer.files[0];\n      this.uploadExcel();\n    }\n  }\n  logout() {\n    // Clear token and other data\n    localStorage.removeItem('jwt_token'); // Fix: Remove 'jwt_token'\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    // Optionally, check that these items are removed:\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    // Navigate to login page\n    this.router.navigate(['/login']);\n  }\n};\n__decorate([ViewChild('myChart')], TransactionsComponent.prototype, \"myChartRef\", void 0);\nTransactionsComponent = __decorate([Component({\n  selector: 'app-transactions',\n  templateUrl: './transactions.component.html',\n  styleUrls: ['./transactions.component.css']\n})], TransactionsComponent);", "map": {"version": 3, "names": ["Component", "ViewChild", "feather", "$", "TransactionsComponent", "constructor", "authService", "router", "http", "ngAfterViewInit", "replace", "onFileSelected", "event", "selectedFile", "target", "files", "uploadExcel", "showModal", "formData", "FormData", "append", "post", "responseType", "subscribe", "next", "response", "error", "message", "success", "modalBody", "document", "getElementById", "modalHeader", "querySelector", "innerText", "classList", "remove", "add", "modal", "onDragOver", "preventDefault", "stopPropagation", "onFileDropped", "dataTransfer", "length", "logout", "localStorage", "removeItem", "console", "log", "getItem", "navigate", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\transactions\\transactions.component.ts"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, ViewChild } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthenticationService } from '../auth/authentication.service';\nimport * as feather from 'feather-icons';\nimport { HttpClient } from '@angular/common/http';\nimport * as $ from 'jquery';\n\n@Component({\n  selector: 'app-transactions',\n  templateUrl: './transactions.component.html',\n  styleUrls: ['./transactions.component.css']\n})\nexport class TransactionsComponent implements AfterViewInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  selectedFile!: File;\n\n  constructor(\n    private authService: AuthenticationService,\n    private router: Router,\n    private http: HttpClient\n  ) {}\n\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  onFileSelected(event: any): void {\n    this.selectedFile = event.target.files[0];\n    if (this.selectedFile) {\n      this.uploadExcel();\n    }\n  }\n\n  uploadExcel(): void {\n    if (!this.selectedFile) {\n      this.showModal(\"Veuillez sélectionner un fichier Excel.\", false);\n      return;\n    }\n\n    const formData = new FormData();\n    formData.append(\"file\", this.selectedFile);\n\n    this.http.post('http://localhost:8000/api/v1/auth/upload-excel', formData, {\n      responseType: 'text'\n    }).subscribe({\n      next: (response) => {\n        this.showModal(\"Fichier envoyé avec succès : \" + response, true);\n      },\n      error: (error) => {\n        this.showModal(\"Erreur lors de l'envoi du fichier : \" + error.error, false);\n      }\n    });\n  }\n\n  showModal(message: string, success: boolean): void {\n    const modalBody = document.getElementById(\"uploadModalBody\");\n    const modalHeader = document.querySelector(\".modal-header\");\n\n    if (modalBody && modalHeader) {\n      modalBody.innerText = message;\n\n      if (success) {\n        modalHeader.classList.remove(\"bg-danger\");\n        modalHeader.classList.add(\"bg-success\");\n      } else {\n        modalHeader.classList.remove(\"bg-success\");\n        modalHeader.classList.add(\"bg-danger\");\n      }\n\n      ($('#uploadModal') as any).modal('show');\n    }\n  }\n\n  onDragOver(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n\n  onFileDropped(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    if (event.dataTransfer && event.dataTransfer.files.length > 0) {\n      this.selectedFile = event.dataTransfer.files[0];\n      this.uploadExcel();\n    }\n  }\n\n  logout() {\n    // Clear token and other data\n    localStorage.removeItem('jwt_token');  // Fix: Remove 'jwt_token'\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    \n    // Optionally, check that these items are removed:\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n  \n    // Navigate to login page\n    this.router.navigate(['/login']);\n  }\n}\n"], "mappings": ";AAAA,SAAwBA,SAAS,EAAcC,SAAS,QAAQ,eAAe;AAG/E,OAAO,KAAKC,OAAO,MAAM,eAAe;AAExC,OAAO,KAAKC,CAAC,MAAM,QAAQ;AAOpB,WAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAIhCC,YACUC,WAAkC,EAClCC,MAAc,EACdC,IAAgB;IAFhB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;EACX;EAEHC,eAAeA,CAAA;IACbP,OAAO,CAACQ,OAAO,EAAE,CAAC,CAAC;EACrB;;EAEAC,cAAcA,CAACC,KAAU;IACvB,IAAI,CAACC,YAAY,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACzC,IAAI,IAAI,CAACF,YAAY,EAAE;MACrB,IAAI,CAACG,WAAW,EAAE;;EAEtB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACH,YAAY,EAAE;MACtB,IAAI,CAACI,SAAS,CAAC,yCAAyC,EAAE,KAAK,CAAC;MAChE;;IAGF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAACP,YAAY,CAAC;IAE1C,IAAI,CAACL,IAAI,CAACa,IAAI,CAAC,gDAAgD,EAAEH,QAAQ,EAAE;MACzEI,YAAY,EAAE;KACf,CAAC,CAACC,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACR,SAAS,CAAC,+BAA+B,GAAGQ,QAAQ,EAAE,IAAI,CAAC;MAClE,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACT,SAAS,CAAC,sCAAsC,GAAGS,KAAK,CAACA,KAAK,EAAE,KAAK,CAAC;MAC7E;KACD,CAAC;EACJ;EAEAT,SAASA,CAACU,OAAe,EAAEC,OAAgB;IACzC,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;IAC5D,MAAMC,WAAW,GAAGF,QAAQ,CAACG,aAAa,CAAC,eAAe,CAAC;IAE3D,IAAIJ,SAAS,IAAIG,WAAW,EAAE;MAC5BH,SAAS,CAACK,SAAS,GAAGP,OAAO;MAE7B,IAAIC,OAAO,EAAE;QACXI,WAAW,CAACG,SAAS,CAACC,MAAM,CAAC,WAAW,CAAC;QACzCJ,WAAW,CAACG,SAAS,CAACE,GAAG,CAAC,YAAY,CAAC;OACxC,MAAM;QACLL,WAAW,CAACG,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;QAC1CJ,WAAW,CAACG,SAAS,CAACE,GAAG,CAAC,WAAW,CAAC;;MAGvClC,CAAC,CAAC,cAAc,CAAS,CAACmC,KAAK,CAAC,MAAM,CAAC;;EAE5C;EAEAC,UAAUA,CAAC3B,KAAgB;IACzBA,KAAK,CAAC4B,cAAc,EAAE;IACtB5B,KAAK,CAAC6B,eAAe,EAAE;EACzB;EAEAC,aAAaA,CAAC9B,KAAgB;IAC5BA,KAAK,CAAC4B,cAAc,EAAE;IACtB5B,KAAK,CAAC6B,eAAe,EAAE;IACvB,IAAI7B,KAAK,CAAC+B,YAAY,IAAI/B,KAAK,CAAC+B,YAAY,CAAC5B,KAAK,CAAC6B,MAAM,GAAG,CAAC,EAAE;MAC7D,IAAI,CAAC/B,YAAY,GAAGD,KAAK,CAAC+B,YAAY,CAAC5B,KAAK,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACC,WAAW,EAAE;;EAEtB;EAEA6B,MAAMA,CAAA;IACJ;IACAC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE;IACvCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAEhC;IACAC,OAAO,CAACC,GAAG,CAACH,YAAY,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAEhD;IACA,IAAI,CAAC3C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;CACD;AAtFuBC,UAAA,EAArBnD,SAAS,CAAC,SAAS,CAAC,C,wDAAyB;AADnCG,qBAAqB,GAAAgD,UAAA,EALjCpD,SAAS,CAAC;EACTqD,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,8BAA8B;CAC3C,CAAC,C,EACWnD,qBAAqB,CAuFjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}