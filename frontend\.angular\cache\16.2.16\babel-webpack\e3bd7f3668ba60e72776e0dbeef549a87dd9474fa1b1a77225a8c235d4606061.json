{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { AuthGuard } from './auth/auth.guard';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\nimport { AdminDashComponent } from './admin-dash/admin-dash.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'signup',\n  component: SignupComponent\n}, {\n  path: 'adminDash',\n  component: AdminDashComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'transactions',\n  component: TransactionsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'actions',\n  component: ActionsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'actionnaires',\n  component: ActionnairesComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'reports',\n  component: ReportsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'port',\n  component: PortefeuillesComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'ResDash',\n  component: ResponDashboardComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'not-authorized',\n  component: NotAuthorizedComponent\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "SignupComponent", "ResponDashboardComponent", "NotAuthorizedComponent", "<PERSON><PERSON><PERSON><PERSON>", "TransactionsComponent", "ActionsComponent", "ActionnairesComponent", "ReportsComponent", "PortefeuillesComponent", "AdminDashComponent", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { AuthGuard } from './auth/auth.guard';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\nimport { AdminDashComponent } from './admin-dash/admin-dash.component';\n\nconst routes: Routes = [\n  { path: '', redirectTo: 'login', pathMatch: 'full' },\n  { path: 'login', component: LoginComponent },\n  { path: 'signup', component: SignupComponent },\n  { path: 'adminDash', component: AdminDashComponent, canActivate: [AuthGuard] },\n\n  { path: 'transactions', component: TransactionsComponent, canActivate: [AuthGuard] },\n  { path: 'actions', component: ActionsComponent, canActivate: [AuthGuard] },\n  { path: 'actionnaires', component: ActionnairesComponent, canActivate: [AuthGuard] },\n  { path: 'reports', component: ReportsComponent, canActivate: [AuthGuard] },\n  { path: 'port', component: PortefeuillesComponent, canActivate: [AuthGuard] },\n  { path: 'ResDash', component: ResponDashboardComponent, canActivate: [AuthGuard] },\n  { path: 'not-authorized', component: NotAuthorizedComponent }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,kBAAkB,QAAQ,mCAAmC;;;AAEtE,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE;AAAM,CAAE,EACpD;EAAEF,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEf;AAAc,CAAE,EAC5C;EAAEY,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAEd;AAAe,CAAE,EAC9C;EAAEW,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEL,kBAAkB;EAAEM,WAAW,EAAE,CAACZ,SAAS;AAAC,CAAE,EAE9E;EAAEQ,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAEV,qBAAqB;EAAEW,WAAW,EAAE,CAACZ,SAAS;AAAC,CAAE,EACpF;EAAEQ,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAET,gBAAgB;EAAEU,WAAW,EAAE,CAACZ,SAAS;AAAC,CAAE,EAC1E;EAAEQ,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAER,qBAAqB;EAAES,WAAW,EAAE,CAACZ,SAAS;AAAC,CAAE,EACpF;EAAEQ,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEP,gBAAgB;EAAEQ,WAAW,EAAE,CAACZ,SAAS;AAAC,CAAE,EAC1E;EAAEQ,IAAI,EAAE,MAAM;EAAEG,SAAS,EAAEN,sBAAsB;EAAEO,WAAW,EAAE,CAACZ,SAAS;AAAC,CAAE,EAC7E;EAAEQ,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEb,wBAAwB;EAAEc,WAAW,EAAE,CAACZ,SAAS;AAAC,CAAE,EAClF;EAAEQ,IAAI,EAAE,gBAAgB;EAAEG,SAAS,EAAEZ;AAAsB,CAAE,CAC9D;AAMD,OAAM,MAAOc,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBlB,YAAY,CAACmB,OAAO,CAACP,MAAM,CAAC,EAC5BZ,YAAY;IAAA;EAAA;;;2EAEXkB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAArB,YAAA;IAAAsB,OAAA,GAFjBtB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}