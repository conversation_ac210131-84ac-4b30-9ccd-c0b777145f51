{"ast": null, "code": "import * as feather from 'feather-icons';\nimport { Modal } from 'bootstrap';\nimport * as XLSX from 'xlsx';\nimport { Chart, registerables } from 'chart.js';\nimport * as FileSaver from 'file-saver';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth/authentication.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"../services/transaction.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"myChart\"];\nconst _c1 = [\"feedbackModal\"];\nfunction TransactionsComponent_tr_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tx_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tx_r4.idTransaction);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tx_r4.type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tx_r4.qantite);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tx_r4.montatnt);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tx_r4.observations);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(13, 9, tx_r4.dateCreation, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 12, tx_r4.dateTransaction, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tx_r4.portefeuille == null ? null : tx_r4.portefeuille.idPortefeuille);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tx_r4.userId);\n  }\n}\nconst _c2 = function (a0, a1) {\n  return {\n    \"modal-header bg-success\": a0,\n    \"modal-header bg-danger\": a1\n  };\n};\nconst _c3 = \"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, 0.25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: 'Poppins', sans-serif;\\n  margin: 0;\\n  padding: 0;\\n  min-height: 100vh;\\n  background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n  background-size: 400% 400%;\\n  animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n  color: #fff;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n  0% {\\n      background-position: 0% 50%;\\n  }\\n\\n  50% {\\n      background-position: 100% 50%;\\n  }\\n\\n  100% {\\n      background-position: 0% 50%;\\n  }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  margin-top: 20px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 1.5rem;\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  color: #fff;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  border-collapse: separate;\\n  border-spacing: 0 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.15);\\n  color: #ffffff;\\n  font-weight: 600;\\n  text-align: center;\\n  border: none;\\n  padding: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  text-align: center;\\n  border-radius: 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.01);\\n  box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  vertical-align: middle;\\n  padding: 0.9rem;\\n  font-size: 0.95rem;\\n}\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n  border: none;\\n  padding: 12px 25px;\\n  border-radius: 30px;\\n  text-transform: uppercase;\\n  font-weight: bold;\\n  transition: background 0.3s, transform 0.2s;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n  background-color: #218838;\\n  transform: translateY(-2px);\\n}\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n  color: #206ee1;\\n  cursor: pointer;\\n  font-size: 20px;\\n  margin: 0 10px;\\n  transition: color 0.3s;\\n}\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffffff;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n  color: #d22d2d;\\n  cursor: pointer;\\n  font-size: 20px;\\n  transition: color 0.3s;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffffff;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 1.5rem;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n  background-color: #f1f1f1;\\n  padding: 1rem;\\n  border-top: 1px solid #ddd;\\n  display: flex;\\n  justify-content: center; \\n\\n  gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  font-size: 1rem;\\n  border-radius: 0.3rem;\\n  transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n  transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column; \\n\\n  align-items: flex-start; \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  background-color: rgba(255, 255, 255, 0.1); \\n\\n  border: 1px solid rgba(255, 255, 255, 0.3); \\n\\n  border-radius: 30px; \\n\\n  color: #fff; \\n\\n  padding: 10px 20px; \\n\\n  font-size: 1rem; \\n\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); \\n\\n  width: 100%; \\n\\n  max-width: 400px; \\n\\n  transition: all 0.3s ease; \\n\\n}\\n\\n.search-bar[_ngcontent-%COMP%]:focus {\\n  outline: none; \\n\\n  border-color: #007bff; \\n\\n  box-shadow: 0 0 8px rgba(0, 123, 255, 0.6); \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n  background: rgba(20, 33, 59, 0.9); \\n\\n  color: #fff;\\n  min-height: 100vh;\\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #ccc;\\n  transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n  background-color: #000 !important;\\n  color: #fff;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n  position: relative;\\n  padding: 0.5rem 1rem;\\n  transition: color 0.3s ease;\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  width: 0;\\n  background: #ff4c60;\\n  transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 30px;\\n  object-fit: cover;\\n  border-radius: 50%; \\n\\n  margin-right: 8px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class TransactionsComponent {\n  constructor(authService, router, http, transactionService) {\n    this.authService = authService;\n    this.router = router;\n    this.http = http;\n    this.transactionService = transactionService;\n    this.transactions = [];\n    this.modalMessage = '';\n    this.isSuccess = true;\n    // Registering chart.js components\n    Chart.register(...registerables);\n  }\n  // Function to export data to Excel\n  exportToExcel() {\n    const exportData = this.transactions.map(t => ({\n      'ID Transaction': t.idTransaction,\n      'Type': t.type,\n      'Quantité': t.qantite,\n      'Montant': t.montatnt,\n      'Observations': t.observations,\n      'Date de création': t.dateCreation,\n      'Date de transaction': t.dateTransaction,\n      'ID Utilisateur': t.userId,\n      'Nom Portefeuille': t.portefeuille?.idPortefeuille || 'N/A'\n    }));\n    const worksheet = XLSX.utils.json_to_sheet(exportData);\n    const workbook = {\n      Sheets: {\n        'Transactions': worksheet\n      },\n      SheetNames: ['Transactions']\n    };\n    const excelBuffer = XLSX.write(workbook, {\n      bookType: 'xlsx',\n      type: 'array'\n    });\n    const data = new Blob([excelBuffer], {\n      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'\n    });\n    FileSaver.saveAs(data, 'transactions_export.xlsx');\n  }\n  // Function to load transactions from the server\n  loadTransactions() {\n    this.transactionService.getAllTransactions().subscribe({\n      next: data => {\n        this.transactions = data;\n        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded\n        // Now render the chart\n        this.renderChart();\n      },\n      error: err => {\n        console.error('Error fetching transactions', err);\n      }\n    });\n  }\n  ngAfterViewInit() {\n    this.loadTransactions();\n    feather.replace();\n    if (this.feedbackModalRef) {\n      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);\n    }\n  }\n  // Function to render the chart using the transaction data\n  renderChart() {\n    const ctx = this.myChartRef.nativeElement.getContext('2d');\n    // Destroy previous chart if exists\n    if (this.chartInstance) {\n      this.chartInstance.destroy();\n    }\n    const labels = this.transactions.map(t => t.type);\n    const data = this.transactions.map(t => t.montatnt);\n    // Create a new chart instance\n    this.chartInstance = new Chart(ctx, {\n      type: 'bar',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Montant par Type de Transaction',\n          data: data,\n          backgroundColor: 'rgba(54, 162, 235, 0.6)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n  // Handle file selection\n  onFileSelected(event) {\n    this.selectedFile = event.target.files[0];\n    if (this.selectedFile) {\n      this.uploadExcel();\n    }\n  }\n  // Function to upload the selected Excel file\n  uploadExcel() {\n    if (!this.selectedFile) {\n      this.showModal(\"Veuillez sélectionner un fichier Excel.\", false);\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"file\", this.selectedFile);\n    this.http.post('http://localhost:8000/api/v1/auth/upload-excel', formData, {\n      responseType: 'text'\n    }).subscribe({\n      next: response => {\n        this.showModal(\"Fichier envoyé avec succès : \" + response, true);\n      },\n      error: error => {\n        this.showModal(\"Erreur lors de l'envoi du fichier : \" + error.error, false);\n      }\n    });\n  }\n  // Function to show modal with feedback message\n  showModal(message, success) {\n    this.modalMessage = message;\n    this.isSuccess = success;\n    this.modalInstance.show();\n  }\n  // File drag-and-drop handlers\n  onDragOver(event) {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  onFileDropped(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    if (event.dataTransfer && event.dataTransfer.files.length > 0) {\n      this.selectedFile = event.dataTransfer.files[0];\n      this.uploadExcel();\n    }\n  }\n  // Logout functionality\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token'));\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function TransactionsComponent_Factory(t) {\n      return new (t || TransactionsComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.TransactionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TransactionsComponent,\n      selectors: [[\"app-transactions\"]],\n      viewQuery: function TransactionsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedbackModalRef = _t.first);\n        }\n      },\n      decls: 110,\n      vars: 7,\n      consts: [[\"lang\", \"en\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-store, no-cache, must-revalidate, max-age=0\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [\"width\", \"900\", \"height\", \"380\", 2, \"background-color\", \"lightgrey\"], [\"myChart\", \"\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"text-center\", \"mt-4\", \"mb-4\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [\"for\", \"excelFile\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", \"mb-0\", \"d-flex\", \"align-items-center\", 2, \"cursor\", \"pointer\", 3, \"dragover\", \"drop\"], [1, \"fas\", \"fa-file-import\", \"mr-2\"], [\"type\", \"file\", \"id\", \"excelFile\", \"accept\", \".xls,.xlsx\", \"hidden\", \"\", 3, \"change\"], [1, \"btn\", \"btn-sm\", \"btn-outline-success\", \"d-flex\", \"align-items-center\", 3, \"click\"], [1, \"fas\", \"fa-file-export\", \"mr-2\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"thead-dark\"], [4, \"ngFor\", \"ngForOf\"], [1, \"my-4\"], [\"width\", \"400\", \"height\", \"200\"], [\"tabindex\", \"-1\", \"id\", \"feedbackModal\", 1, \"modal\", \"fade\"], [\"feedbackModal\", \"\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [3, \"ngClass\"], [1, \"modal-title\", \"text-white\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"modal-body\"]],\n      template: function TransactionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"meta\", 6)(8, \"meta\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10)(12, \"link\", 11);\n          i0.ɵɵelementStart(13, \"title\");\n          i0.ɵɵtext(14, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"link\", 12)(16, \"link\", 13)(17, \"canvas\", 14, 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"body\")(20, \"nav\", 16)(21, \"a\", 17);\n          i0.ɵɵelement(22, \"img\", 18);\n          i0.ɵɵtext(23, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"ul\", 19)(25, \"li\", 20)(26, \"a\", 21);\n          i0.ɵɵlistener(\"click\", function TransactionsComponent_Template_a_click_26_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(27, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"div\", 22)(29, \"div\", 23)(30, \"nav\", 24)(31, \"div\", 25)(32, \"ul\", 26)(33, \"li\", 27)(34, \"a\", 28);\n          i0.ɵɵelement(35, \"span\", 29);\n          i0.ɵɵtext(36, \" Dashboard \");\n          i0.ɵɵelementStart(37, \"span\", 30);\n          i0.ɵɵtext(38, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"li\", 27)(40, \"a\", 31);\n          i0.ɵɵelement(41, \"span\", 32);\n          i0.ɵɵtext(42, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"li\", 27)(44, \"a\", 33);\n          i0.ɵɵelement(45, \"span\", 34);\n          i0.ɵɵtext(46, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"li\", 27)(48, \"a\", 35);\n          i0.ɵɵelement(49, \"span\", 36);\n          i0.ɵɵtext(50, \" Gestion des transactions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\", 27)(52, \"a\", 37);\n          i0.ɵɵelement(53, \"span\", 38);\n          i0.ɵɵtext(54, \" Gestion des actions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"li\", 27)(56, \"a\", 39);\n          i0.ɵɵelement(57, \"span\", 40);\n          i0.ɵɵtext(58, \" Gestion des actionnaires \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(59, \"main\", 41)(60, \"div\", 42)(61, \"h2\", 43);\n          i0.ɵɵtext(62, \"Gestion des transactions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 44)(64, \"div\", 45)(65, \"label\", 46);\n          i0.ɵɵlistener(\"dragover\", function TransactionsComponent_Template_label_dragover_65_listener($event) {\n            return ctx.onDragOver($event);\n          })(\"drop\", function TransactionsComponent_Template_label_drop_65_listener($event) {\n            return ctx.onFileDropped($event);\n          });\n          i0.ɵɵelement(66, \"i\", 47);\n          i0.ɵɵtext(67, \" Importer \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"input\", 48);\n          i0.ɵɵlistener(\"change\", function TransactionsComponent_Template_input_change_68_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"button\", 49);\n          i0.ɵɵlistener(\"click\", function TransactionsComponent_Template_button_click_69_listener() {\n            return ctx.exportToExcel();\n          });\n          i0.ɵɵelement(70, \"i\", 50);\n          i0.ɵɵtext(71, \" Exporter \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(72, \"div\", 51)(73, \"table\", 52)(74, \"thead\", 53)(75, \"tr\")(76, \"th\");\n          i0.ɵɵtext(77, \"R\\u00E9ference\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"th\");\n          i0.ɵɵtext(79, \"Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"th\");\n          i0.ɵɵtext(81, \"Quantit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"th\");\n          i0.ɵɵtext(83, \"Montant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"th\");\n          i0.ɵɵtext(85, \"Observations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"th\");\n          i0.ɵɵtext(87, \"Date de cr\\u00E9ation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"th\");\n          i0.ɵɵtext(89, \"Date de transaction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"th\");\n          i0.ɵɵtext(91, \"Portefeuille ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"th\");\n          i0.ɵɵtext(93, \"User ID\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(94, \"tbody\");\n          i0.ɵɵtemplate(95, TransactionsComponent_tr_95_Template, 21, 15, \"tr\", 54);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(96, \"div\", 55);\n          i0.ɵɵelement(97, \"canvas\", 56, 15);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(99, \"div\", 57, 58)(101, \"div\", 59)(102, \"div\", 60)(103, \"div\", 61)(104, \"h5\", 62);\n          i0.ɵɵtext(105);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(106, \"button\", 63);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"div\", 64)(108, \"p\");\n          i0.ɵɵtext(109);\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(95);\n          i0.ɵɵproperty(\"ngForOf\", ctx.transactions);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c2, ctx.isSuccess, !ctx.isSuccess));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.isSuccess ? \"Succ\\u00E8s\" : \"Erreur\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.modalMessage);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.DatePipe],\n      styles: [_c3, _c3]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "Modal", "XLSX", "Chart", "registerables", "FileSaver", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "tx_r4", "idTransaction", "type", "qantite", "montatnt", "observations", "ɵɵpipeBind2", "dateCreation", "dateTransaction", "portefeuille", "idPortefeuille", "userId", "TransactionsComponent", "constructor", "authService", "router", "http", "transactionService", "transactions", "modalMessage", "isSuccess", "register", "exportToExcel", "exportData", "map", "t", "worksheet", "utils", "json_to_sheet", "workbook", "Sheets", "SheetNames", "excelBuffer", "write", "bookType", "data", "Blob", "saveAs", "loadTransactions", "getAllTransactions", "subscribe", "next", "console", "log", "<PERSON><PERSON><PERSON>", "error", "err", "ngAfterViewInit", "replace", "feedbackModalRef", "modalInstance", "nativeElement", "ctx", "myChartRef", "getContext", "chartInstance", "destroy", "labels", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "options", "responsive", "scales", "y", "beginAtZero", "onFileSelected", "event", "selectedFile", "target", "files", "uploadExcel", "showModal", "formData", "FormData", "append", "post", "responseType", "response", "message", "success", "show", "onDragOver", "preventDefault", "stopPropagation", "onFileDropped", "dataTransfer", "length", "logout", "localStorage", "removeItem", "getItem", "navigate", "ɵɵdirectiveInject", "i1", "AuthenticationService", "i2", "Router", "i3", "HttpClient", "i4", "TransactionService", "selectors", "viewQuery", "TransactionsComponent_Query", "rf", "ɵɵelement", "ɵɵlistener", "TransactionsComponent_Template_a_click_26_listener", "TransactionsComponent_Template_label_dragover_65_listener", "$event", "TransactionsComponent_Template_label_drop_65_listener", "TransactionsComponent_Template_input_change_68_listener", "TransactionsComponent_Template_button_click_69_listener", "ɵɵtemplate", "TransactionsComponent_tr_95_Template", "ɵɵproperty", "ɵɵpureFunction2", "_c2"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\transactions\\transactions.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\transactions\\transactions.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, ViewChild } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthenticationService } from '../auth/authentication.service';\nimport * as feather from 'feather-icons';\nimport { HttpClient } from '@angular/common/http';\nimport { Modal } from 'bootstrap';\nimport { TransactionService } from '../services/transaction.service';\nimport { Transaction } from '../model/transaction.model';\nimport * as XLSX from 'xlsx';\nimport { Chart, registerables } from 'chart.js';\nimport * as FileSaver from 'file-saver';\n\n@Component({\n  selector: 'app-transactions',\n  templateUrl: './transactions.component.html',\n  styleUrls: ['./transactions.component.css']\n})\nexport class TransactionsComponent implements AfterViewInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  @ViewChild('feedbackModal') feedbackModalRef!: ElementRef;\n  transactions: Transaction[] = [];\n\n  selectedFile!: File;\n  modalMessage = '';\n  isSuccess = true;\n  private modalInstance!: Modal;\n  private chartInstance: any;\n\n  constructor(\n    private authService: AuthenticationService,\n    private router: Router,\n    private http: HttpClient,\n    private transactionService: TransactionService\n  ) {\n    // Registering chart.js components\n    Chart.register(...registerables);\n  }\n\n  // Function to export data to Excel\n  exportToExcel(): void {\n    const exportData = this.transactions.map(t => ({\n      'ID Transaction': t.idTransaction,\n      'Type': t.type,\n      'Quantité': t.qantite,\n      'Montant': t.montatnt,\n      'Observations': t.observations,\n      'Date de création': t.dateCreation,\n      'Date de transaction': t.dateTransaction,\n      'ID Utilisateur': t.userId,\n      'Nom Portefeuille': t.portefeuille?.idPortefeuille || 'N/A'\n    }));\n\n    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(exportData);\n    const workbook: XLSX.WorkBook = { Sheets: { 'Transactions': worksheet }, SheetNames: ['Transactions'] };\n    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });\n\n    const data: Blob = new Blob([excelBuffer], {\n      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'\n    });\n    FileSaver.saveAs(data, 'transactions_export.xlsx');\n  }\n\n  // Function to load transactions from the server\n  loadTransactions(): void {\n    this.transactionService.getAllTransactions().subscribe({\n      next: (data) => {\n        this.transactions = data;\n        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded\n\n        // Now render the chart\n        this.renderChart();\n      },\n      error: (err) => {\n        console.error('Error fetching transactions', err);\n      }\n    });\n  }\n\n  ngAfterViewInit() {\n    this.loadTransactions();\n    feather.replace();\n    if (this.feedbackModalRef) {\n      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);\n    }\n  }\n\n  // Function to render the chart using the transaction data\n  renderChart(): void {\n    const ctx = this.myChartRef.nativeElement.getContext('2d');\n\n    // Destroy previous chart if exists\n    if (this.chartInstance) {\n      this.chartInstance.destroy();\n    }\n\n    const labels = this.transactions.map(t => t.type);\n    const data = this.transactions.map(t => t.montatnt);\n\n    // Create a new chart instance\n    this.chartInstance = new Chart(ctx, {\n      type: 'bar',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Montant par Type de Transaction',\n          data: data,\n          backgroundColor: 'rgba(54, 162, 235, 0.6)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n\n  // Handle file selection\n  onFileSelected(event: any): void {\n    this.selectedFile = event.target.files[0];\n    if (this.selectedFile) {\n      this.uploadExcel();\n    }\n  }\n\n  // Function to upload the selected Excel file\n  uploadExcel(): void {\n    if (!this.selectedFile) {\n      this.showModal(\"Veuillez sélectionner un fichier Excel.\", false);\n      return;\n    }\n\n    const formData = new FormData();\n    formData.append(\"file\", this.selectedFile);\n\n    this.http.post('http://localhost:8000/api/v1/auth/upload-excel', formData, {\n      responseType: 'text'\n    }).subscribe({\n      next: (response) => {\n        this.showModal(\"Fichier envoyé avec succès : \" + response, true);\n      },\n      error: (error) => {\n        this.showModal(\"Erreur lors de l'envoi du fichier : \" + error.error, false);\n      }\n    });\n  }\n\n  // Function to show modal with feedback message\n  showModal(message: string, success: boolean): void {\n    this.modalMessage = message;\n    this.isSuccess = success;\n    this.modalInstance.show();\n  }\n\n  // File drag-and-drop handlers\n  onDragOver(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n  }\n\n  onFileDropped(event: DragEvent): void {\n    event.preventDefault();\n    event.stopPropagation();\n    if (event.dataTransfer && event.dataTransfer.files.length > 0) {\n      this.selectedFile = event.dataTransfer.files[0];\n      this.uploadExcel();\n    }\n  }\n\n  // Logout functionality\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token'));\n    this.router.navigate(['/login']);\n  }\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta http-equiv=\"Cache-Control\" content=\"no-store, no-cache, must-revalidate, max-age=0\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n\n    <!-- Custom styles for this template -->\n    <link href=\"./transactions.component.css\" rel=\"stylesheet\">\n    <canvas #myChart width=\"900\" height=\"380\" style=\"background-color: lightgrey;\"></canvas>\n\n  </head>\n\n  <body>\n    \n    <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\" href=\"adminDash\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            \n            <li class=\"nav-item\">\n              <a class=\"nav-link\"  href=\"/transactions\">\n                <span data-feather=\"dollar-sign\"></span>\n                Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/actions\">\n                <span data-feather=\"trending-up\"></span>\n               Gestion des actions\n              </a>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\">\n                  <span data-feather=\"users\"></span>\n                 Gestion des actionnaires\n                </a>\n            </ul>\n          </div>\n        </nav>\n\n        \n\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h2 class=\"text-center mt-4 mb-4\">Gestion des transactions</h2>\n        \n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n                <!-- Import Button with File Upload + Icon -->\n                <label class=\"btn btn-sm btn-outline-primary mb-0 d-flex align-items-center\" \n                       for=\"excelFile\" \n                       (dragover)=\"onDragOver($event)\" \n                       (drop)=\"onFileDropped($event)\"\n                       style=\"cursor: pointer;\">\n                  <i class=\"fas fa-file-import mr-2\"></i> Importer\n                </label>\n                <input type=\"file\" id=\"excelFile\" accept=\".xls,.xlsx\"\n                       (change)=\"onFileSelected($event)\" \n                       hidden />\n        \n                <!-- Export Button with Icon -->\n                <button class=\"btn btn-sm btn-outline-success d-flex align-items-center\" (click)=\"exportToExcel()\">\n                  <i class=\"fas fa-file-export mr-2\"></i> Exporter\n                </button>\n              </div>\n            </div>\n          </div>\n        \n          <div class=\"table-responsive\">\n            <table class=\"table table-hover\">\n              <thead class=\"thead-dark\">\n                <tr>\n                  <th>Réference</th>\n                  <th>Type</th>\n                  <th>Quantité</th>\n                  <th>Montant</th>\n                  <th>Observations</th>\n                  <th>Date de création</th>\n                  <th>Date de transaction</th>\n                  <th>Portefeuille ID</th>\n                  <th>User ID</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let tx of transactions\">\n                  <td>{{ tx.idTransaction }}</td>\n                  <td>{{ tx.type }}</td>\n                  <td>{{ tx.qantite }}</td>\n                  <td>{{ tx.montatnt }}</td>\n                  <td>{{ tx.observations }}</td>\n                  <td>{{ tx.dateCreation | date:'short' }}</td>\n                  <td>{{ tx.dateTransaction | date:'short' }}</td>\n                  <td>{{ tx.portefeuille?.idPortefeuille }}</td>\n                  <td>{{ tx.userId }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n          <div class=\"my-4\">\n            <canvas #myChart width=\"400\" height=\"200\"></canvas>\n          </div>\n          \n      \n        </main>\n           \n      </div>\n    </div>\n\n    \n\n<!-- Modal -->\n<div class=\"modal fade\" tabindex=\"-1\" id=\"feedbackModal\" #feedbackModal>\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div [ngClass]=\"{'modal-header bg-success': isSuccess, 'modal-header bg-danger': !isSuccess}\">\n        <h5 class=\"modal-title text-white\">{{ isSuccess ? 'Succès' : 'Erreur' }}</h5>\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <p>{{ modalMessage }}</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n    <!-- Placed at the end of the document so the pages load faster -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>    \n\n    </script>\n  </body>\n</html>\n"], "mappings": "AAGA,OAAO,KAAKA,OAAO,MAAM,eAAe;AAExC,SAASC,KAAK,QAAQ,WAAW;AAGjC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAC/C,OAAO,KAAKC,SAAS,MAAM,YAAY;;;;;;;;;;;ICyHvBC,EAAA,CAAAC,cAAA,SAAoC;IAC9BD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAoC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAuC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IARpBH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAC,KAAA,CAAAC,aAAA,CAAsB;IACtBP,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,KAAA,CAAAE,IAAA,CAAa;IACbR,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,KAAA,CAAAG,OAAA,CAAgB;IAChBT,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAC,KAAA,CAAAI,QAAA,CAAiB;IACjBV,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,KAAA,CAAAK,YAAA,CAAqB;IACrBX,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAY,WAAA,QAAAN,KAAA,CAAAO,YAAA,WAAoC;IACpCb,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAY,WAAA,SAAAN,KAAA,CAAAQ,eAAA,WAAuC;IACvCd,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAK,iBAAA,CAAAC,KAAA,CAAAS,YAAA,kBAAAT,KAAA,CAAAS,YAAA,CAAAC,cAAA,CAAqC;IACrChB,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAC,KAAA,CAAAW,MAAA,CAAe;;;;;;;;;;AD3HrC,OAAM,MAAOC,qBAAqB;EAWhCC,YACUC,WAAkC,EAClCC,MAAc,EACdC,IAAgB,EAChBC,kBAAsC;IAHtC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAZ5B,KAAAC,YAAY,GAAkB,EAAE;IAGhC,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,SAAS,GAAG,IAAI;IAUd;IACA7B,KAAK,CAAC8B,QAAQ,CAAC,GAAG7B,aAAa,CAAC;EAClC;EAEA;EACA8B,aAAaA,CAAA;IACX,MAAMC,UAAU,GAAG,IAAI,CAACL,YAAY,CAACM,GAAG,CAACC,CAAC,KAAK;MAC7C,gBAAgB,EAAEA,CAAC,CAACxB,aAAa;MACjC,MAAM,EAAEwB,CAAC,CAACvB,IAAI;MACd,UAAU,EAAEuB,CAAC,CAACtB,OAAO;MACrB,SAAS,EAAEsB,CAAC,CAACrB,QAAQ;MACrB,cAAc,EAAEqB,CAAC,CAACpB,YAAY;MAC9B,kBAAkB,EAAEoB,CAAC,CAAClB,YAAY;MAClC,qBAAqB,EAAEkB,CAAC,CAACjB,eAAe;MACxC,gBAAgB,EAAEiB,CAAC,CAACd,MAAM;MAC1B,kBAAkB,EAAEc,CAAC,CAAChB,YAAY,EAAEC,cAAc,IAAI;KACvD,CAAC,CAAC;IAEH,MAAMgB,SAAS,GAAmBpC,IAAI,CAACqC,KAAK,CAACC,aAAa,CAACL,UAAU,CAAC;IACtE,MAAMM,QAAQ,GAAkB;MAAEC,MAAM,EAAE;QAAE,cAAc,EAAEJ;MAAS,CAAE;MAAEK,UAAU,EAAE,CAAC,cAAc;IAAC,CAAE;IACvG,MAAMC,WAAW,GAAQ1C,IAAI,CAAC2C,KAAK,CAACJ,QAAQ,EAAE;MAAEK,QAAQ,EAAE,MAAM;MAAEhC,IAAI,EAAE;IAAO,CAAE,CAAC;IAElF,MAAMiC,IAAI,GAAS,IAAIC,IAAI,CAAC,CAACJ,WAAW,CAAC,EAAE;MACzC9B,IAAI,EAAE;KACP,CAAC;IACFT,SAAS,CAAC4C,MAAM,CAACF,IAAI,EAAE,0BAA0B,CAAC;EACpD;EAEA;EACAG,gBAAgBA,CAAA;IACd,IAAI,CAACrB,kBAAkB,CAACsB,kBAAkB,EAAE,CAACC,SAAS,CAAC;MACrDC,IAAI,EAAGN,IAAI,IAAI;QACb,IAAI,CAACjB,YAAY,GAAGiB,IAAI;QACxBO,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACzB,YAAY,CAAC,CAAC,CAAC;QAEtD;QACA,IAAI,CAAC0B,WAAW,EAAE;MACpB,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACbJ,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;MACnD;KACD,CAAC;EACJ;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACT,gBAAgB,EAAE;IACvBlD,OAAO,CAAC4D,OAAO,EAAE;IACjB,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzB,IAAI,CAACC,aAAa,GAAG,IAAI7D,KAAK,CAAC,IAAI,CAAC4D,gBAAgB,CAACE,aAAa,CAAC;;EAEvE;EAEA;EACAP,WAAWA,CAAA;IACT,MAAMQ,GAAG,GAAG,IAAI,CAACC,UAAU,CAACF,aAAa,CAACG,UAAU,CAAC,IAAI,CAAC;IAE1D;IACA,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;;IAG9B,MAAMC,MAAM,GAAG,IAAI,CAACvC,YAAY,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvB,IAAI,CAAC;IACjD,MAAMiC,IAAI,GAAG,IAAI,CAACjB,YAAY,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACrB,QAAQ,CAAC;IAEnD;IACA,IAAI,CAACmD,aAAa,GAAG,IAAIhE,KAAK,CAAC6D,GAAG,EAAE;MAClClD,IAAI,EAAE,KAAK;MACXiC,IAAI,EAAE;QACJsB,MAAM,EAAEA,MAAM;QACdC,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,iCAAiC;UACxCxB,IAAI,EAAEA,IAAI;UACVyB,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE;SACd;OACF;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE;UACNC,CAAC,EAAE;YACDC,WAAW,EAAE;;;;KAIpB,CAAC;EACJ;EAEA;EACAC,cAAcA,CAACC,KAAU;IACvB,IAAI,CAACC,YAAY,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACzC,IAAI,IAAI,CAACF,YAAY,EAAE;MACrB,IAAI,CAACG,WAAW,EAAE;;EAEtB;EAEA;EACAA,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACH,YAAY,EAAE;MACtB,IAAI,CAACI,SAAS,CAAC,yCAAyC,EAAE,KAAK,CAAC;MAChE;;IAGF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAACP,YAAY,CAAC;IAE1C,IAAI,CAACtD,IAAI,CAAC8D,IAAI,CAAC,gDAAgD,EAAEH,QAAQ,EAAE;MACzEI,YAAY,EAAE;KACf,CAAC,CAACvC,SAAS,CAAC;MACXC,IAAI,EAAGuC,QAAQ,IAAI;QACjB,IAAI,CAACN,SAAS,CAAC,+BAA+B,GAAGM,QAAQ,EAAE,IAAI,CAAC;MAClE,CAAC;MACDnC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC6B,SAAS,CAAC,sCAAsC,GAAG7B,KAAK,CAACA,KAAK,EAAE,KAAK,CAAC;MAC7E;KACD,CAAC;EACJ;EAEA;EACA6B,SAASA,CAACO,OAAe,EAAEC,OAAgB;IACzC,IAAI,CAAC/D,YAAY,GAAG8D,OAAO;IAC3B,IAAI,CAAC7D,SAAS,GAAG8D,OAAO;IACxB,IAAI,CAAChC,aAAa,CAACiC,IAAI,EAAE;EAC3B;EAEA;EACAC,UAAUA,CAACf,KAAgB;IACzBA,KAAK,CAACgB,cAAc,EAAE;IACtBhB,KAAK,CAACiB,eAAe,EAAE;EACzB;EAEAC,aAAaA,CAAClB,KAAgB;IAC5BA,KAAK,CAACgB,cAAc,EAAE;IACtBhB,KAAK,CAACiB,eAAe,EAAE;IACvB,IAAIjB,KAAK,CAACmB,YAAY,IAAInB,KAAK,CAACmB,YAAY,CAAChB,KAAK,CAACiB,MAAM,GAAG,CAAC,EAAE;MAC7D,IAAI,CAACnB,YAAY,GAAGD,KAAK,CAACmB,YAAY,CAAChB,KAAK,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACC,WAAW,EAAE;;EAEtB;EAEA;EACAiB,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChClD,OAAO,CAACC,GAAG,CAACgD,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC9E,MAAM,CAAC+E,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBApKWlF,qBAAqB,EAAAlB,EAAA,CAAAqG,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAvG,EAAA,CAAAqG,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAzG,EAAA,CAAAqG,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAA3G,EAAA,CAAAqG,iBAAA,CAAAO,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAArB3F,qBAAqB;MAAA4F,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAvD,GAAA;QAAA,IAAAuD,EAAA;;;;;;;;;;;;;;;UChBlCjH,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAkH,SAAA,cAA0F;UAY1FlH,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAE,MAAA,wCAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE/CH,EAAA,CAAAkH,SAAA,gBAAmF;UASrFlH,EAAA,CAAAG,YAAA,EAAO;UAEPH,EAAA,CAAAC,cAAA,YAAM;UAIAD,EAAA,CAAAkH,SAAA,eAAwE;UACxElH,EAAA,CAAAE,MAAA,aACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAmH,UAAA,mBAAAC,mDAAA;YAAA,OAAS1D,GAAA,CAAAsC,MAAA,EAAQ;UAAA,EAAC;UAAChG,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKzDH,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAAkH,SAAA,gBAAiC;UACjClH,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGpDH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAkH,SAAA,gBAAiC;UACjClH,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAkH,SAAA,gBAAiC;UACjClH,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGRH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAkH,SAAA,gBAAwC;UACxClH,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAkH,SAAA,gBAAwC;UACzClH,EAAA,CAAAE,MAAA,6BACD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAkH,SAAA,gBAAkC;UACnClH,EAAA,CAAAE,MAAA,kCACD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAQZH,EAAA,CAAAC,cAAA,gBAAkE;UAE5BD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE/DH,EAAA,CAAAC,cAAA,eAAsC;UAK3BD,EAAA,CAAAmH,UAAA,sBAAAE,0DAAAC,MAAA;YAAA,OAAY5D,GAAA,CAAAgC,UAAA,CAAA4B,MAAA,CAAkB;UAAA,EAAC,kBAAAC,sDAAAD,MAAA;YAAA,OACvB5D,GAAA,CAAAmC,aAAA,CAAAyB,MAAA,CAAqB;UAAA,EADE;UAGpCtH,EAAA,CAAAkH,SAAA,aAAuC;UAAClH,EAAA,CAAAE,MAAA,kBAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,iBAEgB;UADTD,EAAA,CAAAmH,UAAA,oBAAAK,wDAAAF,MAAA;YAAA,OAAU5D,GAAA,CAAAgB,cAAA,CAAA4C,MAAA,CAAsB;UAAA,EAAC;UADxCtH,EAAA,CAAAG,YAAA,EAEgB;UAGhBH,EAAA,CAAAC,cAAA,kBAAmG;UAA1BD,EAAA,CAAAmH,UAAA,mBAAAM,wDAAA;YAAA,OAAS/D,GAAA,CAAA9B,aAAA,EAAe;UAAA,EAAC;UAChG5B,EAAA,CAAAkH,SAAA,aAAuC;UAAClH,EAAA,CAAAE,MAAA,kBAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKfH,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAE,MAAA,sBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACbH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpBH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA0H,UAAA,KAAAC,oCAAA,mBAUK;UACP3H,EAAA,CAAAG,YAAA,EAAQ;UAGZH,EAAA,CAAAC,cAAA,eAAkB;UAChBD,EAAA,CAAAkH,SAAA,sBAAmD;UACrDlH,EAAA,CAAAG,YAAA,EAAM;UAWhBH,EAAA,CAAAC,cAAA,mBAAwE;UAI7BD,EAAA,CAAAE,MAAA,KAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7EH,EAAA,CAAAkH,SAAA,mBAA4F;UAC9FlH,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAwB;UACnBD,EAAA,CAAAE,MAAA,KAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UAnCEH,EAAA,CAAAI,SAAA,IAAe;UAAfJ,EAAA,CAAA4H,UAAA,YAAAlE,GAAA,CAAAlC,YAAA,CAAe;UA8BvCxB,EAAA,CAAAI,SAAA,GAAwF;UAAxFJ,EAAA,CAAA4H,UAAA,YAAA5H,EAAA,CAAA6H,eAAA,IAAAC,GAAA,EAAApE,GAAA,CAAAhC,SAAA,GAAAgC,GAAA,CAAAhC,SAAA,EAAwF;UACxD1B,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAK,iBAAA,CAAAqD,GAAA,CAAAhC,SAAA,4BAAqC;UAIrE1B,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAK,iBAAA,CAAAqD,GAAA,CAAAjC,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}