spring.application.name=User


spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.hibernate.ddl-auto=update


spring.jpa.show-sql=true



server.port=8060

spring.config.import=optional:configserver:http://localhost:8888


eureka.client.service-url.defaultZone=http://localhost:8761/eureka/


management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always



spring.datasource.url=*************************************
spring.datasource.username=C##rayen
spring.datasource.password=rayouna123
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver




spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB



application.config.actionnaire-url=http://localhost:8020/GRA/Actionnaire
