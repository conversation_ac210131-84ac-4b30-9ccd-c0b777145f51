package com.Rayen.User.Config;



import com.Rayen.User.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

@Configuration
@RequiredArgsConstructor

public class ApplicationConfig {

    private final UserRepository UserRepository;


    @Bean
    public UserDetailsService userDetailsService() {// we need to get the user from the database
        return username -> UserRepository.findByEmail(username).orElseThrow(() -> new UsernameNotFoundException("User not found"));
    }

}