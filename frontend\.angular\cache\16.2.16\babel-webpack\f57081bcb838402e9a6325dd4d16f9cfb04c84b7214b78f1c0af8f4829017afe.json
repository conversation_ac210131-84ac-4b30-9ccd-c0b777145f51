{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../authentication.service\";\nimport * as i2 from \"@angular/router\";\nexport class SignupComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.firstName = '';\n    this.lastName = '';\n    this.email = '';\n    this.password = '';\n  }\n  onRegister() {\n    this.authService.register(this.firstName, this.lastName, this.email, this.password).subscribe(response => {\n      this.authService.storeToken(response.token); // Store the JWT token\n      this.router.navigate(['/login']); // Redirect to the dashboard\n    });\n  }\n\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 44,\n      vars: 4,\n      consts: [[1, \"container\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"vh-100\"], [1, \"card\", \"p-4\", \"shadow-lg\", \"rounded\", 2, \"width\", \"400px\"], [1, \"text-center\"], [1, \"mb-3\"], [1, \"text-muted\"], [3, \"ngSubmit\"], [\"for\", \"firstName\", 1, \"form-label\", \"fw-bold\"], [1, \"input-group\"], [1, \"input-group-text\"], [1, \"bi\", \"bi-person\"], [\"type\", \"text\", \"id\", \"firstName\", \"name\", \"firstName\", \"required\", \"\", \"placeholder\", \"Enter your first name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"lastName\", 1, \"form-label\", \"fw-bold\"], [\"type\", \"text\", \"id\", \"lastName\", \"name\", \"lastName\", \"required\", \"\", \"placeholder\", \"Enter your last name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\", 1, \"form-label\", \"fw-bold\"], [1, \"bi\", \"bi-envelope\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"placeholder\", \"Enter your email\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\", 1, \"form-label\", \"fw-bold\"], [1, \"bi\", \"bi-lock\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\", \"w-100\", \"fw-bold\"], [1, \"bi\", \"bi-person-check\"], [1, \"text-center\", \"mt-3\"], [\"routerLink\", \"/login\", 1, \"text-decoration-none\", \"fw-bold\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵtext(4, \"Sign Up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Create a new account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onRegister();\n          });\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"label\", 6);\n          i0.ɵɵtext(10, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"span\", 8);\n          i0.ɵɵelement(13, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_14_listener($event) {\n            return ctx.firstName = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 3)(16, \"label\", 11);\n          i0.ɵɵtext(17, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 7)(19, \"span\", 8);\n          i0.ɵɵelement(20, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.lastName = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"div\", 3)(23, \"label\", 13);\n          i0.ɵɵtext(24, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 7)(26, \"span\", 8);\n          i0.ɵɵelement(27, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"input\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_28_listener($event) {\n            return ctx.email = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 3)(30, \"label\", 16);\n          i0.ɵɵtext(31, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 7)(33, \"span\", 8);\n          i0.ɵɵelement(34, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"input\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_35_listener($event) {\n            return ctx.password = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"button\", 19);\n          i0.ɵɵelement(37, \"i\", 20);\n          i0.ɵɵtext(38, \" Register \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 21)(40, \"p\");\n          i0.ɵɵtext(41, \"Already have an account? \");\n          i0.ɵɵelementStart(42, \"a\", 22);\n          i0.ɵɵtext(43, \"Login\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.firstName);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.lastName);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.email);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.password);\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SignupComponent", "constructor", "authService", "router", "firstName", "lastName", "email", "password", "onRegister", "register", "subscribe", "response", "storeToken", "token", "navigate", "i0", "ɵɵdirectiveInject", "i1", "AuthenticationService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_7_listener", "ɵɵelement", "SignupComponent_Template_input_ngModelChange_14_listener", "$event", "SignupComponent_Template_input_ngModelChange_21_listener", "SignupComponent_Template_input_ngModelChange_28_listener", "SignupComponent_Template_input_ngModelChange_35_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\signup\\signup.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { AuthenticationService } from '../authentication.service';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-signup',\n  templateUrl: './signup.component.html',\n  styleUrls: ['./signup.component.css']\n})\nexport class SignupComponent {\n  firstName = '';\n  lastName = '';\n  email = '';\n  password = '';\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  onRegister() {\n    this.authService.register(this.firstName, this.lastName, this.email, this.password).subscribe(response => {\n      this.authService.storeToken(response.token);  // Store the JWT token\n      this.router.navigate(['/login']);  // Redirect to the dashboard\n    });\n  }\n}\n", "<div class=\"container d-flex justify-content-center align-items-center vh-100\">\n  <div class=\"card p-4 shadow-lg rounded\" style=\"width: 400px;\">\n    <div class=\"text-center\">\n      <h2 class=\"mb-3\">Sign Up</h2>\n      <p class=\"text-muted\">Create a new account</p>\n    </div>\n\n    <form (ngSubmit)=\"onRegister()\">\n      <!-- First Name Field -->\n      <div class=\"mb-3\">\n        <label for=\"firstName\" class=\"form-label fw-bold\">First Name</label>\n        <div class=\"input-group\">\n          <span class=\"input-group-text\"><i class=\"bi bi-person\"></i></span>\n          <input type=\"text\" id=\"firstName\" class=\"form-control\" [(ngModel)]=\"firstName\" name=\"firstName\" required placeholder=\"Enter your first name\" />\n        </div>\n      </div>\n\n      <!-- Last Name Field -->\n      <div class=\"mb-3\">\n        <label for=\"lastName\" class=\"form-label fw-bold\">Last Name</label>\n        <div class=\"input-group\">\n          <span class=\"input-group-text\"><i class=\"bi bi-person\"></i></span>\n          <input type=\"text\" id=\"lastName\" class=\"form-control\" [(ngModel)]=\"lastName\" name=\"lastName\" required placeholder=\"Enter your last name\" />\n        </div>\n      </div>\n\n      <!-- Email Field -->\n      <div class=\"mb-3\">\n        <label for=\"email\" class=\"form-label fw-bold\">Email</label>\n        <div class=\"input-group\">\n          <span class=\"input-group-text\"><i class=\"bi bi-envelope\"></i></span>\n          <input type=\"email\" id=\"email\" class=\"form-control\" [(ngModel)]=\"email\" name=\"email\" required placeholder=\"Enter your email\" />\n        </div>\n      </div>\n\n      <!-- Password Field -->\n      <div class=\"mb-3\">\n        <label for=\"password\" class=\"form-label fw-bold\">Password</label>\n        <div class=\"input-group\">\n          <span class=\"input-group-text\"><i class=\"bi bi-lock\"></i></span>\n          <input type=\"password\" id=\"password\" class=\"form-control\" [(ngModel)]=\"password\" name=\"password\" required placeholder=\"Enter your password\" />\n        </div>\n      </div>\n\n      <!-- Register Button -->\n      <button type=\"submit\" class=\"btn btn-success w-100 fw-bold\">\n        <i class=\"bi bi-person-check\"></i> Register\n      </button>\n\n      <!-- Login Link -->\n      <div class=\"text-center mt-3\">\n        <p>Already have an account? <a routerLink=\"/login\" class=\"text-decoration-none fw-bold\">Login</a></p>\n      </div>\n    </form>\n  </div>\n</div>\n"], "mappings": ";;;AASA,OAAM,MAAOA,eAAe;EAM1BC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;IALtE,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,QAAQ,GAAG,EAAE;EAEoE;EAEjFC,UAAUA,CAAA;IACR,IAAI,CAACN,WAAW,CAACO,QAAQ,CAAC,IAAI,CAACL,SAAS,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACC,QAAQ,CAAC,CAACG,SAAS,CAACC,QAAQ,IAAG;MACvG,IAAI,CAACT,WAAW,CAACU,UAAU,CAACD,QAAQ,CAACE,KAAK,CAAC,CAAC,CAAE;MAC9C,IAAI,CAACV,MAAM,CAACW,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAE;IACrC,CAAC,CAAC;EACJ;;;;uBAbWd,eAAe,EAAAe,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfpB,eAAe;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT5BZ,EAAA,CAAAc,cAAA,aAA+E;UAGxDd,EAAA,CAAAe,MAAA,cAAO;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UAC7BhB,EAAA,CAAAc,cAAA,WAAsB;UAAAd,EAAA,CAAAe,MAAA,2BAAoB;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UAGhDhB,EAAA,CAAAc,cAAA,cAAgC;UAA1Bd,EAAA,CAAAiB,UAAA,sBAAAC,kDAAA;YAAA,OAAYL,GAAA,CAAApB,UAAA,EAAY;UAAA,EAAC;UAE7BO,EAAA,CAAAc,cAAA,aAAkB;UACkCd,EAAA,CAAAe,MAAA,kBAAU;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UACpEhB,EAAA,CAAAc,cAAA,cAAyB;UACQd,EAAA,CAAAmB,SAAA,YAA4B;UAAAnB,EAAA,CAAAgB,YAAA,EAAO;UAClEhB,EAAA,CAAAc,cAAA,iBAA+I;UAAxFd,EAAA,CAAAiB,UAAA,2BAAAG,yDAAAC,MAAA;YAAA,OAAAR,GAAA,CAAAxB,SAAA,GAAAgC,MAAA;UAAA,EAAuB;UAA9ErB,EAAA,CAAAgB,YAAA,EAA+I;UAKnJhB,EAAA,CAAAc,cAAA,cAAkB;UACiCd,EAAA,CAAAe,MAAA,iBAAS;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UAClEhB,EAAA,CAAAc,cAAA,cAAyB;UACQd,EAAA,CAAAmB,SAAA,YAA4B;UAAAnB,EAAA,CAAAgB,YAAA,EAAO;UAClEhB,EAAA,CAAAc,cAAA,iBAA2I;UAArFd,EAAA,CAAAiB,UAAA,2BAAAK,yDAAAD,MAAA;YAAA,OAAAR,GAAA,CAAAvB,QAAA,GAAA+B,MAAA;UAAA,EAAsB;UAA5ErB,EAAA,CAAAgB,YAAA,EAA2I;UAK/IhB,EAAA,CAAAc,cAAA,cAAkB;UAC8Bd,EAAA,CAAAe,MAAA,aAAK;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UAC3DhB,EAAA,CAAAc,cAAA,cAAyB;UACQd,EAAA,CAAAmB,SAAA,aAA8B;UAAAnB,EAAA,CAAAgB,YAAA,EAAO;UACpEhB,EAAA,CAAAc,cAAA,iBAA+H;UAA3Ed,EAAA,CAAAiB,UAAA,2BAAAM,yDAAAF,MAAA;YAAA,OAAAR,GAAA,CAAAtB,KAAA,GAAA8B,MAAA;UAAA,EAAmB;UAAvErB,EAAA,CAAAgB,YAAA,EAA+H;UAKnIhB,EAAA,CAAAc,cAAA,cAAkB;UACiCd,EAAA,CAAAe,MAAA,gBAAQ;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UACjEhB,EAAA,CAAAc,cAAA,cAAyB;UACQd,EAAA,CAAAmB,SAAA,aAA0B;UAAAnB,EAAA,CAAAgB,YAAA,EAAO;UAChEhB,EAAA,CAAAc,cAAA,iBAA8I;UAApFd,EAAA,CAAAiB,UAAA,2BAAAO,yDAAAH,MAAA;YAAA,OAAAR,GAAA,CAAArB,QAAA,GAAA6B,MAAA;UAAA,EAAsB;UAAhFrB,EAAA,CAAAgB,YAAA,EAA8I;UAKlJhB,EAAA,CAAAc,cAAA,kBAA4D;UAC1Dd,EAAA,CAAAmB,SAAA,aAAkC;UAACnB,EAAA,CAAAe,MAAA,kBACrC;UAAAf,EAAA,CAAAgB,YAAA,EAAS;UAGThB,EAAA,CAAAc,cAAA,eAA8B;UACzBd,EAAA,CAAAe,MAAA,iCAAyB;UAAAf,EAAA,CAAAc,cAAA,aAA4D;UAAAd,EAAA,CAAAe,MAAA,aAAK;UAAAf,EAAA,CAAAgB,YAAA,EAAI;;;UAtCxChB,EAAA,CAAAyB,SAAA,IAAuB;UAAvBzB,EAAA,CAAA0B,UAAA,YAAAb,GAAA,CAAAxB,SAAA,CAAuB;UASxBW,EAAA,CAAAyB,SAAA,GAAsB;UAAtBzB,EAAA,CAAA0B,UAAA,YAAAb,GAAA,CAAAvB,QAAA,CAAsB;UASxBU,EAAA,CAAAyB,SAAA,GAAmB;UAAnBzB,EAAA,CAAA0B,UAAA,YAAAb,GAAA,CAAAtB,KAAA,CAAmB;UASbS,EAAA,CAAAyB,SAAA,GAAsB;UAAtBzB,EAAA,CAAA0B,UAAA,YAAAb,GAAA,CAAArB,QAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}