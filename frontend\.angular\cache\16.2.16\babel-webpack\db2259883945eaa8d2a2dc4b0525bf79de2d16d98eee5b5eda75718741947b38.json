{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/groupe.service\";\nimport * as i3 from \"../services/ressource.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction ModifyGroupComponent_form_4_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1, \" Group name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModifyGroupComponent_form_4_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"input\", 16);\n    i0.ɵɵlistener(\"change\", function ModifyGroupComponent_form_4_div_10_Template_input_change_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const ressource_r4 = restoredCtx.$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onCheckboxChange($event, ressource_r4.idRessource));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 17);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ressource_r4 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ressource_r4.idRessource)(\"checked\", ctx_r3.selectedRessources.includes(ressource_r4.idRessource));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ressource_r4.nomRessource);\n  }\n}\nfunction ModifyGroupComponent_form_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 3);\n    i0.ɵɵlistener(\"ngSubmit\", function ModifyGroupComponent_form_4_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.updateGroupe());\n    });\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"label\", 5);\n    i0.ɵɵtext(3, \"Nom du groupe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 6, 7);\n    i0.ɵɵlistener(\"ngModelChange\", function ModifyGroupComponent_form_4_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.groupe.nomGroupe = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ModifyGroupComponent_form_4_div_6_Template, 2, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 4)(8, \"label\", 9);\n    i0.ɵɵtext(9, \"Ressources\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ModifyGroupComponent_form_4_div_10_Template, 4, 3, \"div\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 11)(12, \"button\", 12);\n    i0.ɵɵtext(13, \"Mettre \\u00E0 jour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ModifyGroupComponent_form_4_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.cancel());\n    });\n    i0.ɵɵtext(15, \"Annuler\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r1 = i0.ɵɵreference(5);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.groupe.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", _r1.invalid && _r1.touched);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.allRessources);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", _r1.invalid);\n  }\n}\nexport class ModifyGroupComponent {\n  constructor(route, groupeService, ressourceService,\n  // Inject ressource service\n  router) {\n    this.route = route;\n    this.groupeService = groupeService;\n    this.ressourceService = ressourceService;\n    this.router = router;\n    this.groupeId = 0;\n    this.groupe = {\n      idGroupe: 0,\n      nomGroupe: '',\n      ressources: []\n    }; // Include ressources in group\n    this.allRessources = []; // Store all ressources\n    this.selectedRessources = []; // Store selected ressources' ids\n    this.loading = true;\n  }\n  ngOnInit() {\n    this.groupeId = Number(this.route.snapshot.paramMap.get('id'));\n    this.getGroupe(this.groupeId);\n    this.getAllRessources(); // Fetch all ressources\n  }\n\n  getGroupe(id) {\n    this.groupeService.getGroupeById(id).subscribe(data => {\n      this.groupe = data;\n      // Assuming ressources is an array of numbers (IDs)\n      this.selectedRessources = data.ressources ? [...data.ressources] : [];\n      this.loading = false;\n    }, err => {\n      console.error('Error loading group data', err);\n      this.loading = false;\n    });\n  }\n  // Fetch all ressources\n  getAllRessources() {\n    this.ressourceService.getAllRessources().subscribe(data => {\n      this.allRessources = data;\n    }, err => {\n      console.error('Error loading ressources', err);\n    });\n  }\n  // Handle checkbox change to update selectedRessources array\n  onCheckboxChange(event, resourceId) {\n    const checked = event.target.checked;\n    if (checked) {\n      this.selectedRessources.push(resourceId);\n    } else {\n      this.selectedRessources = this.selectedRessources.filter(id => id !== resourceId);\n    }\n  }\n  // Update the group data\n  updateGroupe() {\n    const updatedGroupe = {\n      ...this.groupe,\n      ressources: this.selectedRessources // This should be an array of IDs\n    };\n\n    this.groupeService.updateGroupe(this.groupeId, updatedGroupe).subscribe(response => {\n      console.log(\"Response:\", response);\n      this.router.navigate(['/groups']).then(() => {\n        console.log('Navigated to /groups successfully.');\n      }).catch(error => {\n        console.error('Navigation error:', error);\n      });\n    }, error => {\n      console.error('Error updating group:', error);\n    });\n  }\n  // Handle cancel action\n  cancel() {\n    this.router.navigate(['/groups']);\n  }\n  static {\n    this.ɵfac = function ModifyGroupComponent_Factory(t) {\n      return new (t || ModifyGroupComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.GroupeService), i0.ɵɵdirectiveInject(i3.RessourceService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModifyGroupComponent,\n      selectors: [[\"app-modify-group\"]],\n      decls: 5,\n      vars: 1,\n      consts: [[1, \"container\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"100vh\"], [1, \"form-container\"], [3, \"ngSubmit\", 4, \"ngIf\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"nomGroupe\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", \"placeholder\", \"Enter group name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"nomGroupe\", \"ngModel\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"form-label\"], [\"class\", \"form-check form-check-inline\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-between\", \"mt-4\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"text-danger\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"value\", \"checked\", \"change\"], [1, \"form-check-label\"]],\n      template: function ModifyGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Modifier le groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ModifyGroupComponent_form_4_Template, 16, 4, \"form\", 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupe);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm],\n      styles: [\"\\n\\nbody[_ngcontent-%COMP%] {\\n    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n    background-color: #0c2147; \\n\\n    color: #333;\\n    height: 100vh;\\n    margin: 0;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n  }\\n  \\n  \\n  \\n\\n  .form-container[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.95);\\n    padding: 60px;\\n    border-radius: 15px;\\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n    max-width: 500px;\\n    width: 100%;\\n  }\\n  \\n  \\n\\n  h2[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n    font-weight: 600;\\n    text-align: center;\\n    color: #333;\\n    margin-bottom: 30px;\\n  }\\n  \\n  \\n\\n  .form-group[_ngcontent-%COMP%] {\\n    margin-bottom: 25px;\\n  }\\n  \\n  .form-label[_ngcontent-%COMP%] {\\n    font-weight: 600;\\n    margin-bottom: 10px;\\n    display: block;\\n  }\\n  \\n  .form-control[_ngcontent-%COMP%] {\\n    border-radius: 10px;\\n    border: 1px solid #ddd;\\n    padding: 10px;\\n    font-size: 1rem;\\n    transition: all 0.3s ease;\\n    width: 100%;\\n  }\\n  \\n  .form-control[_ngcontent-%COMP%]:focus {\\n    border-color: #007bff;\\n    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);\\n  }\\n  \\n  \\n\\n  .form-check-inline[_ngcontent-%COMP%] {\\n    margin-right: 15px;\\n    display: inline-block;\\n  }\\n  \\n  .form-check-input[_ngcontent-%COMP%] {\\n    margin-right: 6px;\\n  }\\n  \\n  \\n\\n  .text-danger[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    color: #e74c3c;\\n  }\\n  \\n  \\n\\n  .btn-primary[_ngcontent-%COMP%] {\\n    background-color: #007bff;\\n    border: none;\\n    padding: 12px 30px;\\n    font-size: 1rem;\\n    border-radius: 25px;\\n    transition: background-color 0.3s;\\n    color: white;\\n  }\\n  \\n  .btn-primary[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n  }\\n  \\n  .btn-secondary[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    border: none;\\n    padding: 12px 30px;\\n    font-size: 1rem;\\n    border-radius: 25px;\\n    transition: background-color 0.3s;\\n    color: white;\\n  }\\n  \\n  .btn-secondary[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ModifyGroupComponent_form_4_div_10_Template_input_change_1_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r6", "ressource_r4", "$implicit", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "onCheckboxChange", "idRessource", "ɵɵadvance", "ɵɵproperty", "ctx_r3", "selectedRessources", "includes", "ɵɵtextInterpolate", "nomRessource", "ModifyGroupComponent_form_4_Template_form_ngSubmit_0_listener", "_r8", "ctx_r7", "updateGroupe", "ModifyGroupComponent_form_4_Template_input_ngModelChange_4_listener", "ctx_r9", "groupe", "nomGroupe", "ɵɵtemplate", "ModifyGroupComponent_form_4_div_6_Template", "ModifyGroupComponent_form_4_div_10_Template", "ModifyGroupComponent_form_4_Template_button_click_14_listener", "ctx_r10", "cancel", "ctx_r0", "_r1", "invalid", "touched", "allRessources", "ModifyGroupComponent", "constructor", "route", "groupeService", "ressourceService", "router", "groupeId", "idGroupe", "ressources", "loading", "ngOnInit", "Number", "snapshot", "paramMap", "get", "getGroupe", "getAllRessources", "id", "getGroupeById", "subscribe", "data", "err", "console", "error", "event", "resourceId", "checked", "target", "push", "filter", "updatedGroupe", "response", "log", "navigate", "then", "catch", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "GroupeService", "i3", "RessourceService", "Router", "selectors", "decls", "vars", "consts", "template", "ModifyGroupComponent_Template", "rf", "ctx", "ModifyGroupComponent_form_4_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\modify-group\\modify-group.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\modify-group\\modify-group.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { GroupeService } from '../services/groupe.service';\nimport { RessourceService } from '../services/ressource.service';  // Service to fetch ressources\nimport { Groupe } from '../model/groupe.model';\nimport { Ressource } from '../model/ressource.model';  // Assuming you have this model\n\n@Component({\n  selector: 'app-modify-group',\n  templateUrl: './modify-group.component.html',\n  styleUrls: ['./modify-group.component.css']\n})\nexport class ModifyGroupComponent implements OnInit {\n  groupeId: number = 0; \n  groupe: Groupe = { idGroupe: 0, nomGroupe: '', ressources: [] }; // Include ressources in group\n  allRessources: Ressource[] = [];  // Store all ressources\n  selectedRessources: number[] = [];  // Store selected ressources' ids\n  loading: boolean = true;\n\n  constructor(\n    private route: ActivatedRoute,\n    private groupeService: GroupeService,\n    private ressourceService: RessourceService,  // Inject ressource service\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.groupeId = Number(this.route.snapshot.paramMap.get('id'));\n    this.getGroupe(this.groupeId);\n    this.getAllRessources();  // Fetch all ressources\n  }\n\n  getGroupe(id: number): void {\n    this.groupeService.getGroupeById(id).subscribe(\n      (data: Groupe) => {\n        this.groupe = data;\n        // Assuming ressources is an array of numbers (IDs)\n        this.selectedRessources = data.ressources ? [...data.ressources] : [];\n        this.loading = false;\n      },\n      (err) => {\n        console.error('Error loading group data', err);\n        this.loading = false;\n      }\n    );\n  }\n\n  // Fetch all ressources\n  getAllRessources(): void {\n    this.ressourceService.getAllRessources().subscribe(\n      (data: Ressource[]) => {\n        this.allRessources = data;\n      },\n      (err) => {\n        console.error('Error loading ressources', err);\n      }\n    );\n  }\n\n  // Handle checkbox change to update selectedRessources array\n  onCheckboxChange(event: Event, resourceId: number): void {\n    const checked = (event.target as HTMLInputElement).checked;\n    if (checked) {\n      this.selectedRessources.push(resourceId);\n    } else {\n      this.selectedRessources = this.selectedRessources.filter(id => id !== resourceId);\n    }\n  }\n\n  // Update the group data\n  updateGroupe(): void {\n    const updatedGroupe = {\n      ...this.groupe,\n      ressources: this.selectedRessources // This should be an array of IDs\n    };\n\n    this.groupeService.updateGroupe(this.groupeId, updatedGroupe).subscribe(\n      (response) => {\n        console.log(\"Response:\", response);\n        this.router.navigate(['/groups']).then(() => {\n          console.log('Navigated to /groups successfully.');\n        }).catch((error) => {\n          console.error('Navigation error:', error);\n        });\n      },\n      (error) => {\n        console.error('Error updating group:', error);\n      }\n    );\n  }\n\n  // Handle cancel action\n  cancel(): void {\n    this.router.navigate(['/groups']);\n  }\n}\n", "<div class=\"container d-flex justify-content-center align-items-center\" style=\"height: 100vh;\">\n  <div class=\"form-container\">\n    <h2>Modifier le groupe</h2>\n\n    <form (ngSubmit)=\"updateGroupe()\" *ngIf=\"groupe\">\n      <!-- Group Name -->\n      <div class=\"form-group\">\n        <label for=\"nomGroupe\" class=\"form-label\">Nom du groupe</label>\n        <input\n          type=\"text\"\n          id=\"nomGroupe\"\n          [(ngModel)]=\"groupe.nomGroupe\"\n          name=\"nomGroupe\"\n          class=\"form-control\"\n          required\n          placeholder=\"Enter group name\"\n          #nomGroupe=\"ngModel\"\n        />\n        <div *ngIf=\"nomGroupe.invalid && nomGroupe.touched\" class=\"text-danger\">\n          Group name is required.\n        </div>\n      </div>\n\n      <!-- Ressources checkboxes -->\n      <div class=\"form-group\">\n        <label class=\"form-label\">Ressources</label>\n        <div *ngFor=\"let ressource of allRessources\" class=\"form-check form-check-inline\">\n          <input\n            type=\"checkbox\"\n            class=\"form-check-input\"\n            [value]=\"ressource.idRessource\"\n            [checked]=\"selectedRessources.includes(ressource.idRessource)\"\n            (change)=\"onCheckboxChange($event, ressource.idRessource)\" \n          />\n          <label class=\"form-check-label\">{{ ressource.nomRessource }}</label>\n        </div>\n      </div>\n\n      <!-- Submit and Cancel buttons -->\n      <div class=\"d-flex justify-content-between mt-4\">\n        <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"nomGroupe.invalid\">Mettre à jour</button>\n        <button type=\"button\" class=\"btn btn-secondary\" (click)=\"cancel()\">Annuler</button>\n      </div>\n    </form>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;ICkBQA,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMNH,EAAA,CAAAC,cAAA,cAAkF;IAM9ED,EAAA,CAAAI,UAAA,oBAAAC,oEAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,YAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAUb,EAAA,CAAAc,WAAA,CAAAF,MAAA,CAAAG,gBAAA,CAAAT,MAAA,EAAAI,YAAA,CAAAM,WAAA,CAA+C;IAAA,EAAC;IAL5DhB,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAJlEH,EAAA,CAAAiB,SAAA,GAA+B;IAA/BjB,EAAA,CAAAkB,UAAA,UAAAR,YAAA,CAAAM,WAAA,CAA+B,YAAAG,MAAA,CAAAC,kBAAA,CAAAC,QAAA,CAAAX,YAAA,CAAAM,WAAA;IAIDhB,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAsB,iBAAA,CAAAZ,YAAA,CAAAa,YAAA,CAA4B;;;;;;IA9BlEvB,EAAA,CAAAC,cAAA,cAAiD;IAA3CD,EAAA,CAAAI,UAAA,sBAAAoB,8DAAA;MAAAxB,EAAA,CAAAQ,aAAA,CAAAiB,GAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAAa,aAAA;MAAA,OAAYb,EAAA,CAAAc,WAAA,CAAAY,MAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAE/B3B,EAAA,CAAAC,cAAA,aAAwB;IACoBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,kBASE;IANAD,EAAA,CAAAI,UAAA,2BAAAwB,oEAAAtB,MAAA;MAAAN,EAAA,CAAAQ,aAAA,CAAAiB,GAAA;MAAA,MAAAI,MAAA,GAAA7B,EAAA,CAAAa,aAAA;MAAA,OAAab,EAAA,CAAAc,WAAA,CAAAe,MAAA,CAAAC,MAAA,CAAAC,SAAA,GAAAzB,MAAA,CACjB;IAAA,EADkC;IAHhCN,EAAA,CAAAG,YAAA,EASE;IACFH,EAAA,CAAAgC,UAAA,IAAAC,0CAAA,iBAEM;IACRjC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,aAAwB;IACID,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5CH,EAAA,CAAAgC,UAAA,KAAAE,2CAAA,kBASM;IACRlC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAiD;IAC8BD,EAAA,CAAAE,MAAA,0BAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnGH,EAAA,CAAAC,cAAA,kBAAmE;IAAnBD,EAAA,CAAAI,UAAA,mBAAA+B,8DAAA;MAAAnC,EAAA,CAAAQ,aAAA,CAAAiB,GAAA;MAAA,MAAAW,OAAA,GAAApC,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAsB,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAACrC,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA9BjFH,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAkB,UAAA,YAAAoB,MAAA,CAAAR,MAAA,CAAAC,SAAA,CAA8B;IAO1B/B,EAAA,CAAAiB,SAAA,GAA4C;IAA5CjB,EAAA,CAAAkB,UAAA,SAAAqB,GAAA,CAAAC,OAAA,IAAAD,GAAA,CAAAE,OAAA,CAA4C;IAQvBzC,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,UAAA,YAAAoB,MAAA,CAAAI,aAAA,CAAgB;IAcG1C,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAkB,UAAA,aAAAqB,GAAA,CAAAC,OAAA,CAA8B;;;AD5BpF,OAAM,MAAOG,oBAAoB;EAO/BC,YACUC,KAAqB,EACrBC,aAA4B,EAC5BC,gBAAkC;EAAG;EACrCC,MAAc;IAHd,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,MAAM,GAANA,MAAM;IAVhB,KAAAC,QAAQ,GAAW,CAAC;IACpB,KAAAnB,MAAM,GAAW;MAAEoB,QAAQ,EAAE,CAAC;MAAEnB,SAAS,EAAE,EAAE;MAAEoB,UAAU,EAAE;IAAE,CAAE,CAAC,CAAC;IACjE,KAAAT,aAAa,GAAgB,EAAE,CAAC,CAAE;IAClC,KAAAtB,kBAAkB,GAAa,EAAE,CAAC,CAAE;IACpC,KAAAgC,OAAO,GAAY,IAAI;EAOpB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACJ,QAAQ,GAAGK,MAAM,CAAC,IAAI,CAACT,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9D,IAAI,CAACC,SAAS,CAAC,IAAI,CAACT,QAAQ,CAAC;IAC7B,IAAI,CAACU,gBAAgB,EAAE,CAAC,CAAE;EAC5B;;EAEAD,SAASA,CAACE,EAAU;IAClB,IAAI,CAACd,aAAa,CAACe,aAAa,CAACD,EAAE,CAAC,CAACE,SAAS,CAC3CC,IAAY,IAAI;MACf,IAAI,CAACjC,MAAM,GAAGiC,IAAI;MAClB;MACA,IAAI,CAAC3C,kBAAkB,GAAG2C,IAAI,CAACZ,UAAU,GAAG,CAAC,GAAGY,IAAI,CAACZ,UAAU,CAAC,GAAG,EAAE;MACrE,IAAI,CAACC,OAAO,GAAG,KAAK;IACtB,CAAC,EACAY,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;MAC9C,IAAI,CAACZ,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEA;EACAO,gBAAgBA,CAAA;IACd,IAAI,CAACZ,gBAAgB,CAACY,gBAAgB,EAAE,CAACG,SAAS,CAC/CC,IAAiB,IAAI;MACpB,IAAI,CAACrB,aAAa,GAAGqB,IAAI;IAC3B,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;IAChD,CAAC,CACF;EACH;EAEA;EACAjD,gBAAgBA,CAACoD,KAAY,EAAEC,UAAkB;IAC/C,MAAMC,OAAO,GAAIF,KAAK,CAACG,MAA2B,CAACD,OAAO;IAC1D,IAAIA,OAAO,EAAE;MACX,IAAI,CAACjD,kBAAkB,CAACmD,IAAI,CAACH,UAAU,CAAC;KACzC,MAAM;MACL,IAAI,CAAChD,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACoD,MAAM,CAACZ,EAAE,IAAIA,EAAE,KAAKQ,UAAU,CAAC;;EAErF;EAEA;EACAzC,YAAYA,CAAA;IACV,MAAM8C,aAAa,GAAG;MACpB,GAAG,IAAI,CAAC3C,MAAM;MACdqB,UAAU,EAAE,IAAI,CAAC/B,kBAAkB,CAAC;KACrC;;IAED,IAAI,CAAC0B,aAAa,CAACnB,YAAY,CAAC,IAAI,CAACsB,QAAQ,EAAEwB,aAAa,CAAC,CAACX,SAAS,CACpEY,QAAQ,IAAI;MACXT,OAAO,CAACU,GAAG,CAAC,WAAW,EAAED,QAAQ,CAAC;MAClC,IAAI,CAAC1B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,CAACC,IAAI,CAAC,MAAK;QAC1CZ,OAAO,CAACU,GAAG,CAAC,oCAAoC,CAAC;MACnD,CAAC,CAAC,CAACG,KAAK,CAAEZ,KAAK,IAAI;QACjBD,OAAO,CAACC,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC,EACAA,KAAK,IAAI;MACRD,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,CACF;EACH;EAEA;EACA7B,MAAMA,CAAA;IACJ,IAAI,CAACW,MAAM,CAAC4B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;;;uBAlFWjC,oBAAoB,EAAA3C,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjF,EAAA,CAAA+E,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAnF,EAAA,CAAA+E,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAArF,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAM,MAAA;IAAA;EAAA;;;YAApB3C,oBAAoB;MAAA4C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZjC7F,EAAA,CAAAC,cAAA,aAA+F;UAEvFD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE3BH,EAAA,CAAAgC,UAAA,IAAA+D,oCAAA,mBAuCO;UACT/F,EAAA,CAAAG,YAAA,EAAM;;;UAxC+BH,EAAA,CAAAiB,SAAA,GAAY;UAAZjB,EAAA,CAAAkB,UAAA,SAAA4E,GAAA,CAAAhE,MAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}