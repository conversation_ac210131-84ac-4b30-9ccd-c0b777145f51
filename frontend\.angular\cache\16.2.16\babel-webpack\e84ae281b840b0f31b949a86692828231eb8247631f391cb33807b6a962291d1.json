{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport * as feather from 'feather-icons'; // Import feather icons for UI\nimport { Chart, registerables } from 'chart.js'; // Import Chart.js and register components\nexport let HabilitationComponent = class HabilitationComponent {\n  constructor(responsableService,\n  // Use ResponsableService here\n  cdr, router) {\n    this.responsableService = responsableService;\n    this.cdr = cdr;\n    this.router = router;\n    this.utilisateurs = []; // Array to hold the list of users\n    this.errorMessage = ''; // For displaying error messages\n    this.isLoading = false; // Loading state indicator\n  }\n\n  ngOnInit() {\n    this.loadUsers(); // Load the users when the component is initialized\n  }\n  // Method to fetch the users\n  loadUsers() {\n    this.isLoading = true; // Show loading indicator\n    this.responsableService.getAllUsers().subscribe(data => {\n      this.utilisateurs = data; // Assign fetched data to the array\n      this.isLoading = false; // Hide loading indicator\n    }, error => {\n      this.errorMessage = 'Error fetching users'; // Show error if the API fails\n      console.error('Error fetching users:', error);\n      this.isLoading = false; // Hide loading indicator\n    });\n  }\n  // Method to handle deletion of a user\n  deleteUser(id) {\n    if (id === undefined) {\n      console.error(\"Error: User ID is undefined\");\n      return;\n    }\n    if (confirm('Are you sure you want to delete this user?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: response => {\n          console.log('Delete response:', response);\n          this.utilisateurs = this.utilisateurs.filter(user => user.id !== id); // Remove user from the list\n        },\n\n        error: error => {\n          console.error('Error deleting user:', error);\n        }\n      });\n    }\n  }\n  // Handle logout functionality\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  // Method to update user details\n  updateUser(id, updatedUser) {\n    if (id && updatedUser) {\n      this.responsableService.updateResponsable(id, updatedUser).subscribe(() => {\n        this.loadUsers(); // Reload the users after the update\n      }, error => {\n        console.error('Error updating user:', error);\n      });\n    }\n  }\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n    // Initialize Chart.js after the view is initialized\n    Chart.register(...registerables); // Register chart.js components for version 4+\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement;\n      // Initialize chart with sample data (you can customize this as needed)\n      const chart = new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n          datasets: [{\n            data: [15339, 21345, 18483, 24003, 23489, 24092, 12034],\n            tension: 0.4,\n            backgroundColor: 'rgba(0,123,255,0.2)',\n            borderColor: '#007bff',\n            borderWidth: 4,\n            pointBackgroundColor: '#007bff'\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            y: {\n              beginAtZero: true\n            }\n          }\n        }\n      });\n    }\n  }\n};\n__decorate([ViewChild('myChart')], HabilitationComponent.prototype, \"myChartRef\", void 0);\nHabilitationComponent = __decorate([Component({\n  selector: 'app-habilitation',\n  templateUrl: './habilitation.component.html',\n  styleUrls: ['../../dashboard.css']\n})], HabilitationComponent);", "map": {"version": 3, "names": ["Component", "ViewChild", "feather", "Chart", "registerables", "HabilitationComponent", "constructor", "responsableService", "cdr", "router", "utilisateurs", "errorMessage", "isLoading", "ngOnInit", "loadUsers", "getAllUsers", "subscribe", "data", "error", "console", "deleteUser", "id", "undefined", "confirm", "deleteResponsable", "next", "response", "log", "filter", "user", "logout", "localStorage", "removeItem", "getItem", "navigate", "updateUser", "updatedUser", "updateResponsable", "ngAfterViewInit", "replace", "register", "myChartRef", "ctx", "nativeElement", "chart", "type", "labels", "datasets", "tension", "backgroundColor", "borderColor", "borderWidth", "pointBackgroundColor", "options", "responsive", "maintainAspectRatio", "scales", "y", "beginAtZero", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\habilitation\\habilitation.component.ts"], "sourcesContent": ["import { Component, OnInit, ChangeDetectorRef, ViewChild, AfterViewInit, ElementRef } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';  // Use the correct service\nimport { User } from '../model/user.model'; // Import User model\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';  // Import feather icons for UI\nimport { Chart, registerables } from 'chart.js'; // Import Chart.js and register components\n\n@Component({\n  selector: 'app-habilitation',\n  templateUrl: './habilitation.component.html',\n  styleUrls: ['../../dashboard.css'],\n})\nexport class HabilitationComponent implements OnInit, AfterViewInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  utilisateurs: User[] = []; // Array to hold the list of users\n  errorMessage: string = ''; // For displaying error messages\n  isLoading: boolean = false;  // Loading state indicator\n  responsables: any;\n\n  constructor(\n    private responsableService: ResponsableService,  // Use ResponsableService here\n    private cdr: ChangeDetectorRef, \n    private router: Router \n  ) {}\n\n  ngOnInit(): void {\n    this.loadUsers();  // Load the users when the component is initialized\n  }\n\n  // Method to fetch the users\n  loadUsers() {\n    this.isLoading = true;  // Show loading indicator\n    this.responsableService.getAllUsers().subscribe(\n      (data) => {\n        this.utilisateurs = data;  // Assign fetched data to the array\n        this.isLoading = false;  // Hide loading indicator\n      },\n      (error) => {\n        this.errorMessage = 'Error fetching users';  // Show error if the API fails\n        console.error('Error fetching users:', error);\n        this.isLoading = false;  // Hide loading indicator\n      }\n    );\n  }\n\n  // Method to handle deletion of a user\n  deleteUser(id?: number) {\n    if (id === undefined) {\n      console.error(\"Error: User ID is undefined\");\n      return;\n    }\n    if (confirm('Are you sure you want to delete this user?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: (response) => {\n          console.log('Delete response:', response);\n          this.utilisateurs = this.utilisateurs.filter(user => user.id !== id);  // Remove user from the list\n        },\n        error: (error) => {\n          console.error('Error deleting user:', error);\n        }\n      });\n    }\n  }\n\n  // Handle logout functionality\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n\n  // Method to update user details\n  updateUser(id: number, updatedUser: User): void {\n    if (id && updatedUser) {\n      this.responsableService.updateResponsable(id, updatedUser).subscribe(\n        () => {\n          this.loadUsers();  // Reload the users after the update\n        },\n        (error) => {\n          console.error('Error updating user:', error);\n        }\n      );\n    }\n  }\n\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n\n    // Initialize Chart.js after the view is initialized\n    Chart.register(...registerables);  // Register chart.js components for version 4+\n\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement as HTMLCanvasElement;\n\n      // Initialize chart with sample data (you can customize this as needed)\n      const chart = new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n          datasets: [{\n            data: [15339, 21345, 18483, 24003, 23489, 24092, 12034],\n            tension: 0.4, // Replaces lineTension\n            backgroundColor: 'rgba(0,123,255,0.2)', // Light blue background\n            borderColor: '#007bff',\n            borderWidth: 4,\n            pointBackgroundColor: '#007bff',\n          }]\n        },\n        options: {\n          responsive: true, // Make the chart responsive\n          maintainAspectRatio: false, // Allow the chart to resize with container\n          scales: {\n            y: {\n              beginAtZero: true\n            }\n          }\n        }\n      });\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAA6BC,SAAS,QAAmC,eAAe;AAI1G,OAAO,KAAKC,OAAO,MAAM,eAAe,CAAC,CAAE;AAC3C,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU,CAAC,CAAC;AAO1C,WAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAOhCC,YACUC,kBAAsC;EAAG;EACzCC,GAAsB,EACtBC,MAAc;IAFd,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAC,YAAY,GAAW,EAAE,CAAC,CAAC;IAC3B,KAAAC,YAAY,GAAW,EAAE,CAAC,CAAC;IAC3B,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAE;EAO1B;;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE,CAAC,CAAE;EACrB;EAEA;EACAA,SAASA,CAAA;IACP,IAAI,CAACF,SAAS,GAAG,IAAI,CAAC,CAAE;IACxB,IAAI,CAACL,kBAAkB,CAACQ,WAAW,EAAE,CAACC,SAAS,CAC5CC,IAAI,IAAI;MACP,IAAI,CAACP,YAAY,GAAGO,IAAI,CAAC,CAAE;MAC3B,IAAI,CAACL,SAAS,GAAG,KAAK,CAAC,CAAE;IAC3B,CAAC,EACAM,KAAK,IAAI;MACR,IAAI,CAACP,YAAY,GAAG,sBAAsB,CAAC,CAAE;MAC7CQ,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACN,SAAS,GAAG,KAAK,CAAC,CAAE;IAC3B,CAAC,CACF;EACH;EAEA;EACAQ,UAAUA,CAACC,EAAW;IACpB,IAAIA,EAAE,KAAKC,SAAS,EAAE;MACpBH,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAC;MAC5C;;IAEF,IAAIK,OAAO,CAAC,4CAA4C,CAAC,EAAE;MACzD,IAAI,CAAChB,kBAAkB,CAACiB,iBAAiB,CAACH,EAAE,CAAC,CAACL,SAAS,CAAC;QACtDS,IAAI,EAAGC,QAAQ,IAAI;UACjBP,OAAO,CAACQ,GAAG,CAAC,kBAAkB,EAAED,QAAQ,CAAC;UACzC,IAAI,CAAChB,YAAY,GAAG,IAAI,CAACA,YAAY,CAACkB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACR,EAAE,KAAKA,EAAE,CAAC,CAAC,CAAE;QACzE,CAAC;;QACDH,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C;OACD,CAAC;;EAEN;EAEA;EACAY,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCb,OAAO,CAACQ,GAAG,CAACI,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEA;EACAC,UAAUA,CAACd,EAAU,EAAEe,WAAiB;IACtC,IAAIf,EAAE,IAAIe,WAAW,EAAE;MACrB,IAAI,CAAC7B,kBAAkB,CAAC8B,iBAAiB,CAAChB,EAAE,EAAEe,WAAW,CAAC,CAACpB,SAAS,CAClE,MAAK;QACH,IAAI,CAACF,SAAS,EAAE,CAAC,CAAE;MACrB,CAAC,EACAI,KAAK,IAAI;QACRC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C,CAAC,CACF;;EAEL;EAEAoB,eAAeA,CAAA;IACbpC,OAAO,CAACqC,OAAO,EAAE,CAAC,CAAC;IAEnB;IACApC,KAAK,CAACqC,QAAQ,CAAC,GAAGpC,aAAa,CAAC,CAAC,CAAE;IAEnC,IAAI,IAAI,CAACqC,UAAU,EAAE;MACnB,MAAMC,GAAG,GAAG,IAAI,CAACD,UAAU,CAACE,aAAkC;MAE9D;MACA,MAAMC,KAAK,GAAG,IAAIzC,KAAK,CAACuC,GAAG,EAAE;QAC3BG,IAAI,EAAE,MAAM;QACZ5B,IAAI,EAAE;UACJ6B,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;UACtFC,QAAQ,EAAE,CAAC;YACT9B,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YACvD+B,OAAO,EAAE,GAAG;YACZC,eAAe,EAAE,qBAAqB;YACtCC,WAAW,EAAE,SAAS;YACtBC,WAAW,EAAE,CAAC;YACdC,oBAAoB,EAAE;WACvB;SACF;QACDC,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE,KAAK;UAC1BC,MAAM,EAAE;YACNC,CAAC,EAAE;cACDC,WAAW,EAAE;;;;OAIpB,CAAC;;EAEN;CACD;AA7GuBC,UAAA,EAArB1D,SAAS,CAAC,SAAS,CAAC,C,wDAAyB;AADnCI,qBAAqB,GAAAsD,UAAA,EALjC3D,SAAS,CAAC;EACT4D,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,qBAAqB;CAClC,CAAC,C,EACWzD,qBAAqB,CA8GjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}