package com.Aziz.Administratif.Security.auth;

import com.Aziz.Administratif.Entity.User;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;


@Getter
@Setter
@Entity
public class PasswordResetRequest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private int matricule;


    private String email;

    private boolean completed = false;

    private LocalDateTime requestDate;

    @ManyToOne
    private User assignedAdmin;


}

