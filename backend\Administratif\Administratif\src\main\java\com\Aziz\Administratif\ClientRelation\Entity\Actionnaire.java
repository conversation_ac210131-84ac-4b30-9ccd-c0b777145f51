package com.Aziz.Administratif.ClientRelation.Entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;
import java.util.List;



@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Actionnaire {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idActionnaire;
    private String nomActionnaire;
    private String PrenomActionnaire;
    private String emailActionnaire;
    private Long Telephone;

    @CreationTimestamp
    private LocalDateTime DateCreation;


    @LastModifiedDate
    private LocalDateTime DateModification;



    @OneToMany(mappedBy = "actionnaire")
    @JsonBackReference
    @JsonIgnore
    private List<Portefeuille> portefeuilles;



//    public List<Portefeuille> getActions() {
//        return portefeuilles.stream().toList();
//////                .map(Portefeuille::getIdPortefeuille)
//////                .distinct()
////                .collect(Collectors.toList());
//    }

}
