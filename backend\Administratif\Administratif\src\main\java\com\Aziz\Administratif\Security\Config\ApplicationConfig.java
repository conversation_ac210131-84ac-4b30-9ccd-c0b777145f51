package com.Aziz.Administratif.Security.Config;

import com.Aziz.Administratif.Entity.User;
import com.Aziz.Administratif.Repositories.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.Optional;

@Configuration
@RequiredArgsConstructor
public class ApplicationConfig {

    private final UserRepository userRepository;

    @Bean
    public UserDetailsService userDetailsService() {
        return identifier -> {
            try {
                // Try parsing identifier as matricule
                int matricule = Integer.parseInt(identifier);
                User user = userRepository.findByMatricule(matricule);
                if (user == null) {
                    throw new UsernameNotFoundException("User not found with matricule: " + matricule);
                }
                return user;
            } catch (NumberFormatException e) {
                // If it's not a number, treat it as an email
                Optional<User> userOpt = userRepository.findByEmail(identifier);
                if (userOpt.isEmpty()) {
                    throw new UsernameNotFoundException("User not found with email: " + identifier);
                }
                return userOpt.get();
            }
        };
    }

}
