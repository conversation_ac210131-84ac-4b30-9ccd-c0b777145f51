{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthenticationService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth';\n  }\n  register(firstName, lastName, email, password) {\n    return this.http.post(`${this.apiUrl}/register`, {\n      firstName,\n      lastName,\n      email,\n      password\n    });\n  }\n  login(matricule, password) {\n    return this.http.post(`${this.apiUrl}/authenticate`, {\n      matricule,\n      password\n    });\n  }\n  storeToken(token) {\n    localStorage.setItem('jwt_token', token);\n  }\n  getToken() {\n    return localStorage.getItem('jwt_token');\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    this.router.navigate(['/login']);\n  }\n  isAuthenticated() {\n    return !!this.getToken();\n  }\n  getUserDetailsFromToken(token) {\n    try {\n      console.log(\"Raw token:\", token); // Log the raw token before decoding\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      console.log(\"Decoded payload:\", payload); // Log the decoded payload\n      // Ensure 'groupe' is an object\n      const groupe = typeof payload?.groupe === 'string' ? JSON.parse(payload.groupe) : payload?.groupe;\n      // Fix the allowedPaths URLs to have 'http://' if needed\n      const allowedPaths = (payload?.allowedPaths || []).map(path => {\n        if (path.startsWith('http:/')) {\n          return path.replace('http:/', 'http://');\n        }\n        return path;\n      });\n      // Log the state to check if it's set correctly\n      console.log(\"State value:\", payload?.state);\n      return {\n        role: payload?.role || '',\n        groupe: groupe,\n        allowedPaths: allowedPaths,\n        prenom: payload?.prenom || '',\n        nom: payload?.nom || '',\n        state: payload?.state === true // Force state to boolean\n      };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return {\n        role: '',\n        groupe: {},\n        allowedPaths: [],\n        prenom: '',\n        nom: '',\n        state: false\n      };\n    }\n  }\n  storeAllowedPaths(allowedPaths) {\n    localStorage.setItem('allowed_paths', JSON.stringify(allowedPaths));\n  }\n  getAllowedPaths() {\n    const paths = localStorage.getItem('allowed_paths');\n    return paths ? JSON.parse(paths) : [];\n  }\n  getUserRole() {\n    const token = this.getToken();\n    if (!token) return '';\n    return this.getUserDetailsFromToken(token).role;\n  }\n  getGroupAllowedPaths() {\n    const token = this.getToken();\n    if (!token) return [];\n    return this.getUserDetailsFromToken(token).allowedPaths;\n  }\n  isRouteAllowed(path) {\n    const role = this.getUserRole().toUpperCase();\n    if (role === 'ADMIN') return true;\n    const allowed = this.getGroupAllowedPaths().map(url => {\n      try {\n        return new URL(url).pathname;\n      } catch {\n        return url; // fallback if not a full URL\n      }\n    });\n\n    return allowed.includes(path);\n  }\n  static {\n    this.ɵfac = function AuthenticationService_Factory(t) {\n      return new (t || AuthenticationService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthenticationService,\n      factory: AuthenticationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AuthenticationService", "constructor", "http", "router", "apiUrl", "register", "firstName", "lastName", "email", "password", "post", "login", "matricule", "storeToken", "token", "localStorage", "setItem", "getToken", "getItem", "logout", "removeItem", "navigate", "isAuthenticated", "getUserDetailsFromToken", "console", "log", "payload", "JSON", "parse", "atob", "split", "groupe", "allowedPaths", "map", "path", "startsWith", "replace", "state", "role", "prenom", "nom", "e", "error", "storeAllowedPaths", "stringify", "getAllowedPaths", "paths", "getUserRole", "getGroupAllowedPaths", "isRouteAllowed", "toUpperCase", "allowed", "url", "URL", "pathname", "includes", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\auth\\authentication.service.ts"], "sourcesContent": ["// authentication.service.ts\nimport { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { Observable } from 'rxjs';\n\ninterface AuthResponse {\n  token: string;\n  allowedPaths: string[];\n  groupe: any;\n  state: boolean;\n  prenom?: string;\n  nom?: string;\n  role?: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthenticationService {\n  private apiUrl = 'http://localhost:8000/api/v1/auth';\n\n  constructor(private http: HttpClient, private router: Router) {}\n\n  register(firstName: string, lastName: string, email: string, password: string): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/register`, { firstName, lastName, email, password });\n  }\n\n  login(matricule: number, password: string): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/authenticate`, { matricule, password });\n  }\n\n  storeToken(token: string) {\n    localStorage.setItem('jwt_token', token);\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('jwt_token');\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    this.router.navigate(['/login']);\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n  getUserDetailsFromToken(token: string): { role: string, groupe: any, allowedPaths: string[], prenom: string, nom: string, state: boolean } {\n    try {\n      console.log(\"Raw token:\", token);  // Log the raw token before decoding\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      console.log(\"Decoded payload:\", payload);  // Log the decoded payload\n  \n      // Ensure 'groupe' is an object\n      const groupe = typeof payload?.groupe === 'string' ? JSON.parse(payload.groupe) : payload?.groupe;\n  \n      // Fix the allowedPaths URLs to have 'http://' if needed\n      const allowedPaths = (payload?.allowedPaths || []).map((path: string) => {\n        if (path.startsWith('http:/')) {\n          return path.replace('http:/', 'http://');\n        }\n        return path;\n      });\n  \n      // Log the state to check if it's set correctly\n      console.log(\"State value:\", payload?.state);\n  \n      return {\n        role: payload?.role || '',\n        groupe: groupe,  // Now 'groupe' is an object\n        allowedPaths: allowedPaths,\n        prenom: payload?.prenom || '',\n        nom: payload?.nom || '',\n        state: payload?.state === true // Force state to boolean\n      };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return { role: '', groupe: {}, allowedPaths: [], prenom: '', nom: '', state: false };\n    }\n  }\n  \n  \n  \n  storeAllowedPaths(allowedPaths: string[]) {\n    localStorage.setItem('allowed_paths', JSON.stringify(allowedPaths));\n  }\n  \n  getAllowedPaths(): string[] {\n    const paths = localStorage.getItem('allowed_paths');\n    return paths ? JSON.parse(paths) : [];\n  }\n\n  getUserRole(): string {\n    const token = this.getToken();\n    if (!token) return '';\n    return this.getUserDetailsFromToken(token).role;\n  }\n  \n  getGroupAllowedPaths(): string[] {\n    const token = this.getToken();\n    if (!token) return [];\n    return this.getUserDetailsFromToken(token).allowedPaths;\n  }\n  \n\n  isRouteAllowed(path: string): boolean {\n    const role = this.getUserRole().toUpperCase();\n    if (role === 'ADMIN') return true;\n  \n    const allowed = this.getGroupAllowedPaths().map((url) => {\n      try {\n        return new URL(url).pathname;\n      } catch {\n        return url; // fallback if not a full URL\n      }\n    });\n  \n    return allowed.includes(path);\n  }\n  \n  \n}\n"], "mappings": ";;;AAmBA,OAAM,MAAOA,qBAAqB;EAGhCC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAF5C,KAAAC,MAAM,GAAG,mCAAmC;EAEW;EAE/DC,QAAQA,CAACC,SAAiB,EAAEC,QAAgB,EAAEC,KAAa,EAAEC,QAAgB;IAC3E,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAe,GAAG,IAAI,CAACN,MAAM,WAAW,EAAE;MAAEE,SAAS;MAAEC,QAAQ;MAAEC,KAAK;MAAEC;IAAQ,CAAE,CAAC;EAC1G;EAEAE,KAAKA,CAACC,SAAiB,EAAEH,QAAgB;IACvC,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAe,GAAG,IAAI,CAACN,MAAM,eAAe,EAAE;MAAEQ,SAAS;MAAEH;IAAQ,CAAE,CAAC;EAC7F;EAEAI,UAAUA,CAACC,KAAa;IACtBC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEF,KAAK,CAAC;EAC1C;EAEAG,QAAQA,CAAA;IACN,OAAOF,YAAY,CAACG,OAAO,CAAC,WAAW,CAAC;EAC1C;EAEAC,MAAMA,CAAA;IACJJ,YAAY,CAACK,UAAU,CAAC,WAAW,CAAC;IACpCL,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;IAC/BL,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAACL,QAAQ,EAAE;EAC1B;EACAM,uBAAuBA,CAACT,KAAa;IACnC,IAAI;MACFU,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEX,KAAK,CAAC,CAAC,CAAE;MACnC,MAAMY,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACf,KAAK,CAACgB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDN,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,OAAO,CAAC,CAAC,CAAE;MAE3C;MACA,MAAMK,MAAM,GAAG,OAAOL,OAAO,EAAEK,MAAM,KAAK,QAAQ,GAAGJ,IAAI,CAACC,KAAK,CAACF,OAAO,CAACK,MAAM,CAAC,GAAGL,OAAO,EAAEK,MAAM;MAEjG;MACA,MAAMC,YAAY,GAAG,CAACN,OAAO,EAAEM,YAAY,IAAI,EAAE,EAAEC,GAAG,CAAEC,IAAY,IAAI;QACtE,IAAIA,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;UAC7B,OAAOD,IAAI,CAACE,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC;;QAE1C,OAAOF,IAAI;MACb,CAAC,CAAC;MAEF;MACAV,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEC,OAAO,EAAEW,KAAK,CAAC;MAE3C,OAAO;QACLC,IAAI,EAAEZ,OAAO,EAAEY,IAAI,IAAI,EAAE;QACzBP,MAAM,EAAEA,MAAM;QACdC,YAAY,EAAEA,YAAY;QAC1BO,MAAM,EAAEb,OAAO,EAAEa,MAAM,IAAI,EAAE;QAC7BC,GAAG,EAAEd,OAAO,EAAEc,GAAG,IAAI,EAAE;QACvBH,KAAK,EAAEX,OAAO,EAAEW,KAAK,KAAK,IAAI,CAAC;OAChC;KACF,CAAC,OAAOI,CAAC,EAAE;MACVjB,OAAO,CAACkB,KAAK,CAAC,uBAAuB,EAAED,CAAC,CAAC;MACzC,OAAO;QAAEH,IAAI,EAAE,EAAE;QAAEP,MAAM,EAAE,EAAE;QAAEC,YAAY,EAAE,EAAE;QAAEO,MAAM,EAAE,EAAE;QAAEC,GAAG,EAAE,EAAE;QAAEH,KAAK,EAAE;MAAK,CAAE;;EAExF;EAIAM,iBAAiBA,CAACX,YAAsB;IACtCjB,YAAY,CAACC,OAAO,CAAC,eAAe,EAAEW,IAAI,CAACiB,SAAS,CAACZ,YAAY,CAAC,CAAC;EACrE;EAEAa,eAAeA,CAAA;IACb,MAAMC,KAAK,GAAG/B,YAAY,CAACG,OAAO,CAAC,eAAe,CAAC;IACnD,OAAO4B,KAAK,GAAGnB,IAAI,CAACC,KAAK,CAACkB,KAAK,CAAC,GAAG,EAAE;EACvC;EAEAC,WAAWA,CAAA;IACT,MAAMjC,KAAK,GAAG,IAAI,CAACG,QAAQ,EAAE;IAC7B,IAAI,CAACH,KAAK,EAAE,OAAO,EAAE;IACrB,OAAO,IAAI,CAACS,uBAAuB,CAACT,KAAK,CAAC,CAACwB,IAAI;EACjD;EAEAU,oBAAoBA,CAAA;IAClB,MAAMlC,KAAK,GAAG,IAAI,CAACG,QAAQ,EAAE;IAC7B,IAAI,CAACH,KAAK,EAAE,OAAO,EAAE;IACrB,OAAO,IAAI,CAACS,uBAAuB,CAACT,KAAK,CAAC,CAACkB,YAAY;EACzD;EAGAiB,cAAcA,CAACf,IAAY;IACzB,MAAMI,IAAI,GAAG,IAAI,CAACS,WAAW,EAAE,CAACG,WAAW,EAAE;IAC7C,IAAIZ,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IAEjC,MAAMa,OAAO,GAAG,IAAI,CAACH,oBAAoB,EAAE,CAACf,GAAG,CAAEmB,GAAG,IAAI;MACtD,IAAI;QACF,OAAO,IAAIC,GAAG,CAACD,GAAG,CAAC,CAACE,QAAQ;OAC7B,CAAC,MAAM;QACN,OAAOF,GAAG,CAAC,CAAC;;IAEhB,CAAC,CAAC;;IAEF,OAAOD,OAAO,CAACI,QAAQ,CAACrB,IAAI,CAAC;EAC/B;;;uBAtGWlC,qBAAqB,EAAAwD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAArB7D,qBAAqB;MAAA8D,OAAA,EAArB9D,qBAAqB,CAAA+D,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}