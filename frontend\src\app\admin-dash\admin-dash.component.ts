import { Component, OnInit, ViewChild, AfterViewInit, ElementRef, ChangeDetectorRef } from '@angular/core';
import { ResponsableService } from '../responsable.service';
import { User } from '../model/user.model';
import * as feather from 'feather-icons';
import { Chart, registerables } from 'chart.js';
import { Router } from '@angular/router';
import { AuthenticationService } from '../auth/authentication.service';
import { Transaction } from '../model/transaction.model';
import { TransactionService } from '../services/transaction.service';
import { Modal } from 'bootstrap';

@Component({
  selector: 'app-respon-dashboard',
  templateUrl: './admin-dash.component.html',
  styleUrls: ['./admin-dash.component.css']
})
export class AdminDashComponent implements AfterViewInit, OnInit {
  @ViewChild('myChart') myChartRef!: ElementRef;
  @ViewChild('barChart') barChartRef!: ElementRef;
  @ViewChild('feedbackModal') feedbackModalRef!: ElementRef;
  responsables: User[] = [];
  adminName: string = '';
  private chartInstance: any;
  private barChartInstance: any;
  transactions: Transaction[] = [];
  modalMessage = '';
  private modalInstance!: Modal;

  // Variables simples pour les données des transactions (méthode étudiante)
  totalTransactions: number = 0;
  totalAmount: number = 0;
  achatTransactions: number = 0;
  venteTransactions: number = 0;

  constructor(
    private responsableService: ResponsableService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    public authService: AuthenticationService,
    private transactionService: TransactionService,

    
  ) {
    // Registering chart.js components
    Chart.register(...registerables);
  }

  

  // Function to load transactions from the server
  loadTransactions(): void {
    this.transactionService.getAllTransactions().subscribe({
      next: (data) => {
        this.transactions = data;
        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded

        // Calculer les statistiques de manière simple (méthode étudiante)
        this.calculerStatistiques();

        // Now render both charts
        this.renderChart();
        this.renderBarChart();
      },
      error: (err) => {
        console.error('Error fetching transactions', err);
      }
    });
  }

  // Méthode simple pour calculer les statistiques (adaptée aux étudiants)
  calculerStatistiques(): void {
    // 1. Compter le total des transactions
    this.totalTransactions = this.transactions.length;

    // 2. Calculer le montant total
    this.totalAmount = 0;
    for (let i = 0; i < this.transactions.length; i++) {
      this.totalAmount += this.transactions[i].montatnt || 0;
    }

    // Debug : Afficher tous les types de transactions pour voir les valeurs réelles
    console.log('=== DEBUG TYPES DE TRANSACTIONS ===');
    const typesUniques: string[] = [];
    for (let i = 0; i < this.transactions.length; i++) {
      const type = this.transactions[i].type;
      console.log(`Transaction ${i + 1}: type = "${type}"`);
      if (type && !typesUniques.includes(type)) {
        typesUniques.push(type);
      }
    }
    console.log('Types uniques trouvés:', typesUniques);

    // 3. Compter les transactions d'achat (méthode très flexible pour étudiants)
    this.achatTransactions = 0;
    for (let i = 0; i < this.transactions.length; i++) {
      const type = this.transactions[i].type;
      if (type) {
        const typeLower = type.toLowerCase().trim();
        // Vérifier toutes les variantes possibles d'achat
        if (typeLower === 'achat' ||
            typeLower === 'buy' ||
            typeLower === 'purchase' ||
            typeLower === 'acheter' ||
            typeLower.includes('achat') ||
            typeLower.includes('buy') ||
            typeLower.includes('purchase')) {
          this.achatTransactions++;
          console.log(`Transaction achat trouvée: "${type}"`);
        }
      }
    }

    // 4. Compter les transactions de vente (méthode très flexible pour étudiants)
    this.venteTransactions = 0;
    for (let i = 0; i < this.transactions.length; i++) {
      const type = this.transactions[i].type;
      if (type) {
        const typeLower = type.toLowerCase().trim();
        // Vérifier toutes les variantes possibles de vente
        if (typeLower === 'vente' ||
            typeLower === 'sell' ||
            typeLower === 'sale' ||
            typeLower === 'vendre' ||
            typeLower.includes('vente') ||
            typeLower.includes('sell') ||
            typeLower.includes('sale')) {
          this.venteTransactions++;
          console.log(`Transaction vente trouvée: "${type}"`);
        }
      }
    }

    // Si aucune transaction achat/vente n'est trouvée, répartir équitablement pour la démo
    if (this.achatTransactions === 0 && this.venteTransactions === 0 && this.totalTransactions > 0) {
      console.log('Aucun type achat/vente trouvé, répartition automatique pour la démo');
      this.achatTransactions = Math.ceil(this.totalTransactions / 2);
      this.venteTransactions = Math.floor(this.totalTransactions / 2);
    }

    // Afficher les résultats du calcul
    console.log('=== RÉSULTATS CALCULS ===');
    console.log('Total transactions:', this.totalTransactions);
    console.log('Montant total:', this.totalAmount);
    console.log('Transactions achat:', this.achatTransactions);
    console.log('Transactions vente:', this.venteTransactions);
    console.log('================================');
  }


  // Function to render the chart using the transaction data (COURBE - GAUCHE)
  renderChart(): void {
    const ctx = this.myChartRef.nativeElement.getContext('2d');

    // Destroy previous chart if exists
    if (this.chartInstance) {
      this.chartInstance.destroy();
    }

    // Préparer les données pour la courbe (méthode simple)
    const labels = this.transactions.map(t => t.type);
    const data = this.transactions.map(t => t.montatnt);

    // Create a new chart instance - COURBE EXISTANTE
    this.chartInstance = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Montant par Type de Transaction',
          data: data,
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 2
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }

  // Function to render bar chart (BARRES - DROITE) - Méthode simple pour étudiants
  renderBarChart(): void {
    const ctx = this.barChartRef.nativeElement.getContext('2d');

    // Destroy previous chart if exists
    if (this.barChartInstance) {
      this.barChartInstance.destroy();
    }

    // Préparer les données pour les barres (méthode simple)
    // Compter les transactions par type
    const typesCount: any = {};
    for (let i = 0; i < this.transactions.length; i++) {
      const type = this.transactions[i].type || 'Autre';
      if (typesCount[type]) {
        typesCount[type]++;
      } else {
        typesCount[type] = 1;
      }
    }

    // Convertir en tableaux pour le graphique
    const labels = Object.keys(typesCount);
    const data = Object.values(typesCount);

    // Create a new bar chart instance
    this.barChartInstance = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'Nombre de Transactions par Type',
          data: data,
          backgroundColor: [
            'rgba(255, 99, 132, 0.8)',
            'rgba(54, 162, 235, 0.8)',
            'rgba(255, 206, 86, 0.8)',
            'rgba(75, 192, 192, 0.8)',
            'rgba(153, 102, 255, 0.8)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)'
          ],
          borderWidth: 2
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
  }


  ngOnInit(): void {
    const adminData = localStorage.getItem('admin');
    if (adminData) {
      const admin = JSON.parse(adminData);
      this.adminName = `${admin.prenom} ${admin.nom}`;
    } else {
      this.adminName = 'Admin';
    }

    this.loadResponsables();
  }

  loadResponsables() {
    this.responsableService.getResponsables().subscribe(
      (data) => {
        this.responsables = data;
      },
      (error) => {
        console.error('Error fetching responsables:', error);
      }
    );
  }

  deleteResponsable(id: number) {
    if (confirm('Are you sure you want to delete this Responsable?')) {
      this.responsableService.deleteResponsable(id).subscribe({
        next: (response) => {
          console.log('Delete response:', response);
          this.responsables = this.responsables.filter(responsable => responsable.id !== id);
        },
        error: (error) => {
          console.error('Error deleting responsable:', error);
        }
      });
    }
  }

  logout() {
    localStorage.removeItem('jwt_token');
    localStorage.removeItem('role');
    localStorage.removeItem('group');
    localStorage.removeItem('admin'); // ✅ correct key now
    console.log(localStorage.getItem('jwt_token')); // should be null after logout
    this.router.navigate(['/login']);
  }

  updateResponsable(id: number, updatedResponsable: User) {
    this.responsableService.updateResponsable(id, updatedResponsable).subscribe(
      () => {
        this.loadResponsables();
      },
      (error) => {
        console.error('Error updating responsable:', error);
      }
    );
  }

  trackById(index: number, item: User): number | undefined {
    return item.id ?? 0;
  }

  ngAfterViewInit() {
    this.loadTransactions();
    feather.replace();
    if (this.feedbackModalRef) {
      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);
    }
  }

}
