package com.Aziz.Administratif.Entity;

import com.Aziz.Administratif.Enum.Role;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;


@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="Users")
public class User implements UserDetails{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String nom;
    private String prenom;
    private String password;
    private String email;
    private Integer telephone;
    private Integer matricule;

    @Enumerated(EnumType.STRING)//Role de Users
    private Role role;


@CreationTimestamp
private LocalDateTime DateCreation;

@LastModifiedDate
private LocalDateTime DateModification;

    @ManyToOne
    @JoinColumn(name = "idGroupe", referencedColumnName = "idGroupe")
    @JsonBackReference // This tells Jackson to avoid serializing the Groupe object in the User entity
    private Groupe groupe;

    @ManyToOne // This indicates that many users can belong to one habilitation
    @JoinColumn(name = "idHabilitation") // This is the foreign key in the User table
    @JsonIgnore
    private Habilitation habilitation;


//les droits

    private Boolean canCreate;

    private Boolean canRead;

    private Boolean canUpdate;

    private Boolean canDelete;



    @JsonProperty("state")
    public Boolean getState() {
        return State;
    }

    public void setState(Boolean state) {
        State = state;
    }


    public Boolean getFlag() {
        return Flag;
    }

    public void setFlag(Boolean flag) {
        Flag = flag;
    }

    private Boolean State;

    private Boolean Flag;




    public void setPermissionsFromHabilitation(Habilitation habilitation) {
        if (habilitation != null) {
            // These permissions can be calculated from the Habilitation logic if needed
            this.canCreate = false;
            this.canRead = false;
            this.canUpdate = false;
            this.canDelete = false;
        }
    }

    public Groupe getGroupe() {
        return groupe;
    }

    public void setGroupe(Groupe groupe) {
        this.groupe = groupe;
    }

    public Role getRole() {
        return role;
    }

    public void setRole(Role role) {
        this.role = role;
    }



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNom() {
        return nom;
    }

    public void setNom(String nom) {
        this.nom = nom;
    }

    public String getPrenom() {
        return prenom;
    }

    public void setPrenom(String prenom) {
        this.prenom = prenom;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return List.of();
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }



    public void setTelephone(Integer telephone) {
        this.telephone = telephone;
    }




    @Override
    public String getUsername() {
        return email;
    }
    @Override
    public boolean isAccountNonExpired()
    {
        return true;
    }
    @Override
    public boolean isAccountNonLocked()
    {
        return true;
    }
    @Override
    public boolean isCredentialsNonExpired()
    {
        return true;
    }
    @Override
    public boolean isEnabled()
    {
        return true;
    }
}


