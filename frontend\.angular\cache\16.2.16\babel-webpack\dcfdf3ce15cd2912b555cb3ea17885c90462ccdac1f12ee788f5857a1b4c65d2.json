{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, tap, throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class GroupeService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth/Groupes';\n  }\n  // Get all groupes\n  getAllGroupes() {\n    return this.http.get(`${this.apiUrl}/ALLGroupes`, {\n      headers: this.getHeaders()\n    });\n  }\n  // Get a groupe by ID\n  getGroupeById(id) {\n    return this.http.get(`${this.apiUrl}/find/${id}`).pipe(tap(data => console.log('Group data fetched:', data), error => {\n      console.error('Error fetching group:', error);\n    }));\n  }\n  // Add a new groupe\n  addGroupe(groupe) {\n    return this.http.post(`${this.apiUrl}/ajout`, groupe, {\n      headers: this.getHeaders()\n    });\n  }\n  getGroupeByIdFromBackend(groupeId) {\n    return this.http.get(`${this.apiUrl}/${groupeId}`, {\n      headers: this.getHeaders()\n    });\n  }\n  // Update an existing groupe\n  updateGroupe(id, groupe) {\n    // Send the request with the group data including ressources as an array of IDs\n    return this.http.put(`${this.apiUrl}/edit/${id}`, groupe, {\n      headers: this.getHeaders()\n    }).pipe(tap(response => console.log('Group updated successfully:', response), error => console.error('Error updating group:', error)));\n  }\n  // Delete a groupe\n  deleteGroupe(id) {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, {\n      responseType: 'text'\n    }).pipe(catchError(error => {\n      console.error('Error deleting group:', error);\n      // Pass custom error message to component\n      if (error.status === 409) {\n        return throwError(() => new Error('CONFLICT_GROUP_USERS'));\n      } else {\n        return throwError(() => new Error('GENERIC_DELETE_ERROR'));\n      }\n    }));\n  }\n  // Helper function to get headers with Authorization\n  getHeaders() {\n    const token = localStorage.getItem('jwt_token');\n    console.log('Token being sent in headers:', token); // Log token for debugging\n    if (!token) {\n      console.error('No token found!');\n      // Redirect to login page\n      window.location.href = '/login';\n      throw new Error('No token found!');\n    }\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`\n    });\n  }\n  static {\n    this.ɵfac = function GroupeService_Factory(t) {\n      return new (t || GroupeService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GroupeService,\n      factory: GroupeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "catchError", "tap", "throwError", "GroupeService", "constructor", "http", "apiUrl", "getAllGroupes", "get", "headers", "getHeaders", "getGroupeById", "id", "pipe", "data", "console", "log", "error", "addGroupe", "groupe", "post", "getGroupeByIdFromBackend", "groupeId", "updateGroupe", "put", "response", "deleteGroupe", "delete", "responseType", "status", "Error", "token", "localStorage", "getItem", "window", "location", "href", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Final - Copie\\frontend\\src\\app\\services\\groupe.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { catchError, Observable, tap, throwError } from 'rxjs';\nimport { Groupe } from '../model/groupe.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class GroupeService {\n  private apiUrl = 'http://localhost:8000/api/v1/auth/Groupes';\n\n  constructor(private http: HttpClient) {}\n\n  // Get all groupes\n  getAllGroupes(): Observable<Groupe[]> {\n    return this.http.get<Groupe[]>(`${this.apiUrl}/ALLGroupes`, {\n      headers: this.getHeaders(),\n    });\n  }\n\n  // Get a groupe by ID\n  getGroupeById(id: number): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/find/${id}`).pipe(\n      tap(\n        (data: any) => console.log('Group data fetched:', data),\n        (error: any) => {\n          console.error('Error fetching group:', error);\n        }\n      )\n    );\n  }\n\n  // Add a new groupe\n  addGroupe(groupe: Groupe): Observable<Groupe> {\n    return this.http.post<Groupe>(`${this.apiUrl}/ajout`, groupe, {\n      headers: this.getHeaders(),\n    });\n  }\n\n  getGroupeByIdFromBackend(groupeId: number): Observable<Groupe> {\n    return this.http.get<Groupe>(`${this.apiUrl}/${groupeId}`, {\n      headers: this.getHeaders(),\n    });\n  }\n\n// Update an existing groupe\nupdateGroupe(id: number, groupe: Groupe): Observable<any> {\n  // Send the request with the group data including ressources as an array of IDs\n  return this.http.put<any>(`${this.apiUrl}/edit/${id}`, groupe, {\n    headers: this.getHeaders(),\n  }).pipe(\n    tap(\n      (response) => console.log('Group updated successfully:', response),\n      (error) => console.error('Error updating group:', error)\n    )\n  );\n}\n\n  \n  // Delete a groupe\n  deleteGroupe(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, { responseType: 'text' })\n      .pipe(\n        catchError((error) => {\n          console.error('Error deleting group:', error);\n  \n          // Pass custom error message to component\n          if (error.status === 409) {\n            return throwError(() => new Error('CONFLICT_GROUP_USERS'));\n          } else {\n            return throwError(() => new Error('GENERIC_DELETE_ERROR'));\n          }\n        })\n      );\n  }\n  \n\n  // Helper function to get headers with Authorization\n  private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('jwt_token');\n    console.log('Token being sent in headers:', token); // Log token for debugging\n    if (!token) {\n      console.error('No token found!');\n      // Redirect to login page\n      window.location.href = '/login';\n      throw new Error('No token found!');\n    }\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n    });\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAcC,GAAG,EAAEC,UAAU,QAAQ,MAAM;;;AAM9D,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,2CAA2C;EAErB;EAEvC;EACAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAW,GAAG,IAAI,CAACF,MAAM,aAAa,EAAE;MAC1DG,OAAO,EAAE,IAAI,CAACC,UAAU;KACzB,CAAC;EACJ;EAEA;EACAC,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACP,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,SAASM,EAAE,EAAE,CAAC,CAACC,IAAI,CACzDZ,GAAG,CACAa,IAAS,IAAKC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,IAAI,CAAC,EACtDG,KAAU,IAAI;MACbF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,CACF,CACF;EACH;EAEA;EACAC,SAASA,CAACC,MAAc;IACtB,OAAO,IAAI,CAACd,IAAI,CAACe,IAAI,CAAS,GAAG,IAAI,CAACd,MAAM,QAAQ,EAAEa,MAAM,EAAE;MAC5DV,OAAO,EAAE,IAAI,CAACC,UAAU;KACzB,CAAC;EACJ;EAEAW,wBAAwBA,CAACC,QAAgB;IACvC,OAAO,IAAI,CAACjB,IAAI,CAACG,GAAG,CAAS,GAAG,IAAI,CAACF,MAAM,IAAIgB,QAAQ,EAAE,EAAE;MACzDb,OAAO,EAAE,IAAI,CAACC,UAAU;KACzB,CAAC;EACJ;EAEF;EACAa,YAAYA,CAACX,EAAU,EAAEO,MAAc;IACrC;IACA,OAAO,IAAI,CAACd,IAAI,CAACmB,GAAG,CAAM,GAAG,IAAI,CAAClB,MAAM,SAASM,EAAE,EAAE,EAAEO,MAAM,EAAE;MAC7DV,OAAO,EAAE,IAAI,CAACC,UAAU;KACzB,CAAC,CAACG,IAAI,CACLZ,GAAG,CACAwB,QAAQ,IAAKV,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAES,QAAQ,CAAC,EACjER,KAAK,IAAKF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC,CACzD,CACF;EACH;EAGE;EACAS,YAAYA,CAACd,EAAU;IACrB,OAAO,IAAI,CAACP,IAAI,CAACsB,MAAM,CAAC,GAAG,IAAI,CAACrB,MAAM,WAAWM,EAAE,EAAE,EAAE;MAAEgB,YAAY,EAAE;IAAM,CAAE,CAAC,CAC7Ef,IAAI,CACHb,UAAU,CAAEiB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAE7C;MACA,IAAIA,KAAK,CAACY,MAAM,KAAK,GAAG,EAAE;QACxB,OAAO3B,UAAU,CAAC,MAAM,IAAI4B,KAAK,CAAC,sBAAsB,CAAC,CAAC;OAC3D,MAAM;QACL,OAAO5B,UAAU,CAAC,MAAM,IAAI4B,KAAK,CAAC,sBAAsB,CAAC,CAAC;;IAE9D,CAAC,CAAC,CACH;EACL;EAGA;EACQpB,UAAUA,CAAA;IAChB,MAAMqB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/ClB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEe,KAAK,CAAC,CAAC,CAAC;IACpD,IAAI,CAACA,KAAK,EAAE;MACVhB,OAAO,CAACE,KAAK,CAAC,iBAAiB,CAAC;MAChC;MACAiB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MAC/B,MAAM,IAAIN,KAAK,CAAC,iBAAiB,CAAC;;IAEpC,OAAO,IAAI/B,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUgC,KAAK;KACjC,CAAC;EACJ;;;uBAlFW5B,aAAa,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAbrC,aAAa;MAAAsC,OAAA,EAAbtC,aAAa,CAAAuC,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}