package com.Aziz.Client.Repositories;

import com.Aziz.Client.Entity.Action;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ActionRepository extends JpaRepository<Action, Long> {

    // Recherche d'une action par son code ISIN
    Action findByIsinAction(String ISINAction);

    // Recherche par nom de la société
    Action findByNomSociete(String nomSociete);
}
