package com.Aziz.Client.Repositories;

import com.Aziz.Client.Entity.Cotation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CotationRepository extends JpaRepository<Cotation, Long> {

    // Rechercher les cotations d'une action spécifique
    List<Cotation> findByAction_IdAction(Long idAction);
//
//    // Rechercher les cotations d'une date précise
//    List<Cotation> findByDateCotation(LocalDate dateCotation);
//
//    // Rechercher les cotations d'une action à une date donnée
//    List<Cotation> findByAction_IdActionAndDateCotation(Long idAction, LocalDate dateCotation);
}
