{"ast": null, "code": "/*!\n * express\n * Copyright(c) 2009-2013 <PERSON><PERSON>\n * Copyright(c) 2013 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module dependencies.\n * @private\n */\nvar pathRegexp = require('path-to-regexp');\nvar debug = require('debug')('express:router:layer');\n\n/**\n * Module variables.\n * @private\n */\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = Layer;\nfunction Layer(path, options, fn) {\n  if (!(this instanceof Layer)) {\n    return new Layer(path, options, fn);\n  }\n  debug('new %o', path);\n  var opts = options || {};\n  this.handle = fn;\n  this.name = fn.name || '<anonymous>';\n  this.params = undefined;\n  this.path = undefined;\n  this.regexp = pathRegexp(path, this.keys = [], opts);\n\n  // set fast path flags\n  this.regexp.fast_star = path === '*';\n  this.regexp.fast_slash = path === '/' && opts.end === false;\n}\n\n/**\n * Handle the error for the layer.\n *\n * @param {Error} error\n * @param {Request} req\n * @param {Response} res\n * @param {function} next\n * @api private\n */\n\nLayer.prototype.handle_error = function handle_error(error, req, res, next) {\n  var fn = this.handle;\n  if (fn.length !== 4) {\n    // not a standard error handler\n    return next(error);\n  }\n  try {\n    fn(error, req, res, next);\n  } catch (err) {\n    next(err);\n  }\n};\n\n/**\n * Handle the request for the layer.\n *\n * @param {Request} req\n * @param {Response} res\n * @param {function} next\n * @api private\n */\n\nLayer.prototype.handle_request = function handle(req, res, next) {\n  var fn = this.handle;\n  if (fn.length > 3) {\n    // not a standard request handler\n    return next();\n  }\n  try {\n    fn(req, res, next);\n  } catch (err) {\n    next(err);\n  }\n};\n\n/**\n * Check if this route matches `path`, if so\n * populate `.params`.\n *\n * @param {String} path\n * @return {Boolean}\n * @api private\n */\n\nLayer.prototype.match = function match(path) {\n  var match;\n  if (path != null) {\n    // fast path non-ending match for / (any path matches)\n    if (this.regexp.fast_slash) {\n      this.params = {};\n      this.path = '';\n      return true;\n    }\n\n    // fast path for * (everything matched in a param)\n    if (this.regexp.fast_star) {\n      this.params = {\n        '0': decode_param(path)\n      };\n      this.path = path;\n      return true;\n    }\n\n    // match the path\n    match = this.regexp.exec(path);\n  }\n  if (!match) {\n    this.params = undefined;\n    this.path = undefined;\n    return false;\n  }\n\n  // store values\n  this.params = {};\n  this.path = match[0];\n  var keys = this.keys;\n  var params = this.params;\n  for (var i = 1; i < match.length; i++) {\n    var key = keys[i - 1];\n    var prop = key.name;\n    var val = decode_param(match[i]);\n    if (val !== undefined || !hasOwnProperty.call(params, prop)) {\n      params[prop] = val;\n    }\n  }\n  return true;\n};\n\n/**\n * Decode param value.\n *\n * @param {string} val\n * @return {string}\n * @private\n */\n\nfunction decode_param(val) {\n  if (typeof val !== 'string' || val.length === 0) {\n    return val;\n  }\n  try {\n    return decodeURIComponent(val);\n  } catch (err) {\n    if (err instanceof URIError) {\n      err.message = 'Failed to decode param \\'' + val + '\\'';\n      err.status = err.statusCode = 400;\n    }\n    throw err;\n  }\n}", "map": {"version": 3, "names": ["pathRegexp", "require", "debug", "hasOwnProperty", "Object", "prototype", "module", "exports", "Layer", "path", "options", "fn", "opts", "handle", "name", "params", "undefined", "regexp", "keys", "fast_star", "fast_slash", "end", "handle_error", "error", "req", "res", "next", "length", "err", "handle_request", "match", "decode_param", "exec", "i", "key", "prop", "val", "call", "decodeURIComponent", "URIError", "message", "status", "statusCode"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/express/lib/router/layer.js"], "sourcesContent": ["/*!\n * express\n * Copyright(c) 2009-2013 <PERSON><PERSON>\n * Copyright(c) 2013 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar pathRegexp = require('path-to-regexp');\nvar debug = require('debug')('express:router:layer');\n\n/**\n * Module variables.\n * @private\n */\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = Layer;\n\nfunction Layer(path, options, fn) {\n  if (!(this instanceof Layer)) {\n    return new Layer(path, options, fn);\n  }\n\n  debug('new %o', path)\n  var opts = options || {};\n\n  this.handle = fn;\n  this.name = fn.name || '<anonymous>';\n  this.params = undefined;\n  this.path = undefined;\n  this.regexp = pathRegexp(path, this.keys = [], opts);\n\n  // set fast path flags\n  this.regexp.fast_star = path === '*'\n  this.regexp.fast_slash = path === '/' && opts.end === false\n}\n\n/**\n * Handle the error for the layer.\n *\n * @param {Error} error\n * @param {Request} req\n * @param {Response} res\n * @param {function} next\n * @api private\n */\n\nLayer.prototype.handle_error = function handle_error(error, req, res, next) {\n  var fn = this.handle;\n\n  if (fn.length !== 4) {\n    // not a standard error handler\n    return next(error);\n  }\n\n  try {\n    fn(error, req, res, next);\n  } catch (err) {\n    next(err);\n  }\n};\n\n/**\n * Handle the request for the layer.\n *\n * @param {Request} req\n * @param {Response} res\n * @param {function} next\n * @api private\n */\n\nLayer.prototype.handle_request = function handle(req, res, next) {\n  var fn = this.handle;\n\n  if (fn.length > 3) {\n    // not a standard request handler\n    return next();\n  }\n\n  try {\n    fn(req, res, next);\n  } catch (err) {\n    next(err);\n  }\n};\n\n/**\n * Check if this route matches `path`, if so\n * populate `.params`.\n *\n * @param {String} path\n * @return {Boolean}\n * @api private\n */\n\nLayer.prototype.match = function match(path) {\n  var match\n\n  if (path != null) {\n    // fast path non-ending match for / (any path matches)\n    if (this.regexp.fast_slash) {\n      this.params = {}\n      this.path = ''\n      return true\n    }\n\n    // fast path for * (everything matched in a param)\n    if (this.regexp.fast_star) {\n      this.params = {'0': decode_param(path)}\n      this.path = path\n      return true\n    }\n\n    // match the path\n    match = this.regexp.exec(path)\n  }\n\n  if (!match) {\n    this.params = undefined;\n    this.path = undefined;\n    return false;\n  }\n\n  // store values\n  this.params = {};\n  this.path = match[0]\n\n  var keys = this.keys;\n  var params = this.params;\n\n  for (var i = 1; i < match.length; i++) {\n    var key = keys[i - 1];\n    var prop = key.name;\n    var val = decode_param(match[i])\n\n    if (val !== undefined || !(hasOwnProperty.call(params, prop))) {\n      params[prop] = val;\n    }\n  }\n\n  return true;\n};\n\n/**\n * Decode param value.\n *\n * @param {string} val\n * @return {string}\n * @private\n */\n\nfunction decode_param(val) {\n  if (typeof val !== 'string' || val.length === 0) {\n    return val;\n  }\n\n  try {\n    return decodeURIComponent(val);\n  } catch (err) {\n    if (err instanceof URIError) {\n      err.message = 'Failed to decode param \\'' + val + '\\'';\n      err.status = err.statusCode = 400;\n    }\n\n    throw err;\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AAEA,IAAIA,UAAU,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAC1C,IAAIC,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC;;AAEpD;AACA;AACA;AACA;;AAEA,IAAIE,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc;;AAEpD;AACA;AACA;AACA;;AAEAG,MAAM,CAACC,OAAO,GAAGC,KAAK;AAEtB,SAASA,KAAKA,CAACC,IAAI,EAAEC,OAAO,EAAEC,EAAE,EAAE;EAChC,IAAI,EAAE,IAAI,YAAYH,KAAK,CAAC,EAAE;IAC5B,OAAO,IAAIA,KAAK,CAACC,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC;EACrC;EAEAT,KAAK,CAAC,QAAQ,EAAEO,IAAI,CAAC;EACrB,IAAIG,IAAI,GAAGF,OAAO,IAAI,CAAC,CAAC;EAExB,IAAI,CAACG,MAAM,GAAGF,EAAE;EAChB,IAAI,CAACG,IAAI,GAAGH,EAAE,CAACG,IAAI,IAAI,aAAa;EACpC,IAAI,CAACC,MAAM,GAAGC,SAAS;EACvB,IAAI,CAACP,IAAI,GAAGO,SAAS;EACrB,IAAI,CAACC,MAAM,GAAGjB,UAAU,CAACS,IAAI,EAAE,IAAI,CAACS,IAAI,GAAG,EAAE,EAAEN,IAAI,CAAC;;EAEpD;EACA,IAAI,CAACK,MAAM,CAACE,SAAS,GAAGV,IAAI,KAAK,GAAG;EACpC,IAAI,CAACQ,MAAM,CAACG,UAAU,GAAGX,IAAI,KAAK,GAAG,IAAIG,IAAI,CAACS,GAAG,KAAK,KAAK;AAC7D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAb,KAAK,CAACH,SAAS,CAACiB,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAE;EAC1E,IAAIf,EAAE,GAAG,IAAI,CAACE,MAAM;EAEpB,IAAIF,EAAE,CAACgB,MAAM,KAAK,CAAC,EAAE;IACnB;IACA,OAAOD,IAAI,CAACH,KAAK,CAAC;EACpB;EAEA,IAAI;IACFZ,EAAE,CAACY,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC;EAC3B,CAAC,CAAC,OAAOE,GAAG,EAAE;IACZF,IAAI,CAACE,GAAG,CAAC;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEApB,KAAK,CAACH,SAAS,CAACwB,cAAc,GAAG,SAAShB,MAAMA,CAACW,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAE;EAC/D,IAAIf,EAAE,GAAG,IAAI,CAACE,MAAM;EAEpB,IAAIF,EAAE,CAACgB,MAAM,GAAG,CAAC,EAAE;IACjB;IACA,OAAOD,IAAI,CAAC,CAAC;EACf;EAEA,IAAI;IACFf,EAAE,CAACa,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC;EACpB,CAAC,CAAC,OAAOE,GAAG,EAAE;IACZF,IAAI,CAACE,GAAG,CAAC;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEApB,KAAK,CAACH,SAAS,CAACyB,KAAK,GAAG,SAASA,KAAKA,CAACrB,IAAI,EAAE;EAC3C,IAAIqB,KAAK;EAET,IAAIrB,IAAI,IAAI,IAAI,EAAE;IAChB;IACA,IAAI,IAAI,CAACQ,MAAM,CAACG,UAAU,EAAE;MAC1B,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC;MAChB,IAAI,CAACN,IAAI,GAAG,EAAE;MACd,OAAO,IAAI;IACb;;IAEA;IACA,IAAI,IAAI,CAACQ,MAAM,CAACE,SAAS,EAAE;MACzB,IAAI,CAACJ,MAAM,GAAG;QAAC,GAAG,EAAEgB,YAAY,CAACtB,IAAI;MAAC,CAAC;MACvC,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,OAAO,IAAI;IACb;;IAEA;IACAqB,KAAK,GAAG,IAAI,CAACb,MAAM,CAACe,IAAI,CAACvB,IAAI,CAAC;EAChC;EAEA,IAAI,CAACqB,KAAK,EAAE;IACV,IAAI,CAACf,MAAM,GAAGC,SAAS;IACvB,IAAI,CAACP,IAAI,GAAGO,SAAS;IACrB,OAAO,KAAK;EACd;;EAEA;EACA,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC;EAChB,IAAI,CAACN,IAAI,GAAGqB,KAAK,CAAC,CAAC,CAAC;EAEpB,IAAIZ,IAAI,GAAG,IAAI,CAACA,IAAI;EACpB,IAAIH,MAAM,GAAG,IAAI,CAACA,MAAM;EAExB,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACH,MAAM,EAAEM,CAAC,EAAE,EAAE;IACrC,IAAIC,GAAG,GAAGhB,IAAI,CAACe,CAAC,GAAG,CAAC,CAAC;IACrB,IAAIE,IAAI,GAAGD,GAAG,CAACpB,IAAI;IACnB,IAAIsB,GAAG,GAAGL,YAAY,CAACD,KAAK,CAACG,CAAC,CAAC,CAAC;IAEhC,IAAIG,GAAG,KAAKpB,SAAS,IAAI,CAAEb,cAAc,CAACkC,IAAI,CAACtB,MAAM,EAAEoB,IAAI,CAAE,EAAE;MAC7DpB,MAAM,CAACoB,IAAI,CAAC,GAAGC,GAAG;IACpB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASL,YAAYA,CAACK,GAAG,EAAE;EACzB,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACT,MAAM,KAAK,CAAC,EAAE;IAC/C,OAAOS,GAAG;EACZ;EAEA,IAAI;IACF,OAAOE,kBAAkB,CAACF,GAAG,CAAC;EAChC,CAAC,CAAC,OAAOR,GAAG,EAAE;IACZ,IAAIA,GAAG,YAAYW,QAAQ,EAAE;MAC3BX,GAAG,CAACY,OAAO,GAAG,2BAA2B,GAAGJ,GAAG,GAAG,IAAI;MACtDR,GAAG,CAACa,MAAM,GAAGb,GAAG,CAACc,UAAU,GAAG,GAAG;IACnC;IAEA,MAAMd,GAAG;EACX;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}