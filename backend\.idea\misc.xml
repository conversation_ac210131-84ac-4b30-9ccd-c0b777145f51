<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/User/pom.xml" />
        <option value="$PROJECT_DIR$/Action/pom.xml" />
        <option value="$PROJECT_DIR$/Config/Config/pom.xml" />
        <option value="$PROJECT_DIR$/Gateway/Gateway/pom.xml" />
        <option value="$PROJECT_DIR$/security/pom.xml" />
        <option value="$PROJECT_DIR$/Discovery/Discovery/pom.xml" />
        <option value="$PROJECT_DIR$/Actionnaire/pom.xml" />
        <option value="$PROJECT_DIR$/Transaction/pom.xml" />
        <option value="$PROJECT_DIR$/Notification/pom.xml" />
        <option value="$PROJECT_DIR$/Portefeuille/pom.xml" />
        <option value="$PROJECT_DIR$/Administratif/Administratif/pom.xml" />
        <option value="$PROJECT_DIR$/Client/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_23" default="true" project-jdk-name="20" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>