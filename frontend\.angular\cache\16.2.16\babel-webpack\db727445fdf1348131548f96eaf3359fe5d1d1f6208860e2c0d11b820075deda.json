{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./authentication.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(next, state) {\n    const token = this.authService.getToken();\n    console.log(\"Token:\", token); // Debugging: log the token\n    if (token) {\n      const {\n        role,\n        groupe\n      } = this.getUserDetailsFromToken(token);\n      console.log(\"User role from token:\", role, \"Group:\", groupe); // Debugging: log role and group\n      // Ensure path matching and group comparison are consistent and case insensitive\n      if (role === 'RESPONSABLE') {\n        const allowedGroups = {\n          '/transactions': 'TRANSACTION',\n          '/actions': 'ACTION',\n          '/actionnaires': 'ACTIONNAIRE',\n          '/reports': 'NOTIFICATION',\n          '/port': 'PORTEFEUILLE',\n          '/ResDash': groupe // Always allow responsible users to access their dashboard\n        };\n        // Trim any trailing slashes to avoid issues with path matching\n        const currentPath = state.url.endsWith('/') ? state.url.slice(0, -1) : state.url;\n        console.log('Current path (trimmed):', currentPath);\n        const expectedGroup = allowedGroups[currentPath];\n        console.log('Expected group:', expectedGroup);\n        // Check if the expected group is available and match it with the user group\n        if (expectedGroup && groupe.toUpperCase() !== expectedGroup.toUpperCase()) {\n          console.warn(\"Access denied for group:\", groupe);\n          this.router.navigate(['/not-authorized']);\n          return false;\n        }\n      }\n      return true;\n    }\n    // Redirect to login if there's no valid token\n    this.router.navigate(['/login']);\n    return false;\n  }\n  getUserDetailsFromToken(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT\n      return {\n        role: payload?.role || '',\n        groupe: payload?.groupe || ''\n      };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return {\n        role: '',\n        groupe: ''\n      };\n    }\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthenticationService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "next", "state", "token", "getToken", "console", "log", "role", "groupe", "getUserDetailsFromToken", "allowedGroups", "currentPath", "url", "endsWith", "slice", "expectedGroup", "toUpperCase", "warn", "navigate", "payload", "JSON", "parse", "atob", "split", "e", "error", "i0", "ɵɵinject", "i1", "AuthenticationService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { AuthenticationService } from './authentication.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {\n    const token = this.authService.getToken();\n    console.log(\"Token:\", token); // Debugging: log the token\n\n    if (token) {\n      const { role, groupe } = this.getUserDetailsFromToken(token);\n      console.log(\"User role from token:\", role, \"Group:\", groupe); // Debugging: log role and group\n\n      // Ensure path matching and group comparison are consistent and case insensitive\n      if (role === 'RESPONSABLE') {\n        const allowedGroups: Record<string, string> = {\n          '/transactions': 'TRANSACTION',\n          '/actions': 'ACTION',\n          '/actionnaires': 'ACTIONNAIRE',\n          '/reports': 'NOTIFICATION',\n          '/port': 'PORTEFEUILLE',\n          '/ResDash': groupe  // Always allow responsible users to access their dashboard\n        };\n\n        // Trim any trailing slashes to avoid issues with path matching\n        const currentPath = state.url.endsWith('/') ? state.url.slice(0, -1) : state.url;\n        console.log('Current path (trimmed):', currentPath);\n\n        const expectedGroup = allowedGroups[currentPath];\n        console.log('Expected group:', expectedGroup);\n\n        // Check if the expected group is available and match it with the user group\n        if (expectedGroup && groupe.toUpperCase() !== expectedGroup.toUpperCase()) {\n          console.warn(\"Access denied for group:\", groupe);\n          this.router.navigate(['/not-authorized']);\n          return false;\n        }\n      }\n\n      return true;\n    }\n\n    // Redirect to login if there's no valid token\n    this.router.navigate(['/login']);\n    return false;\n  }\n\n  private getUserDetailsFromToken(token: string): { role: string, groupe: string } {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT\n      return { role: payload?.role || '', groupe: payload?.groupe || '' };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return { role: '', groupe: '' };\n    }\n  }\n}\n"], "mappings": ";;;AAOA,OAAM,MAAOA,SAAS;EAEpBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;EAAW;EAEjFC,WAAWA,CAACC,IAA4B,EAAEC,KAA0B;IAClE,MAAMC,KAAK,GAAG,IAAI,CAACL,WAAW,CAACM,QAAQ,EAAE;IACzCC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEH,KAAK,CAAC,CAAC,CAAC;IAE9B,IAAIA,KAAK,EAAE;MACT,MAAM;QAAEI,IAAI;QAAEC;MAAM,CAAE,GAAG,IAAI,CAACC,uBAAuB,CAACN,KAAK,CAAC;MAC5DE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,IAAI,EAAE,QAAQ,EAAEC,MAAM,CAAC,CAAC,CAAC;MAE9D;MACA,IAAID,IAAI,KAAK,aAAa,EAAE;QAC1B,MAAMG,aAAa,GAA2B;UAC5C,eAAe,EAAE,aAAa;UAC9B,UAAU,EAAE,QAAQ;UACpB,eAAe,EAAE,aAAa;UAC9B,UAAU,EAAE,cAAc;UAC1B,OAAO,EAAE,cAAc;UACvB,UAAU,EAAEF,MAAM,CAAE;SACrB;QAED;QACA,MAAMG,WAAW,GAAGT,KAAK,CAACU,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,GAAGX,KAAK,CAACU,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGZ,KAAK,CAACU,GAAG;QAChFP,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEK,WAAW,CAAC;QAEnD,MAAMI,aAAa,GAAGL,aAAa,CAACC,WAAW,CAAC;QAChDN,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAES,aAAa,CAAC;QAE7C;QACA,IAAIA,aAAa,IAAIP,MAAM,CAACQ,WAAW,EAAE,KAAKD,aAAa,CAACC,WAAW,EAAE,EAAE;UACzEX,OAAO,CAACY,IAAI,CAAC,0BAA0B,EAAET,MAAM,CAAC;UAChD,IAAI,CAACT,MAAM,CAACmB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;UACzC,OAAO,KAAK;;;MAIhB,OAAO,IAAI;;IAGb;IACA,IAAI,CAACnB,MAAM,CAACmB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAChC,OAAO,KAAK;EACd;EAEQT,uBAAuBA,CAACN,KAAa;IAC3C,IAAI;MACF,MAAMgB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACnB,KAAK,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,OAAO;QAAEhB,IAAI,EAAEY,OAAO,EAAEZ,IAAI,IAAI,EAAE;QAAEC,MAAM,EAAEW,OAAO,EAAEX,MAAM,IAAI;MAAE,CAAE;KACpE,CAAC,OAAOgB,CAAC,EAAE;MACVnB,OAAO,CAACoB,KAAK,CAAC,uBAAuB,EAAED,CAAC,CAAC;MACzC,OAAO;QAAEjB,IAAI,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAE;;EAEnC;;;uBAtDWZ,SAAS,EAAA8B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATnC,SAAS;MAAAoC,OAAA,EAATpC,SAAS,CAAAqC,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}