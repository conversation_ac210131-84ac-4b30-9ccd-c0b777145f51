package com.Aziz.Administratif.Controllers;

import com.Aziz.Administratif.Entity.Habilitation;
import com.Aziz.Administratif.Services.HabilitationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/habilitations")
public class HabilitationController {

    private final HabilitationService habilitationService;

    @Autowired
    public HabilitationController(HabilitationService habilitationService) {
        this.habilitationService = habilitationService;
    }

    // Get all habilitations
    @GetMapping
    public ResponseEntity<List<Habilitation>> getAllHabilitations() {
        List<Habilitation> habilitations = habilitationService.findAllHabilitations();
        return new ResponseEntity<>(habilitations, HttpStatus.OK);
    }

    // Get habilitation by id
    @GetMapping("/{id}")
    public ResponseEntity<Habilitation> getHabilitationById(@PathVariable Long id) {
        Optional<Habilitation> habilitation = habilitationService.findHabilitationById(id);
        return habilitation.map(h -> new ResponseEntity<>(h, HttpStatus.OK))
                .orElseGet(() -> new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    // Create a new habilitation
    @PostMapping
    public ResponseEntity<Habilitation> createHabilitation(@RequestBody HabilitationRequest habilitationRequest) {
        Habilitation createdHabilitation = habilitationService.saveHabilitation(habilitationRequest.getIdGroupe(), habilitationRequest.getIdRessource());
        return new ResponseEntity<>(createdHabilitation, HttpStatus.CREATED);
    }
    // Update habilitation
    @PutMapping("/{id}")
    public ResponseEntity<Habilitation> updateHabilitation(@PathVariable Long id, @RequestBody Habilitation habilitation) {
        Habilitation updatedHabilitation = habilitationService.updateHabilitation(id, habilitation);
        return new ResponseEntity<>(updatedHabilitation, HttpStatus.OK);
    }

    // Delete habilitation
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteHabilitation(@PathVariable Long id) {
        habilitationService.deleteHabilitation(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    // Get all habilitations by groupeId
    @GetMapping("/byGroupe/{groupeId}")
    public ResponseEntity<List<Habilitation>> getHabilitationsByGroupeId(@PathVariable Long groupeId) {
        List<Habilitation> habilitations = habilitationService.findHabilitationsByGroupeId(groupeId);
        return new ResponseEntity<>(habilitations, HttpStatus.OK);
    }

}
