package com.Aziz.Client.Repositories;

import com.Aziz.Client.Entity.Actionnaire;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActionnaireRepository extends JpaRepository<Actionnaire, Long> {

    // Trouver un actionnaire par email
   Actionnaire findByEmailActionnaire(String emailActionnaire);

    // Chercher tous les actionnaires par nom
    List<Actionnaire> findByNomActionnaire(String nomActionnaire);



    // Recherche combinée (nom + prénom)
//    List<Actionnaire> findByNomActionnaireAndPrenomActionnaire(String nom, String prenom);
}
