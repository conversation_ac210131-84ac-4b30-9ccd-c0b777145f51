{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../authentication.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nexport class LoginComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.email = '';\n    this.password = '';\n  }\n  onLogin() {\n    this.authService.login(this.email, this.password).subscribe(response => {\n      this.authService.storeToken(response.token); // Store the JWT token\n      const userRole = this.getUserRoleFromToken(response.token);\n      if (userRole === 'Responsable') {\n        this.router.navigate(['/ResDash']); // ✅ Redirect if the role is \"Responsable\"\n      } else {\n        this.router.navigate(['/not-authorized']); // 🚫 Redirect if role is not allowed\n      }\n    });\n  }\n\n  getUserRoleFromToken(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT token\n      return payload.role || ''; // ✅ Ensure role exists\n    } catch (e) {\n      console.error('Invalid token:', e);\n      return '';\n    }\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 12,\n      vars: 2,\n      consts: [[3, \"ngSubmit\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"h2\");\n          i0.ɵɵtext(2, \"Login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"form\", 0);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_3_listener() {\n            return ctx.onLogin();\n          });\n          i0.ɵɵelementStart(4, \"label\", 1);\n          i0.ɵɵtext(5, \"Email:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"input\", 2);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_6_listener($event) {\n            return ctx.email = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"label\", 3);\n          i0.ɵɵtext(8, \"Password:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_9_listener($event) {\n            return ctx.password = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"button\", 5);\n          i0.ɵɵtext(11, \"Login\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.email);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.password);\n        }\n      },\n      dependencies: [i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["LoginComponent", "constructor", "authService", "router", "email", "password", "onLogin", "login", "subscribe", "response", "storeToken", "token", "userRole", "getUserRoleFromToken", "navigate", "payload", "JSON", "parse", "atob", "split", "role", "e", "console", "error", "i0", "ɵɵdirectiveInject", "i1", "AuthenticationService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_3_listener", "LoginComponent_Template_input_ngModelChange_6_listener", "$event", "LoginComponent_Template_input_ngModelChange_9_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { AuthenticationService } from '../authentication.service';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent {\n  email = '';\n  password = '';\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  onLogin() {\n    this.authService.login(this.email, this.password).subscribe(response => {\n      this.authService.storeToken(response.token);  // Store the JWT token\n      \n      const userRole = this.getUserRoleFromToken(response.token);\n      \n      if (userRole === 'Responsable') {\n        this.router.navigate(['/ResDash']);  // ✅ Redirect if the role is \"Responsable\"\n      } else {\n        this.router.navigate(['/not-authorized']);  // 🚫 Redirect if role is not allowed\n      }\n    });\n  }\n\n  private getUserRoleFromToken(token: string): string {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));  // Decode JWT token\n      return payload.role || ''; // ✅ Ensure role exists\n    } catch (e) {\n      console.error('Invalid token:', e);\n      return '';\n    }\n  }\n}\n", "<div>\n    <h2>Login</h2>\n    <form (ngSubmit)=\"onLogin()\">\n      <label for=\"email\">Email:</label>\n      <input type=\"email\" id=\"email\" [(ngModel)]=\"email\" name=\"email\" required />\n      \n      <label for=\"password\">Password:</label>\n      <input type=\"password\" id=\"password\" [(ngModel)]=\"password\" name=\"password\" required />\n      \n      <button type=\"submit\">Login</button>\n    </form>\n  </div>\n  "], "mappings": ";;;;AASA,OAAM,MAAOA,cAAc;EAIzBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;IAHtE,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,QAAQ,GAAG,EAAE;EAEoE;EAEjFC,OAAOA,CAAA;IACL,IAAI,CAACJ,WAAW,CAACK,KAAK,CAAC,IAAI,CAACH,KAAK,EAAE,IAAI,CAACC,QAAQ,CAAC,CAACG,SAAS,CAACC,QAAQ,IAAG;MACrE,IAAI,CAACP,WAAW,CAACQ,UAAU,CAACD,QAAQ,CAACE,KAAK,CAAC,CAAC,CAAE;MAE9C,MAAMC,QAAQ,GAAG,IAAI,CAACC,oBAAoB,CAACJ,QAAQ,CAACE,KAAK,CAAC;MAE1D,IAAIC,QAAQ,KAAK,aAAa,EAAE;QAC9B,IAAI,CAACT,MAAM,CAACW,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAE;OACtC,MAAM;QACL,IAAI,CAACX,MAAM,CAACW,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAE;;IAEhD,CAAC,CAAC;EACJ;;EAEQD,oBAAoBA,CAACF,KAAa;IACxC,IAAI;MACF,MAAMI,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACP,KAAK,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE;MACxD,OAAOJ,OAAO,CAACK,IAAI,IAAI,EAAE,CAAC,CAAC;KAC5B,CAAC,OAAOC,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEF,CAAC,CAAC;MAClC,OAAO,EAAE;;EAEb;;;uBA5BWrB,cAAc,EAAAwB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAd7B,cAAc;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT3BZ,EAAA,CAAAc,cAAA,UAAK;UACGd,EAAA,CAAAe,MAAA,YAAK;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACdhB,EAAA,CAAAc,cAAA,cAA6B;UAAvBd,EAAA,CAAAiB,UAAA,sBAAAC,iDAAA;YAAA,OAAYL,GAAA,CAAA/B,OAAA,EAAS;UAAA,EAAC;UAC1BkB,EAAA,CAAAc,cAAA,eAAmB;UAAAd,EAAA,CAAAe,MAAA,aAAM;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UACjChB,EAAA,CAAAc,cAAA,eAA2E;UAA5Cd,EAAA,CAAAiB,UAAA,2BAAAE,uDAAAC,MAAA;YAAA,OAAAP,GAAA,CAAAjC,KAAA,GAAAwC,MAAA;UAAA,EAAmB;UAAlDpB,EAAA,CAAAgB,YAAA,EAA2E;UAE3EhB,EAAA,CAAAc,cAAA,eAAsB;UAAAd,EAAA,CAAAe,MAAA,gBAAS;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UACvChB,EAAA,CAAAc,cAAA,eAAuF;UAAlDd,EAAA,CAAAiB,UAAA,2BAAAI,uDAAAD,MAAA;YAAA,OAAAP,GAAA,CAAAhC,QAAA,GAAAuC,MAAA;UAAA,EAAsB;UAA3DpB,EAAA,CAAAgB,YAAA,EAAuF;UAEvFhB,EAAA,CAAAc,cAAA,iBAAsB;UAAAd,EAAA,CAAAe,MAAA,aAAK;UAAAf,EAAA,CAAAgB,YAAA,EAAS;;;UALLhB,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAuB,UAAA,YAAAV,GAAA,CAAAjC,KAAA,CAAmB;UAGboB,EAAA,CAAAsB,SAAA,GAAsB;UAAtBtB,EAAA,CAAAuB,UAAA,YAAAV,GAAA,CAAAhC,QAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}