package com.Aziz.Client.Services;

import com.Aziz.Client.Entity.Action;
import com.Aziz.Client.Entity.Actionnaire;
import com.Aziz.Client.Entity.Portefeuille;
import com.Aziz.Client.Repositories.ActionnaireRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


@Service
@RequiredArgsConstructor
public class ActionnaireService {



    @Autowired
    private ActionnaireRepository ActionnaireRepository;



    public Optional<Actionnaire> findById(Long idActionnaire){
        return ActionnaireRepository.findById(idActionnaire);
    }




    public void saveActionnaire(Actionnaire actionnaire){
        Optional<Actionnaire> actionnaire1=ActionnaireRepository.findById(actionnaire.getIdActionnaire());
        if(actionnaire1.isPresent()){
            throw new RuntimeException("Actionnaire exist");
        }
        else{
            ActionnaireRepository.save(actionnaire);
        }
    }


    public List<Actionnaire> findAllActionnaires(){
        return ActionnaireRepository.findAll();
    }


    public void deleteActionnaire(Long idActionnaire) {
        ActionnaireRepository.deleteById(idActionnaire);
    }










    public Actionnaire updateActionnaire(Long idActionnaire, Actionnaire updatedActionnaire) {
        return ActionnaireRepository.findById(idActionnaire).map(existingActionnaire -> {
            // Update basic user details
            existingActionnaire.setNomActionnaire(updatedActionnaire.getNomActionnaire());
            existingActionnaire.setTelephone(updatedActionnaire.getTelephone());
            existingActionnaire.setPrenomActionnaire(updatedActionnaire.getPrenomActionnaire());

            Actionnaire savedActionnaire = ActionnaireRepository.save(existingActionnaire);


            return savedActionnaire;

        }).orElseThrow(() -> new RuntimeException("User not found"));
    }


    public List<Portefeuille> getPortefeuillesByActionnaireId(Long idActionnaire) {
        Actionnaire actionnaire = ActionnaireRepository.findById(idActionnaire)
                .orElseThrow(() -> new RuntimeException("Actionnaire not found"));

        return actionnaire.getPortefeuilles();
    }


    public List<Action> getActionsByActionnaireId(Long idActionnaire) {
        Actionnaire actionnaire = ActionnaireRepository.findById(idActionnaire)
                .orElseThrow(() -> new RuntimeException("Actionnaire not found"));

        return actionnaire.getPortefeuilles().stream()
                .map(Portefeuille::getAction)
                .distinct()
                .toList(); // Java 16+ ; sinon, utilise `.collect(Collectors.toList())`
    }


}
