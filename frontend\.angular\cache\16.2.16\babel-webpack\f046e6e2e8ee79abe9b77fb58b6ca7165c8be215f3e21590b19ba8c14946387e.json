{"ast": null, "code": "import * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js'; // Import Chart.js and register components\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = [\"myChart\"];\nfunction UsersComponent_tr_77_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\")(1, \"i\", 44);\n    i0.ɵɵlistener(\"click\", function UsersComponent_tr_77_span_20_Template_i_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const responsable_r1 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(responsable_r1.id !== undefined ? ctx_r5.toggleState(responsable_r1.id, responsable_r1.state) : null);\n    });\n    i0.ɵɵtext(2, \" check_circle \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UsersComponent_tr_77_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 45);\n    i0.ɵɵlistener(\"click\", function UsersComponent_tr_77_ng_template_21_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const responsable_r1 = i0.ɵɵnextContext().$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(responsable_r1.id !== undefined && responsable_r1.state !== undefined ? ctx_r8.toggleState(responsable_r1.id, responsable_r1.state) : null);\n    });\n    i0.ɵɵtext(1, \" lock \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a1) {\n  return [\"/edit-responsable\", a1];\n};\nfunction UsersComponent_tr_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"td\");\n    i0.ɵɵtemplate(20, UsersComponent_tr_77_span_20_Template, 3, 0, \"span\", 40);\n    i0.ɵɵtemplate(21, UsersComponent_tr_77_ng_template_21_Template, 2, 0, \"ng-template\", null, 41, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(23, \"a\", 42)(24, \"i\", 43);\n    i0.ɵɵtext(25, \"edit\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const responsable_r1 = ctx.$implicit;\n    const _r3 = i0.ɵɵreference(22);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.nom);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.prenom);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.telephone);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.role);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.groupe == null ? null : responsable_r1.groupe.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", responsable_r1.state === true ? \"Actife\" : \"bloqu\\u00E9\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", responsable_r1.flag ? \"Yes\" : \"No\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", responsable_r1.state === true)(\"ngIfElse\", _r3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(12, _c1, responsable_r1.id));\n  }\n}\nconst _c2 = function () {\n  return [\"/add-responsable\"];\n};\nconst _c3 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n  }\\n  \\n  .feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);\\n  }\\n  \\n  .sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #333;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n  }\\n  \\n  .sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, .25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .border-top[_ngcontent-%COMP%] { border-top: 1px solid #e5e5e5; }\\n  .border-bottom[_ngcontent-%COMP%] { border-bottom: 1px solid #e5e5e5; }\\n  \\n\\n  .btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n  }\\n  \\n\\n\\n  body[_ngcontent-%COMP%] {\\n    font-family: 'Poppins', sans-serif;\\n    margin: 0;\\n    padding: 0;\\n    min-height: 100vh;\\n    background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n    background-size: 400% 400%;\\n    animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n    color: #fff;\\n  }\\n\\n  @keyframes _ngcontent-%COMP%_gradientMove {\\n    0% { background-position: 0% 50%; }\\n    50% { background-position: 100% 50%; }\\n    100% { background-position: 0% 50%; }\\n  }\\n\\n  .container-fluid[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 2rem;\\n    margin-top: 20px;\\n  }\\n\\n  .table-responsive[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 1.5rem;\\n    -webkit-backdrop-filter: blur(12px);\\n            backdrop-filter: blur(12px);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n  }\\n\\n  .table[_ngcontent-%COMP%] {\\n    color: #fff;\\n    border-radius: 15px;\\n    overflow: hidden;\\n    border-collapse: separate;\\n    border-spacing: 0 12px;\\n  }\\n\\n  .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.15);\\n    color: #ffffff;\\n    font-weight: 600;\\n    text-align: center;\\n    border: none;\\n    padding: 1rem;\\n  }\\n\\n  .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.1);\\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\\n    text-align: center;\\n    border-radius: 12px;\\n  }\\n\\n  .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.01);\\n    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n  }\\n\\n  .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    vertical-align: middle;\\n    padding: 0.9rem;\\n    font-size: 0.95rem;\\n  }\\n\\n  .btn-custom[_ngcontent-%COMP%] {\\n    background-color: #28a745;\\n    color: white;\\n    border: none;\\n    padding: 12px 25px;\\n    border-radius: 30px;\\n    text-transform: uppercase;\\n    font-weight: bold;\\n    transition: background 0.3s, transform 0.2s;\\n    box-shadow: 0 4px 15px rgba(0,0,0,0.2);\\n  }\\n\\n  .btn-custom[_ngcontent-%COMP%]:hover {\\n    background-color: #218838;\\n    transform: translateY(-2px);\\n  }\\n\\n  .edit-icon[_ngcontent-%COMP%] {\\n    color: #299216;\\n    cursor: pointer;\\n    font-size: 20px;\\n    margin: 0 10px;\\n    transition: color 0.3s;\\n  }\\n\\n  .edit-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n  }\\n\\n  .delete-icon[_ngcontent-%COMP%] {\\n    color: #d22d2d;\\n    cursor: pointer;\\n    font-size: 20px;\\n    transition: color 0.3s;\\n  }\\n\\n  .delete-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n  }\\n\\n  .navbar[_ngcontent-%COMP%] {\\n    background-color: #1c1c1c !important;\\n  }\\n\\n  h2[_ngcontent-%COMP%] {\\n    color: #fff;\\n  }\\n\\n  a.router-link[_ngcontent-%COMP%] {\\n    text-decoration: none;\\n  }\\n\\n\\n  \\n\\n.mb-3[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column; \\n\\n    align-items: flex-start; \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n    margin-top: 10px;\\n    background-color: rgba(255, 255, 255, 0.1); \\n\\n    border: 1px solid rgba(255, 255, 255, 0.3); \\n\\n    border-radius: 30px; \\n\\n    color: #fff; \\n\\n    padding: 10px 20px; \\n\\n    font-size: 1rem; \\n\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); \\n\\n    width: 100%; \\n\\n    max-width: 400px; \\n\\n    transition: all 0.3s ease; \\n\\n}\\n\\n.search-bar[_ngcontent-%COMP%]:focus {\\n    outline: none; \\n\\n    border-color: #007bff; \\n\\n    box-shadow: 0 0 8px rgba(0, 123, 255, 0.6); \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%]::placeholder {\\n    color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n  background: rgba(20, 33, 59, 0.9); \\n\\n  color: #fff;\\n  min-height: 100vh;\\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #ccc;\\n  transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n\\n  \\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n  background-color: #000 !important;\\n  color: #fff;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n  position: relative;\\n  padding: 0.5rem 1rem;\\n  transition: color 0.3s ease;\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  width: 0;\\n  background: #ff4c60;\\n  transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n.logo-img[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 30px;\\n  object-fit: cover;\\n  border-radius: 50%; \\n\\n  margin-right: 8px;\\n}\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class UsersComponent {\n  constructor(responsableService, cdr, router) {\n    this.responsableService = responsableService;\n    this.cdr = cdr;\n    this.router = router;\n    this.responsables = [];\n    this.filteredResponsables = [];\n    this.searchQuery = '';\n  }\n  ngOnInit() {\n    this.loadResponsables();\n  }\n  loadResponsables() {\n    this.responsableService.getAllUsers().subscribe(data => {\n      // Add default values for missing state and flag\n      this.responsables = data.map(responsable => ({\n        ...responsable,\n        state: responsable.state || false,\n        flag: responsable.flag || false // Default to false if flag is missing\n      }));\n\n      this.filteredResponsables = this.responsables;\n    }, error => {\n      console.error('Error fetching users:', error);\n    });\n  }\n  filterUsers() {\n    this.filteredResponsables = this.responsables.filter(responsable => responsable.nom.toLowerCase().includes(this.searchQuery.toLowerCase()) || responsable.prenom.toLowerCase().includes(this.searchQuery.toLowerCase()) || responsable.email.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n  deleteResponsable(id) {\n    if (id === undefined) {\n      console.error(\"Error: Responsable ID is undefined\");\n      return;\n    }\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: response => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n          this.filterUsers(); // Reapply filter after deletion\n        },\n\n        error: error => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n  toggleState(id, currentState) {\n    const newState = !currentState; // Toggle the state value\n    const updatedResponsable = this.responsables.find(responsable => responsable.id === id);\n    if (updatedResponsable) {\n      updatedResponsable.state = newState; // Update the state locally first\n      this.updateResponsable(id, updatedResponsable); // Update the backend\n    }\n  }\n\n  toggleFlag(id, currentFlag) {\n    const newFlag = !currentFlag; // Toggle the flag value\n    const updatedResponsable = this.responsables.find(responsable => responsable.id === id);\n    if (updatedResponsable) {\n      updatedResponsable.flag = newFlag; // Update the flag locally first\n      this.updateResponsable(id, updatedResponsable); // Update the backend\n    }\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  updateResponsable(id, updatedResponsable) {\n    if (id && updatedResponsable) {\n      // Ensure that the id and updatedResponsable are valid\n      this.responsableService.updateResponsable(id, updatedResponsable).subscribe(() => {\n        this.loadResponsables();\n      }, error => {\n        console.error('Error updating responsable:', error);\n      });\n    }\n  }\n  trackById(index, item) {\n    return item.id ?? 0; // Return 0 if id is undefined\n  }\n\n  ngAfterViewInit() {\n    feather.replace();\n    Chart.register(...registerables); // Register chart.js components for version 4+\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement;\n      // Ensure the chart has data and a configuration\n      const chart = new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n          datasets: [{\n            data: [15339, 21345, 18483, 24003, 23489, 24092, 12034],\n            tension: 0.4,\n            backgroundColor: 'rgba(78,115,223,0.2)',\n            borderColor: 'rgba(78,115,223,1)',\n            borderWidth: 2\n          }]\n        },\n        options: {\n          responsive: true,\n          scales: {\n            x: {\n              beginAtZero: true\n            },\n            y: {\n              beginAtZero: true\n            }\n          }\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function UsersComponent_Factory(t) {\n      return new (t || UsersComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UsersComponent,\n      selectors: [[\"app-respon-dashboard\"]],\n      viewQuery: function UsersComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 78,\n      vars: 4,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/css/bootstrap.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"text-center\", \"mt-4\", \"mb-4\"], [1, \"d-flex\", \"justify-content-end\", \"mb-2\"], [1, \"btn\", \"btn-custom\", 3, \"routerLink\"], [1, \"material-icons\", \"align-middle\", 2, \"font-size\", \"20px\"], [1, \"table-responsive\"], [1, \"mb-3\", \"d-flex\", \"justify-content-between\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"w-50\", \"search-bar\", 3, \"ngModel\", \"ngModelChange\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\", \"ngIfElse\"], [\"blocked\", \"\"], [3, \"routerLink\"], [\"data-toggle\", \"tooltip\", \"title\", \"Modifier\", 1, \"material-icons\", \"edit-icon\"], [1, \"material-icons\", \"text-success\", 3, \"click\"], [1, \"material-icons\", \"text-danger\", 3, \"click\"]],\n      template: function UsersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"link\", 6)(8, \"link\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10);\n          i0.ɵɵelementStart(12, \"title\");\n          i0.ɵɵtext(13, \"Dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"body\")(15, \"nav\", 11)(16, \"a\", 12);\n          i0.ɵɵelement(17, \"img\", 13);\n          i0.ɵɵtext(18, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"ul\", 14)(20, \"li\", 15)(21, \"a\", 16);\n          i0.ɵɵlistener(\"click\", function UsersComponent_Template_a_click_21_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(22, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 17)(24, \"div\", 18)(25, \"nav\", 19)(26, \"div\", 20)(27, \"ul\", 21)(28, \"li\", 22)(29, \"a\", 23);\n          i0.ɵɵelement(30, \"span\", 24);\n          i0.ɵɵtext(31, \" Dashboard \");\n          i0.ɵɵelementStart(32, \"span\", 25);\n          i0.ɵɵtext(33, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"li\", 22)(35, \"a\", 26);\n          i0.ɵɵelement(36, \"span\", 27);\n          i0.ɵɵtext(37, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"li\", 22)(39, \"a\", 28);\n          i0.ɵɵelement(40, \"span\", 29);\n          i0.ɵɵtext(41, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(42, \"main\", 30)(43, \"h2\", 31);\n          i0.ɵɵtext(44, \"Liste des utilisateurs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 32)(46, \"a\", 33)(47, \"i\", 34);\n          i0.ɵɵtext(48, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" Ajouter un utilisateur \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 35)(51, \"div\", 36)(52, \"input\", 37);\n          i0.ɵɵlistener(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_52_listener($event) {\n            return ctx.searchQuery = $event;\n          })(\"ngModelChange\", function UsersComponent_Template_input_ngModelChange_52_listener() {\n            return ctx.filterUsers();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"table\", 38)(54, \"thead\")(55, \"tr\")(56, \"th\");\n          i0.ɵɵtext(57, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"th\");\n          i0.ɵɵtext(59, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"th\");\n          i0.ɵɵtext(61, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"th\");\n          i0.ɵɵtext(63, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"th\");\n          i0.ɵɵtext(65, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"th\");\n          i0.ɵɵtext(67, \"R\\u00F4le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"th\");\n          i0.ɵɵtext(69, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"th\");\n          i0.ɵɵtext(71, \"\\u00C9tat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"th\");\n          i0.ɵɵtext(73, \"Flag\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"th\");\n          i0.ɵɵtext(75, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(76, \"tbody\");\n          i0.ɵɵtemplate(77, UsersComponent_tr_77_Template, 26, 14, \"tr\", 39);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(46);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c2));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredResponsables);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i2.RouterLink],\n      styles: [_c3, _c3]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "Chart", "registerables", "i0", "ɵɵelementStart", "ɵɵlistener", "UsersComponent_tr_77_span_20_Template_i_click_1_listener", "ɵɵrestoreView", "_r7", "responsable_r1", "ɵɵnextContext", "$implicit", "ctx_r5", "ɵɵresetView", "id", "undefined", "toggleState", "state", "ɵɵtext", "ɵɵelementEnd", "UsersComponent_tr_77_ng_template_21_Template_i_click_0_listener", "_r10", "ctx_r8", "ɵɵtemplate", "UsersComponent_tr_77_span_20_Template", "UsersComponent_tr_77_ng_template_21_Template", "ɵɵtemplateRefExtractor", "ɵɵadvance", "ɵɵtextInterpolate", "nom", "prenom", "email", "telephone", "role", "groupe", "nomGroupe", "ɵɵtextInterpolate1", "flag", "ɵɵproperty", "_r3", "ɵɵpureFunction1", "_c1", "UsersComponent", "constructor", "responsableService", "cdr", "router", "responsables", "filteredResponsables", "searchQuery", "ngOnInit", "loadResponsables", "getAllUsers", "subscribe", "data", "map", "responsable", "error", "console", "filterUsers", "filter", "toLowerCase", "includes", "deleteResponsable", "confirm", "next", "response", "log", "currentState", "newState", "updatedResponsable", "find", "updateResponsable", "toggleFlag", "currentFlag", "newFlag", "logout", "localStorage", "removeItem", "getItem", "navigate", "trackById", "index", "item", "ngAfterViewInit", "replace", "register", "myChartRef", "ctx", "nativeElement", "chart", "type", "labels", "datasets", "tension", "backgroundColor", "borderColor", "borderWidth", "options", "responsive", "scales", "x", "beginAtZero", "y", "ɵɵdirectiveInject", "i1", "ResponsableService", "ChangeDetectorRef", "i2", "Router", "selectors", "viewQuery", "UsersComponent_Query", "rf", "ɵɵelement", "UsersComponent_Template_a_click_21_listener", "UsersComponent_Template_input_ngModelChange_52_listener", "$event", "UsersComponent_tr_77_Template", "ɵɵpureFunction0", "_c2"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\users\\users.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\users\\users.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, AfterViewInit, ElementRef, ChangeDetectorRef } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js'; // Import Chart.js and register components\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './users.component.html',\n  styleUrls: ['./users.component.css']\n})\nexport class UsersComponent implements AfterViewInit, OnInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  responsables: User[] = []; \n  filteredResponsables: User[] = [];\n  searchQuery: string = '';\n\n  constructor(\n    private responsableService: ResponsableService,\n    private cdr: ChangeDetectorRef, \n    private router: Router \n  ) {}\n\n  ngOnInit(): void {\n    this.loadResponsables();\n  }\n\n  loadResponsables() {\n    this.responsableService.getAllUsers().subscribe(\n      (data) => {\n        // Add default values for missing state and flag\n        this.responsables = data.map(responsable => ({\n          ...responsable,\n          state: responsable.state || false, // Default to false if state is missing\n          flag: responsable.flag || false,   // Default to false if flag is missing\n        }));\n        this.filteredResponsables = this.responsables;\n      },\n      (error) => {\n        console.error('Error fetching users:', error);\n      }\n    );\n  }\n\n  filterUsers() {\n    this.filteredResponsables = this.responsables.filter(responsable => \n      responsable.nom.toLowerCase().includes(this.searchQuery.toLowerCase()) || \n      responsable.prenom.toLowerCase().includes(this.searchQuery.toLowerCase()) || \n      responsable.email.toLowerCase().includes(this.searchQuery.toLowerCase())\n    );\n  }\n\n  deleteResponsable(id?: number) {\n    if (id === undefined) {\n      console.error(\"Error: Responsable ID is undefined\");\n      return;\n    }\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: (response) => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n          this.filterUsers();  // Reapply filter after deletion\n        },\n        error: (error) => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n\n  toggleState(id: number, currentState: boolean) {\n    const newState = !currentState;  // Toggle the state value\n    const updatedResponsable = this.responsables.find(responsable => responsable.id === id);\n    if (updatedResponsable) {\n      updatedResponsable.state = newState;  // Update the state locally first\n      this.updateResponsable(id, updatedResponsable);  // Update the backend\n    }\n  }\n\n  toggleFlag(id: number, currentFlag: boolean) {\n    const newFlag = !currentFlag;  // Toggle the flag value\n    const updatedResponsable = this.responsables.find(responsable => responsable.id === id);\n    if (updatedResponsable) {\n      updatedResponsable.flag = newFlag;  // Update the flag locally first\n      this.updateResponsable(id, updatedResponsable);  // Update the backend\n    }\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n\n  updateResponsable(id: number, updatedResponsable: User): void {\n    if (id && updatedResponsable) {  // Ensure that the id and updatedResponsable are valid\n      this.responsableService.updateResponsable(id, updatedResponsable).subscribe(\n        () => {\n          this.loadResponsables(); \n        },\n        (error) => {\n          console.error('Error updating responsable:', error);\n        }\n      );\n    }\n  }\n\n  trackById(index: number, item: User): number | undefined {\n    return item.id ?? 0;  // Return 0 if id is undefined\n  }\n\n  ngAfterViewInit() {\n    feather.replace();\n    Chart.register(...registerables);  // Register chart.js components for version 4+\n\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement as HTMLCanvasElement;\n\n      // Ensure the chart has data and a configuration\n      const chart = new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n          datasets: [{\n            data: [15339, 21345, 18483, 24003, 23489, 24092, 12034],\n            tension: 0.4, // Replaces lineTension\n            backgroundColor: 'rgba(78,115,223,0.2)',\n            borderColor: 'rgba(78,115,223,1)',\n            borderWidth: 2\n          }]\n        },\n        options: {\n          responsive: true,\n          scales: {\n            x: { beginAtZero: true },\n            y: { beginAtZero: true }\n          }\n        }\n      });\n    }\n  }\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/css/bootstrap.min.css\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n    <link href=\"./users.component.css\" rel=\"stylesheet\">\n    <title>Dashboard</title>\n  </head>\n\n  <body>\n    <!-- Navbar -->\n    <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\" href=\"adminDash\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"users\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <h2 class=\"text-center mt-4 mb-4\">Liste des utilisateurs</h2>\n        \n         \n          <div class=\"d-flex justify-content-end mb-2\">\n            <a [routerLink]=\"['/add-responsable']\" class=\"btn btn-custom\">\n              <i class=\"material-icons align-middle\" style=\"font-size: 20px;\">add</i>\n              Ajouter un utilisateur\n            </a>\n          </div>\n          <div class=\"table-responsive\">\n <!-- Search Bar and Add Button -->\n <div class=\"mb-3 d-flex justify-content-between\">      \n  <!-- Add class 'search-bar' to search input -->\n  <input class=\"form-control w-50 search-bar\" \n         type=\"text\" \n         placeholder=\"Search\" \n         aria-label=\"Search\" \n         [(ngModel)]=\"searchQuery\" \n         (ngModelChange)=\"filterUsers()\">\n</div>      \n            <table class=\"table table-hover\">\n              <thead>\n                <tr>\n                  <th>ID</th>\n                  <th>Nom</th>\n                  <th>Prénom</th>\n                  <th>Email</th>\n                  <th>Téléphone</th>\n                  <th>Rôle</th>\n                  <th>Groupe</th>\n                  <th>État</th> <!-- Added column for State -->\n                  <th>Flag</th> <!-- Added column for Flag -->\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let responsable of filteredResponsables\">\n                  <td>{{ responsable.id }}</td>\n                  <td>{{ responsable.nom }}</td>\n                  <td>{{ responsable.prenom }}</td>\n                  <td>{{ responsable.email }}</td>\n                  <td>{{ responsable.telephone }}</td>\n                  <td>{{ responsable.role }}</td>\n                  <td>{{ responsable.groupe?.nomGroupe }}</td>\n                  \n\n                  <td>\n                    <!-- Ensure 'state' is treated as a boolean for comparison -->\n                    {{ responsable.state === true ? 'Actife' : 'bloqué' }}\n                  </td>\n                  \n                  <td>\n                    <!-- Flag: Display 'Yes' if true, 'No' if false -->\n                    {{ responsable.flag ? 'Yes' : 'No' }}\n                    <td>\n                      <span *ngIf=\"responsable.state === true; else blocked\">\n                        <i class=\"material-icons text-success\" \n                           (click)=\"responsable.id !== undefined ? toggleState(responsable.id, responsable.state) : null\">\n                          check_circle\n                        </i> \n                        \n                      </span>\n                      <ng-template #blocked>\n                        <i class=\"material-icons text-danger\" \n                           (click)=\"responsable.id !== undefined && responsable.state !== undefined ? toggleState(responsable.id, responsable.state) : null\">\n                          lock\n                        </i> \n                        \n                      </ng-template>\n                      \n                    \n                    \n                    <a [routerLink]=\"['/edit-responsable', responsable.id]\">\n                      <i class=\"material-icons edit-icon\" data-toggle=\"tooltip\" title=\"Modifier\">edit</i>\n                    </a>\n                 \n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <!-- Scripts -->\n    <script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js\"></script>\n    <script src=\"https://unpkg.com/feather-icons\"></script>\n    <script>feather.replace()</script>\n  </body>\n</html>\n"], "mappings": "AAGA,OAAO,KAAKA,OAAO,MAAM,eAAe;AACxC,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU,CAAC,CAAC;;;;;;;;;;IC8G3BC,EAAA,CAAAC,cAAA,WAAuD;IAElDD,EAAA,CAAAE,UAAA,mBAAAC,yDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,cAAA,GAAAN,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAU,WAAA,CAAAJ,cAAA,CAAAK,EAAA,KAAmBC,SAAS,GAAGH,MAAA,CAAAI,WAAA,CAAAP,cAAA,CAAAK,EAAA,EAAAL,cAAA,CAAAQ,KAAA,CAA8C,GAAG,IAAI;IAAA,EAAC;IAC/Fd,EAAA,CAAAe,MAAA,qBACF;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;;IAIJhB,EAAA,CAAAC,cAAA,YACqI;IAAlID,EAAA,CAAAE,UAAA,mBAAAe,gEAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAc,IAAA;MAAA,MAAAZ,cAAA,GAAAN,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAW,MAAA,GAAAnB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAU,WAAA,CAAAJ,cAAA,CAAAK,EAAA,KAAmBC,SAAS,IAAAN,cAAA,CAAAQ,KAAA,KAA0BF,SAAS,GAAGO,MAAA,CAAAN,WAAA,CAAAP,cAAA,CAAAK,EAAA,EAAAL,cAAA,CAAAQ,KAAA,CAA8C,GAAG,IAAI;IAAA,EAAC;IAClId,EAAA,CAAAe,MAAA,aACF;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;;;;IA9BZhB,EAAA,CAAAC,cAAA,SAAqD;IAC/CD,EAAA,CAAAe,MAAA,GAAoB;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAC7BhB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAe,MAAA,GAAqB;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAC9BhB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAe,MAAA,GAAwB;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IACjChB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAe,MAAA,GAAuB;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAChChB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAe,MAAA,IAA2B;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IACpChB,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAe,MAAA,IAAsB;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAC/BhB,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAe,MAAA,IAAmC;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAG5ChB,EAAA,CAAAC,cAAA,UAAI;IAEFD,EAAA,CAAAe,MAAA,IACF;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IAELhB,EAAA,CAAAC,cAAA,UAAI;IAEFD,EAAA,CAAAe,MAAA,IACA;IAHFf,EAAA,CAAAgB,YAAA,EAAI;IAGFhB,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAoB,UAAA,KAAAC,qCAAA,mBAMO;IACPrB,EAAA,CAAAoB,UAAA,KAAAE,4CAAA,iCAAAtB,EAAA,CAAAuB,sBAAA,CAMc;IAIhBvB,EAAA,CAAAC,cAAA,aAAwD;IACqBD,EAAA,CAAAe,MAAA,YAAI;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;IApCnFhB,EAAA,CAAAwB,SAAA,GAAoB;IAApBxB,EAAA,CAAAyB,iBAAA,CAAAnB,cAAA,CAAAK,EAAA,CAAoB;IACpBX,EAAA,CAAAwB,SAAA,GAAqB;IAArBxB,EAAA,CAAAyB,iBAAA,CAAAnB,cAAA,CAAAoB,GAAA,CAAqB;IACrB1B,EAAA,CAAAwB,SAAA,GAAwB;IAAxBxB,EAAA,CAAAyB,iBAAA,CAAAnB,cAAA,CAAAqB,MAAA,CAAwB;IACxB3B,EAAA,CAAAwB,SAAA,GAAuB;IAAvBxB,EAAA,CAAAyB,iBAAA,CAAAnB,cAAA,CAAAsB,KAAA,CAAuB;IACvB5B,EAAA,CAAAwB,SAAA,GAA2B;IAA3BxB,EAAA,CAAAyB,iBAAA,CAAAnB,cAAA,CAAAuB,SAAA,CAA2B;IAC3B7B,EAAA,CAAAwB,SAAA,GAAsB;IAAtBxB,EAAA,CAAAyB,iBAAA,CAAAnB,cAAA,CAAAwB,IAAA,CAAsB;IACtB9B,EAAA,CAAAwB,SAAA,GAAmC;IAAnCxB,EAAA,CAAAyB,iBAAA,CAAAnB,cAAA,CAAAyB,MAAA,kBAAAzB,cAAA,CAAAyB,MAAA,CAAAC,SAAA,CAAmC;IAKrChC,EAAA,CAAAwB,SAAA,GACF;IADExB,EAAA,CAAAiC,kBAAA,MAAA3B,cAAA,CAAAQ,KAAA,0CACF;IAIEd,EAAA,CAAAwB,SAAA,GACA;IADAxB,EAAA,CAAAiC,kBAAA,MAAA3B,cAAA,CAAA4B,IAAA,qBACA;IACSlC,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAmC,UAAA,SAAA7B,cAAA,CAAAQ,KAAA,UAAkC,aAAAsB,GAAA;IAiBxCpC,EAAA,CAAAwB,SAAA,GAAoD;IAApDxB,EAAA,CAAAmC,UAAA,eAAAnC,EAAA,CAAAqC,eAAA,KAAAC,GAAA,EAAAhC,cAAA,CAAAK,EAAA,EAAoD;;;;;;;ADvH3E,OAAM,MAAO4B,cAAc;EAMzBC,YACUC,kBAAsC,EACtCC,GAAsB,EACtBC,MAAc;IAFd,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,oBAAoB,GAAW,EAAE;IACjC,KAAAC,WAAW,GAAW,EAAE;EAMrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACP,kBAAkB,CAACQ,WAAW,EAAE,CAACC,SAAS,CAC5CC,IAAI,IAAI;MACP;MACA,IAAI,CAACP,YAAY,GAAGO,IAAI,CAACC,GAAG,CAACC,WAAW,KAAK;QAC3C,GAAGA,WAAW;QACdvC,KAAK,EAAEuC,WAAW,CAACvC,KAAK,IAAI,KAAK;QACjCoB,IAAI,EAAEmB,WAAW,CAACnB,IAAI,IAAI,KAAK,CAAI;OACpC,CAAC,CAAC;;MACH,IAAI,CAACW,oBAAoB,GAAG,IAAI,CAACD,YAAY;IAC/C,CAAC,EACAU,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,CACF;EACH;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACX,oBAAoB,GAAG,IAAI,CAACD,YAAY,CAACa,MAAM,CAACJ,WAAW,IAC9DA,WAAW,CAAC3B,GAAG,CAACgC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACb,WAAW,CAACY,WAAW,EAAE,CAAC,IACtEL,WAAW,CAAC1B,MAAM,CAAC+B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACb,WAAW,CAACY,WAAW,EAAE,CAAC,IACzEL,WAAW,CAACzB,KAAK,CAAC8B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACb,WAAW,CAACY,WAAW,EAAE,CAAC,CACzE;EACH;EAEAE,iBAAiBA,CAACjD,EAAW;IAC3B,IAAIA,EAAE,KAAKC,SAAS,EAAE;MACpB2C,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAC;MACnD;;IAEF,IAAIO,OAAO,CAAC,mDAAmD,CAAC,EAAE;MAChE,IAAI,CAACpB,kBAAkB,CAACmB,iBAAiB,CAACjD,EAAE,CAAC,CAACuC,SAAS,CAAC;QACtDY,IAAI,EAAGC,QAAQ,IAAI;UACjBR,OAAO,CAACS,GAAG,CAAC,kBAAkB,EAAED,QAAQ,CAAC;UACzC,IAAI,CAACnB,YAAY,GAAG,IAAI,CAACA,YAAY,CAACa,MAAM,CAACJ,WAAW,IAAIA,WAAW,CAAC1C,EAAE,KAAKA,EAAE,CAAC;UAClF,IAAI,CAAC6C,WAAW,EAAE,CAAC,CAAE;QACvB,CAAC;;QACDF,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAEAzC,WAAWA,CAACF,EAAU,EAAEsD,YAAqB;IAC3C,MAAMC,QAAQ,GAAG,CAACD,YAAY,CAAC,CAAE;IACjC,MAAME,kBAAkB,GAAG,IAAI,CAACvB,YAAY,CAACwB,IAAI,CAACf,WAAW,IAAIA,WAAW,CAAC1C,EAAE,KAAKA,EAAE,CAAC;IACvF,IAAIwD,kBAAkB,EAAE;MACtBA,kBAAkB,CAACrD,KAAK,GAAGoD,QAAQ,CAAC,CAAE;MACtC,IAAI,CAACG,iBAAiB,CAAC1D,EAAE,EAAEwD,kBAAkB,CAAC,CAAC,CAAE;;EAErD;;EAEAG,UAAUA,CAAC3D,EAAU,EAAE4D,WAAoB;IACzC,MAAMC,OAAO,GAAG,CAACD,WAAW,CAAC,CAAE;IAC/B,MAAMJ,kBAAkB,GAAG,IAAI,CAACvB,YAAY,CAACwB,IAAI,CAACf,WAAW,IAAIA,WAAW,CAAC1C,EAAE,KAAKA,EAAE,CAAC;IACvF,IAAIwD,kBAAkB,EAAE;MACtBA,kBAAkB,CAACjC,IAAI,GAAGsC,OAAO,CAAC,CAAE;MACpC,IAAI,CAACH,iBAAiB,CAAC1D,EAAE,EAAEwD,kBAAkB,CAAC,CAAC,CAAE;;EAErD;;EAEAM,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCpB,OAAO,CAACS,GAAG,CAACU,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACjC,MAAM,CAACkC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAR,iBAAiBA,CAAC1D,EAAU,EAAEwD,kBAAwB;IACpD,IAAIxD,EAAE,IAAIwD,kBAAkB,EAAE;MAAG;MAC/B,IAAI,CAAC1B,kBAAkB,CAAC4B,iBAAiB,CAAC1D,EAAE,EAAEwD,kBAAkB,CAAC,CAACjB,SAAS,CACzE,MAAK;QACH,IAAI,CAACF,gBAAgB,EAAE;MACzB,CAAC,EACAM,KAAK,IAAI;QACRC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD,CAAC,CACF;;EAEL;EAEAwB,SAASA,CAACC,KAAa,EAAEC,IAAU;IACjC,OAAOA,IAAI,CAACrE,EAAE,IAAI,CAAC,CAAC,CAAE;EACxB;;EAEAsE,eAAeA,CAAA;IACbpF,OAAO,CAACqF,OAAO,EAAE;IACjBpF,KAAK,CAACqF,QAAQ,CAAC,GAAGpF,aAAa,CAAC,CAAC,CAAE;IAEnC,IAAI,IAAI,CAACqF,UAAU,EAAE;MACnB,MAAMC,GAAG,GAAG,IAAI,CAACD,UAAU,CAACE,aAAkC;MAE9D;MACA,MAAMC,KAAK,GAAG,IAAIzF,KAAK,CAACuF,GAAG,EAAE;QAC3BG,IAAI,EAAE,MAAM;QACZrC,IAAI,EAAE;UACJsC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;UACtFC,QAAQ,EAAE,CAAC;YACTvC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YACvDwC,OAAO,EAAE,GAAG;YACZC,eAAe,EAAE,sBAAsB;YACvCC,WAAW,EAAE,oBAAoB;YACjCC,WAAW,EAAE;WACd;SACF;QACDC,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACNC,CAAC,EAAE;cAAEC,WAAW,EAAE;YAAI,CAAE;YACxBC,CAAC,EAAE;cAAED,WAAW,EAAE;YAAI;;;OAG3B,CAAC;;EAEN;;;uBApIW5D,cAAc,EAAAvC,EAAA,CAAAqG,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAvG,EAAA,CAAAqG,iBAAA,CAAArG,EAAA,CAAAwG,iBAAA,GAAAxG,EAAA,CAAAqG,iBAAA,CAAAI,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdnE,cAAc;MAAAoE,SAAA;MAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAAzB,GAAA;QAAA,IAAAyB,EAAA;;;;;;;;;;;;;UCX3B9G,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAA+G,SAAA,cAAsB;UAWtB/G,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAe,MAAA,iBAAS;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UAG1BhB,EAAA,CAAAC,cAAA,YAAM;UAIAD,EAAA,CAAA+G,SAAA,eAAwE;UACxE/G,EAAA,CAAAe,MAAA,aACF;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UACJhB,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAE,UAAA,mBAAA8G,4CAAA;YAAA,OAAS3B,GAAA,CAAAZ,MAAA,EAAQ;UAAA,EAAC;UAACzE,EAAA,CAAAe,MAAA,gBAAQ;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UAKzDhB,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAA+G,SAAA,gBAAiC;UACjC/G,EAAA,CAAAe,MAAA,mBAAU;UAAAf,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAe,MAAA,iBAAS;UAAAf,EAAA,CAAAgB,YAAA,EAAO;UAGpDhB,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAA+G,SAAA,gBAAiC;UACjC/G,EAAA,CAAAe,MAAA,kCACF;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UAENhB,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAA+G,SAAA,gBAAkC;UAClC/G,EAAA,CAAAe,MAAA,6BACF;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UAMZhB,EAAA,CAAAC,cAAA,gBAAkE;UAC9BD,EAAA,CAAAe,MAAA,8BAAsB;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UAG7DhB,EAAA,CAAAC,cAAA,eAA6C;UAEuBD,EAAA,CAAAe,MAAA,WAAG;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UACvEhB,EAAA,CAAAe,MAAA,gCACF;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UAENhB,EAAA,CAAAC,cAAA,eAA8B;UAQ/BD,EAAA,CAAAE,UAAA,2BAAA+G,wDAAAC,MAAA;YAAA,OAAA7B,GAAA,CAAAvC,WAAA,GAAAoE,MAAA;UAAA,EAAyB,2BAAAD,wDAAA;YAAA,OACR5B,GAAA,CAAA7B,WAAA,EAAa;UAAA,EADL;UAJhCxD,EAAA,CAAAgB,YAAA,EAKuC;UAE7BhB,EAAA,CAAAC,cAAA,iBAAiC;UAGvBD,EAAA,CAAAe,MAAA,UAAE;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACXhB,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAe,MAAA,WAAG;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACZhB,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAe,MAAA,mBAAM;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACfhB,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAe,MAAA,aAAK;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACdhB,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAe,MAAA,2BAAS;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UAClBhB,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAe,MAAA,iBAAI;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACbhB,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAe,MAAA,cAAM;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACfhB,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAe,MAAA,iBAAI;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACbhB,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAe,MAAA,YAAI;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACbhB,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAe,MAAA,eAAO;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UAGpBhB,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAoB,UAAA,KAAA+F,6BAAA,mBAyCK;UACPnH,EAAA,CAAAgB,YAAA,EAAQ;;;UA1EPhB,EAAA,CAAAwB,SAAA,IAAmC;UAAnCxB,EAAA,CAAAmC,UAAA,eAAAnC,EAAA,CAAAoH,eAAA,IAAAC,GAAA,EAAmC;UAazCrH,EAAA,CAAAwB,SAAA,GAAyB;UAAzBxB,EAAA,CAAAmC,UAAA,YAAAkD,GAAA,CAAAvC,WAAA,CAAyB;UAmBU9C,EAAA,CAAAwB,SAAA,IAAuB;UAAvBxB,EAAA,CAAAmC,UAAA,YAAAkD,GAAA,CAAAxC,oBAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}