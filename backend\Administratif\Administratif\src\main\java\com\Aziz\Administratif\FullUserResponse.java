package com.Aziz.Administratif;

import com.Aziz.Administratif.ClientRelation.Entity.Action;
import com.Aziz.Administratif.ClientRelation.Entity.Transaction;
import com.Aziz.Administratif.ClientRelation.Entity.Actionnaire;
import com.Aziz.Administratif.Enum.Role;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.*;

import java.util.List;



@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FullUserResponse {

    private String nom;
    private String prenom;
    private String password;
    private String email;
    private Integer telephone;
    @Enumerated(EnumType.STRING)
    private Role role;



    List<Actionnaire> actionnaires;
    List<Action>actions;
    List<Transaction> transations;
}
