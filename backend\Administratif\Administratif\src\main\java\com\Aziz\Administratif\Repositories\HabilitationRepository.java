package com.Aziz.Administratif.Repositories;


import com.Aziz.Administratif.Entity.Habilitation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;


public interface HabilitationRepository extends JpaRepository<Habilitation,Long>{

    void deleteById(Long id);
    Optional<Habilitation> findByIdGroupeAndRessource_IdRessource(Long idGroupe, Long idRessource);
    List<Habilitation> findAllByIdGroupe(Long idGroupe);
    Optional<Habilitation> findByIdGroupe(Long idGroupe);
    void deleteByIdGroupe(Long idGroupe);

}
