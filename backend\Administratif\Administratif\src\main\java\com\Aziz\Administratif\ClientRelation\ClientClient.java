package com.Aziz.Administratif.ClientRelation;

// Importation des entités partagées entre les microservices
import com.Aziz.Administratif.ClientRelation.Entity.Action;
import com.Aziz.Administratif.ClientRelation.Entity.Actionnaire;
import com.Aziz.Administratif.ClientRelation.Entity.Portefeuille;
import com.Aziz.Administratif.ClientRelation.Entity.Transaction;

// Importation des annotations nécessaires pour Feign et HTTP
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;
@FeignClient(name="Client-Service",url="${application.config.client-url}")
public interface ClientClient {

    // Récupérer les actionnaires associés à un utilisateur spécifique
    @GetMapping("/user/{user-id}/actionnaires")
    List<Actionnaire> findActionnairesByUser(@PathVariable("user-id") Long userId);

    // Récupérer les actions associées à un utilisateur spécifique
    @GetMapping("/user/{user-id}/actions")
    List<Action> findActionsByUser(@PathVariable("user-id") Long userId);

    // Récupérer les transactions associées à un utilisateur spécifique
    @GetMapping("/Transaction/{user-id}/transactions")
    List<Transaction> findTransactionsByUser(@PathVariable("user-id") Long userId);

    // Ajouter une transaction
    @PostMapping("/transactions")
    Transaction addTransaction(@RequestBody Transaction transaction);


    // Importer un fichier Excel contenant des transactions
    @PostMapping(value = "/Transaction/ExelInjecter_Transaction", consumes = "multipart/form-data")
    ResponseEntity<String> uploadExcelFile(@RequestPart("file") MultipartFile file, @RequestParam("userId") Long userId);

    // Récupérer toutes les transactions
    @GetMapping("/Transaction/All_Transactions")
    public ResponseEntity<List<Transaction>> getAllTransactions();


    // Créer une nouvelle action
    @PostMapping("/Action/create")
    public ResponseEntity<String> createAction(@RequestBody Action action);

    // Récupérer une action par son ID
    @GetMapping(value="/Action/{idAction}")
    ResponseEntity<Action>getActionById(@PathVariable Long idAction);

    // Récupérer toutes les actions
    @GetMapping(value ="/Action/All_Actions")
    ResponseEntity<List<Action>>getAllActions();

    // Mettre à jour une action
    @PutMapping("/Action/update/{idAction}")
    ResponseEntity<Action>updateAction(@PathVariable Long idAction, @RequestBody Action updatedAction);

    // Supprimer une action
    @DeleteMapping("/Action/delete/{idAction}")
    public ResponseEntity<String> deleteAction(@PathVariable Long idAction);

    // Récupérer les portefeuilles associés à une action
    @GetMapping("/Action/Action_/{idAction}/Portefeuiles")
    public ResponseEntity<List<Portefeuille>> getPortefeuillesByActionId(@PathVariable Long idAction);

    // Récupérer les actionnaires qui possèdent une action spécifique
    @GetMapping("/Action/Action_/{idAction}/Actionnaires")
    public ResponseEntity<List<Actionnaire>> getActionnairesByAction(@PathVariable Long id);

    // Récupérer tous les actionnaires
    @GetMapping("/Actionnaire/All_Actionnaires")
    public ResponseEntity<List<Actionnaire>> getAllActionnaires();

    // Récupérer un actionnaire par son ID
    @GetMapping("/Actionnaire/{idActionnaire}")
    public ResponseEntity<Actionnaire> getActionnaireById(@PathVariable Long idActionnaire);

    // Créer un nouvel actionnaire
    @PostMapping("/Actionnaire/create")
    public ResponseEntity<String> createActionnaire(@RequestBody Actionnaire actionnaire);

    // Mettre à jour un actionnaire
    @PutMapping("/Actionnaire/update/{idActionnaire}")
    public ResponseEntity<Actionnaire> updateActionnaire(@PathVariable Long idActionnaire, @RequestBody Actionnaire updatedActionnaire);

    // Supprimer un actionnaire
    @DeleteMapping("/Actionnaire/Actionnaire/{idActionnaire}")
    public ResponseEntity<String> deleteActionnaire(@PathVariable Long idActionnaire);

    // Récupérer les portefeuilles détenus par un actionnaire
    @GetMapping("/Actionnaire/{id}/portefeuilles")
    public ResponseEntity<List<Portefeuille>> getPortefeuilles(@PathVariable Long id);

    // Récupérer les actions détenues par un actionnaire
    @GetMapping("/Actionnaire/{id}/actions")
    public ResponseEntity<List<Action>> getActions(@PathVariable Long id);

    // Créer un portefeuille
    @PostMapping("/Portefeuille/create")
    public ResponseEntity<String> createPortefeuille(@RequestBody Portefeuille portefeuille);

    // Récupérer tous les portefeuilles
    @GetMapping("/Portefeuille/All_Portefeuilles")
    public ResponseEntity<List<Portefeuille>> getAllPortefeuilles();

    // Supprimer un portefeuille par ID
    @DeleteMapping("/Portefeuille/delete/{idPortefeuille}")
    public ResponseEntity<String> deletePortefeuille(@PathVariable Long idPortefeuille);

    // Rechercher un portefeuille par ISIN et ID d’actionnaire
    @GetMapping("/Portefeuille/search")
    public ResponseEntity<Portefeuille> getByISINAndActionnaire(@RequestParam String isinAction, @RequestParam Long idActionnaire);

    // Récupérer les portefeuilles appartenant à un actionnaire donné
    @GetMapping("/Portefeuille/actionnaire/{idActionnaire}")
    public ResponseEntity<List<Portefeuille>> getPortefeuillesByActionnaire(@PathVariable Long idActionnaire);

    // Récupérer un portefeuille par son ID
    @GetMapping("/{idPortfeuille}")
    public ResponseEntity<Portefeuille> getPortefeuilleById(@PathVariable Long idPortfeuille);
}
