{"ast": null, "code": "import * as feather from 'feather-icons';\nimport { Chart } from 'chart.js';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = [\"myChart\"];\nfunction AdminDashComponent_tr_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\")(14, \"i\", 54);\n    i0.ɵɵlistener(\"click\", function AdminDashComponent_tr_111_Template_i_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const responsable_r2 = restoredCtx.$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.deleteResponsable(responsable_r2.id));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const responsable_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r2.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r2.nom);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r2.prenom);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r2.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r2.telephone);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r2.groupe);\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n  }\\n  \\n  .feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);\\n  }\\n  \\n  .sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #333;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n  }\\n  \\n  .sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, .25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .border-top[_ngcontent-%COMP%] { border-top: 1px solid #e5e5e5; }\\n  .border-bottom[_ngcontent-%COMP%] { border-bottom: 1px solid #e5e5e5; }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class AdminDashComponent {\n  constructor(responsableService) {\n    this.responsableService = responsableService;\n    this.responsables = []; // Array to hold the fetched data\n  }\n\n  ngOnInit() {\n    // Fetch the list of responsables on component initialization\n    this.responsableService.getResponsables().subscribe(data => {\n      this.responsables = data; // Store the fetched data\n      console.log(data); // Optionally log the data to inspect the response\n    }, error => {\n      console.error('Error fetching responsables:', error);\n    });\n  }\n  deleteResponsable(id) {\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe(() => {\n        this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n      });\n    }\n  }\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n    // Ensure chart element exists\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement;\n      console.log(ctx); // Check if this logs the canvas element\n      new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday'],\n          datasets: [{\n            data: [15339, 21345, 18483],\n            backgroundColor: 'transparent',\n            borderColor: '#007bff',\n            borderWidth: 4,\n            pointBackgroundColor: '#007bff'\n          }]\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function AdminDashComponent_Factory(t) {\n      return new (t || AdminDashComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashComponent,\n      selectors: [[\"app-respon-dashboard\"]],\n      viewQuery: function AdminDashComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 112,\n      vars: 1,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [\"width\", \"900\", \"height\", \"380\", 2, \"background-color\", \"lightgrey\"], [\"myChart\", \"\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"ResDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"form-control-dark\", \"w-100\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [\"href\", \"/login\", 1, \"nav-link\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-light\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/ResDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/reports\", 1, \"nav-link\"], [\"data-feather\", \"bar-chart-2\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"layers\"], [1, \"sidebar-heading\", \"d-flex\", \"justify-content-between\", \"align-items-center\", \"px-3\", \"mt-4\", \"mb-1\", \"text-muted\"], [\"href\", \"#\", 1, \"d-flex\", \"align-items-center\", \"text-muted\"], [\"data-feather\", \"plus-circle\"], [1, \"nav\", \"flex-column\", \"mb-2\"], [\"href\", \"#\", 1, \"nav-link\"], [\"data-feather\", \"file-text\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"dropdown-toggle\"], [\"data-feather\", \"calendar\"], [\"id\", \"myChart\", \"width\", \"900\", \"height\", \"380\", 1, \"my-4\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-sm\"], [4, \"ngFor\", \"ngForOf\"], [1, \"fas\", \"fa-trash\", \"delete-icon\", 3, \"click\"]],\n      template: function AdminDashComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"link\", 5)(7, \"link\", 6);\n          i0.ɵɵelementStart(8, \"title\");\n          i0.ɵɵtext(9, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"link\", 7)(11, \"link\", 8)(12, \"canvas\", 9, 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"body\")(15, \"nav\", 11)(16, \"a\", 12);\n          i0.ɵɵtext(17, \"GTI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"input\", 13);\n          i0.ɵɵelementStart(19, \"ul\", 14)(20, \"li\", 15)(21, \"a\", 16);\n          i0.ɵɵtext(22, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 17)(24, \"div\", 18)(25, \"nav\", 19)(26, \"div\", 20)(27, \"ul\", 21)(28, \"li\", 22)(29, \"a\", 23);\n          i0.ɵɵelement(30, \"span\", 24);\n          i0.ɵɵtext(31, \" Dashboard \");\n          i0.ɵɵelementStart(32, \"span\", 25);\n          i0.ɵɵtext(33, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"li\", 22)(35, \"a\", 26);\n          i0.ɵɵelement(36, \"span\", 27);\n          i0.ɵɵtext(37, \" Transactions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"li\", 22)(39, \"a\", 28);\n          i0.ɵɵelement(40, \"span\", 29);\n          i0.ɵɵtext(41, \" Actions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"li\", 22)(43, \"a\", 30);\n          i0.ɵɵelement(44, \"span\", 31);\n          i0.ɵɵtext(45, \" Actionnaires \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"li\", 22)(47, \"a\", 32);\n          i0.ɵɵelement(48, \"span\", 33);\n          i0.ɵɵtext(49, \" Reports \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"li\", 22)(51, \"a\", 34);\n          i0.ɵɵelement(52, \"span\", 35);\n          i0.ɵɵtext(53, \" Portefeuilles \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"h6\", 36)(55, \"span\");\n          i0.ɵɵtext(56, \"Saved reports\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"a\", 37);\n          i0.ɵɵelement(58, \"span\", 38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"ul\", 39)(60, \"li\", 22)(61, \"a\", 40);\n          i0.ɵɵelement(62, \"span\", 41);\n          i0.ɵɵtext(63, \" Current month \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"li\", 22)(65, \"a\", 40);\n          i0.ɵɵelement(66, \"span\", 41);\n          i0.ɵɵtext(67, \" Last quarter \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"li\", 22)(69, \"a\", 40);\n          i0.ɵɵelement(70, \"span\", 41);\n          i0.ɵɵtext(71, \" Social engagement \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"li\", 22)(73, \"a\", 40);\n          i0.ɵɵelement(74, \"span\", 41);\n          i0.ɵɵtext(75, \" Year-end sale \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(76, \"main\", 42)(77, \"div\", 43)(78, \"h1\", 44);\n          i0.ɵɵtext(79, \"Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"div\", 45)(81, \"div\", 46)(82, \"button\", 47);\n          i0.ɵɵtext(83, \"Share\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"button\", 47);\n          i0.ɵɵtext(85, \"Export\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"button\", 48);\n          i0.ɵɵelement(87, \"span\", 49);\n          i0.ɵɵtext(88, \" This week \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(89, \"canvas\", 50);\n          i0.ɵɵelementStart(90, \"h2\");\n          i0.ɵɵtext(91, \"List of Responsables\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"div\", 51)(93, \"table\", 52)(94, \"thead\")(95, \"tr\")(96, \"th\");\n          i0.ɵɵtext(97, \"#id\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"th\");\n          i0.ɵɵtext(99, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"th\");\n          i0.ɵɵtext(101, \"Prenom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"th\");\n          i0.ɵɵtext(103, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"th\");\n          i0.ɵɵtext(105, \"Telephone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"th\");\n          i0.ɵɵtext(107, \"Group\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"th\");\n          i0.ɵɵtext(109, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(110, \"tbody\");\n          i0.ɵɵtemplate(111, AdminDashComponent_tr_111_Template, 15, 6, \"tr\", 53);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(111);\n          i0.ɵɵproperty(\"ngForOf\", ctx.responsables);\n        }\n      },\n      dependencies: [i2.NgForOf],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "Chart", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AdminDashComponent_tr_111_Template_i_click_14_listener", "restoredCtx", "ɵɵrestoreView", "_r4", "responsable_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "deleteResponsable", "id", "ɵɵadvance", "ɵɵtextInterpolate", "nom", "prenom", "email", "telephone", "groupe", "AdminDashComponent", "constructor", "responsableService", "responsables", "ngOnInit", "getResponsables", "subscribe", "data", "console", "log", "error", "confirm", "filter", "responsable", "ngAfterViewInit", "replace", "myChartRef", "ctx", "nativeElement", "type", "labels", "datasets", "backgroundColor", "borderColor", "borderWidth", "pointBackgroundColor", "ɵɵdirectiveInject", "i1", "ResponsableService", "selectors", "viewQuery", "AdminDashComponent_Query", "rf", "ɵɵelement", "ɵɵtemplate", "AdminDashComponent_tr_111_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\admin-dash\\admin-dash.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\admin-dash\\admin-dash.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, AfterViewInit, ElementRef } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';  // Import your service\nimport { User } from '../model/user.model';  // Import the User model\nimport * as feather from 'feather-icons';\nimport { Chart } from 'chart.js';\n\n@Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './admin-dash.component.html',\n  styleUrls: ['../../dashboard.css']\n})\nexport class AdminDashComponent implements AfterViewInit, OnInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  responsables: User[] = [];  // Array to hold the fetched data\n\n  constructor(private responsableService: ResponsableService) {}\n\n  ngOnInit(): void {\n    // Fetch the list of responsables on component initialization\n    this.responsableService.getResponsables().subscribe(\n      (data) => {\n        this.responsables = data;  // Store the fetched data\n        console.log(data);  // Optionally log the data to inspect the response\n      },\n      (error) => {\n        console.error('Error fetching responsables:', error);\n      }\n    );\n  }\n  deleteResponsable(id: number) {\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe(() => {\n        this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n      });\n    }\n  }\n\n  ngAfterViewInit() {\n    feather.replace();// Initialize feather icons\n  \n    // Ensure chart element exists\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement as HTMLCanvasElement;\n      console.log(ctx); // Check if this logs the canvas element\n      new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday'],\n          datasets: [\n            {\n              data: [15339, 21345, 18483],\n              backgroundColor: 'transparent',\n              borderColor: '#007bff',\n              borderWidth: 4,\n              pointBackgroundColor: '#007bff',\n            },\n          ],\n        },\n      });\n    }\n  }\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n\n\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n\n    <!-- Custom styles for this template -->\n    <link href=\"../../dashboard.css\" rel=\"stylesheet\">\n\n    <canvas #myChart width=\"900\" height=\"380\" style=\"background-color: lightgrey;\"></canvas>\n\n  </head>\n\n  <body>\n    \n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"ResDash\">GTI</a>\n      <input class=\"form-control form-control-dark w-100\" type=\"text\" placeholder=\"Search\" aria-label=\"Search\">\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" href=\"/login\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block bg-light sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/ResDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\"  href=\"/transactions\">\n                  <span data-feather=\"dollar-sign\"></span>\n                  Transactions\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actions\">\n                  <span data-feather=\"trending-up\"></span>\n                  Actions\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\">\n                  <span data-feather=\"users\"></span>\n                  Actionnaires\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/reports\">\n                  <span data-feather=\"bar-chart-2\"></span>\n                  Reports\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/port\">\n                  <span data-feather=\"layers\"></span>\n                  Portefeuilles\n                </a>\n              </li>\n            </ul>\n\n            <h6 class=\"sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted\">\n              <span>Saved reports</span>\n              <a class=\"d-flex align-items-center text-muted\" href=\"#\">\n                <span data-feather=\"plus-circle\"></span>\n              </a>\n            </h6>\n            <ul class=\"nav flex-column mb-2\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"#\">\n                  <span data-feather=\"file-text\"></span>\n                  Current month\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"#\">\n                  <span data-feather=\"file-text\"></span>\n                  Last quarter\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"#\">\n                  <span data-feather=\"file-text\"></span>\n                  Social engagement\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"#\">\n                  <span data-feather=\"file-text\"></span>\n                  Year-end sale\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Dashboard</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n                <button class=\"btn btn-sm btn-outline-secondary\">Share</button>\n                <button class=\"btn btn-sm btn-outline-secondary\">Export</button>\n              </div>\n              <button class=\"btn btn-sm btn-outline-secondary dropdown-toggle\">\n                <span data-feather=\"calendar\"></span>\n                This week\n              </button>\n            </div>\n          </div>\n\n          <canvas class=\"my-4\" id=\"myChart\" width=\"900\" height=\"380\"></canvas>\n\n          <h2>List of Responsables</h2>\n          <div class=\"table-responsive\">\n            <table class=\"table table-striped table-sm\">\n              <thead>\n                <tr>\n                  <th>#id</th>\n                  <th>Nom</th>\n                  <th>Prenom</th>\n                  <th>Email</th>\n                  <th>Telephone</th>\n                  <th>Group</th>\n                  <th>Actions</th>\n\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let responsable of responsables\">\n                  <td>{{ responsable.id }}</td>\n                  <td>{{ responsable.nom }}</td>\n                  <td>{{ responsable.prenom }}</td>\n                  <td>{{ responsable.email }}</td>\n                  <td>{{ responsable.telephone }}</td>\n                  <td>{{ responsable.groupe }}</td>\n                  <td>\n                    <i class=\"fas fa-trash delete-icon\" (click)=\"deleteResponsable(responsable.id)\"></i>\n                  </td>\n                \n                  \n                  \n                  \n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <!-- Bootstrap core JavaScript\n    ================================================== -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>\n      feather.replace()\n    </script>\n\n    <!-- Graphs -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js@2.7.1/dist/Chart.min.js\"></script>\n    <script>\n      var ctx = document.getElementById(\"myChart\");\n      var myChart = new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"],\n          datasets: [{\n            data: [15339, 21345, 18483, 24003, 23489, 24092, 12034],\n            lineTension: 0,\n            backgroundColor: 'transparent',\n            borderColor: '#007bff',\n            borderWidth: 4,\n            pointBackgroundColor: '#007bff'\n          }]\n        },\n        options: {\n          scales: {\n            yAxes: [{\n              ticks: {\n                beginAtZero: false\n              }\n            }]\n          },\n          legend: {\n            display: false,\n          }\n        }\n      });\n    </script>\n  </body>\n</html>\n"], "mappings": "AAGA,OAAO,KAAKA,OAAO,MAAM,eAAe;AACxC,SAASC,KAAK,QAAQ,UAAU;;;;;;;;ICgJhBC,EAAA,CAAAC,cAAA,SAA6C;IACvCD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,UAAI;IACkCD,EAAA,CAAAI,UAAA,mBAAAC,uDAAA;MAAA,MAAAC,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,cAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAG,iBAAA,CAAAL,cAAA,CAAAM,EAAA,CAAiC;IAAA,EAAC;IAACf,EAAA,CAAAG,YAAA,EAAI;;;;IAPlFH,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAM,EAAA,CAAoB;IACpBf,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAS,GAAA,CAAqB;IACrBlB,EAAA,CAAAgB,SAAA,GAAwB;IAAxBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAU,MAAA,CAAwB;IACxBnB,EAAA,CAAAgB,SAAA,GAAuB;IAAvBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAW,KAAA,CAAuB;IACvBpB,EAAA,CAAAgB,SAAA,GAA2B;IAA3BhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAY,SAAA,CAA2B;IAC3BrB,EAAA,CAAAgB,SAAA,GAAwB;IAAxBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAa,MAAA,CAAwB;;;;AD/I9C,OAAM,MAAOC,kBAAkB;EAI7BC,YAAoBC,kBAAsC;IAAtC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAFtC,KAAAC,YAAY,GAAW,EAAE,CAAC,CAAE;EAEiC;;EAE7DC,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,kBAAkB,CAACG,eAAe,EAAE,CAACC,SAAS,CAChDC,IAAI,IAAI;MACP,IAAI,CAACJ,YAAY,GAAGI,IAAI,CAAC,CAAE;MAC3BC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC,CAAC,CAAE;IACtB,CAAC,EACAG,KAAK,IAAI;MACRF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,CACF;EACH;EACAnB,iBAAiBA,CAACC,EAAU;IAC1B,IAAImB,OAAO,CAAC,mDAAmD,CAAC,EAAE;MAChE,IAAI,CAACT,kBAAkB,CAACX,iBAAiB,CAACC,EAAE,CAAC,CAACc,SAAS,CAAC,MAAK;QAC3D,IAAI,CAACH,YAAY,GAAG,IAAI,CAACA,YAAY,CAACS,MAAM,CAACC,WAAW,IAAIA,WAAW,CAACrB,EAAE,KAAKA,EAAE,CAAC;MACpF,CAAC,CAAC;;EAEN;EAEAsB,eAAeA,CAAA;IACbvC,OAAO,CAACwC,OAAO,EAAE,CAAC;IAElB;IACA,IAAI,IAAI,CAACC,UAAU,EAAE;MACnB,MAAMC,GAAG,GAAG,IAAI,CAACD,UAAU,CAACE,aAAkC;MAC9DV,OAAO,CAACC,GAAG,CAACQ,GAAG,CAAC,CAAC,CAAC;MAClB,IAAIzC,KAAK,CAACyC,GAAG,EAAE;QACbE,IAAI,EAAE,MAAM;QACZZ,IAAI,EAAE;UACJa,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;UACvCC,QAAQ,EAAE,CACR;YACEd,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YAC3Be,eAAe,EAAE,aAAa;YAC9BC,WAAW,EAAE,SAAS;YACtBC,WAAW,EAAE,CAAC;YACdC,oBAAoB,EAAE;WACvB;;OAGN,CAAC;;EAEN;;;uBAjDWzB,kBAAkB,EAAAvB,EAAA,CAAAiD,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAlB5B,kBAAkB;MAAA6B,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAf,GAAA;QAAA,IAAAe,EAAA;;;;;;;;;;;;;UCV/BvD,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAwD,SAAA,cAAsB;UAQtBxD,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uCAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE/CH,EAAA,CAAAwD,SAAA,eAAmF;UAUrFxD,EAAA,CAAAG,YAAA,EAAO;UAEPH,EAAA,CAAAC,cAAA,YAAM;UAG4DD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACrEH,EAAA,CAAAwD,SAAA,iBAAyG;UACzGxD,EAAA,CAAAC,cAAA,cAA4B;UAEUD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKpDH,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAAwD,SAAA,gBAAiC;UACjCxD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGpDH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAwD,SAAA,gBAAwC;UACxCxD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAwD,SAAA,gBAAwC;UACxCxD,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAwD,SAAA,gBAAkC;UAClCxD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAwD,SAAA,gBAAwC;UACxCxD,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAwD,SAAA,gBAAmC;UACnCxD,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIRH,EAAA,CAAAC,cAAA,cAAwG;UAChGD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC1BH,EAAA,CAAAC,cAAA,aAAyD;UACvDD,EAAA,CAAAwD,SAAA,gBAAwC;UAC1CxD,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAiC;UAG3BD,EAAA,CAAAwD,SAAA,gBAAsC;UACtCxD,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAwD,SAAA,gBAAsC;UACtCxD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAwD,SAAA,gBAAsC;UACtCxD,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAwD,SAAA,gBAAsC;UACtCxD,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMZH,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,eAAsC;UAEeD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/DH,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAElEH,EAAA,CAAAC,cAAA,kBAAiE;UAC/DD,EAAA,CAAAwD,SAAA,gBAAqC;UACrCxD,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAwD,SAAA,kBAAoE;UAEpExD,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACZH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACZH,EAAA,CAAAC,cAAA,WAAI;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACfH,EAAA,CAAAC,cAAA,WAAI;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,WAAI;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,WAAI;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,WAAI;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIpBH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAyD,UAAA,MAAAC,kCAAA,kBAcK;UACP1D,EAAA,CAAAG,YAAA,EAAQ;;;UAfsBH,EAAA,CAAAgB,SAAA,KAAe;UAAfhB,EAAA,CAAA2D,UAAA,YAAAnB,GAAA,CAAAd,YAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}