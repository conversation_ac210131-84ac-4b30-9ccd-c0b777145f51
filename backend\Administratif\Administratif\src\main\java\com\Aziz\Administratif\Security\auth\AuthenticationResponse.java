package com.Aziz.Administratif.Security.auth;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthenticationResponse {
    private  String token;
    private List<String> allowedPaths;  // Add allowedPaths to the response
    private String groupe;  // Add this field to include the group name or ID
    private Boolean state;


    // Getters and Setters



    public Boolean getState() {
        return state;
    }

    public void setState(Boolean state) {
        this.state = state;
    }
    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public List<String> getAllowedPaths() {
        return allowedPaths;
    }

    public void setAllowedPaths(List<String> allowedPaths) {
        this.allowedPaths = allowedPaths;
    }

    public String getGroupe() {
        return groupe;
    }

    public void setGroupe(String groupe) {
        this.groupe = groupe;
    }

    // Builder for easy creation
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String token;
        private List<String> allowedPaths;
        private String groupe;
        private Boolean state;

        public Builder token(String token) {
            this.token = token;
            return this;
        }

        public Builder allowedPaths(List<String> allowedPaths) {
            this.allowedPaths = allowedPaths;
            return this;
        }

        public Builder groupe(String groupe) {
            this.groupe = groupe;
            return this;
        }


        public Builder state(Boolean state) {
            this.state = state;
            return this;
        }

        public AuthenticationResponse build() {
            AuthenticationResponse response = new AuthenticationResponse();
            response.token = this.token;
            response.allowedPaths = this.allowedPaths;
            response.groupe = this.groupe;
            response.state = this.state;

            return response;
        }

    }


}