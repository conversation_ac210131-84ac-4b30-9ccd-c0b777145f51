{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./authentication.service\";\nimport * as i2 from \"@angular/router\";\nexport class LoginGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate() {\n    if (this.authService.isAuthenticated()) {\n      // If the user is authenticated, prevent them from accessing the login page\n      this.router.navigate(['/dashboard']); // You can redirect to a protected route like dashboard\n      return false;\n    }\n    return true;\n  }\n  static {\n    this.ɵfac = function LoginGuard_Factory(t) {\n      return new (t || LoginGuard)(i0.ɵɵinject(i1.AuthenticationService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LoginGuard,\n      factory: LoginGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "isAuthenticated", "navigate", "i0", "ɵɵinject", "i1", "AuthenticationService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\login.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate } from '@angular/router';\nimport { Router } from '@angular/router';\nimport { AuthenticationService } from './authentication.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class LoginGuard implements CanActivate {\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  canActivate(): boolean {\n    if (this.authService.isAuthenticated()) {\n      // If the user is authenticated, prevent them from accessing the login page\n      this.router.navigate(['/dashboard']); // You can redirect to a protected route like dashboard\n      return false;\n    }\n    return true;\n  }\n}"], "mappings": ";;;AAQA,OAAM,MAAOA,UAAU;EAErBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;EAAW;EAEjFC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACF,WAAW,CAACG,eAAe,EAAE,EAAE;MACtC;MACA,IAAI,CAACF,MAAM,CAACG,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;MACtC,OAAO,KAAK;;IAEd,OAAO,IAAI;EACb;;;uBAXWN,UAAU,EAAAO,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAVZ,UAAU;MAAAa,OAAA,EAAVb,UAAU,CAAAc,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}