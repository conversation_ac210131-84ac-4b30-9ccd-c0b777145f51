{"ast": null, "code": "/*!\n * forwarded\n * Copyright(c) 2014-2017 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\nmodule.exports = forwarded;\n\n/**\n * Get all addresses in the request, using the `X-Forwarded-For` header.\n *\n * @param {object} req\n * @return {array}\n * @public\n */\n\nfunction forwarded(req) {\n  if (!req) {\n    throw new TypeError('argument req is required');\n  }\n\n  // simple header parsing\n  var proxyAddrs = parse(req.headers['x-forwarded-for'] || '');\n  var socketAddr = getSocketAddr(req);\n  var addrs = [socketAddr].concat(proxyAddrs);\n\n  // return all addresses\n  return addrs;\n}\n\n/**\n * Get the socket address for a request.\n *\n * @param {object} req\n * @return {string}\n * @private\n */\n\nfunction getSocketAddr(req) {\n  return req.socket ? req.socket.remoteAddress : req.connection.remoteAddress;\n}\n\n/**\n * Parse the X-Forwarded-For header.\n *\n * @param {string} header\n * @private\n */\n\nfunction parse(header) {\n  var end = header.length;\n  var list = [];\n  var start = header.length;\n\n  // gather addresses, backwards\n  for (var i = header.length - 1; i >= 0; i--) {\n    switch (header.charCodeAt(i)) {\n      case 0x20:\n        /*   */\n        if (start === end) {\n          start = end = i;\n        }\n        break;\n      case 0x2c:\n        /* , */\n        if (start !== end) {\n          list.push(header.substring(start, end));\n        }\n        start = end = i;\n        break;\n      default:\n        start = i;\n        break;\n    }\n  }\n\n  // final address\n  if (start !== end) {\n    list.push(header.substring(start, end));\n  }\n  return list;\n}", "map": {"version": 3, "names": ["module", "exports", "forwarded", "req", "TypeError", "proxyAddrs", "parse", "headers", "socketAddr", "getSocketAddr", "addrs", "concat", "socket", "remoteAddress", "connection", "header", "end", "length", "list", "start", "i", "charCodeAt", "push", "substring"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/forwarded/index.js"], "sourcesContent": ["/*!\n * forwarded\n * Copyright(c) 2014-2017 <PERSON>\n * MIT Licensed\n */\n\n'use strict'\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = forwarded\n\n/**\n * Get all addresses in the request, using the `X-Forwarded-For` header.\n *\n * @param {object} req\n * @return {array}\n * @public\n */\n\nfunction forwarded (req) {\n  if (!req) {\n    throw new TypeError('argument req is required')\n  }\n\n  // simple header parsing\n  var proxyAddrs = parse(req.headers['x-forwarded-for'] || '')\n  var socketAddr = getSocketAddr(req)\n  var addrs = [socketAddr].concat(proxyAddrs)\n\n  // return all addresses\n  return addrs\n}\n\n/**\n * Get the socket address for a request.\n *\n * @param {object} req\n * @return {string}\n * @private\n */\n\nfunction getSocketAddr (req) {\n  return req.socket\n    ? req.socket.remoteAddress\n    : req.connection.remoteAddress\n}\n\n/**\n * Parse the X-Forwarded-For header.\n *\n * @param {string} header\n * @private\n */\n\nfunction parse (header) {\n  var end = header.length\n  var list = []\n  var start = header.length\n\n  // gather addresses, backwards\n  for (var i = header.length - 1; i >= 0; i--) {\n    switch (header.charCodeAt(i)) {\n      case 0x20: /*   */\n        if (start === end) {\n          start = end = i\n        }\n        break\n      case 0x2c: /* , */\n        if (start !== end) {\n          list.push(header.substring(start, end))\n        }\n        start = end = i\n        break\n      default:\n        start = i\n        break\n    }\n  }\n\n  // final address\n  if (start !== end) {\n    list.push(header.substring(start, end))\n  }\n\n  return list\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AAEAA,MAAM,CAACC,OAAO,GAAGC,SAAS;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,SAASA,CAAEC,GAAG,EAAE;EACvB,IAAI,CAACA,GAAG,EAAE;IACR,MAAM,IAAIC,SAAS,CAAC,0BAA0B,CAAC;EACjD;;EAEA;EACA,IAAIC,UAAU,GAAGC,KAAK,CAACH,GAAG,CAACI,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;EAC5D,IAAIC,UAAU,GAAGC,aAAa,CAACN,GAAG,CAAC;EACnC,IAAIO,KAAK,GAAG,CAACF,UAAU,CAAC,CAACG,MAAM,CAACN,UAAU,CAAC;;EAE3C;EACA,OAAOK,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASD,aAAaA,CAAEN,GAAG,EAAE;EAC3B,OAAOA,GAAG,CAACS,MAAM,GACbT,GAAG,CAACS,MAAM,CAACC,aAAa,GACxBV,GAAG,CAACW,UAAU,CAACD,aAAa;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASP,KAAKA,CAAES,MAAM,EAAE;EACtB,IAAIC,GAAG,GAAGD,MAAM,CAACE,MAAM;EACvB,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,KAAK,GAAGJ,MAAM,CAACE,MAAM;;EAEzB;EACA,KAAK,IAAIG,CAAC,GAAGL,MAAM,CAACE,MAAM,GAAG,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3C,QAAQL,MAAM,CAACM,UAAU,CAACD,CAAC,CAAC;MAC1B,KAAK,IAAI;QAAE;QACT,IAAID,KAAK,KAAKH,GAAG,EAAE;UACjBG,KAAK,GAAGH,GAAG,GAAGI,CAAC;QACjB;QACA;MACF,KAAK,IAAI;QAAE;QACT,IAAID,KAAK,KAAKH,GAAG,EAAE;UACjBE,IAAI,CAACI,IAAI,CAACP,MAAM,CAACQ,SAAS,CAACJ,KAAK,EAAEH,GAAG,CAAC,CAAC;QACzC;QACAG,KAAK,GAAGH,GAAG,GAAGI,CAAC;QACf;MACF;QACED,KAAK,GAAGC,CAAC;QACT;IACJ;EACF;;EAEA;EACA,IAAID,KAAK,KAAKH,GAAG,EAAE;IACjBE,IAAI,CAACI,IAAI,CAACP,MAAM,CAACQ,SAAS,CAACJ,KAAK,EAAEH,GAAG,CAAC,CAAC;EACzC;EAEA,OAAOE,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}