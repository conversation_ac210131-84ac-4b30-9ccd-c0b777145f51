package com.Rayen.User.auth;

import com.Rayen.User.Config.JwtService;
import com.Rayen.User.Entity.User;
import com.Rayen.User.Enum.Role;
import com.Rayen.User.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class AuthenticationService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final AuthenticationManager authenticationManager;

    public AuthenticationResponse register(RegisterRequest request) {
        // Vérifier les contraintes de rôle avant de procéder
        if (request.getRole() == Role.ADMIN && request.getGroupe() != null) {
            return AuthenticationResponse.builder()

                    .token(null)
                    .build();
        }
        if (request.getRole() == Role.RESPONSABLE && request.getGroupe() == null) {
            return AuthenticationResponse.builder()

                    .token(null)
                    .build();
        }

        // Générer le token avant d'enregistrer l'utilisateur
        Map<String, Object> extraClaims = new HashMap<>();
        extraClaims.put("role", request.getRole().name());
        if (request.getGroupe() != null) {
            extraClaims.put("groupe", request.getGroupe().name());
        }

        var tempUser = User.builder()
                .nom(request.getFirstName())
                .prenom(request.getLastName())
                .email(request.getEmail())
                .password(passwordEncoder.encode(request.getPassword()))
                .role(request.getRole())
                .groupe(request.getGroupe())
                .build();

        var jwtToken = jwtService.generateToken(extraClaims, tempUser);

        if (jwtToken == null || jwtToken.isEmpty()) {
            return AuthenticationResponse.builder()

                    .token(null)
                    .build();
        }

        // Enregistrer l'utilisateur seulement si le token est bien généré
        userRepository.save(tempUser);

        return AuthenticationResponse.builder()
                .token(jwtToken)

                .build();
    }

    public AuthenticationResponse authenticate(AuthenticationRequest request) {
        try {
            authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            request.getEmail(),
                            request.getPassword()
                    )
            );
        } catch (Exception e) {
            return AuthenticationResponse.builder()

                    .token(null)
                    .build();
        }

        var user = userRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new RuntimeException("Utilisateur non trouvé !"));

        var jwtToken = jwtService.generateToken(new HashMap<>(), user);

        return AuthenticationResponse.builder()
                .token(jwtToken)

                .build();
    }
}
