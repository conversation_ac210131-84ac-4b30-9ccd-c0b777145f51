{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { AuthGuard } from './auth/auth.guard';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\nimport { AdminDashComponent } from './admin-dash/admin-dash.component';\nimport { ResponsableEditComponent } from './responsable-edit/responsable-edit.component';\nimport { UsersComponent } from './users/users.component';\nimport { GroupsComponent } from './groups/groups.component';\nimport { ModifyGroupComponent } from './modify-group/modify-group.component';\nimport { AddResponsableComponent } from './add-responsable/add-responsable.component'; // Import AddResponsableComponent\nimport { HabilitationComponent } from './habilitation/habilitation.component'; // Import the component\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'signup',\n  component: SignupComponent\n}, {\n  path: 'edit-responsable/:id',\n  component: ResponsableEditComponent\n}, {\n  path: 'adminDash',\n  component: AdminDashComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'transactions',\n  component: TransactionsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'actions',\n  component: ActionsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'actionnaires',\n  component: ActionnairesComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'reports',\n  component: ReportsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'port',\n  component: PortefeuillesComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'ResDash',\n  component: ResponDashboardComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'users',\n  component: UsersComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'groups',\n  component: GroupsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'edit-groupe/:id',\n  component: ModifyGroupComponent\n}, {\n  path: 'add-responsable',\n  component: AddResponsableComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'not-authorized',\n  component: NotAuthorizedComponent\n}, {\n  path: 'habilitation',\n  component: HabilitationComponent,\n  canActivate: [AuthGuard]\n} // Added route for Habilitation\n];\n\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "SignupComponent", "ResponDashboardComponent", "NotAuthorizedComponent", "<PERSON><PERSON><PERSON><PERSON>", "TransactionsComponent", "ActionsComponent", "ActionnairesComponent", "ReportsComponent", "PortefeuillesComponent", "AdminDashComponent", "ResponsableEditComponent", "UsersComponent", "GroupsComponent", "ModifyGroupComponent", "AddResponsableComponent", "HabilitationComponent", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { AuthGuard } from './auth/auth.guard';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\nimport { AdminDashComponent } from './admin-dash/admin-dash.component';\nimport { ResponsableEditComponent } from './responsable-edit/responsable-edit.component';\nimport { UsersComponent } from './users/users.component';\nimport { GroupsComponent } from './groups/groups.component';\nimport { ModifyGroupComponent } from './modify-group/modify-group.component';\nimport { AddResponsableComponent } from './add-responsable/add-responsable.component';  // Import AddResponsableComponent\nimport { HabilitationComponent } from './habilitation/habilitation.component';  // Import the component\n\nconst routes: Routes = [\n  { path: '', redirectTo: 'login', pathMatch: 'full' },\n  { path: 'login', component: LoginComponent },\n  { path: 'signup', component: SignupComponent },\n  { path: 'edit-responsable/:id', component: ResponsableEditComponent },\n  { path: 'adminDash', component: AdminDashComponent, canActivate: [AuthGuard] },\n  { path: 'transactions', component: TransactionsComponent, canActivate: [AuthGuard] },\n  { path: 'actions', component: ActionsComponent, canActivate: [AuthGuard] },\n  { path: 'actionnaires', component: ActionnairesComponent, canActivate: [AuthGuard] },\n  { path: 'reports', component: ReportsComponent, canActivate: [AuthGuard] },\n  { path: 'port', component: PortefeuillesComponent, canActivate: [AuthGuard] },\n  { path: 'ResDash', component: ResponDashboardComponent, canActivate: [AuthGuard] },\n  { path: 'users', component: UsersComponent, canActivate: [AuthGuard] }, // Admin only\n  { path: 'groups', component: GroupsComponent, canActivate: [AuthGuard] }, // Admin only\n  { path: 'edit-groupe/:id', component: ModifyGroupComponent }, // Added route for editing a group\n  { path: 'add-responsable', component: AddResponsableComponent, canActivate: [AuthGuard] },  // Added route for AddResponsableComponent\n  { path: 'not-authorized', component: NotAuthorizedComponent },\n    { path: 'habilitation', component: HabilitationComponent, canActivate: [AuthGuard] },  // Added route for Habilitation\n\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,uBAAuB,QAAQ,6CAA6C,CAAC,CAAE;AACxF,SAASC,qBAAqB,QAAQ,uCAAuC,CAAC,CAAE;;;AAEhF,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE;AAAM,CAAE,EACpD;EAAEF,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAErB;AAAc,CAAE,EAC5C;EAAEkB,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAEpB;AAAe,CAAE,EAC9C;EAAEiB,IAAI,EAAE,sBAAsB;EAAEG,SAAS,EAAEV;AAAwB,CAAE,EACrE;EAAEO,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEX,kBAAkB;EAAEY,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EAC9E;EAAEc,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAEhB,qBAAqB;EAAEiB,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACpF;EAAEc,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEf,gBAAgB;EAAEgB,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EAC1E;EAAEc,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAEd,qBAAqB;EAAEe,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACpF;EAAEc,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEb,gBAAgB;EAAEc,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EAC1E;EAAEc,IAAI,EAAE,MAAM;EAAEG,SAAS,EAAEZ,sBAAsB;EAAEa,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EAC7E;EAAEc,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEnB,wBAAwB;EAAEoB,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EAClF;EAAEc,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAET,cAAc;EAAEU,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACtE;EAAEc,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAER,eAAe;EAAES,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACxE;EAAEc,IAAI,EAAE,iBAAiB;EAAEG,SAAS,EAAEP;AAAoB,CAAE,EAC5D;EAAEI,IAAI,EAAE,iBAAiB;EAAEG,SAAS,EAAEN,uBAAuB;EAAEO,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,EACzF;EAAEc,IAAI,EAAE,gBAAgB;EAAEG,SAAS,EAAElB;AAAsB,CAAE,EAC3D;EAAEe,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAEL,qBAAqB;EAAEM,WAAW,EAAE,CAAClB,SAAS;AAAC,CAAE,CAAG;AAAA,CAE1F;;AAMD,OAAM,MAAOmB,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBxB,YAAY,CAACyB,OAAO,CAACP,MAAM,CAAC,EAC5BlB,YAAY;IAAA;EAAA;;;2EAEXwB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAA3B,YAAA;IAAA4B,OAAA,GAFjB5B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}