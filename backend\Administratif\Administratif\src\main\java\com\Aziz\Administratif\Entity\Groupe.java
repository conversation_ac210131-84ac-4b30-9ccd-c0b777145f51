package com.Aziz.Administratif.Entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "Groupe")
public class Groupe {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idGroupe;

    private String NomGroupe;

    public Groupe(Long idGroupe, String nomGroupe) {
        this.idGroupe = idGroupe;
        this.NomGroupe = nomGroupe;
    }

    @OneToMany(mappedBy = "groupe")
    @JsonIgnore
    private List<User> users;

    // Relation to Habilitations through idGroupe (not an entity relation in Habilitation)
    @OneToMany
    @JoinColumn(name = "idGroupe", referencedColumnName = "idGroupe", insertable = false, updatable = false)
    private List<Habilitation> habilitations;

    // This method extracts the Ressources from the linked Habilitations
    @Transient
    public List<Ressource> getRessources() {
        if (habilitations == null) return null;

        return habilitations.stream()
                .map(Habilitation::getRessource)
                .distinct()
                .collect(Collectors.toList());
    }

    // Getters and Setters
    public Long getIdGroupe() {
        return idGroupe;
    }

    public void setIdGroupe(Long idGroupe) {
        this.idGroupe = idGroupe;
    }

    public String getNomGroupe() {
        return NomGroupe;
    }

    public void setNomGroupe(String nomGroupe) {
        NomGroupe = nomGroupe;
    }

    public List<User> getUsers() {
        return users;
    }

    public void setUsers(List<User> users) {
        this.users = users;
    }

    public List<Habilitation> getHabilitations() {
        return habilitations;
    }

    public void setHabilitations(List<Habilitation> habilitations) {
        this.habilitations = habilitations;
    }
}
