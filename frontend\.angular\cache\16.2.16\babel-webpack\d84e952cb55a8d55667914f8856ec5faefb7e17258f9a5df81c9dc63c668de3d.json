{"ast": null, "code": "\"use strict\";\n\n// Some environments don't have global Buffer (e.g. React Native).\n// Solution would be installing npm modules \"buffer\" and \"stream\" explicitly.\nvar Buffer = require(\"safer-buffer\").Buffer;\nvar bomHandling = require(\"./bom-handling\"),\n  iconv = module.exports;\n\n// All codecs and aliases are kept here, keyed by encoding name/alias.\n// They are lazy loaded in `iconv.getCodec` from `encodings/index.js`.\niconv.encodings = null;\n\n// Characters emitted in case of error.\niconv.defaultCharUnicode = '�';\niconv.defaultCharSingleByte = '?';\n\n// Public API.\niconv.encode = function encode(str, encoding, options) {\n  str = \"\" + (str || \"\"); // Ensure string.\n\n  var encoder = iconv.getEncoder(encoding, options);\n  var res = encoder.write(str);\n  var trail = encoder.end();\n  return trail && trail.length > 0 ? Buffer.concat([res, trail]) : res;\n};\niconv.decode = function decode(buf, encoding, options) {\n  if (typeof buf === 'string') {\n    if (!iconv.skipDecodeWarning) {\n      console.error('Iconv-lite warning: decode()-ing strings is deprecated. Refer to https://github.com/ashtuchkin/iconv-lite/wiki/Use-Buffers-when-decoding');\n      iconv.skipDecodeWarning = true;\n    }\n    buf = Buffer.from(\"\" + (buf || \"\"), \"binary\"); // Ensure buffer.\n  }\n\n  var decoder = iconv.getDecoder(encoding, options);\n  var res = decoder.write(buf);\n  var trail = decoder.end();\n  return trail ? res + trail : res;\n};\niconv.encodingExists = function encodingExists(enc) {\n  try {\n    iconv.getCodec(enc);\n    return true;\n  } catch (e) {\n    return false;\n  }\n};\n\n// Legacy aliases to convert functions\niconv.toEncoding = iconv.encode;\niconv.fromEncoding = iconv.decode;\n\n// Search for a codec in iconv.encodings. Cache codec data in iconv._codecDataCache.\niconv._codecDataCache = {};\niconv.getCodec = function getCodec(encoding) {\n  if (!iconv.encodings) iconv.encodings = require(\"../encodings\"); // Lazy load all encoding definitions.\n\n  // Canonicalize encoding name: strip all non-alphanumeric chars and appended year.\n  var enc = iconv._canonicalizeEncoding(encoding);\n\n  // Traverse iconv.encodings to find actual codec.\n  var codecOptions = {};\n  while (true) {\n    var codec = iconv._codecDataCache[enc];\n    if (codec) return codec;\n    var codecDef = iconv.encodings[enc];\n    switch (typeof codecDef) {\n      case \"string\":\n        // Direct alias to other encoding.\n        enc = codecDef;\n        break;\n      case \"object\":\n        // Alias with options. Can be layered.\n        for (var key in codecDef) codecOptions[key] = codecDef[key];\n        if (!codecOptions.encodingName) codecOptions.encodingName = enc;\n        enc = codecDef.type;\n        break;\n      case \"function\":\n        // Codec itself.\n        if (!codecOptions.encodingName) codecOptions.encodingName = enc;\n\n        // The codec function must load all tables and return object with .encoder and .decoder methods.\n        // It'll be called only once (for each different options object).\n        codec = new codecDef(codecOptions, iconv);\n        iconv._codecDataCache[codecOptions.encodingName] = codec; // Save it to be reused later.\n        return codec;\n      default:\n        throw new Error(\"Encoding not recognized: '\" + encoding + \"' (searched as: '\" + enc + \"')\");\n    }\n  }\n};\niconv._canonicalizeEncoding = function (encoding) {\n  // Canonicalize encoding name: strip all non-alphanumeric chars and appended year.\n  return ('' + encoding).toLowerCase().replace(/:\\d{4}$|[^0-9a-z]/g, \"\");\n};\niconv.getEncoder = function getEncoder(encoding, options) {\n  var codec = iconv.getCodec(encoding),\n    encoder = new codec.encoder(options, codec);\n  if (codec.bomAware && options && options.addBOM) encoder = new bomHandling.PrependBOM(encoder, options);\n  return encoder;\n};\niconv.getDecoder = function getDecoder(encoding, options) {\n  var codec = iconv.getCodec(encoding),\n    decoder = new codec.decoder(options, codec);\n  if (codec.bomAware && !(options && options.stripBOM === false)) decoder = new bomHandling.StripBOM(decoder, options);\n  return decoder;\n};\n\n// Load extensions in Node. All of them are omitted in Browserify build via 'browser' field in package.json.\nvar nodeVer = typeof process !== 'undefined' && process.versions && process.versions.node;\nif (nodeVer) {\n  // Load streaming support in Node v0.10+\n  var nodeVerArr = nodeVer.split(\".\").map(Number);\n  if (nodeVerArr[0] > 0 || nodeVerArr[1] >= 10) {\n    require(\"./streams\")(iconv);\n  }\n\n  // Load Node primitive extensions.\n  require(\"./extend-node\")(iconv);\n}\nif (\"Ā\" != \"\\u0100\") {\n  console.error(\"iconv-lite warning: javascript files use encoding different from utf-8. See https://github.com/ashtuchkin/iconv-lite/wiki/Javascript-source-file-encodings for more info.\");\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "bom<PERSON><PERSON>ling", "iconv", "module", "exports", "encodings", "defaultCharUnicode", "defaultCharSingleByte", "encode", "str", "encoding", "options", "encoder", "get<PERSON>ncoder", "res", "write", "trail", "end", "length", "concat", "decode", "buf", "skipDecode<PERSON><PERSON>ning", "console", "error", "from", "decoder", "getDecoder", "encodingExists", "enc", "getCodec", "e", "toEncoding", "fromEncoding", "_codecDataCache", "_canonicalizeEncoding", "codecOptions", "codec", "codecDef", "key", "encodingName", "type", "Error", "toLowerCase", "replace", "bomAware", "addBOM", "PrependBOM", "stripBOM", "StripBOM", "nodeVer", "process", "versions", "node", "nodeVerArr", "split", "map", "Number"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/iconv-lite/lib/index.js"], "sourcesContent": ["\"use strict\";\n\n// Some environments don't have global Buffer (e.g. React Native).\n// Solution would be installing npm modules \"buffer\" and \"stream\" explicitly.\nvar Buffer = require(\"safer-buffer\").Buffer;\n\nvar bomHandling = require(\"./bom-handling\"),\n    iconv = module.exports;\n\n// All codecs and aliases are kept here, keyed by encoding name/alias.\n// They are lazy loaded in `iconv.getCodec` from `encodings/index.js`.\niconv.encodings = null;\n\n// Characters emitted in case of error.\niconv.defaultCharUnicode = '�';\niconv.defaultCharSingleByte = '?';\n\n// Public API.\niconv.encode = function encode(str, encoding, options) {\n    str = \"\" + (str || \"\"); // Ensure string.\n\n    var encoder = iconv.getEncoder(encoding, options);\n\n    var res = encoder.write(str);\n    var trail = encoder.end();\n    \n    return (trail && trail.length > 0) ? Buffer.concat([res, trail]) : res;\n}\n\niconv.decode = function decode(buf, encoding, options) {\n    if (typeof buf === 'string') {\n        if (!iconv.skipDecodeWarning) {\n            console.error('Iconv-lite warning: decode()-ing strings is deprecated. Refer to https://github.com/ashtuchkin/iconv-lite/wiki/Use-Buffers-when-decoding');\n            iconv.skipDecodeWarning = true;\n        }\n\n        buf = Buffer.from(\"\" + (buf || \"\"), \"binary\"); // Ensure buffer.\n    }\n\n    var decoder = iconv.getDecoder(encoding, options);\n\n    var res = decoder.write(buf);\n    var trail = decoder.end();\n\n    return trail ? (res + trail) : res;\n}\n\niconv.encodingExists = function encodingExists(enc) {\n    try {\n        iconv.getCodec(enc);\n        return true;\n    } catch (e) {\n        return false;\n    }\n}\n\n// Legacy aliases to convert functions\niconv.toEncoding = iconv.encode;\niconv.fromEncoding = iconv.decode;\n\n// Search for a codec in iconv.encodings. Cache codec data in iconv._codecDataCache.\niconv._codecDataCache = {};\niconv.getCodec = function getCodec(encoding) {\n    if (!iconv.encodings)\n        iconv.encodings = require(\"../encodings\"); // Lazy load all encoding definitions.\n    \n    // Canonicalize encoding name: strip all non-alphanumeric chars and appended year.\n    var enc = iconv._canonicalizeEncoding(encoding);\n\n    // Traverse iconv.encodings to find actual codec.\n    var codecOptions = {};\n    while (true) {\n        var codec = iconv._codecDataCache[enc];\n        if (codec)\n            return codec;\n\n        var codecDef = iconv.encodings[enc];\n\n        switch (typeof codecDef) {\n            case \"string\": // Direct alias to other encoding.\n                enc = codecDef;\n                break;\n\n            case \"object\": // Alias with options. Can be layered.\n                for (var key in codecDef)\n                    codecOptions[key] = codecDef[key];\n\n                if (!codecOptions.encodingName)\n                    codecOptions.encodingName = enc;\n                \n                enc = codecDef.type;\n                break;\n\n            case \"function\": // Codec itself.\n                if (!codecOptions.encodingName)\n                    codecOptions.encodingName = enc;\n\n                // The codec function must load all tables and return object with .encoder and .decoder methods.\n                // It'll be called only once (for each different options object).\n                codec = new codecDef(codecOptions, iconv);\n\n                iconv._codecDataCache[codecOptions.encodingName] = codec; // Save it to be reused later.\n                return codec;\n\n            default:\n                throw new Error(\"Encoding not recognized: '\" + encoding + \"' (searched as: '\"+enc+\"')\");\n        }\n    }\n}\n\niconv._canonicalizeEncoding = function(encoding) {\n    // Canonicalize encoding name: strip all non-alphanumeric chars and appended year.\n    return (''+encoding).toLowerCase().replace(/:\\d{4}$|[^0-9a-z]/g, \"\");\n}\n\niconv.getEncoder = function getEncoder(encoding, options) {\n    var codec = iconv.getCodec(encoding),\n        encoder = new codec.encoder(options, codec);\n\n    if (codec.bomAware && options && options.addBOM)\n        encoder = new bomHandling.PrependBOM(encoder, options);\n\n    return encoder;\n}\n\niconv.getDecoder = function getDecoder(encoding, options) {\n    var codec = iconv.getCodec(encoding),\n        decoder = new codec.decoder(options, codec);\n\n    if (codec.bomAware && !(options && options.stripBOM === false))\n        decoder = new bomHandling.StripBOM(decoder, options);\n\n    return decoder;\n}\n\n\n// Load extensions in Node. All of them are omitted in Browserify build via 'browser' field in package.json.\nvar nodeVer = typeof process !== 'undefined' && process.versions && process.versions.node;\nif (nodeVer) {\n\n    // Load streaming support in Node v0.10+\n    var nodeVerArr = nodeVer.split(\".\").map(Number);\n    if (nodeVerArr[0] > 0 || nodeVerArr[1] >= 10) {\n        require(\"./streams\")(iconv);\n    }\n\n    // Load Node primitive extensions.\n    require(\"./extend-node\")(iconv);\n}\n\nif (\"Ā\" != \"\\u0100\") {\n    console.error(\"iconv-lite warning: javascript files use encoding different from utf-8. See https://github.com/ashtuchkin/iconv-lite/wiki/Javascript-source-file-encodings for more info.\");\n}\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA,IAAIA,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC,CAACD,MAAM;AAE3C,IAAIE,WAAW,GAAGD,OAAO,CAAC,gBAAgB,CAAC;EACvCE,KAAK,GAAGC,MAAM,CAACC,OAAO;;AAE1B;AACA;AACAF,KAAK,CAACG,SAAS,GAAG,IAAI;;AAEtB;AACAH,KAAK,CAACI,kBAAkB,GAAG,GAAG;AAC9BJ,KAAK,CAACK,qBAAqB,GAAG,GAAG;;AAEjC;AACAL,KAAK,CAACM,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACnDF,GAAG,GAAG,EAAE,IAAIA,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;;EAExB,IAAIG,OAAO,GAAGV,KAAK,CAACW,UAAU,CAACH,QAAQ,EAAEC,OAAO,CAAC;EAEjD,IAAIG,GAAG,GAAGF,OAAO,CAACG,KAAK,CAACN,GAAG,CAAC;EAC5B,IAAIO,KAAK,GAAGJ,OAAO,CAACK,GAAG,CAAC,CAAC;EAEzB,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GAAG,CAAC,GAAInB,MAAM,CAACoB,MAAM,CAAC,CAACL,GAAG,EAAEE,KAAK,CAAC,CAAC,GAAGF,GAAG;AAC1E,CAAC;AAEDZ,KAAK,CAACkB,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAEX,QAAQ,EAAEC,OAAO,EAAE;EACnD,IAAI,OAAOU,GAAG,KAAK,QAAQ,EAAE;IACzB,IAAI,CAACnB,KAAK,CAACoB,iBAAiB,EAAE;MAC1BC,OAAO,CAACC,KAAK,CAAC,0IAA0I,CAAC;MACzJtB,KAAK,CAACoB,iBAAiB,GAAG,IAAI;IAClC;IAEAD,GAAG,GAAGtB,MAAM,CAAC0B,IAAI,CAAC,EAAE,IAAIJ,GAAG,IAAI,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;EACnD;;EAEA,IAAIK,OAAO,GAAGxB,KAAK,CAACyB,UAAU,CAACjB,QAAQ,EAAEC,OAAO,CAAC;EAEjD,IAAIG,GAAG,GAAGY,OAAO,CAACX,KAAK,CAACM,GAAG,CAAC;EAC5B,IAAIL,KAAK,GAAGU,OAAO,CAACT,GAAG,CAAC,CAAC;EAEzB,OAAOD,KAAK,GAAIF,GAAG,GAAGE,KAAK,GAAIF,GAAG;AACtC,CAAC;AAEDZ,KAAK,CAAC0B,cAAc,GAAG,SAASA,cAAcA,CAACC,GAAG,EAAE;EAChD,IAAI;IACA3B,KAAK,CAAC4B,QAAQ,CAACD,GAAG,CAAC;IACnB,OAAO,IAAI;EACf,CAAC,CAAC,OAAOE,CAAC,EAAE;IACR,OAAO,KAAK;EAChB;AACJ,CAAC;;AAED;AACA7B,KAAK,CAAC8B,UAAU,GAAG9B,KAAK,CAACM,MAAM;AAC/BN,KAAK,CAAC+B,YAAY,GAAG/B,KAAK,CAACkB,MAAM;;AAEjC;AACAlB,KAAK,CAACgC,eAAe,GAAG,CAAC,CAAC;AAC1BhC,KAAK,CAAC4B,QAAQ,GAAG,SAASA,QAAQA,CAACpB,QAAQ,EAAE;EACzC,IAAI,CAACR,KAAK,CAACG,SAAS,EAChBH,KAAK,CAACG,SAAS,GAAGL,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;;EAE/C;EACA,IAAI6B,GAAG,GAAG3B,KAAK,CAACiC,qBAAqB,CAACzB,QAAQ,CAAC;;EAE/C;EACA,IAAI0B,YAAY,GAAG,CAAC,CAAC;EACrB,OAAO,IAAI,EAAE;IACT,IAAIC,KAAK,GAAGnC,KAAK,CAACgC,eAAe,CAACL,GAAG,CAAC;IACtC,IAAIQ,KAAK,EACL,OAAOA,KAAK;IAEhB,IAAIC,QAAQ,GAAGpC,KAAK,CAACG,SAAS,CAACwB,GAAG,CAAC;IAEnC,QAAQ,OAAOS,QAAQ;MACnB,KAAK,QAAQ;QAAE;QACXT,GAAG,GAAGS,QAAQ;QACd;MAEJ,KAAK,QAAQ;QAAE;QACX,KAAK,IAAIC,GAAG,IAAID,QAAQ,EACpBF,YAAY,CAACG,GAAG,CAAC,GAAGD,QAAQ,CAACC,GAAG,CAAC;QAErC,IAAI,CAACH,YAAY,CAACI,YAAY,EAC1BJ,YAAY,CAACI,YAAY,GAAGX,GAAG;QAEnCA,GAAG,GAAGS,QAAQ,CAACG,IAAI;QACnB;MAEJ,KAAK,UAAU;QAAE;QACb,IAAI,CAACL,YAAY,CAACI,YAAY,EAC1BJ,YAAY,CAACI,YAAY,GAAGX,GAAG;;QAEnC;QACA;QACAQ,KAAK,GAAG,IAAIC,QAAQ,CAACF,YAAY,EAAElC,KAAK,CAAC;QAEzCA,KAAK,CAACgC,eAAe,CAACE,YAAY,CAACI,YAAY,CAAC,GAAGH,KAAK,CAAC,CAAC;QAC1D,OAAOA,KAAK;MAEhB;QACI,MAAM,IAAIK,KAAK,CAAC,4BAA4B,GAAGhC,QAAQ,GAAG,mBAAmB,GAACmB,GAAG,GAAC,IAAI,CAAC;IAC/F;EACJ;AACJ,CAAC;AAED3B,KAAK,CAACiC,qBAAqB,GAAG,UAASzB,QAAQ,EAAE;EAC7C;EACA,OAAO,CAAC,EAAE,GAACA,QAAQ,EAAEiC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;AACxE,CAAC;AAED1C,KAAK,CAACW,UAAU,GAAG,SAASA,UAAUA,CAACH,QAAQ,EAAEC,OAAO,EAAE;EACtD,IAAI0B,KAAK,GAAGnC,KAAK,CAAC4B,QAAQ,CAACpB,QAAQ,CAAC;IAChCE,OAAO,GAAG,IAAIyB,KAAK,CAACzB,OAAO,CAACD,OAAO,EAAE0B,KAAK,CAAC;EAE/C,IAAIA,KAAK,CAACQ,QAAQ,IAAIlC,OAAO,IAAIA,OAAO,CAACmC,MAAM,EAC3ClC,OAAO,GAAG,IAAIX,WAAW,CAAC8C,UAAU,CAACnC,OAAO,EAAED,OAAO,CAAC;EAE1D,OAAOC,OAAO;AAClB,CAAC;AAEDV,KAAK,CAACyB,UAAU,GAAG,SAASA,UAAUA,CAACjB,QAAQ,EAAEC,OAAO,EAAE;EACtD,IAAI0B,KAAK,GAAGnC,KAAK,CAAC4B,QAAQ,CAACpB,QAAQ,CAAC;IAChCgB,OAAO,GAAG,IAAIW,KAAK,CAACX,OAAO,CAACf,OAAO,EAAE0B,KAAK,CAAC;EAE/C,IAAIA,KAAK,CAACQ,QAAQ,IAAI,EAAElC,OAAO,IAAIA,OAAO,CAACqC,QAAQ,KAAK,KAAK,CAAC,EAC1DtB,OAAO,GAAG,IAAIzB,WAAW,CAACgD,QAAQ,CAACvB,OAAO,EAAEf,OAAO,CAAC;EAExD,OAAOe,OAAO;AAClB,CAAC;;AAGD;AACA,IAAIwB,OAAO,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,QAAQ,IAAID,OAAO,CAACC,QAAQ,CAACC,IAAI;AACzF,IAAIH,OAAO,EAAE;EAET;EACA,IAAII,UAAU,GAAGJ,OAAO,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;EAC/C,IAAIH,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE;IAC1CtD,OAAO,CAAC,WAAW,CAAC,CAACE,KAAK,CAAC;EAC/B;;EAEA;EACAF,OAAO,CAAC,eAAe,CAAC,CAACE,KAAK,CAAC;AACnC;AAEA,IAAI,GAAG,IAAI,QAAQ,EAAE;EACjBqB,OAAO,CAACC,KAAK,CAAC,2KAA2K,CAAC;AAC9L", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}