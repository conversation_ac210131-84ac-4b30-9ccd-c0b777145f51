{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthenticationService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8060/api/v1/auth';\n  }\n  register(firstName, lastName, email, password) {\n    return this.http.post(`${this.apiUrl}/register`, {\n      firstName,\n      lastName,\n      email,\n      password\n    });\n  }\n  login(email, password) {\n    return this.http.post(`${this.apiUrl}/authenticate`, {\n      email,\n      password\n    });\n  }\n  storeToken(token) {\n    localStorage.setItem('jwt_token', token);\n  }\n  getToken() {\n    const token = localStorage.getItem('jwt_token');\n    return token && !this.isTokenExpired(token) ? token : null;\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    sessionStorage.clear();\n    // 🔥 Clear browser history and redirect to login\n    this.router.navigate(['/login']).then(() => {\n      window.history.pushState(null, '', '/login'); // Replace history state\n    });\n  }\n\n  isAuthenticated() {\n    return !!this.getToken();\n  }\n  getUserDetailsFromToken(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT\n      return {\n        role: payload?.role || '',\n        groupe: payload?.groupe || ''\n      };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return {\n        role: '',\n        groupe: ''\n      };\n    }\n  }\n  isTokenExpired(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return Date.now() > payload.exp * 1000;\n    } catch (e) {\n      return true;\n    }\n  }\n  static {\n    this.ɵfac = function AuthenticationService_Factory(t) {\n      return new (t || AuthenticationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthenticationService,\n      factory: AuthenticationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AuthenticationService", "constructor", "http", "apiUrl", "register", "firstName", "lastName", "email", "password", "post", "login", "storeToken", "token", "localStorage", "setItem", "getToken", "getItem", "isTokenExpired", "logout", "removeItem", "sessionStorage", "clear", "router", "navigate", "then", "window", "history", "pushState", "isAuthenticated", "getUserDetailsFromToken", "payload", "JSON", "parse", "atob", "split", "role", "groupe", "e", "console", "error", "Date", "now", "exp", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\authentication.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Router } from '@angular/router';  // ✅ Import Router\nimport { Observable } from 'rxjs';\n\ninterface AuthResponse {\n  token: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthenticationService {\n  private apiUrl = 'http://localhost:8060/api/v1/auth';\n\n  constructor(private http: HttpClient) {}\n\n  register(firstName: string, lastName: string, email: string, password: string): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/register`, { firstName, lastName, email, password });\n  }\n\n  login(email: string, password: string): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/authenticate`, { email, password });\n  }\n\n  storeToken(token: string) {\n    localStorage.setItem('jwt_token', token);\n  }\n\n  getToken(): string | null {\n    const token = localStorage.getItem('jwt_token');\n    return token && !this.isTokenExpired(token) ? token : null;\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    sessionStorage.clear();\n  \n    // 🔥 Clear browser history and redirect to login\n    this.router.navigate(['/login']).then(() => {\n      window.history.pushState(null, '', '/login'); // Replace history state\n    });\n  }\n  \n  isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n\n  getUserDetailsFromToken(token: string): { role: string, groupe: string } {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT\n      return { role: payload?.role || '', groupe: payload?.groupe || '' };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return { role: '', groupe: '' };\n    }\n  }\n\n  private isTokenExpired(token: string): boolean {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return Date.now() > payload.exp * 1000;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n"], "mappings": ";;AAYA,OAAM,MAAOA,qBAAqB;EAGhCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,mCAAmC;EAEb;EAEvCC,QAAQA,CAACC,SAAiB,EAAEC,QAAgB,EAAEC,KAAa,EAAEC,QAAgB;IAC3E,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAe,GAAG,IAAI,CAACN,MAAM,WAAW,EAAE;MAAEE,SAAS;MAAEC,QAAQ;MAAEC,KAAK;MAAEC;IAAQ,CAAE,CAAC;EAC1G;EAEAE,KAAKA,CAACH,KAAa,EAAEC,QAAgB;IACnC,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAe,GAAG,IAAI,CAACN,MAAM,eAAe,EAAE;MAAEI,KAAK;MAAEC;IAAQ,CAAE,CAAC;EACzF;EAEAG,UAAUA,CAACC,KAAa;IACtBC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEF,KAAK,CAAC;EAC1C;EAEAG,QAAQA,CAAA;IACN,MAAMH,KAAK,GAAGC,YAAY,CAACG,OAAO,CAAC,WAAW,CAAC;IAC/C,OAAOJ,KAAK,IAAI,CAAC,IAAI,CAACK,cAAc,CAACL,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAI;EAC5D;EAEAM,MAAMA,CAAA;IACJL,YAAY,CAACM,UAAU,CAAC,WAAW,CAAC;IACpCC,cAAc,CAACC,KAAK,EAAE;IAEtB;IACA,IAAI,CAACC,MAAM,CAACC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,MAAK;MACzCC,MAAM,CAACC,OAAO,CAACC,SAAS,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;EACJ;;EAEAC,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAACb,QAAQ,EAAE;EAC1B;EAEAc,uBAAuBA,CAACjB,KAAa;IACnC,IAAI;MACF,MAAMkB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACrB,KAAK,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,OAAO;QAAEC,IAAI,EAAEL,OAAO,EAAEK,IAAI,IAAI,EAAE;QAAEC,MAAM,EAAEN,OAAO,EAAEM,MAAM,IAAI;MAAE,CAAE;KACpE,CAAC,OAAOC,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,CAAC,CAAC;MACzC,OAAO;QAAEF,IAAI,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAE;;EAEnC;EAEQnB,cAAcA,CAACL,KAAa;IAClC,IAAI;MACF,MAAMkB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACrB,KAAK,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,OAAOM,IAAI,CAACC,GAAG,EAAE,GAAGX,OAAO,CAACY,GAAG,GAAG,IAAI;KACvC,CAAC,OAAOL,CAAC,EAAE;MACV,OAAO,IAAI;;EAEf;;;uBArDWrC,qBAAqB,EAAA2C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArB9C,qBAAqB;MAAA+C,OAAA,EAArB/C,qBAAqB,CAAAgD,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}