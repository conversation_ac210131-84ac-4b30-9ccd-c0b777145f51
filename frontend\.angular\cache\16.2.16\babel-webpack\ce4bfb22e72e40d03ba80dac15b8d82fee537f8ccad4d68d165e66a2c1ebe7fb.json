{"ast": null, "code": "import * as feather from 'feather-icons';\nimport { Modal } from 'bootstrap';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/groupe.service\";\nimport * as i2 from \"../services/ressource.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../auth/authentication.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction GroupsComponent_a_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 60);\n    i0.ɵɵelement(1, \"span\", 61);\n    i0.ɵɵtext(2, \" Dashboard \");\n    i0.ɵɵelementStart(3, \"span\", 62);\n    i0.ɵɵtext(4, \"(current)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GroupsComponent_a_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 63);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Gestion des utilisateurs \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupsComponent_a_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 65);\n    i0.ɵɵelement(1, \"span\", 66);\n    i0.ɵɵtext(2, \" Gestion des groupes \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupsComponent_a_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 67);\n    i0.ɵɵelement(1, \"span\", 68);\n    i0.ɵɵtext(2, \" Gestion des transactions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupsComponent_a_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 69);\n    i0.ɵɵelement(1, \"span\", 70);\n    i0.ɵɵtext(2, \" Gestion des actions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupsComponent_a_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 71);\n    i0.ɵɵelement(1, \"span\", 72);\n    i0.ɵɵtext(2, \" Gestion des actionnaires \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GroupsComponent_a_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 73);\n    i0.ɵɵelement(1, \"span\", 74);\n    i0.ɵɵtext(2, \" Gestion des Portefeuilles \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/edit-groupe\", a1];\n};\nfunction GroupsComponent_tr_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"a\", 75)(7, \"i\", 76);\n    i0.ɵɵtext(8, \"\\uE254\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"a\", 77);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_tr_62_Template_a_click_9_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const groupe_r9 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(groupe_r9.idGroupe ? ctx_r10.deleteGroupe(groupe_r9.idGroupe, $event) : null);\n    });\n    i0.ɵɵelementStart(10, \"i\", 78);\n    i0.ɵɵtext(11, \"\\uE872\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const groupe_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r9.idGroupe || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r9.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, groupe_r9.idGroupe));\n  }\n}\nfunction GroupsComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 79)(2, \"input\", 80);\n    i0.ɵɵlistener(\"change\", function GroupsComponent_div_78_Template_input_change_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const ressource_r12 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onCheckboxChange($event, ressource_r12.idRessource));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 81);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementStart(5, \"small\", 82);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ressource_r12 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r8.selectedRessources.includes(ressource_r12.idRessource));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ressource_r12.nomRessource, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ressource_r12.link_path, \")\");\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, 0.25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n    border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n    border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n    font-size: 14px;\\n    border: none;\\n    cursor: pointer;\\n    border-radius: 4px;\\n    font-weight: bold;\\n    transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n    font-family: 'Poppins', sans-serif;\\n    margin: 0;\\n    padding: 0;\\n    min-height: 100vh;\\n    background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n    background-size: 400% 400%;\\n    animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n    color: #fff;\\n}\\n\\n\\n.modal-content.border-danger[_ngcontent-%COMP%] {\\n    border: 2px solid #dc3545;\\n    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);\\n  }\\n  \\n  .modal-header.bg-danger[_ngcontent-%COMP%] {\\n    background-color: #dc3545 !important;\\n    color: white;\\n  }\\n  \\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n    0% {\\n        background-position: 0% 50%;\\n    }\\n\\n    50% {\\n        background-position: 100% 50%;\\n    }\\n\\n    100% {\\n        background-position: 0% 50%;\\n    }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 2rem;\\n    margin-top: 20px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 1.5rem;\\n    -webkit-backdrop-filter: blur(12px);\\n            backdrop-filter: blur(12px);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n    color: #fff;\\n    border-radius: 15px;\\n    overflow: hidden;\\n    border-collapse: separate;\\n    border-spacing: 0 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.15);\\n    color: #ffffff;\\n    font-weight: 600;\\n    text-align: center;\\n    border: none;\\n    padding: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.1);\\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\\n    text-align: center;\\n    border-radius: 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.01);\\n    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    vertical-align: middle;\\n    padding: 0.9rem;\\n    font-size: 0.95rem;\\n}\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n    background-color: #28a745;\\n    color: white;\\n    border: none;\\n    padding: 12px 25px;\\n    border-radius: 30px;\\n    text-transform: uppercase;\\n    font-weight: bold;\\n    transition: background 0.3s, transform 0.2s;\\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n    background-color: #218838;\\n    transform: translateY(-2px);\\n}\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n    color: #206ee1;\\n    cursor: pointer;\\n    font-size: 20px;\\n    margin: 0 10px;\\n    transition: color 0.3s;\\n}\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n    color: #d22d2d;\\n    cursor: pointer;\\n    font-size: 20px;\\n    transition: color 0.3s;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n    background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n    color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n    text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n    background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n    background-color: #fff;\\n    padding: 1.5rem;\\n    color: #333;\\n    font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n    background-color: #f1f1f1;\\n    padding: 1rem;\\n    border-top: 1px solid #ddd;\\n    display: flex;\\n    justify-content: center; \\n\\n    gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem;\\n    font-size: 1rem;\\n    border-radius: 0.3rem;\\n    transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    color: #fff;\\n    border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n    transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n    background-color: #007bff;\\n    color: #fff;\\n    border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n    transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column; \\n\\n    align-items: flex-start; \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n    margin-top: 10px;\\n    background-color: rgba(255, 255, 255, 0.1); \\n\\n    border: 1px solid rgba(255, 255, 255, 0.3); \\n\\n    border-radius: 30px; \\n\\n    color: #fff; \\n\\n    padding: 10px 20px; \\n\\n    font-size: 1rem; \\n\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); \\n\\n    width: 100%; \\n\\n    max-width: 400px; \\n\\n    transition: all 0.3s ease; \\n\\n}\\n\\n.search-bar[_ngcontent-%COMP%]:focus {\\n    outline: none; \\n\\n    border-color: #007bff; \\n\\n    box-shadow: 0 0 8px rgba(0, 123, 255, 0.6); \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%]::placeholder {\\n    color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n    background: rgba(20, 33, 59, 0.9); \\n\\n    color: #fff;\\n    min-height: 100vh;\\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #ccc;\\n    transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, 0.1);\\n    border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n    background-color: #000 !important;\\n    color: #fff;\\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n    position: relative;\\n    padding: 0.5rem 1rem;\\n    transition: color 0.3s ease;\\n    font-weight: 500;\\n    letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n    content: '';\\n    position: absolute;\\n    left: 0;\\n    bottom: 0;\\n    height: 2px;\\n    width: 0;\\n    background: #ff4c60;\\n    transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n    width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n    height: 30px;\\n    width: 30px;\\n    object-fit: cover;\\n    border-radius: 50%; \\n\\n    margin-right: 8px;\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZ3JvdXBzL2dyb3Vwcy5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0lBQ0ksa0JBQWtCO0FBQ3RCOztBQUVBO0lBQ0ksV0FBVztJQUNYLFlBQVk7SUFDWiwyQkFBMkI7QUFDL0I7O0FBRUE7O0VBRUU7O0FBRUY7SUFDSSxlQUFlO0lBQ2YsTUFBTTtJQUNOLFNBQVM7SUFDVCxPQUFPO0lBQ1AsWUFBWSxFQUFFLHNCQUFzQjtJQUNwQyxVQUFVO0lBQ1YsNkNBQTZDO0FBQ2pEOztBQUVBO0lBRUksZ0JBQWdCO0lBQ2hCLFNBQVMsRUFBRSxxQkFBcUI7SUFDaEMsMEJBQTBCO0lBQzFCLGtCQUFrQjtJQUNsQixrQkFBa0I7SUFDbEIsZ0JBQWdCLEVBQUUsNkRBQTZEO0FBQ25GOztBQUVBO0lBQ0ksZ0JBQWdCO0lBQ2hCLGNBQWM7QUFDbEI7O0FBRUE7SUFDSSxpQkFBaUI7SUFDakIsV0FBVztBQUNmOztBQUVBO0lBQ0ksY0FBYztBQUNsQjs7QUFFQTs7SUFFSSxjQUFjO0FBQ2xCOztBQUVBO0lBQ0ksaUJBQWlCO0lBQ2pCLHlCQUF5QjtBQUM3Qjs7QUFFQTs7RUFFRTs7QUFFRjtJQUNJLG1CQUFtQjtJQUNuQixzQkFBc0I7SUFDdEIsZUFBZTtJQUNmLHFDQUFxQztJQUNyQyw4Q0FBOEM7QUFDbEQ7O0FBRUE7SUFDSSxvQkFBb0I7SUFDcEIsZUFBZTtJQUNmLGdCQUFnQjtBQUNwQjs7QUFFQTtJQUNJLFdBQVc7SUFDWCx5Q0FBeUM7SUFDekMscUNBQXFDO0FBQ3pDOztBQUVBO0lBQ0kseUJBQXlCO0lBQ3pCLDhDQUE4QztBQUNsRDs7QUFFQTs7RUFFRTs7QUFFRjtJQUNJLDZCQUE2QjtBQUNqQzs7QUFFQTtJQUNJLGdDQUFnQztBQUNwQzs7QUFFQSxzQkFBc0I7QUFDdEI7SUFDSSxpQkFBaUI7SUFDakIsZUFBZTtJQUNmLFlBQVk7SUFDWixlQUFlO0lBQ2Ysa0JBQWtCO0lBQ2xCLGlCQUFpQjtJQUNqQix5QkFBeUI7QUFDN0I7O0FBRUE7SUFDSSxrQ0FBa0M7SUFDbEMsU0FBUztJQUNULFVBQVU7SUFDVixpQkFBaUI7SUFDakIsdUVBQXVFO0lBQ3ZFLDBCQUEwQjtJQUMxQix5Q0FBeUM7SUFDekMsV0FBVztBQUNmOzs7QUFHQTtJQUNJLHlCQUF5QjtJQUN6QiwyQ0FBMkM7RUFDN0M7O0VBRUE7SUFDRSxvQ0FBb0M7SUFDcEMsWUFBWTtFQUNkOzs7QUFHRjtJQUNJO1FBQ0ksMkJBQTJCO0lBQy9COztJQUVBO1FBQ0ksNkJBQTZCO0lBQ2pDOztJQUVBO1FBQ0ksMkJBQTJCO0lBQy9CO0FBQ0o7O0FBRUE7SUFDSSxxQ0FBcUM7SUFDckMsbUJBQW1CO0lBQ25CLGFBQWE7SUFDYixnQkFBZ0I7QUFDcEI7O0FBRUE7SUFDSSxxQ0FBcUM7SUFDckMsbUJBQW1CO0lBQ25CLGVBQWU7SUFDZixtQ0FBMkI7WUFBM0IsMkJBQTJCO0lBQzNCLDBDQUEwQztBQUM5Qzs7QUFFQTtJQUNJLFdBQVc7SUFDWCxtQkFBbUI7SUFDbkIsZ0JBQWdCO0lBQ2hCLHlCQUF5QjtJQUN6QixzQkFBc0I7QUFDMUI7O0FBRUE7SUFDSSwyQ0FBMkM7SUFDM0MsY0FBYztJQUNkLGdCQUFnQjtJQUNoQixrQkFBa0I7SUFDbEIsWUFBWTtJQUNaLGFBQWE7QUFDakI7O0FBRUE7SUFDSSwwQ0FBMEM7SUFDMUMscURBQXFEO0lBQ3JELGtCQUFrQjtJQUNsQixtQkFBbUI7QUFDdkI7O0FBRUE7SUFDSSxzQkFBc0I7SUFDdEIsK0NBQStDO0FBQ25EOztBQUVBO0lBQ0ksc0JBQXNCO0lBQ3RCLGVBQWU7SUFDZixrQkFBa0I7QUFDdEI7O0FBRUE7SUFDSSx5QkFBeUI7SUFDekIsWUFBWTtJQUNaLFlBQVk7SUFDWixrQkFBa0I7SUFDbEIsbUJBQW1CO0lBQ25CLHlCQUF5QjtJQUN6QixpQkFBaUI7SUFDakIsMkNBQTJDO0lBQzNDLHlDQUF5QztBQUM3Qzs7QUFFQTtJQUNJLHlCQUF5QjtJQUN6QiwyQkFBMkI7QUFDL0I7O0FBRUE7SUFDSSxjQUFjO0lBQ2QsZUFBZTtJQUNmLGVBQWU7SUFDZixjQUFjO0lBQ2Qsc0JBQXNCO0FBQzFCOztBQUVBO0lBQ0ksY0FBYztBQUNsQjs7QUFFQTtJQUNJLGNBQWM7SUFDZCxlQUFlO0lBQ2YsZUFBZTtJQUNmLHNCQUFzQjtBQUMxQjs7QUFFQTtJQUNJLGNBQWM7QUFDbEI7O0FBRUE7SUFDSSxvQ0FBb0M7QUFDeEM7O0FBRUE7SUFDSSxXQUFXO0FBQ2Y7O0FBRUE7SUFDSSxxQkFBcUI7QUFDekI7O0FBRUE7SUFDSSx5QkFBeUI7QUFDN0I7O0FBRUEsZUFBZTtBQUNmO0lBQ0ksc0JBQXNCO0lBQ3RCLGVBQWU7SUFDZixXQUFXO0lBQ1gsZUFBZTtBQUNuQjs7QUFFQSxpQkFBaUI7QUFDakI7SUFDSSx5QkFBeUI7SUFDekIsYUFBYTtJQUNiLDBCQUEwQjtJQUMxQixhQUFhO0lBQ2IsdUJBQXVCLEVBQUUsZ0NBQWdDO0lBQ3pELFNBQVMsRUFBRSw4QkFBOEI7QUFDN0M7O0FBRUE7SUFDSSxvQkFBb0I7SUFDcEIsZUFBZTtJQUNmLHFCQUFxQjtJQUNyQixpREFBaUQ7QUFDckQ7O0FBRUE7SUFDSSx5QkFBeUI7SUFDekIsV0FBVztJQUNYLFlBQVk7QUFDaEI7O0FBRUE7SUFDSSx5QkFBeUI7SUFDekIsMkJBQTJCO0FBQy9COztBQUVBO0lBQ0kseUJBQXlCO0lBQ3pCLFdBQVc7SUFDWCxZQUFZO0FBQ2hCOztBQUVBO0lBQ0kseUJBQXlCO0lBQ3pCLDJCQUEyQjtBQUMvQjs7QUFFQSwrQ0FBK0M7QUFDL0M7SUFDSSxhQUFhO0lBQ2Isc0JBQXNCLEVBQUUsMkJBQTJCO0lBQ25ELHVCQUF1QixFQUFFLGlDQUFpQztBQUM5RDs7QUFFQSxzQkFBc0I7QUFDdEI7SUFDSSxnQkFBZ0I7SUFDaEIsMENBQTBDLEVBQUUsaUNBQWlDO0lBQzdFLDBDQUEwQyxFQUFFLGtCQUFrQjtJQUM5RCxtQkFBbUIsRUFBRSxvQkFBb0I7SUFDekMsV0FBVyxFQUFFLGVBQWU7SUFDNUIsa0JBQWtCLEVBQUUsNkJBQTZCO0lBQ2pELGVBQWUsRUFBRSxxQ0FBcUM7SUFDdEQsd0NBQXdDLEVBQUUsNEJBQTRCO0lBQ3RFLFdBQVcsRUFBRSx1QkFBdUI7SUFDcEMsZ0JBQWdCLEVBQUUsNERBQTREO0lBQzlFLHlCQUF5QixFQUFFLHNCQUFzQjtBQUNyRDs7QUFFQTtJQUNJLGFBQWEsRUFBRSwyQkFBMkI7SUFDMUMscUJBQXFCLEVBQUUsd0NBQXdDO0lBQy9ELDBDQUEwQyxFQUFFLHFCQUFxQjtBQUNyRTs7QUFFQSwyQkFBMkI7QUFDM0I7SUFDSSwrQkFBK0IsRUFBRSwyQkFBMkI7QUFDaEU7OztBQUdBO0lBQ0ksaUNBQWlDLEVBQUUsK0JBQStCO0lBQ2xFLFdBQVc7SUFDWCxpQkFBaUI7SUFDakIsZ0RBQWdEO0FBQ3BEOztBQUVBO0lBQ0ksV0FBVztJQUNYLGlEQUFpRDtBQUNyRDs7QUFFQTs7SUFFSSxXQUFXO0lBQ1gsMENBQTBDO0lBQzFDLGtCQUFrQjtBQUN0Qjs7O0FBR0E7SUFDSSxpQ0FBaUM7SUFDakMsV0FBVztJQUNYLGlEQUFpRDtBQUNyRDs7QUFFQTs7SUFFSSxzQkFBc0I7QUFDMUI7O0FBRUE7SUFDSSwwQkFBMEI7QUFDOUI7Ozs7QUFJQTtJQUNJLHNCQUFzQjtJQUN0QixrQkFBa0I7SUFDbEIsb0JBQW9CO0lBQ3BCLDJCQUEyQjtJQUMzQixnQkFBZ0I7SUFDaEIscUJBQXFCO0FBQ3pCOztBQUVBO0lBQ0ksV0FBVztJQUNYLGtCQUFrQjtJQUNsQixPQUFPO0lBQ1AsU0FBUztJQUNULFdBQVc7SUFDWCxRQUFRO0lBQ1IsbUJBQW1CO0lBQ25CLDJCQUEyQjtBQUMvQjs7QUFFQTtJQUNJLHlCQUF5QjtBQUM3Qjs7QUFFQTtJQUNJLFdBQVc7QUFDZjs7QUFFQTtJQUNJLFlBQVk7SUFDWixXQUFXO0lBQ1gsaUJBQWlCO0lBQ2pCLGtCQUFrQixFQUFFLDZCQUE2QjtJQUNqRCxpQkFBaUI7RUFDbkIiLCJzb3VyY2VzQ29udGVudCI6WyJib2R5IHtcclxuICAgIGZvbnQtc2l6ZTogLjg3NXJlbTtcclxufVxyXG5cclxuLmZlYXRoZXIge1xyXG4gICAgd2lkdGg6IDE2cHg7XHJcbiAgICBoZWlnaHQ6IDE2cHg7XHJcbiAgICB2ZXJ0aWNhbC1hbGlnbjogdGV4dC1ib3R0b207XHJcbn1cclxuXHJcbi8qXHJcbiAqIFNpZGViYXJcclxuICovXHJcblxyXG4uc2lkZWJhciB7XHJcbiAgICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgICB0b3A6IDA7XHJcbiAgICBib3R0b206IDA7XHJcbiAgICBsZWZ0OiAwO1xyXG4gICAgei1pbmRleDogMTAwOyAvKiBCZWhpbmQgdGhlIG5hdmJhciAqL1xyXG4gICAgcGFkZGluZzogMDtcclxuICAgIGJveC1zaGFkb3c6IGluc2V0IC0xcHggMCAwIHJnYmEoMCwgMCwgMCwgMC4xKTtcclxufVxyXG5cclxuLnNpZGViYXItc3RpY2t5IHtcclxuICAgIHBvc2l0aW9uOiAtd2Via2l0LXN0aWNreTtcclxuICAgIHBvc2l0aW9uOiBzdGlja3k7XHJcbiAgICB0b3A6IDQ4cHg7IC8qIEhlaWdodCBvZiBuYXZiYXIgKi9cclxuICAgIGhlaWdodDogY2FsYygxMDB2aCAtIDQ4cHgpO1xyXG4gICAgcGFkZGluZy10b3A6IC41cmVtO1xyXG4gICAgb3ZlcmZsb3cteDogaGlkZGVuO1xyXG4gICAgb3ZlcmZsb3cteTogYXV0bzsgLyogU2Nyb2xsYWJsZSBjb250ZW50cyBpZiB2aWV3cG9ydCBpcyBzaG9ydGVyIHRoYW4gY29udGVudC4gKi9cclxufVxyXG5cclxuLnNpZGViYXIgLm5hdi1saW5rIHtcclxuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICBjb2xvcjogIzAwMDAwMDtcclxufVxyXG5cclxuLnNpZGViYXIgLm5hdi1saW5rIC5mZWF0aGVyIHtcclxuICAgIG1hcmdpbi1yaWdodDogNHB4O1xyXG4gICAgY29sb3I6ICM5OTk7XHJcbn1cclxuXHJcbi5zaWRlYmFyIC5uYXYtbGluay5hY3RpdmUge1xyXG4gICAgY29sb3I6ICMwMDdiZmY7XHJcbn1cclxuXHJcbi5zaWRlYmFyIC5uYXYtbGluazpob3ZlciAuZmVhdGhlcixcclxuLnNpZGViYXIgLm5hdi1saW5rLmFjdGl2ZSAuZmVhdGhlciB7XHJcbiAgICBjb2xvcjogaW5oZXJpdDtcclxufVxyXG5cclxuLnNpZGViYXItaGVhZGluZyB7XHJcbiAgICBmb250LXNpemU6IC43NXJlbTtcclxuICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XHJcbn1cclxuXHJcbi8qXHJcbiAqIE5hdmJhclxyXG4gKi9cclxuXHJcbi5uYXZiYXItYnJhbmQge1xyXG4gICAgcGFkZGluZy10b3A6IC43NXJlbTtcclxuICAgIHBhZGRpbmctYm90dG9tOiAuNzVyZW07XHJcbiAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuMjUpO1xyXG4gICAgYm94LXNoYWRvdzogaW5zZXQgLTFweCAwIDAgcmdiYSgwLCAwLCAwLCAwLjI1KTtcclxufVxyXG5cclxuLm5hdmJhciAuZm9ybS1jb250cm9sIHtcclxuICAgIHBhZGRpbmc6IC43NXJlbSAxcmVtO1xyXG4gICAgYm9yZGVyLXdpZHRoOiAwO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMDtcclxufVxyXG5cclxuLmZvcm0tY29udHJvbC1kYXJrIHtcclxuICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAuMSk7XHJcbiAgICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgLjEpO1xyXG59XHJcblxyXG4uZm9ybS1jb250cm9sLWRhcms6Zm9jdXMge1xyXG4gICAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcclxuICAgIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKDI1NSwgMjU1LCAyNTUsIC4yNSk7XHJcbn1cclxuXHJcbi8qXHJcbiAqIFV0aWxpdGllc1xyXG4gKi9cclxuXHJcbi5ib3JkZXItdG9wIHtcclxuICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTVlNWU1O1xyXG59XHJcblxyXG4uYm9yZGVyLWJvdHRvbSB7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTVlNTtcclxufVxyXG5cclxuLyogQmFzZSBidXR0b24gc3R5bGUgKi9cclxuLmJ0biB7XHJcbiAgICBwYWRkaW5nOiA4cHggMTZweDtcclxuICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgIGJvcmRlcjogbm9uZTtcclxuICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxufVxyXG5cclxuYm9keSB7XHJcbiAgICBmb250LWZhbWlseTogJ1BvcHBpbnMnLCBzYW5zLXNlcmlmO1xyXG4gICAgbWFyZ2luOiAwO1xyXG4gICAgcGFkZGluZzogMDtcclxuICAgIG1pbi1oZWlnaHQ6IDEwMHZoO1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KC00NWRlZywgIzI0Mzc1YSwgIzEwMjAzYSwgIzE0MjEzYiwgIzBjMTcyOSk7XHJcbiAgICBiYWNrZ3JvdW5kLXNpemU6IDQwMCUgNDAwJTtcclxuICAgIGFuaW1hdGlvbjogZ3JhZGllbnRNb3ZlIDIwcyBlYXNlIGluZmluaXRlO1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbn1cclxuXHJcblxyXG4ubW9kYWwtY29udGVudC5ib3JkZXItZGFuZ2VyIHtcclxuICAgIGJvcmRlcjogMnB4IHNvbGlkICNkYzM1NDU7XHJcbiAgICBib3gtc2hhZG93OiAwIDAgMTBweCByZ2JhKDIyMCwgNTMsIDY5LCAwLjUpO1xyXG4gIH1cclxuICBcclxuICAubW9kYWwtaGVhZGVyLmJnLWRhbmdlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZGMzNTQ1ICFpbXBvcnRhbnQ7XHJcbiAgICBjb2xvcjogd2hpdGU7XHJcbiAgfVxyXG4gIFxyXG5cclxuQGtleWZyYW1lcyBncmFkaWVudE1vdmUge1xyXG4gICAgMCUge1xyXG4gICAgICAgIGJhY2tncm91bmQtcG9zaXRpb246IDAlIDUwJTtcclxuICAgIH1cclxuXHJcbiAgICA1MCUge1xyXG4gICAgICAgIGJhY2tncm91bmQtcG9zaXRpb246IDEwMCUgNTAlO1xyXG4gICAgfVxyXG5cclxuICAgIDEwMCUge1xyXG4gICAgICAgIGJhY2tncm91bmQtcG9zaXRpb246IDAlIDUwJTtcclxuICAgIH1cclxufVxyXG5cclxuLmNvbnRhaW5lci1mbHVpZCB7XHJcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDYpO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTVweDtcclxuICAgIHBhZGRpbmc6IDJyZW07XHJcbiAgICBtYXJnaW4tdG9wOiAyMHB4O1xyXG59XHJcblxyXG4udGFibGUtcmVzcG9uc2l2ZSB7XHJcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDYpO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTVweDtcclxuICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuICAgIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMnB4KTtcclxuICAgIGJveC1zaGFkb3c6IDAgOHB4IDMycHggcmdiYSgwLCAwLCAwLCAwLjI1KTtcclxufVxyXG5cclxuLnRhYmxlIHtcclxuICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTVweDtcclxuICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICBib3JkZXItY29sbGFwc2U6IHNlcGFyYXRlO1xyXG4gICAgYm9yZGVyLXNwYWNpbmc6IDAgMTJweDtcclxufVxyXG5cclxuLnRhYmxlIHRoZWFkIHRoIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XHJcbiAgICBjb2xvcjogI2ZmZmZmZjtcclxuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICBib3JkZXI6IG5vbmU7XHJcbiAgICBwYWRkaW5nOiAxcmVtO1xyXG59XHJcblxyXG4udGFibGUgdGJvZHkgdHIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xyXG4gICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZSwgYm94LXNoYWRvdyAwLjJzIGVhc2U7XHJcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG59XHJcblxyXG4udGFibGUgdGJvZHkgdHI6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjAxKTtcclxuICAgIGJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xyXG59XHJcblxyXG4udGFibGUgdGQge1xyXG4gICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTtcclxuICAgIHBhZGRpbmc6IDAuOXJlbTtcclxuICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcclxufVxyXG5cclxuLmJ0bi1jdXN0b20ge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzI4YTc0NTtcclxuICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgIGJvcmRlcjogbm9uZTtcclxuICAgIHBhZGRpbmc6IDEycHggMjVweDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDMwcHg7XHJcbiAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xyXG4gICAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIDAuM3MsIHRyYW5zZm9ybSAwLjJzO1xyXG4gICAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMik7XHJcbn1cclxuXHJcbi5idG4tY3VzdG9tOmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMyMTg4Mzg7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbn1cclxuXHJcbi5lZGl0LWljb24ge1xyXG4gICAgY29sb3I6ICMyMDZlZTE7XHJcbiAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICBmb250LXNpemU6IDIwcHg7XHJcbiAgICBtYXJnaW46IDAgMTBweDtcclxuICAgIHRyYW5zaXRpb246IGNvbG9yIDAuM3M7XHJcbn1cclxuXHJcbi5lZGl0LWljb246aG92ZXIge1xyXG4gICAgY29sb3I6ICNmZmZmZmY7XHJcbn1cclxuXHJcbi5kZWxldGUtaWNvbiB7XHJcbiAgICBjb2xvcjogI2QyMmQyZDtcclxuICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIGZvbnQtc2l6ZTogMjBweDtcclxuICAgIHRyYW5zaXRpb246IGNvbG9yIDAuM3M7XHJcbn1cclxuXHJcbi5kZWxldGUtaWNvbjpob3ZlciB7XHJcbiAgICBjb2xvcjogI2ZmZmZmZjtcclxufVxyXG5cclxuLm5hdmJhciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMWMxYzFjICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbmgyIHtcclxuICAgIGNvbG9yOiAjZmZmO1xyXG59XHJcblxyXG5hLnJvdXRlci1saW5rIHtcclxuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxufVxyXG5cclxuLm1vZGFsLWhlYWRlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjUyNTI4O1xyXG59XHJcblxyXG4vKiBNb2RhbCBCb2R5ICovXHJcbi5tb2RhbC1ib2R5IHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBjb2xvcjogIzMzMztcclxuICAgIGZvbnQtc2l6ZTogMXJlbTtcclxufVxyXG5cclxuLyogTW9kYWwgRm9vdGVyICovXHJcbi5tb2RhbC1mb290ZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2YxZjFmMTtcclxuICAgIHBhZGRpbmc6IDFyZW07XHJcbiAgICBib3JkZXItdG9wOiAxcHggc29saWQgI2RkZDtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsgLyogQ2VudGVyIGJ1dHRvbnMgaG9yaXpvbnRhbGx5ICovXHJcbiAgICBnYXA6IDFyZW07IC8qIFNwYWNlIGJldHdlZW4gdGhlIGJ1dHRvbnMgKi9cclxufVxyXG5cclxuLm1vZGFsLWZvb3RlciBidXR0b24ge1xyXG4gICAgcGFkZGluZzogMC41cmVtIDFyZW07XHJcbiAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICBib3JkZXItcmFkaXVzOiAwLjNyZW07XHJcbiAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuM3MsIHRyYW5zZm9ybSAwLjJzO1xyXG59XHJcblxyXG4ubW9kYWwtZm9vdGVyIC5idG4tc2Vjb25kYXJ5IHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICBjb2xvcjogI2ZmZjtcclxuICAgIGJvcmRlcjogbm9uZTtcclxufVxyXG5cclxuLm1vZGFsLWZvb3RlciAuYnRuLXNlY29uZGFyeTpob3ZlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNWE2MjY4O1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xyXG59XHJcblxyXG4ubW9kYWwtZm9vdGVyIC5idG4tcHJpbWFyeSB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDA3YmZmO1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbiAgICBib3JkZXI6IG5vbmU7XHJcbn1cclxuXHJcbi5tb2RhbC1mb290ZXIgLmJ0bi1wcmltYXJ5OmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMwMDU2YjM7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbn1cclxuXHJcbi8qIEVuc3VyZSBzZWFyY2ggYmFyIGFwcGVhcnMgYmVsb3cgdGhlIGJ1dHRvbiAqL1xyXG4ubWItMyB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsgLyogU3RhY2sgaXRlbXMgdmVydGljYWxseSAqL1xyXG4gICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7IC8qIEFsaWduIHRoZSBpdGVtcyB0byB0aGUgc3RhcnQgKi9cclxufVxyXG5cclxuLyogU2VhcmNoIEJhciBTdHlsZXMgKi9cclxuLnNlYXJjaC1iYXIge1xyXG4gICAgbWFyZ2luLXRvcDogMTBweDtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsgLyogVHJhbnNwYXJlbnQgd2hpdGUgYmFja2dyb3VuZCAqL1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpOyAvKiBTdWJ0bGUgYm9yZGVyICovXHJcbiAgICBib3JkZXItcmFkaXVzOiAzMHB4OyAvKiBSb3VuZGVkIGNvcm5lcnMgKi9cclxuICAgIGNvbG9yOiAjZmZmOyAvKiBXaGl0ZSB0ZXh0ICovXHJcbiAgICBwYWRkaW5nOiAxMHB4IDIwcHg7IC8qIFBhZGRpbmcgaW5zaWRlIHRoZSBpbnB1dCAqL1xyXG4gICAgZm9udC1zaXplOiAxcmVtOyAvKiBGb250IHNpemUgZm9yIGJldHRlciByZWFkYWJpbGl0eSAqL1xyXG4gICAgYm94LXNoYWRvdzogMCA0cHggOHB4IHJnYmEoMCwgMCwgMCwgMC4zKTsgLyogU2xpZ2h0IHNoYWRvdyBmb3IgZGVwdGggKi9cclxuICAgIHdpZHRoOiAxMDAlOyAvKiBNYWtlIGl0IHJlc3BvbnNpdmUgKi9cclxuICAgIG1heC13aWR0aDogNDAwcHg7IC8qIFNldCBhIG1heCB3aWR0aCBmb3IgYmV0dGVyIGFwcGVhcmFuY2Ugb24gbGFyZ2VyIHNjcmVlbnMgKi9cclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7IC8qIFNtb290aCB0cmFuc2l0aW9uICovXHJcbn1cclxuXHJcbi5zZWFyY2gtYmFyOmZvY3VzIHtcclxuICAgIG91dGxpbmU6IG5vbmU7IC8qIFJlbW92ZSBkZWZhdWx0IG91dGxpbmUgKi9cclxuICAgIGJvcmRlci1jb2xvcjogIzAwN2JmZjsgLyogSGlnaGxpZ2h0IHdpdGggYmx1ZSBib3JkZXIgb24gZm9jdXMgKi9cclxuICAgIGJveC1zaGFkb3c6IDAgMCA4cHggcmdiYSgwLCAxMjMsIDI1NSwgMC42KTsgLyogQmx1ZSBnbG93IGVmZmVjdCAqL1xyXG59XHJcblxyXG4vKiBQbGFjZWhvbGRlciB0ZXh0IGNvbG9yICovXHJcbi5zZWFyY2gtYmFyOjpwbGFjZWhvbGRlciB7XHJcbiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpOyAvKiBMaWdodCBncmF5IHBsYWNlaG9sZGVyICovXHJcbn1cclxuXHJcblxyXG4uY3VzdG9tLXNpZGViYXIge1xyXG4gICAgYmFja2dyb3VuZDogcmdiYSgyMCwgMzMsIDU5LCAwLjkpOyAvKiBkYXJrLCBzbGlnaHRseSB0cmFuc3BhcmVudCAqL1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbiAgICBtaW4taGVpZ2h0OiAxMDB2aDtcclxuICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcclxufVxyXG5cclxuLmN1c3RvbS1zaWRlYmFyIC5uYXYtbGluayB7XHJcbiAgICBjb2xvcjogI2NjYztcclxuICAgIHRyYW5zaXRpb246IGNvbG9yIDAuM3MgZWFzZSwgYmFja2dyb3VuZCAwLjNzIGVhc2U7XHJcbn1cclxuXHJcbi5jdXN0b20tc2lkZWJhciAubmF2LWxpbms6aG92ZXIsXHJcbi5jdXN0b20tc2lkZWJhciAubmF2LWxpbmsuYWN0aXZlIHtcclxuICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xyXG4gICAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG59XHJcblxyXG5cclxuLmN1c3RvbS10b3BiYXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzAwMCAhaW1wb3J0YW50O1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xyXG59XHJcblxyXG4uY3VzdG9tLXRvcGJhciAubmF2YmFyLWJyYW5kLFxyXG4uY3VzdG9tLXRvcGJhciAubmF2LWxpbmsge1xyXG4gICAgY29sb3I6ICNmZmYgIWltcG9ydGFudDtcclxufVxyXG5cclxuLmN1c3RvbS10b3BiYXIgLm5hdi1saW5rOmhvdmVyIHtcclxuICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG59XHJcblxyXG5cclxuXHJcbi5uYXZiYXIgLm5hdi1saW5rIHtcclxuICAgIGNvbG9yOiAjZmZmICFpbXBvcnRhbnQ7XHJcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcclxuICAgIHRyYW5zaXRpb246IGNvbG9yIDAuM3MgZWFzZTtcclxuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICBsZXR0ZXItc3BhY2luZzogMC41cHg7XHJcbn1cclxuXHJcbi5uYXZiYXIgLm5hdi1saW5rOjphZnRlciB7XHJcbiAgICBjb250ZW50OiAnJztcclxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgIGxlZnQ6IDA7XHJcbiAgICBib3R0b206IDA7XHJcbiAgICBoZWlnaHQ6IDJweDtcclxuICAgIHdpZHRoOiAwO1xyXG4gICAgYmFja2dyb3VuZDogI2ZmNGM2MDtcclxuICAgIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZTtcclxufVxyXG5cclxuLm5hdmJhciAubmF2LWxpbms6aG92ZXIge1xyXG4gICAgY29sb3I6ICNmZjRjNjAgIWltcG9ydGFudDtcclxufVxyXG5cclxuLm5hdmJhciAubmF2LWxpbms6aG92ZXI6OmFmdGVyIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG59XHJcblxyXG4ubG9nby1pbWcge1xyXG4gICAgaGVpZ2h0OiAzMHB4O1xyXG4gICAgd2lkdGg6IDMwcHg7XHJcbiAgICBvYmplY3QtZml0OiBjb3ZlcjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwJTsgLyogT3B0aW9uYWw6IG1ha2VzIGl0IHJvdW5kICovXHJcbiAgICBtYXJnaW4tcmlnaHQ6IDhweDtcclxuICB9XHJcbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\";\nexport class GroupsComponent {\n  constructor(groupeService, ressourceService, router, authService) {\n    this.groupeService = groupeService;\n    this.ressourceService = ressourceService;\n    this.router = router;\n    this.authService = authService;\n    this.groupes = [];\n    this.filteredGroupes = [];\n    this.newGroupe = {\n      nomGroupe: '',\n      ressources: []\n    };\n    this.editGroupe = null;\n    this.searchQuery = '';\n    this.ressources = [];\n    this.selectedRessources = [];\n    this.modalMessage = '';\n  }\n  ngOnInit() {\n    this.loadGroupes();\n    this.loadRessources();\n    const modalElement = document.getElementById('deleteErrorModal');\n    if (modalElement) {\n      this.errorModal = new Modal(modalElement);\n    }\n  }\n  ngAfterViewInit() {\n    feather.replace();\n    const modalElement = document.getElementById('errorModal');\n    if (modalElement) {\n      this.errorModal = new Modal(modalElement);\n    }\n  }\n  loadGroupes() {\n    this.groupeService.getAllGroupes().subscribe(data => {\n      this.groupes = data;\n      this.filteredGroupes = data;\n    }, err => {\n      console.error('Error loading groups', err);\n    });\n  }\n  loadRessources() {\n    this.ressourceService.getAllRessources().subscribe(data => {\n      this.ressources = data;\n    }, err => {\n      console.error('Error loading ressources', err);\n    });\n  }\n  closeModal() {\n    this.errorModal?.hide();\n  }\n  filterGroups() {\n    if (!this.searchQuery) {\n      this.filteredGroupes = this.groupes;\n    } else {\n      this.filteredGroupes = this.groupes.filter(groupe => groupe.nomGroupe.toLowerCase().includes(this.searchQuery.toLowerCase()));\n    }\n  }\n  addGroupe() {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.newGroupe.ressources = this.selectedRessources;\n      this.groupeService.addGroupe(this.newGroupe).subscribe(() => {\n        this.newGroupe = {\n          nomGroupe: '',\n          ressources: []\n        };\n        this.selectedRessources = [];\n        this.loadGroupes();\n      }, err => {\n        console.error('Error adding group', err);\n      });\n    }\n  }\n  setEditGroupe(groupe) {\n    this.editGroupe = {\n      ...groupe\n    };\n  }\n  updateGroupe() {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(() => {\n        this.editGroupe = null;\n        this.loadGroupes();\n      }, err => {\n        console.error('Error updating group', err);\n      });\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n  deleteGroupe(id, event) {\n    event.preventDefault();\n    this.groupeService.deleteGroupe(id).subscribe({\n      next: () => {\n        console.log('Group deleted successfully');\n        this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n        this.filterGroups();\n      },\n      error: err => {\n        if (err.error && err.error.message && err.error.message.includes('users')) {\n          this.modalMessage = '❌ Vous ne pouvez pas supprimer ce groupe car il est lié à des utilisateurs.';\n        } else {\n          this.modalMessage = 'Vous ne pouvez pas supprimer ce groupe car il est lié à des utilisateurs';\n        }\n        const messageElement = document.getElementById('deleteModalMessage');\n        if (messageElement) {\n          messageElement.innerText = this.modalMessage;\n        }\n        this.errorModal?.show();\n      }\n    });\n  }\n  onCheckboxChange(event, ressourceId) {\n    if (event.target.checked) {\n      if (!this.selectedRessources.includes(ressourceId)) {\n        this.selectedRessources.push(ressourceId);\n      }\n    } else {\n      this.selectedRessources = this.selectedRessources.filter(id => id !== ressourceId);\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token'));\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function GroupsComponent_Factory(t) {\n      return new (t || GroupsComponent)(i0.ɵɵdirectiveInject(i1.GroupeService), i0.ɵɵdirectiveInject(i2.RessourceService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.AuthenticationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupsComponent,\n      selectors: [[\"app-groups\"]],\n      decls: 95,\n      vars: 11,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"class\", \"nav-link\", \"href\", \"/adminDash\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/users\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/groups\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/transactions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actionnaires\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/port\", 4, \"ngIf\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"text-center\", \"mt-4\", \"mb-4\"], [1, \"d-flex\", \"justify-content-end\", \"mb-2\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#addGroupeModal\", 1, \"btn\", \"btn-custom\"], [1, \"material-icons\", \"align-middle\", 2, \"font-size\", \"20px\"], [1, \"table-responsive\"], [1, \"mb-3\", \"d-flex\", \"justify-content-between\"], [\"type\", \"text\", \"placeholder\", \"Rechercher\", \"aria-label\", \"Search\", 1, \"form-control\", \"w-50\", \"search-bar\", 3, \"ngModel\", \"ngModelChange\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"addGroupeModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"addGroupeModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [3, \"ngSubmit\"], [1, \"modal-header\"], [\"id\", \"addGroupeModalLabel\", 1, \"modal-title\"], [1, \"modal-body\"], [1, \"form-group\"], [\"for\", \"nomGroupe\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [\"id\", \"deleteErrorModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"deleteErrorModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-dialog-centered\"], [1, \"modal-content\", \"border-danger\"], [1, \"modal-header\", \"bg-danger\", \"text-white\"], [\"id\", \"deleteErrorModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Fermer\", 1, \"btn-close\"], [\"id\", \"deleteModalMessage\", 1, \"modal-body\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [\"href\", \"/adminDash\", 1, \"nav-link\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"book\"], [1, \"edit\", 3, \"routerLink\"], [\"data-toggle\", \"tooltip\", \"title\", \"Modifier\", 1, \"material-icons\", \"edit-icon\"], [1, \"delete\", 3, \"click\"], [\"data-toggle\", \"tooltip\", \"title\", \"Supprimer\", 1, \"material-icons\", \"delete-icon\"], [1, \"form-check\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"checked\", \"change\"], [1, \"form-check-label\"], [1, \"text-muted\"]],\n      template: function GroupsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"link\", 6)(8, \"link\", 7)(9, \"link\", 8)(10, \"link\", 9);\n          i0.ɵɵelementStart(11, \"title\");\n          i0.ɵɵtext(12, \"Gestion des Groupes\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"body\")(14, \"nav\", 10)(15, \"a\", 11);\n          i0.ɵɵelement(16, \"img\", 12);\n          i0.ɵɵtext(17, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"ul\", 13)(19, \"li\", 14)(20, \"a\", 15);\n          i0.ɵɵlistener(\"click\", function GroupsComponent_Template_a_click_20_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(21, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"nav\", 18)(25, \"div\", 19)(26, \"ul\", 20)(27, \"li\", 21);\n          i0.ɵɵtemplate(28, GroupsComponent_a_28_Template, 5, 0, \"a\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"li\", 21);\n          i0.ɵɵtemplate(30, GroupsComponent_a_30_Template, 3, 0, \"a\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"li\", 21);\n          i0.ɵɵtemplate(32, GroupsComponent_a_32_Template, 3, 0, \"a\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"li\", 21);\n          i0.ɵɵtemplate(34, GroupsComponent_a_34_Template, 3, 0, \"a\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"li\", 21);\n          i0.ɵɵtemplate(36, GroupsComponent_a_36_Template, 3, 0, \"a\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"li\", 21);\n          i0.ɵɵtemplate(38, GroupsComponent_a_38_Template, 3, 0, \"a\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"li\", 21);\n          i0.ɵɵtemplate(40, GroupsComponent_a_40_Template, 3, 0, \"a\", 28);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"main\", 29)(42, \"h2\", 30);\n          i0.ɵɵtext(43, \"Gestion des groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 31)(45, \"button\", 32)(46, \"i\", 33);\n          i0.ɵɵtext(47, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \" Ajouter un groupe \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 34)(50, \"div\", 35)(51, \"input\", 36);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_51_listener($event) {\n            return ctx.searchQuery = $event;\n          })(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_51_listener() {\n            return ctx.filterGroups();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"table\", 37)(53, \"thead\")(54, \"tr\")(55, \"th\");\n          i0.ɵɵtext(56, \"R\\u00E9ference\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"th\");\n          i0.ɵɵtext(58, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\");\n          i0.ɵɵtext(60, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(61, \"tbody\");\n          i0.ɵɵtemplate(62, GroupsComponent_tr_62_Template, 12, 5, \"tr\", 38);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(63, \"div\", 39)(64, \"div\", 40)(65, \"div\", 41)(66, \"form\", 42);\n          i0.ɵɵlistener(\"ngSubmit\", function GroupsComponent_Template_form_ngSubmit_66_listener() {\n            return ctx.addGroupe();\n          });\n          i0.ɵɵelementStart(67, \"div\", 43)(68, \"h5\", 44);\n          i0.ɵɵtext(69, \"Ajouter un groupe\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"div\", 45)(71, \"div\", 46)(72, \"label\", 47);\n          i0.ɵɵtext(73, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"input\", 48);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_74_listener($event) {\n            return ctx.newGroupe.nomGroupe = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 46)(76, \"label\");\n          i0.ɵɵtext(77, \"Ressources associ\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(78, GroupsComponent_div_78_Template, 7, 3, \"div\", 38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"div\", 49)(80, \"button\", 50);\n          i0.ɵɵtext(81, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"button\", 51);\n          i0.ɵɵtext(83, \"Ajouter\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(84, \"div\", 52)(85, \"div\", 53)(86, \"div\", 54)(87, \"div\", 55)(88, \"h5\", 56);\n          i0.ɵɵtext(89, \"suppression\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(90, \"button\", 57);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(91, \"div\", 58);\n          i0.ɵɵelementStart(92, \"div\", 49)(93, \"button\", 59);\n          i0.ɵɵlistener(\"click\", function GroupsComponent_Template_button_click_93_listener() {\n            return ctx.closeModal();\n          });\n          i0.ɵɵtext(94, \"Fermer\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/adminDash\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/users\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/groups\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/transactions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actionnaires\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/port\"));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredGroupes);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngModel\", ctx.newGroupe.nomGroupe);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.ressources);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i3.RouterLink, i6.ɵNgNoValidate, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgControlStatusGroup, i6.RequiredValidator, i6.NgModel, i6.NgForm],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "Modal", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GroupsComponent_tr_62_Template_a_click_9_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r11", "groupe_r9", "$implicit", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "idGroupe", "deleteGroupe", "ɵɵadvance", "ɵɵtextInterpolate", "nomGroupe", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "GroupsComponent_div_78_Template_input_change_2_listener", "_r14", "ressource_r12", "ctx_r13", "onCheckboxChange", "idRessource", "ctx_r8", "selectedRessources", "includes", "ɵɵtextInterpolate1", "nomRessource", "link_path", "GroupsComponent", "constructor", "groupeService", "ressourceService", "router", "authService", "groupes", "filteredGroupes", "newGroupe", "ressources", "editGroupe", "searchQuery", "modalMessage", "ngOnInit", "loadGroupes", "loadRessources", "modalElement", "document", "getElementById", "errorModal", "ngAfterViewInit", "replace", "getAllGroupes", "subscribe", "data", "err", "console", "error", "getAllRessources", "closeModal", "hide", "filterGroups", "filter", "groupe", "toLowerCase", "addGroupe", "trim", "setEditGroupe", "updateGroupe", "id", "event", "preventDefault", "next", "log", "message", "messageElement", "innerText", "show", "ressourceId", "target", "checked", "push", "logout", "localStorage", "removeItem", "getItem", "navigate", "ɵɵdirectiveInject", "i1", "GroupeService", "i2", "RessourceService", "i3", "Router", "i4", "AuthenticationService", "selectors", "decls", "vars", "consts", "template", "GroupsComponent_Template", "rf", "ctx", "GroupsComponent_Template_a_click_20_listener", "ɵɵtemplate", "GroupsComponent_a_28_Template", "GroupsComponent_a_30_Template", "GroupsComponent_a_32_Template", "GroupsComponent_a_34_Template", "GroupsComponent_a_36_Template", "GroupsComponent_a_38_Template", "GroupsComponent_a_40_Template", "GroupsComponent_Template_input_ngModelChange_51_listener", "GroupsComponent_tr_62_Template", "GroupsComponent_Template_form_ngSubmit_66_listener", "GroupsComponent_Template_input_ngModelChange_74_listener", "GroupsComponent_div_78_Template", "GroupsComponent_Template_button_click_93_listener", "isRouteAllowed"], "sources": ["C:\\Users\\<USER>\\Desktop\\Final - Copie\\frontend\\src\\app\\groups\\groups.component.ts", "C:\\Users\\<USER>\\Desktop\\Final - Copie\\frontend\\src\\app\\groups\\groups.component.html"], "sourcesContent": ["import { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Component, OnInit, AfterViewInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\nimport { RessourceService } from '../services/ressource.service';\nimport { Ressource } from '../model/ressource.model';\nimport { Modal } from 'bootstrap';\nimport { AuthenticationService } from '../auth/authentication.service';\n\n@Component({\n  selector: 'app-groups',\n  templateUrl: './groups.component.html',\n  styleUrls: ['./groups.component.css']\n})\n\nexport class GroupsComponent implements OnInit, AfterViewInit {\n  groupes: Groupe[] = [];\n  filteredGroupes: Groupe[] = [];\n  newGroupe: Groupe = { nomGroupe: '', ressources: [] };\n  editGroupe: Groupe | null = null;\n  searchQuery: string = '';\n  private errorModal!: Modal;\n  ressources: Ressource[] = [];\n  selectedRessources: number[] = [];\n  modalMessage: string = '';\n\n  constructor(\n    private groupeService: GroupeService,\n    private ressourceService: RessourceService,\n    private router: Router,\n    public authService: AuthenticationService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadGroupes();\n    this.loadRessources();\n\n\n    const modalElement = document.getElementById('deleteErrorModal');\n  if (modalElement) {\n    this.errorModal = new Modal(modalElement);\n  }\n    \n  }\n\n  ngAfterViewInit(): void {\n    feather.replace();\n    const modalElement = document.getElementById('errorModal');\n  if (modalElement) {\n    this.errorModal = new Modal(modalElement);\n  }\n  }\n\n  loadGroupes(): void {\n    this.groupeService.getAllGroupes().subscribe(\n      (data) => {\n        this.groupes = data;\n        this.filteredGroupes = data;\n      },\n      (err) => {\n        console.error('Error loading groups', err);\n      }\n    );\n  }\n\n  loadRessources(): void {\n    this.ressourceService.getAllRessources().subscribe(\n      (data) => {\n        this.ressources = data;\n      },\n      (err) => {\n        console.error('Error loading ressources', err);\n      }\n    );\n  }\n\n  closeModal(): void {\n    this.errorModal?.hide();\n  }\n\n  filterGroups(): void {\n    if (!this.searchQuery) {\n      this.filteredGroupes = this.groupes;\n    } else {\n      this.filteredGroupes = this.groupes.filter(groupe =>\n        groupe.nomGroupe.toLowerCase().includes(this.searchQuery.toLowerCase())\n      );\n    }\n  }\n\n  addGroupe(): void {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.newGroupe.ressources = this.selectedRessources;\n\n      this.groupeService.addGroupe(this.newGroupe).subscribe(\n        () => {\n          this.newGroupe = { nomGroupe: '', ressources: [] };\n          this.selectedRessources = [];\n          this.loadGroupes();\n        },\n        (err) => {\n          console.error('Error adding group', err);\n        }\n      );\n    }\n  }\n\n  setEditGroupe(groupe: Groupe): void {\n    this.editGroupe = { ...groupe };\n  }\n\n  updateGroupe(): void {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(\n        () => {\n          this.editGroupe = null;\n          this.loadGroupes();\n        },\n        (err) => {\n          console.error('Error updating group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n\n  deleteGroupe(id: number, event: Event): void {\n    event.preventDefault();\n  \n    this.groupeService.deleteGroupe(id).subscribe({\n      next: () => {\n        console.log('Group deleted successfully');\n        this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n        this.filterGroups();\n      },\n      error: (err) => {\n        if (err.error && err.error.message && err.error.message.includes('users')) {\n          this.modalMessage = '❌ Vous ne pouvez pas supprimer ce groupe car il est lié à des utilisateurs.';\n        } else {\n          this.modalMessage = 'Vous ne pouvez pas supprimer ce groupe car il est lié à des utilisateurs';\n        }\n  \n        const messageElement = document.getElementById('deleteModalMessage');\n        if (messageElement) {\n          messageElement.innerText = this.modalMessage;\n        }\n  \n        this.errorModal?.show();\n      }\n    });\n  }\n  \n\n  onCheckboxChange(event: any, ressourceId: number): void {\n    if (event.target.checked) {\n      if (!this.selectedRessources.includes(ressourceId)) {\n        this.selectedRessources.push(ressourceId);\n      }\n    } else {\n      this.selectedRessources = this.selectedRessources.filter(id => id !== ressourceId);\n    }\n  }\n\n  logout(): void {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token'));\n    this.router.navigate(['/login']);\n  }\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n    <link href=\"./groups.component.css\" rel=\"stylesheet\">\n    <title>Gestion des Groupes</title>\n  </head>\n\n  <body>\n    <!-- Navbar -->\n    <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/adminDash\" *ngIf=\"authService.isRouteAllowed('/adminDash')\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\" *ngIf=\"authService.isRouteAllowed('/users')\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\" *ngIf=\"authService.isRouteAllowed('/groups')\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            \n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/transactions\" *ngIf=\"authService.isRouteAllowed('/transactions')\">\n                <span data-feather=\"dollar-sign\"></span>\n                Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/actions\" *ngIf=\"authService.isRouteAllowed('/actions')\">\n                <span data-feather=\"trending-up\"></span>\n               Gestion des actions\n              </a>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\" *ngIf=\"authService.isRouteAllowed('/actionnaires')\">\n                  <span data-feather=\"users\"></span>\n                 Gestion des actionnaires\n                </a>\n                     <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/port\" *ngIf=\"authService.isRouteAllowed('/port')\">\n                <span data-feather=\"book\"></span>\n               Gestion des Portefeuilles\n              </a>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <h2 class=\"text-center mt-4 mb-4\">Gestion des groupes</h2>\n\n       \n\n          <div class=\"d-flex justify-content-end mb-2\">\n            <button type=\"button\" class=\"btn btn-custom\" data-toggle=\"modal\" data-target=\"#addGroupeModal\">\n              <i class=\"material-icons align-middle\" style=\"font-size: 20px;\">add</i>\n              Ajouter un groupe\n            </button>\n          </div>\n          \n          \n\n          <!-- Table -->\n          <div class=\"table-responsive\">\n             <!-- Search Bar and Add Button -->\n             <div class=\"mb-3 d-flex justify-content-between\">\n              <input class=\"form-control w-50 search-bar\" \n                     type=\"text\" \n                     placeholder=\"Rechercher\" \n                     aria-label=\"Search\" \n                     [(ngModel)]=\"searchQuery\" \n                     (ngModelChange)=\"filterGroups()\">\n            </div>\n            <table class=\"table table-hover\">\n              <thead>\n                <tr>\n                  <th>Réference</th>\n                  <th>Nom</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let groupe of filteredGroupes\">\n                  <td>{{ groupe.idGroupe || 'N/A' }}</td>\n                  <td>{{ groupe.nomGroupe }}</td>\n                  <td>\n                    <a [routerLink]=\"['/edit-groupe', groupe.idGroupe]\" class=\"edit\">\n                      <i class=\"material-icons edit-icon\" data-toggle=\"tooltip\" title=\"Modifier\">&#xE254;</i>\n                    </a>\n                    <a class=\"delete\" (click)=\"groupe.idGroupe ? deleteGroupe(groupe.idGroupe, $event) : null\">\n                      <i class=\"material-icons delete-icon\" data-toggle=\"tooltip\" title=\"Supprimer\">&#xE872;</i>\n                    </a>\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <!-- Modal: Ajouter un groupe -->\n    <div class=\"modal fade\" id=\"addGroupeModal\" tabindex=\"-1\" aria-labelledby=\"addGroupeModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog\">\n        <div class=\"modal-content\">\n          <form (ngSubmit)=\"addGroupe()\">\n            <div class=\"modal-header\">\n              <h5 class=\"modal-title\" id=\"addGroupeModalLabel\">Ajouter un groupe</h5>\n            </div>\n            <div class=\"modal-body\">\n              <div class=\"form-group\">\n                <label for=\"nomGroupe\">Nom du groupe</label>\n                <input type=\"text\" id=\"nomGroupe\" [(ngModel)]=\"newGroupe.nomGroupe\" name=\"nomGroupe\" class=\"form-control\" required>\n              </div>\n\n              <!-- Ressource Checkboxes -->\n              <div class=\"form-group\">\n              <label>Ressources associées</label>\n               <div *ngFor=\"let ressource of ressources\">\n                   <div class=\"form-check\">\n                   <input type=\"checkbox\"\n                  class=\"form-check-input\"\n                   [checked]=\"selectedRessources.includes(ressource.idRessource)\"\n                     (change)=\"onCheckboxChange($event, ressource.idRessource)\">\n                   <label class=\"form-check-label\">\n              {{ ressource.nomRessource }} <small class=\"text-muted\">({{ ressource.link_path }})</small>\n                 </label>\n                  </div>\n                    </div>\n                    </div>\n\n\n            </div>\n            <div class=\"modal-footer\">\n              <button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\">Annuler</button>\n              <button type=\"submit\" class=\"btn btn-primary\">Ajouter</button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n\n<!-- Bootstrap Modal for Delete Error -->\n<div class=\"modal fade\" id=\"deleteErrorModal\" tabindex=\"-1\" aria-labelledby=\"deleteErrorModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-dialog-centered\">\n    <div class=\"modal-content border-danger\">\n      <div class=\"modal-header bg-danger text-white\">\n        <h5 class=\"modal-title\" id=\"deleteErrorModalLabel\">suppression</h5>\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\n      </div>\n      <div class=\"modal-body\" id=\"deleteModalMessage\">\n        <!-- Error message will be dynamically injected -->\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-danger\" (click)=\"closeModal()\">Fermer</button>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n    <!-- Scripts -->\n    <script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/@popperjs/core@2.10.2/dist/umd/popper.min.js\"></script>\n    <script src=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/js/bootstrap.min.js\"></script>\n  </body>\n</html>\n"], "mappings": "AAIA,OAAO,KAAKA,OAAO,MAAM,eAAe;AAGxC,SAASC,KAAK,QAAQ,WAAW;;;;;;;;;;IC6BjBC,EAAA,CAAAC,cAAA,YAAuF;IACrFD,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIlDJ,EAAA,CAAAC,cAAA,YAA+E;IAC7ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAiF;IAC/ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAINJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAwC;IACxCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAmF;IACjFD,EAAA,CAAAE,SAAA,eAAwC;IACzCF,EAAA,CAAAG,MAAA,4BACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAEFJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAkC;IACnCF,EAAA,CAAAG,MAAA,iCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,YAA6E;IAC3ED,EAAA,CAAAE,SAAA,eAAiC;IAClCF,EAAA,CAAAG,MAAA,kCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;;;;IAuCFJ,EAAA,CAAAC,cAAA,SAA2C;IACrCD,EAAA,CAAAG,MAAA,GAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,SAAI;IAE2ED,EAAA,CAAAG,MAAA,aAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEzFJ,EAAA,CAAAC,cAAA,YAA2F;IAAzED,EAAA,CAAAK,UAAA,mBAAAC,kDAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAR,EAAA,CAAAS,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAASd,EAAA,CAAAe,WAAA,CAAAJ,SAAA,CAAAK,QAAA,GAAkBH,OAAA,CAAAI,YAAA,CAAAN,SAAA,CAAAK,QAAA,EAAAT,MAAA,CAAqC,GAAG,IAAI;IAAA,EAAC;IACxFP,EAAA,CAAAC,cAAA,aAA8E;IAAAD,EAAA,CAAAG,MAAA,cAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAP1FJ,EAAA,CAAAkB,SAAA,GAA8B;IAA9BlB,EAAA,CAAAmB,iBAAA,CAAAR,SAAA,CAAAK,QAAA,UAA8B;IAC9BhB,EAAA,CAAAkB,SAAA,GAAsB;IAAtBlB,EAAA,CAAAmB,iBAAA,CAAAR,SAAA,CAAAS,SAAA,CAAsB;IAErBpB,EAAA,CAAAkB,SAAA,GAAgD;IAAhDlB,EAAA,CAAAqB,UAAA,eAAArB,EAAA,CAAAsB,eAAA,IAAAC,GAAA,EAAAZ,SAAA,CAAAK,QAAA,EAAgD;;;;;;IAgCxDhB,EAAA,CAAAC,cAAA,UAA0C;IAKpCD,EAAA,CAAAK,UAAA,oBAAAmB,wDAAAjB,MAAA;MAAA,MAAAC,WAAA,GAAAR,EAAA,CAAAS,aAAA,CAAAgB,IAAA;MAAA,MAAAC,aAAA,GAAAlB,WAAA,CAAAI,SAAA;MAAA,MAAAe,OAAA,GAAA3B,EAAA,CAAAc,aAAA;MAAA,OAAUd,EAAA,CAAAe,WAAA,CAAAY,OAAA,CAAAC,gBAAA,CAAArB,MAAA,EAAAmB,aAAA,CAAAG,WAAA,CAA+C;IAAA,EAAC;IAH5D7B,EAAA,CAAAI,YAAA,EAG6D;IAC7DJ,EAAA,CAAAC,cAAA,gBAAgC;IACrCD,EAAA,CAAAG,MAAA,GAA6B;IAAAH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;;IAHrFJ,EAAA,CAAAkB,SAAA,GAA8D;IAA9DlB,EAAA,CAAAqB,UAAA,YAAAS,MAAA,CAAAC,kBAAA,CAAAC,QAAA,CAAAN,aAAA,CAAAG,WAAA,EAA8D;IAGnE7B,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAiC,kBAAA,MAAAP,aAAA,CAAAQ,YAAA,MAA6B;IAA0BlC,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAAiC,kBAAA,MAAAP,aAAA,CAAAS,SAAA,MAA2B;;;;AD5IhG,OAAM,MAAOC,eAAe;EAW1BC,YACUC,aAA4B,EAC5BC,gBAAkC,EAClCC,MAAc,EACfC,WAAkC;IAHjC,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IAdpB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,SAAS,GAAW;MAAExB,SAAS,EAAE,EAAE;MAAEyB,UAAU,EAAE;IAAE,CAAE;IACrD,KAAAC,UAAU,GAAkB,IAAI;IAChC,KAAAC,WAAW,GAAW,EAAE;IAExB,KAAAF,UAAU,GAAgB,EAAE;IAC5B,KAAAd,kBAAkB,GAAa,EAAE;IACjC,KAAAiB,YAAY,GAAW,EAAE;EAOtB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,cAAc,EAAE;IAGrB,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,kBAAkB,CAAC;IAClE,IAAIF,YAAY,EAAE;MAChB,IAAI,CAACG,UAAU,GAAG,IAAIxD,KAAK,CAACqD,YAAY,CAAC;;EAG3C;EAEAI,eAAeA,CAAA;IACb1D,OAAO,CAAC2D,OAAO,EAAE;IACjB,MAAML,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,YAAY,CAAC;IAC5D,IAAIF,YAAY,EAAE;MAChB,IAAI,CAACG,UAAU,GAAG,IAAIxD,KAAK,CAACqD,YAAY,CAAC;;EAE3C;EAEAF,WAAWA,CAAA;IACT,IAAI,CAACZ,aAAa,CAACoB,aAAa,EAAE,CAACC,SAAS,CACzCC,IAAI,IAAI;MACP,IAAI,CAAClB,OAAO,GAAGkB,IAAI;MACnB,IAAI,CAACjB,eAAe,GAAGiB,IAAI;IAC7B,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;IAC5C,CAAC,CACF;EACH;EAEAV,cAAcA,CAAA;IACZ,IAAI,CAACZ,gBAAgB,CAACyB,gBAAgB,EAAE,CAACL,SAAS,CAC/CC,IAAI,IAAI;MACP,IAAI,CAACf,UAAU,GAAGe,IAAI;IACxB,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;IAChD,CAAC,CACF;EACH;EAEAI,UAAUA,CAAA;IACR,IAAI,CAACV,UAAU,EAAEW,IAAI,EAAE;EACzB;EAEAC,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACpB,WAAW,EAAE;MACrB,IAAI,CAACJ,eAAe,GAAG,IAAI,CAACD,OAAO;KACpC,MAAM;MACL,IAAI,CAACC,eAAe,GAAG,IAAI,CAACD,OAAO,CAAC0B,MAAM,CAACC,MAAM,IAC/CA,MAAM,CAACjD,SAAS,CAACkD,WAAW,EAAE,CAACtC,QAAQ,CAAC,IAAI,CAACe,WAAW,CAACuB,WAAW,EAAE,CAAC,CACxE;;EAEL;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAC3B,SAAS,CAACxB,SAAS,CAACoD,IAAI,EAAE,EAAE;MACnC,IAAI,CAAC5B,SAAS,CAACC,UAAU,GAAG,IAAI,CAACd,kBAAkB;MAEnD,IAAI,CAACO,aAAa,CAACiC,SAAS,CAAC,IAAI,CAAC3B,SAAS,CAAC,CAACe,SAAS,CACpD,MAAK;QACH,IAAI,CAACf,SAAS,GAAG;UAAExB,SAAS,EAAE,EAAE;UAAEyB,UAAU,EAAE;QAAE,CAAE;QAClD,IAAI,CAACd,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACmB,WAAW,EAAE;MACpB,CAAC,EACAW,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;MAC1C,CAAC,CACF;;EAEL;EAEAY,aAAaA,CAACJ,MAAc;IAC1B,IAAI,CAACvB,UAAU,GAAG;MAAE,GAAGuB;IAAM,CAAE;EACjC;EAEAK,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC5B,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC9B,QAAQ,EAAE;MAC/C,IAAI,CAACsB,aAAa,CAACoC,YAAY,CAAC,IAAI,CAAC5B,UAAU,CAAC9B,QAAQ,EAAE,IAAI,CAAC8B,UAAU,CAAC,CAACa,SAAS,CAClF,MAAK;QACH,IAAI,CAACb,UAAU,GAAG,IAAI;QACtB,IAAI,CAACI,WAAW,EAAE;MACpB,CAAC,EACAW,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;;EAEtD;EAEA9C,YAAYA,CAAC0D,EAAU,EAAEC,KAAY;IACnCA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI,CAACvC,aAAa,CAACrB,YAAY,CAAC0D,EAAE,CAAC,CAAChB,SAAS,CAAC;MAC5CmB,IAAI,EAAEA,CAAA,KAAK;QACThB,OAAO,CAACiB,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAACrC,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC0B,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACrD,QAAQ,KAAK2D,EAAE,CAAC;QACpE,IAAI,CAACR,YAAY,EAAE;MACrB,CAAC;MACDJ,KAAK,EAAGF,GAAG,IAAI;QACb,IAAIA,GAAG,CAACE,KAAK,IAAIF,GAAG,CAACE,KAAK,CAACiB,OAAO,IAAInB,GAAG,CAACE,KAAK,CAACiB,OAAO,CAAChD,QAAQ,CAAC,OAAO,CAAC,EAAE;UACzE,IAAI,CAACgB,YAAY,GAAG,6EAA6E;SAClG,MAAM;UACL,IAAI,CAACA,YAAY,GAAG,0EAA0E;;QAGhG,MAAMiC,cAAc,GAAG5B,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC;QACpE,IAAI2B,cAAc,EAAE;UAClBA,cAAc,CAACC,SAAS,GAAG,IAAI,CAAClC,YAAY;;QAG9C,IAAI,CAACO,UAAU,EAAE4B,IAAI,EAAE;MACzB;KACD,CAAC;EACJ;EAGAvD,gBAAgBA,CAACgD,KAAU,EAAEQ,WAAmB;IAC9C,IAAIR,KAAK,CAACS,MAAM,CAACC,OAAO,EAAE;MACxB,IAAI,CAAC,IAAI,CAACvD,kBAAkB,CAACC,QAAQ,CAACoD,WAAW,CAAC,EAAE;QAClD,IAAI,CAACrD,kBAAkB,CAACwD,IAAI,CAACH,WAAW,CAAC;;KAE5C,MAAM;MACL,IAAI,CAACrD,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACqC,MAAM,CAACO,EAAE,IAAIA,EAAE,KAAKS,WAAW,CAAC;;EAEtF;EAEAI,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC5B,OAAO,CAACiB,GAAG,CAACU,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACnD,MAAM,CAACoD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBA3JWxD,eAAe,EAAApC,EAAA,CAAA6F,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA/F,EAAA,CAAA6F,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAjG,EAAA,CAAA6F,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAnG,EAAA,CAAA6F,iBAAA,CAAAO,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAAfjE,eAAe;MAAAkE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCf5B5G,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAE,SAAA,cAAsB;UAWtBF,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAG,MAAA,2BAAmB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAGpCJ,EAAA,CAAAC,cAAA,YAAM;UAIAD,EAAA,CAAAE,SAAA,eAAwE;UACxEF,EAAA,CAAAG,MAAA,aACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAK,UAAA,mBAAAyG,6CAAA;YAAA,OAASD,GAAA,CAAArB,MAAA,EAAQ;UAAA,EAAC;UAACxF,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAIzDJ,EAAA,CAAAC,cAAA,eAA6B;UAMjBD,EAAA,CAAA+G,UAAA,KAAAC,6BAAA,gBAGI;UACNhH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA+G,UAAA,KAAAE,6BAAA,gBAGI;UACNjH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA+G,UAAA,KAAAG,6BAAA,gBAGI;UACNlH,EAAA,CAAAI,YAAA,EAAK;UAEPJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA+G,UAAA,KAAAI,6BAAA,gBAGI;UACNnH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA+G,UAAA,KAAAK,6BAAA,gBAGI;UAJNpH,EAAA,CAAAI,YAAA,EAAqB;UAKnBJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA+G,UAAA,KAAAM,6BAAA,gBAGI;UAJNrH,EAAA,CAAAI,YAAA,EAAqB;UAKdJ,EAAA,CAAAC,cAAA,cAAqB;UAC5BD,EAAA,CAAA+G,UAAA,KAAAO,6BAAA,gBAGI;UAJGtH,EAAA,CAAAI,YAAA,EAAqB;UASlCJ,EAAA,CAAAC,cAAA,gBAAkE;UAC9BD,EAAA,CAAAG,MAAA,2BAAmB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAI1DJ,EAAA,CAAAC,cAAA,eAA6C;UAEuBD,EAAA,CAAAG,MAAA,WAAG;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACvEJ,EAAA,CAAAG,MAAA,2BACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAMXJ,EAAA,CAAAC,cAAA,eAA8B;UAOnBD,EAAA,CAAAK,UAAA,2BAAAkH,yDAAAhH,MAAA;YAAA,OAAAsG,GAAA,CAAA9D,WAAA,GAAAxC,MAAA;UAAA,EAAyB,2BAAAgH,yDAAA;YAAA,OACRV,GAAA,CAAA1C,YAAA,EAAc;UAAA,EADN;UAJhCnE,EAAA,CAAAI,YAAA,EAKwC;UAE1CJ,EAAA,CAAAC,cAAA,iBAAiC;UAGvBD,EAAA,CAAAG,MAAA,sBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,WAAG;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACZJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGpBJ,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA+G,UAAA,KAAAS,8BAAA,kBAWK;UACPxH,EAAA,CAAAI,YAAA,EAAQ;UAQlBJ,EAAA,CAAAC,cAAA,eAAmH;UAGvGD,EAAA,CAAAK,UAAA,sBAAAoH,mDAAA;YAAA,OAAYZ,GAAA,CAAAtC,SAAA,EAAW;UAAA,EAAC;UAC5BvE,EAAA,CAAAC,cAAA,eAA0B;UACyBD,EAAA,CAAAG,MAAA,yBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEzEJ,EAAA,CAAAC,cAAA,eAAwB;UAEGD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC5CJ,EAAA,CAAAC,cAAA,iBAAmH;UAAjFD,EAAA,CAAAK,UAAA,2BAAAqH,yDAAAnH,MAAA;YAAA,OAAAsG,GAAA,CAAAjE,SAAA,CAAAxB,SAAA,GAAAb,MAAA;UAAA,EAAiC;UAAnEP,EAAA,CAAAI,YAAA,EAAmH;UAIrHJ,EAAA,CAAAC,cAAA,eAAwB;UACjBD,EAAA,CAAAG,MAAA,iCAAoB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAClCJ,EAAA,CAAA+G,UAAA,KAAAY,+BAAA,kBAUW;UACN3H,EAAA,CAAAI,YAAA,EAAM;UAIdJ,EAAA,CAAAC,cAAA,eAA0B;UAC6CD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACrFJ,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAQ5EJ,EAAA,CAAAC,cAAA,eAAuH;UAI5DD,EAAA,CAAAG,MAAA,mBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnEJ,EAAA,CAAAE,SAAA,kBAA6F;UAC/FF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,SAAA,eAEM;UACNF,EAAA,CAAAC,cAAA,eAA0B;UACqBD,EAAA,CAAAK,UAAA,mBAAAuH,kDAAA;YAAA,OAASf,GAAA,CAAA5C,UAAA,EAAY;UAAA,EAAC;UAACjE,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAS;;;UArJpCJ,EAAA,CAAAkB,SAAA,IAA8C;UAA9ClB,EAAA,CAAAqB,UAAA,SAAAwF,GAAA,CAAApE,WAAA,CAAAoF,cAAA,eAA8C;UAMlD7H,EAAA,CAAAkB,SAAA,GAA0C;UAA1ClB,EAAA,CAAAqB,UAAA,SAAAwF,GAAA,CAAApE,WAAA,CAAAoF,cAAA,WAA0C;UAMzC7H,EAAA,CAAAkB,SAAA,GAA2C;UAA3ClB,EAAA,CAAAqB,UAAA,SAAAwF,GAAA,CAAApE,WAAA,CAAAoF,cAAA,YAA2C;UAOvC7H,EAAA,CAAAkB,SAAA,GAAiD;UAAjDlB,EAAA,CAAAqB,UAAA,SAAAwF,GAAA,CAAApE,WAAA,CAAAoF,cAAA,kBAAiD;UAMtD7H,EAAA,CAAAkB,SAAA,GAA4C;UAA5ClB,EAAA,CAAAqB,UAAA,SAAAwF,GAAA,CAAApE,WAAA,CAAAoF,cAAA,aAA4C;UAKrC7H,EAAA,CAAAkB,SAAA,GAAiD;UAAjDlB,EAAA,CAAAqB,UAAA,SAAAwF,GAAA,CAAApE,WAAA,CAAAoF,cAAA,kBAAiD;UAK3D7H,EAAA,CAAAkB,SAAA,GAAyC;UAAzClB,EAAA,CAAAqB,UAAA,SAAAwF,GAAA,CAAApE,WAAA,CAAAoF,cAAA,UAAyC;UA8BpE7H,EAAA,CAAAkB,SAAA,IAAyB;UAAzBlB,EAAA,CAAAqB,UAAA,YAAAwF,GAAA,CAAA9D,WAAA,CAAyB;UAYP/C,EAAA,CAAAkB,SAAA,IAAkB;UAAlBlB,EAAA,CAAAqB,UAAA,YAAAwF,GAAA,CAAAlE,eAAA,CAAkB;UA8BP3C,EAAA,CAAAkB,SAAA,IAAiC;UAAjClB,EAAA,CAAAqB,UAAA,YAAAwF,GAAA,CAAAjE,SAAA,CAAAxB,SAAA,CAAiC;UAMzCpB,EAAA,CAAAkB,SAAA,GAAa;UAAblB,EAAA,CAAAqB,UAAA,YAAAwF,GAAA,CAAAhE,UAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}