package com.Aziz.Client.Controllers;


import com.Aziz.Client.Entity.Action;
import com.Aziz.Client.Entity.Actionnaire;
import com.Aziz.Client.Entity.Portefeuille;
import com.Aziz.Client.Repositories.ActionRepository;
import com.Aziz.Client.Services.ActionService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("GRA/Client/Action")
@RequiredArgsConstructor
public class ActionController {

    private final ActionService actionService;

@Autowired
private final ActionRepository ActionRepository;


    @GetMapping("/All_Actions")
    public ResponseEntity<List<Action>> getAllActions() {
        return ResponseEntity.ok(ActionRepository.findAll());
    }

    @GetMapping("/{idAction}")
    public ResponseEntity<Action> getActionById(@PathVariable Long idAction) {
        return ActionRepository.findById(idAction)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/isin/{isin}")
    public ResponseEntity<Action> getActionByISIN(@PathVariable String isin) {
        Action action = ActionRepository.findByIsinAction(isin);
        if (action != null) {
            return ResponseEntity.ok(action);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping("/create")
    public ResponseEntity<String> createAction(@RequestBody Action action) {
        try {
            actionService.saveAction(action);
            return ResponseEntity.ok("Action saved successfully");
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("update/{idAction}")
    public ResponseEntity<Action> updateAction(@PathVariable Long id, @RequestBody Action updatedAction) {
        try {
            return ResponseEntity.ok(actionService.updateAction(id, updatedAction));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{idAction}")
    public ResponseEntity<String> deleteAction(@PathVariable Long id) {
        actionService.deleteAction(id);
        return ResponseEntity.ok("Action deleted successfully");
    }


    @GetMapping("Action_/{idAction}/Portefeuiles")
    public ResponseEntity<List<Portefeuille>> getPortefeuillesByActionId(@PathVariable Long idAction) {
        try {
            List<Portefeuille> portefeuilles = actionService.getPortefeuillesByActionId(idAction);
            return ResponseEntity.ok(portefeuilles);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }


    @GetMapping("Action_/{idAction}/Actionnaires")
    public ResponseEntity<List<Actionnaire>> getActionnairesByAction(@PathVariable Long id) {
        try {
            List<Actionnaire> actionnaires = actionService.getActionnairesByActionId(id);
            return ResponseEntity.ok(actionnaires);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

}
