{"ast": null, "code": "import * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js';\nimport { Modal } from 'bootstrap';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../auth/authentication.service\";\nimport * as i4 from \"../services/transaction.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"myChart\"];\nconst _c1 = [\"barChart\"];\nconst _c2 = [\"feedbackModal\"];\nfunction AdminDashComponent_a_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 68);\n    i0.ɵɵelement(1, \"span\", 69);\n    i0.ɵɵtext(2, \" Dashboard \");\n    i0.ɵɵelementStart(3, \"span\", 70);\n    i0.ɵɵtext(4, \"(current)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminDashComponent_a_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 71);\n    i0.ɵɵelement(1, \"span\", 72);\n    i0.ɵɵtext(2, \" Gestion des utilisateurs \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 73);\n    i0.ɵɵelement(1, \"span\", 74);\n    i0.ɵɵtext(2, \" Gestion des groupes \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 75);\n    i0.ɵɵelement(1, \"span\", 76);\n    i0.ɵɵtext(2, \" Gestion des transactions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 77);\n    i0.ɵɵelement(1, \"span\", 78);\n    i0.ɵɵtext(2, \" Gestion des actions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 79);\n    i0.ɵɵelement(1, \"span\", 80);\n    i0.ɵɵtext(2, \" Gestion des actionnaires \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 81);\n    i0.ɵɵelement(1, \"span\", 82);\n    i0.ɵɵtext(2, \" Gestion des Portefeuilles \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = \"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, 0.25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: 'Poppins', sans-serif;\\n  margin: 0;\\n  padding: 0;\\n  min-height: 100vh;\\n  background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n  background-size: 400% 400%;\\n  animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n  color: #fff;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n  0% {\\n      background-position: 0% 50%;\\n  }\\n\\n  50% {\\n      background-position: 100% 50%;\\n  }\\n\\n  100% {\\n      background-position: 0% 50%;\\n  }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  margin-top: 20px;\\n}\\n\\n\\n\\n\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n  background-color: #218838;\\n  transform: translateY(-2px);\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 1.5rem;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n  background-color: #f1f1f1;\\n  padding: 1rem;\\n  border-top: 1px solid #ddd;\\n  display: flex;\\n  justify-content: center; \\n\\n  gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  font-size: 1rem;\\n  border-radius: 0.3rem;\\n  transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n  transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column; \\n\\n  align-items: flex-start; \\n\\n}\\n\\n\\n\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n  background: rgba(20, 33, 59, 0.9); \\n\\n  color: #fff;\\n  min-height: 100vh;\\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #ccc;\\n  transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n  background-color: #000 !important;\\n  color: #fff;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n  position: relative;\\n  padding: 0.5rem 1rem;\\n  transition: color 0.3s ease;\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  width: 0;\\n  background: #ff4c60;\\n  transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 30px;\\n  object-fit: cover;\\n  border-radius: 50%; \\n\\n  margin-right: 8px;\\n}\\n\\n\\n\\n.titre-colore[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  font-weight: bold;\\n  text-align: center;\\n  margin-bottom: 2rem;\\n  font-size: 1.8rem;\\n}\\n\\n\\n\\n.card-bleu[_ngcontent-%COMP%] {\\n  border-left: 5px solid #007bff;\\n  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.05));\\n}\\n\\n.card-vert[_ngcontent-%COMP%] {\\n  border-left: 5px solid #28a745;\\n  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));\\n}\\n\\n.card-cyan[_ngcontent-%COMP%] {\\n  border-left: 5px solid #17a2b8;\\n  background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));\\n}\\n\\n.card-orange[_ngcontent-%COMP%] {\\n  border-left: 5px solid #fd7e14;\\n  background: linear-gradient(135deg, rgba(253, 126, 20, 0.1), rgba(253, 126, 20, 0.05));\\n}\\n\\n\\n\\n.icone-bleu[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  text-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\\n}\\n\\n.icone-vert[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  text-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);\\n}\\n\\n.icone-cyan[_ngcontent-%COMP%] {\\n  color: #17a2b8;\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  text-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);\\n}\\n\\n.icone-orange[_ngcontent-%COMP%] {\\n  color: #fd7e14;\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  text-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);\\n}\\n\\n\\n\\n.nom-donnee-bleu[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  font-weight: bold;\\n  text-shadow: 0 1px 3px rgba(0, 123, 255, 0.3);\\n  margin-bottom: 1rem;\\n}\\n\\n.nom-donnee-vert[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-weight: bold;\\n  text-shadow: 0 1px 3px rgba(40, 167, 69, 0.3);\\n  margin-bottom: 1rem;\\n}\\n\\n.nom-donnee-cyan[_ngcontent-%COMP%] {\\n  color: #17a2b8;\\n  font-weight: bold;\\n  text-shadow: 0 1px 3px rgba(23, 162, 184, 0.3);\\n  margin-bottom: 1rem;\\n}\\n\\n.nom-donnee-orange[_ngcontent-%COMP%] {\\n  color: #fd7e14;\\n  font-weight: bold;\\n  text-shadow: 0 1px 3px rgba(253, 126, 20, 0.3);\\n  margin-bottom: 1rem;\\n}\\n\\n\\n\\n.valeur-bleu[_ngcontent-%COMP%] {\\n  color: #0056b3;\\n  font-weight: bold;\\n  font-size: 2.5rem;\\n  text-shadow: 0 2px 6px rgba(0, 86, 179, 0.4);\\n  margin: 0;\\n}\\n\\n.valeur-vert[_ngcontent-%COMP%] {\\n  color: #1e7e34;\\n  font-weight: bold;\\n  font-size: 2.5rem;\\n  text-shadow: 0 2px 6px rgba(30, 126, 52, 0.4);\\n  margin: 0;\\n}\\n\\n.valeur-cyan[_ngcontent-%COMP%] {\\n  color: #138496;\\n  font-weight: bold;\\n  font-size: 2.5rem;\\n  text-shadow: 0 2px 6px rgba(19, 132, 150, 0.4);\\n  margin: 0;\\n}\\n\\n.valeur-orange[_ngcontent-%COMP%] {\\n  color: #e0a800;\\n  font-weight: bold;\\n  font-size: 2.5rem;\\n  text-shadow: 0 2px 6px rgba(224, 168, 0, 0.4);\\n  margin: 0;\\n}\\n\\n\\n\\n.card-bleu[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);\\n}\\n\\n.card-vert[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);\\n}\\n\\n.card-cyan[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 25px rgba(23, 162, 184, 0.3);\\n}\\n\\n.card-orange[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 10px 25px rgba(253, 126, 20, 0.3);\\n}\\n\\n\\n\\n.titre-graphique-courbe[_ngcontent-%COMP%] {\\n  color: #000000 !important;\\n  font-weight: bold;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  background: linear-gradient(135deg, #333333, #000000);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  font-size: 1.1rem;\\n}\\n\\n.titre-graphique-barres[_ngcontent-%COMP%] {\\n  color: #ff6b35 !important;\\n  font-weight: bold;\\n  text-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);\\n  background: linear-gradient(135deg, #ff6b35, #f7931e);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  font-size: 1.1rem;\\n}\\n\\n\\n\\n.titre-graphique-courbe[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  transition: transform 0.3s ease;\\n}\\n\\n.titre-graphique-barres[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  transition: transform 0.3s ease;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .titre-colore[_ngcontent-%COMP%] {\\n    font-size: 1.4rem;\\n  }\\n\\n  .valeur-bleu[_ngcontent-%COMP%], .valeur-vert[_ngcontent-%COMP%], .valeur-cyan[_ngcontent-%COMP%], .valeur-orange[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .icone-bleu[_ngcontent-%COMP%], .icone-vert[_ngcontent-%COMP%], .icone-cyan[_ngcontent-%COMP%], .icone-orange[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .titre-graphique-courbe[_ngcontent-%COMP%], .titre-graphique-barres[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class AdminDashComponent {\n  constructor(responsableService, cdr, router, authService, transactionService) {\n    this.responsableService = responsableService;\n    this.cdr = cdr;\n    this.router = router;\n    this.authService = authService;\n    this.transactionService = transactionService;\n    this.responsables = [];\n    this.adminName = '';\n    this.transactions = [];\n    this.modalMessage = '';\n    // Variables simples pour les données des transactions (méthode étudiante)\n    this.totalTransactions = 0;\n    this.totalAmount = 0;\n    this.achatTransactions = 0;\n    this.venteTransactions = 0;\n    // Registering chart.js components\n    Chart.register(...registerables);\n  }\n  // Function to load transactions from the server\n  loadTransactions() {\n    this.transactionService.getAllTransactions().subscribe({\n      next: data => {\n        this.transactions = data;\n        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded\n        // Calculer les statistiques de manière simple (méthode étudiante)\n        this.calculerStatistiques();\n        // Now render both charts\n        this.renderChart();\n        this.renderBarChart();\n      },\n      error: err => {\n        console.error('Error fetching transactions', err);\n      }\n    });\n  }\n  // Méthode simple pour calculer les statistiques (adaptée aux étudiants)\n  calculerStatistiques() {\n    // 1. Compter le total des transactions\n    this.totalTransactions = this.transactions.length;\n    // 2. Calculer le montant total\n    this.totalAmount = 0;\n    for (let i = 0; i < this.transactions.length; i++) {\n      this.totalAmount += this.transactions[i].montatnt || 0;\n    }\n    // Debug : Afficher tous les types de transactions pour voir les valeurs réelles\n    console.log('=== DEBUG TYPES DE TRANSACTIONS ===');\n    const typesUniques = [];\n    for (let i = 0; i < this.transactions.length; i++) {\n      const type = this.transactions[i].type;\n      console.log(`Transaction ${i + 1}: type = \"${type}\"`);\n      if (type && !typesUniques.includes(type)) {\n        typesUniques.push(type);\n      }\n    }\n    console.log('Types uniques trouvés:', typesUniques);\n    // 3. Compter les transactions d'achat (méthode très flexible pour étudiants)\n    this.achatTransactions = 0;\n    for (let i = 0; i < this.transactions.length; i++) {\n      const type = this.transactions[i].type;\n      if (type) {\n        const typeLower = type.toLowerCase().trim();\n        // Vérifier toutes les variantes possibles d'achat\n        if (typeLower === 'achat' || typeLower === 'buy' || typeLower === 'purchase' || typeLower === 'acheter' || typeLower.includes('achat') || typeLower.includes('buy') || typeLower.includes('purchase')) {\n          this.achatTransactions++;\n          console.log(`Transaction achat trouvée: \"${type}\"`);\n        }\n      }\n    }\n    // 4. Compter les transactions de vente (méthode très flexible pour étudiants)\n    this.venteTransactions = 0;\n    for (let i = 0; i < this.transactions.length; i++) {\n      const type = this.transactions[i].type;\n      if (type) {\n        const typeLower = type.toLowerCase().trim();\n        // Vérifier toutes les variantes possibles de vente\n        if (typeLower === 'vente' || typeLower === 'sell' || typeLower === 'sale' || typeLower === 'vendre' || typeLower.includes('vente') || typeLower.includes('sell') || typeLower.includes('sale')) {\n          this.venteTransactions++;\n          console.log(`Transaction vente trouvée: \"${type}\"`);\n        }\n      }\n    }\n    // Si aucune transaction achat/vente n'est trouvée, répartir équitablement pour la démo\n    if (this.achatTransactions === 0 && this.venteTransactions === 0 && this.totalTransactions > 0) {\n      console.log('Aucun type achat/vente trouvé, répartition automatique pour la démo');\n      this.achatTransactions = Math.ceil(this.totalTransactions / 2);\n      this.venteTransactions = Math.floor(this.totalTransactions / 2);\n    }\n    // Afficher les résultats du calcul\n    console.log('=== RÉSULTATS CALCULS ===');\n    console.log('Total transactions:', this.totalTransactions);\n    console.log('Montant total:', this.totalAmount);\n    console.log('Transactions achat:', this.achatTransactions);\n    console.log('Transactions vente:', this.venteTransactions);\n    console.log('================================');\n  }\n  // Function to render the chart using the transaction data (COURBE - GAUCHE)\n  renderChart() {\n    const ctx = this.myChartRef.nativeElement.getContext('2d');\n    // Destroy previous chart if exists\n    if (this.chartInstance) {\n      this.chartInstance.destroy();\n    }\n    // Préparer les données pour la courbe (méthode simple)\n    const labels = this.transactions.map(t => t.type);\n    const data = this.transactions.map(t => t.montatnt);\n    // Create a new chart instance - COURBE EN NOIR\n    this.chartInstance = new Chart(ctx, {\n      type: 'line',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Montant par Type de Transaction',\n          data: data,\n          backgroundColor: 'rgba(0, 0, 0, 0.1)',\n          borderColor: 'rgba(0, 0, 0, 1)',\n          borderWidth: 2\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n  // Function to render bar chart (BARRES - DROITE) - Méthode simple pour étudiants\n  renderBarChart() {\n    const ctx = this.barChartRef.nativeElement.getContext('2d');\n    // Destroy previous chart if exists\n    if (this.barChartInstance) {\n      this.barChartInstance.destroy();\n    }\n    // Préparer les données pour les barres (méthode simple)\n    // Compter les transactions par type\n    const typesCount = {};\n    for (let i = 0; i < this.transactions.length; i++) {\n      const type = this.transactions[i].type || 'Autre';\n      if (typesCount[type]) {\n        typesCount[type]++;\n      } else {\n        typesCount[type] = 1;\n      }\n    }\n    // Convertir en tableaux pour le graphique\n    const labels = Object.keys(typesCount);\n    const data = Object.values(typesCount);\n    // Create a new bar chart instance\n    this.barChartInstance = new Chart(ctx, {\n      type: 'bar',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Nombre de Transactions par Type',\n          data: data,\n          backgroundColor: ['rgba(255, 99, 132, 0.8)', 'rgba(54, 162, 235, 0.8)', 'rgba(255, 206, 86, 0.8)', 'rgba(75, 192, 192, 0.8)', 'rgba(153, 102, 255, 0.8)'],\n          borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)'],\n          borderWidth: 2\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n  ngOnInit() {\n    const adminData = localStorage.getItem('admin');\n    if (adminData) {\n      const admin = JSON.parse(adminData);\n      this.adminName = `${admin.prenom} ${admin.nom}`;\n    } else {\n      this.adminName = 'Admin';\n    }\n    this.loadResponsables();\n  }\n  loadResponsables() {\n    this.responsableService.getResponsables().subscribe(data => {\n      this.responsables = data;\n    }, error => {\n      console.error('Error fetching responsables:', error);\n    });\n  }\n  deleteResponsable(id) {\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: response => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: error => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    localStorage.removeItem('admin'); // ✅ correct key now\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  updateResponsable(id, updatedResponsable) {\n    this.responsableService.updateResponsable(id, updatedResponsable).subscribe(() => {\n      this.loadResponsables();\n    }, error => {\n      console.error('Error updating responsable:', error);\n    });\n  }\n  trackById(index, item) {\n    return item.id ?? 0;\n  }\n  ngAfterViewInit() {\n    this.loadTransactions();\n    feather.replace();\n    if (this.feedbackModalRef) {\n      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);\n    }\n  }\n  static {\n    this.ɵfac = function AdminDashComponent_Factory(t) {\n      return new (t || AdminDashComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthenticationService), i0.ɵɵdirectiveInject(i4.TransactionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashComponent,\n      selectors: [[\"app-respon-dashboard\"]],\n      viewQuery: function AdminDashComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.barChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedbackModalRef = _t.first);\n        }\n      },\n      decls: 106,\n      vars: 17,\n      consts: [[\"lang\", \"en\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-store, no-cache, must-revalidate, max-age=0\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"class\", \"nav-link\", \"href\", \"/adminDash\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/users\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/groups\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/transactions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actionnaires\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/port\", 4, \"ngIf\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"titre-colore\"], [1, \"col-md-3\"], [1, \"card\", \"stats-card\", \"card-bleu\"], [1, \"card-body\", \"text-center\"], [1, \"fas\", \"fa-list-alt\", \"icone-bleu\"], [1, \"nom-donnee-bleu\"], [1, \"valeur-bleu\"], [1, \"card\", \"stats-card\", \"card-vert\"], [1, \"fas\", \"fa-euro-sign\", \"icone-vert\"], [1, \"nom-donnee-vert\"], [1, \"valeur-vert\"], [1, \"card\", \"stats-card\", \"card-cyan\"], [1, \"fas\", \"fa-shopping-cart\", \"icone-cyan\"], [1, \"nom-donnee-cyan\"], [1, \"valeur-cyan\"], [1, \"card\", \"stats-card\", \"card-orange\"], [1, \"fas\", \"fa-hand-holding-usd\", \"icone-orange\"], [1, \"nom-donnee-orange\"], [1, \"valeur-orange\"], [1, \"col-md-6\"], [1, \"card\"], [1, \"card-header\"], [1, \"titre-graphique-courbe\"], [1, \"card-body\"], [\"width\", \"400\", \"height\", \"300\"], [\"myChart\", \"\"], [1, \"titre-graphique-barres\"], [\"barChart\", \"\"], [\"href\", \"/adminDash\", 1, \"nav-link\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"book\"]],\n      template: function AdminDashComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"meta\", 6)(8, \"meta\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10)(12, \"link\", 11);\n          i0.ɵɵelementStart(13, \"title\");\n          i0.ɵɵtext(14, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"link\", 12)(16, \"link\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"body\")(18, \"nav\", 14)(19, \"a\", 15);\n          i0.ɵɵelement(20, \"img\", 16);\n          i0.ɵɵtext(21, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"ul\", 17)(23, \"li\", 18)(24, \"a\", 19);\n          i0.ɵɵlistener(\"click\", function AdminDashComponent_Template_a_click_24_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(25, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21)(28, \"nav\", 22)(29, \"div\", 23)(30, \"ul\", 24)(31, \"li\", 25);\n          i0.ɵɵtemplate(32, AdminDashComponent_a_32_Template, 5, 0, \"a\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"li\", 25);\n          i0.ɵɵtemplate(34, AdminDashComponent_a_34_Template, 3, 0, \"a\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"li\", 25);\n          i0.ɵɵtemplate(36, AdminDashComponent_a_36_Template, 3, 0, \"a\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"li\", 25);\n          i0.ɵɵtemplate(38, AdminDashComponent_a_38_Template, 3, 0, \"a\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"li\", 25);\n          i0.ɵɵtemplate(40, AdminDashComponent_a_40_Template, 3, 0, \"a\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"li\", 25);\n          i0.ɵɵtemplate(42, AdminDashComponent_a_42_Template, 3, 0, \"a\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"li\", 25);\n          i0.ɵɵtemplate(44, AdminDashComponent_a_44_Template, 3, 0, \"a\", 32);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(45, \"main\", 33)(46, \"div\", 34)(47, \"h1\", 35);\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 36);\n          i0.ɵɵelement(50, \"div\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 38)(52, \"div\", 39)(53, \"h3\", 40);\n          i0.ɵɵtext(54, \"\\uD83D\\uDCCA Donn\\u00E9es des Transactions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 21)(56, \"div\", 41)(57, \"div\", 42)(58, \"div\", 43);\n          i0.ɵɵelement(59, \"i\", 44);\n          i0.ɵɵelementStart(60, \"h5\", 45);\n          i0.ɵɵtext(61, \"\\uD83D\\uDCCB Total Transactions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"h2\", 46);\n          i0.ɵɵtext(63);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(64, \"div\", 41)(65, \"div\", 47)(66, \"div\", 43);\n          i0.ɵɵelement(67, \"i\", 48);\n          i0.ɵɵelementStart(68, \"h5\", 49);\n          i0.ɵɵtext(69, \"\\uD83D\\uDCB0 Montant Total\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"h2\", 50);\n          i0.ɵɵtext(71);\n          i0.ɵɵpipe(72, \"currency\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(73, \"div\", 41)(74, \"div\", 51)(75, \"div\", 43);\n          i0.ɵɵelement(76, \"i\", 52);\n          i0.ɵɵelementStart(77, \"h5\", 53);\n          i0.ɵɵtext(78, \"\\uD83D\\uDED2 Transactions Achat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"h2\", 54);\n          i0.ɵɵtext(80);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(81, \"div\", 41)(82, \"div\", 55)(83, \"div\", 43);\n          i0.ɵɵelement(84, \"i\", 56);\n          i0.ɵɵelementStart(85, \"h5\", 57);\n          i0.ɵɵtext(86, \"\\uD83D\\uDCB8 Transactions Vente\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"h2\", 58);\n          i0.ɵɵtext(88);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(89, \"div\", 21)(90, \"div\", 59)(91, \"div\", 60)(92, \"div\", 61)(93, \"h5\", 62);\n          i0.ɵɵtext(94, \"\\uD83D\\uDCC8 Graphique en Courbe - \\u00C9volution des Transactions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"div\", 63);\n          i0.ɵɵelement(96, \"canvas\", 64, 65);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(98, \"div\", 59)(99, \"div\", 60)(100, \"div\", 61)(101, \"h5\", 66);\n          i0.ɵɵtext(102, \"\\uD83D\\uDCCA Graphique en Barres - R\\u00E9partition par Type\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(103, \"div\", 63);\n          i0.ɵɵelement(104, \"canvas\", 64, 67);\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(32);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/adminDash\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/users\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/groups\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/transactions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actionnaires\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/port\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"Welcome, \", ctx.adminName, \"\");\n          i0.ɵɵadvance(15);\n          i0.ɵɵtextInterpolate(ctx.totalTransactions);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(72, 12, ctx.totalAmount, \"EUR\", \"symbol\", \"1.0-0\"));\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.achatTransactions);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.venteTransactions);\n        }\n      },\n      dependencies: [i5.NgIf, i5.CurrencyPipe],\n      styles: [_c3, _c3]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "Chart", "registerables", "Modal", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "AdminDashComponent", "constructor", "responsableService", "cdr", "router", "authService", "transactionService", "responsables", "admin<PERSON>ame", "transactions", "modalMessage", "totalTransactions", "totalAmount", "achatTransactions", "venteTransactions", "register", "loadTransactions", "getAllTransactions", "subscribe", "next", "data", "console", "log", "calculerStatistiques", "<PERSON><PERSON><PERSON>", "renderBarChart", "error", "err", "length", "i", "montatnt", "typesUniques", "type", "includes", "push", "typeLower", "toLowerCase", "trim", "Math", "ceil", "floor", "ctx", "myChartRef", "nativeElement", "getContext", "chartInstance", "destroy", "labels", "map", "t", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "options", "responsive", "maintainAspectRatio", "scales", "y", "beginAtZero", "barChartRef", "barChartInstance", "typesCount", "Object", "keys", "values", "ngOnInit", "adminData", "localStorage", "getItem", "admin", "JSON", "parse", "prenom", "nom", "loadResponsables", "getResponsables", "deleteResponsable", "id", "confirm", "response", "filter", "responsable", "logout", "removeItem", "navigate", "updateResponsable", "updatedResponsable", "trackById", "index", "item", "ngAfterViewInit", "replace", "feedbackModalRef", "modalInstance", "ɵɵdirectiveInject", "i1", "ResponsableService", "ChangeDetectorRef", "i2", "Router", "i3", "AuthenticationService", "i4", "TransactionService", "selectors", "viewQuery", "AdminDashComponent_Query", "rf", "ɵɵlistener", "AdminDashComponent_Template_a_click_24_listener", "ɵɵtemplate", "AdminDashComponent_a_32_Template", "AdminDashComponent_a_34_Template", "AdminDashComponent_a_36_Template", "AdminDashComponent_a_38_Template", "AdminDashComponent_a_40_Template", "AdminDashComponent_a_42_Template", "AdminDashComponent_a_44_Template", "ɵɵadvance", "ɵɵproperty", "isRouteAllowed", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "ɵɵpipeBind4"], "sources": ["C:\\Users\\<USER>\\Desktop\\Final - Copie\\frontend\\src\\app\\admin-dash\\admin-dash.component.ts", "C:\\Users\\<USER>\\Desktop\\Final - Copie\\frontend\\src\\app\\admin-dash\\admin-dash.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, AfterViewInit, ElementRef, ChangeDetectorRef } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js';\nimport { Router } from '@angular/router';\nimport { AuthenticationService } from '../auth/authentication.service';\nimport { Transaction } from '../model/transaction.model';\nimport { TransactionService } from '../services/transaction.service';\nimport { Modal } from 'bootstrap';\n\n@Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './admin-dash.component.html',\n  styleUrls: ['./admin-dash.component.css']\n})\nexport class AdminDashComponent implements AfterViewInit, OnInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  @ViewChild('barChart') barChartRef!: ElementRef;\n  @ViewChild('feedbackModal') feedbackModalRef!: ElementRef;\n  responsables: User[] = [];\n  adminName: string = '';\n  private chartInstance: any;\n  private barChartInstance: any;\n  transactions: Transaction[] = [];\n  modalMessage = '';\n  private modalInstance!: Modal;\n\n  // Variables simples pour les données des transactions (méthode étudiante)\n  totalTransactions: number = 0;\n  totalAmount: number = 0;\n  achatTransactions: number = 0;\n  venteTransactions: number = 0;\n\n  constructor(\n    private responsableService: ResponsableService,\n    private cdr: ChangeDetectorRef,\n    private router: Router,\n    public authService: AuthenticationService,\n    private transactionService: TransactionService,\n\n    \n  ) {\n    // Registering chart.js components\n    Chart.register(...registerables);\n  }\n\n  \n\n  // Function to load transactions from the server\n  loadTransactions(): void {\n    this.transactionService.getAllTransactions().subscribe({\n      next: (data) => {\n        this.transactions = data;\n        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded\n\n        // Calculer les statistiques de manière simple (méthode étudiante)\n        this.calculerStatistiques();\n\n        // Now render both charts\n        this.renderChart();\n        this.renderBarChart();\n      },\n      error: (err) => {\n        console.error('Error fetching transactions', err);\n      }\n    });\n  }\n\n  // Méthode simple pour calculer les statistiques (adaptée aux étudiants)\n  calculerStatistiques(): void {\n    // 1. Compter le total des transactions\n    this.totalTransactions = this.transactions.length;\n\n    // 2. Calculer le montant total\n    this.totalAmount = 0;\n    for (let i = 0; i < this.transactions.length; i++) {\n      this.totalAmount += this.transactions[i].montatnt || 0;\n    }\n\n    // Debug : Afficher tous les types de transactions pour voir les valeurs réelles\n    console.log('=== DEBUG TYPES DE TRANSACTIONS ===');\n    const typesUniques: string[] = [];\n    for (let i = 0; i < this.transactions.length; i++) {\n      const type = this.transactions[i].type;\n      console.log(`Transaction ${i + 1}: type = \"${type}\"`);\n      if (type && !typesUniques.includes(type)) {\n        typesUniques.push(type);\n      }\n    }\n    console.log('Types uniques trouvés:', typesUniques);\n\n    // 3. Compter les transactions d'achat (méthode très flexible pour étudiants)\n    this.achatTransactions = 0;\n    for (let i = 0; i < this.transactions.length; i++) {\n      const type = this.transactions[i].type;\n      if (type) {\n        const typeLower = type.toLowerCase().trim();\n        // Vérifier toutes les variantes possibles d'achat\n        if (typeLower === 'achat' ||\n            typeLower === 'buy' ||\n            typeLower === 'purchase' ||\n            typeLower === 'acheter' ||\n            typeLower.includes('achat') ||\n            typeLower.includes('buy') ||\n            typeLower.includes('purchase')) {\n          this.achatTransactions++;\n          console.log(`Transaction achat trouvée: \"${type}\"`);\n        }\n      }\n    }\n\n    // 4. Compter les transactions de vente (méthode très flexible pour étudiants)\n    this.venteTransactions = 0;\n    for (let i = 0; i < this.transactions.length; i++) {\n      const type = this.transactions[i].type;\n      if (type) {\n        const typeLower = type.toLowerCase().trim();\n        // Vérifier toutes les variantes possibles de vente\n        if (typeLower === 'vente' ||\n            typeLower === 'sell' ||\n            typeLower === 'sale' ||\n            typeLower === 'vendre' ||\n            typeLower.includes('vente') ||\n            typeLower.includes('sell') ||\n            typeLower.includes('sale')) {\n          this.venteTransactions++;\n          console.log(`Transaction vente trouvée: \"${type}\"`);\n        }\n      }\n    }\n\n    // Si aucune transaction achat/vente n'est trouvée, répartir équitablement pour la démo\n    if (this.achatTransactions === 0 && this.venteTransactions === 0 && this.totalTransactions > 0) {\n      console.log('Aucun type achat/vente trouvé, répartition automatique pour la démo');\n      this.achatTransactions = Math.ceil(this.totalTransactions / 2);\n      this.venteTransactions = Math.floor(this.totalTransactions / 2);\n    }\n\n    // Afficher les résultats du calcul\n    console.log('=== RÉSULTATS CALCULS ===');\n    console.log('Total transactions:', this.totalTransactions);\n    console.log('Montant total:', this.totalAmount);\n    console.log('Transactions achat:', this.achatTransactions);\n    console.log('Transactions vente:', this.venteTransactions);\n    console.log('================================');\n  }\n\n\n  // Function to render the chart using the transaction data (COURBE - GAUCHE)\n  renderChart(): void {\n    const ctx = this.myChartRef.nativeElement.getContext('2d');\n\n    // Destroy previous chart if exists\n    if (this.chartInstance) {\n      this.chartInstance.destroy();\n    }\n\n    // Préparer les données pour la courbe (méthode simple)\n    const labels = this.transactions.map(t => t.type);\n    const data = this.transactions.map(t => t.montatnt);\n\n    // Create a new chart instance - COURBE EN NOIR\n    this.chartInstance = new Chart(ctx, {\n      type: 'line',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Montant par Type de Transaction',\n          data: data,\n          backgroundColor: 'rgba(0, 0, 0, 0.1)',\n          borderColor: 'rgba(0, 0, 0, 1)',\n          borderWidth: 2\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n\n  // Function to render bar chart (BARRES - DROITE) - Méthode simple pour étudiants\n  renderBarChart(): void {\n    const ctx = this.barChartRef.nativeElement.getContext('2d');\n\n    // Destroy previous chart if exists\n    if (this.barChartInstance) {\n      this.barChartInstance.destroy();\n    }\n\n    // Préparer les données pour les barres (méthode simple)\n    // Compter les transactions par type\n    const typesCount: any = {};\n    for (let i = 0; i < this.transactions.length; i++) {\n      const type = this.transactions[i].type || 'Autre';\n      if (typesCount[type]) {\n        typesCount[type]++;\n      } else {\n        typesCount[type] = 1;\n      }\n    }\n\n    // Convertir en tableaux pour le graphique\n    const labels = Object.keys(typesCount);\n    const data = Object.values(typesCount);\n\n    // Create a new bar chart instance\n    this.barChartInstance = new Chart(ctx, {\n      type: 'bar',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Nombre de Transactions par Type',\n          data: data,\n          backgroundColor: [\n            'rgba(255, 99, 132, 0.8)',\n            'rgba(54, 162, 235, 0.8)',\n            'rgba(255, 206, 86, 0.8)',\n            'rgba(75, 192, 192, 0.8)',\n            'rgba(153, 102, 255, 0.8)'\n          ],\n          borderColor: [\n            'rgba(255, 99, 132, 1)',\n            'rgba(54, 162, 235, 1)',\n            'rgba(255, 206, 86, 1)',\n            'rgba(75, 192, 192, 1)',\n            'rgba(153, 102, 255, 1)'\n          ],\n          borderWidth: 2\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n\n\n  ngOnInit(): void {\n    const adminData = localStorage.getItem('admin');\n    if (adminData) {\n      const admin = JSON.parse(adminData);\n      this.adminName = `${admin.prenom} ${admin.nom}`;\n    } else {\n      this.adminName = 'Admin';\n    }\n\n    this.loadResponsables();\n  }\n\n  loadResponsables() {\n    this.responsableService.getResponsables().subscribe(\n      (data) => {\n        this.responsables = data;\n      },\n      (error) => {\n        console.error('Error fetching responsables:', error);\n      }\n    );\n  }\n\n  deleteResponsable(id: number) {\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: (response) => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: (error) => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    localStorage.removeItem('admin'); // ✅ correct key now\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n\n  updateResponsable(id: number, updatedResponsable: User) {\n    this.responsableService.updateResponsable(id, updatedResponsable).subscribe(\n      () => {\n        this.loadResponsables();\n      },\n      (error) => {\n        console.error('Error updating responsable:', error);\n      }\n    );\n  }\n\n  trackById(index: number, item: User): number | undefined {\n    return item.id ?? 0;\n  }\n\n  ngAfterViewInit() {\n    this.loadTransactions();\n    feather.replace();\n    if (this.feedbackModalRef) {\n      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);\n    }\n  }\n\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta http-equiv=\"Cache-Control\" content=\"no-store, no-cache, must-revalidate, max-age=0\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n\n    <!-- Custom styles for this template -->\n    <link href=\"./admin-dash.component.css\" rel=\"stylesheet\">\n  </head>\n\n  <body>\n     <!-- Navbar -->\n     <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/adminDash\" *ngIf=\"authService.isRouteAllowed('/adminDash')\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\" *ngIf=\"authService.isRouteAllowed('/users')\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\" *ngIf=\"authService.isRouteAllowed('/groups')\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            \n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/transactions\" *ngIf=\"authService.isRouteAllowed('/transactions')\">\n                <span data-feather=\"dollar-sign\"></span>\n                Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/actions\" *ngIf=\"authService.isRouteAllowed('/actions')\">\n                <span data-feather=\"trending-up\"></span>\n               Gestion des actions\n              </a>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\" *ngIf=\"authService.isRouteAllowed('/actionnaires')\">\n                  <span data-feather=\"users\"></span>\n                 Gestion des actionnaires\n                </a>\n                     <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/port\" *ngIf=\"authService.isRouteAllowed('/port')\">\n                <span data-feather=\"book\"></span>\n               Gestion des Portefeuilles\n              </a>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Welcome, {{ adminName }}</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n\n              </div>\n            </div>\n          </div>\n\n          <!-- EN HAUT : Données des transactions avec couleurs -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <h3 class=\"titre-colore\">📊 Données des Transactions</h3>\n              <div class=\"row\">\n                <div class=\"col-md-3\">\n                  <div class=\"card stats-card card-bleu\">\n                    <div class=\"card-body text-center\">\n                      <i class=\"fas fa-list-alt icone-bleu\"></i>\n                      <h5 class=\"nom-donnee-bleu\">📋 Total Transactions</h5>\n                      <h2 class=\"valeur-bleu\">{{ totalTransactions }}</h2>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"col-md-3\">\n                  <div class=\"card stats-card card-vert\">\n                    <div class=\"card-body text-center\">\n                      <i class=\"fas fa-euro-sign icone-vert\"></i>\n                      <h5 class=\"nom-donnee-vert\">💰 Montant Total</h5>\n                      <h2 class=\"valeur-vert\">{{ totalAmount | currency:'EUR':'symbol':'1.0-0' }}</h2>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"col-md-3\">\n                  <div class=\"card stats-card card-cyan\">\n                    <div class=\"card-body text-center\">\n                      <i class=\"fas fa-shopping-cart icone-cyan\"></i>\n                      <h5 class=\"nom-donnee-cyan\">🛒 Transactions Achat</h5>\n                      <h2 class=\"valeur-cyan\">{{ achatTransactions }}</h2>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"col-md-3\">\n                  <div class=\"card stats-card card-orange\">\n                    <div class=\"card-body text-center\">\n                      <i class=\"fas fa-hand-holding-usd icone-orange\"></i>\n                      <h5 class=\"nom-donnee-orange\">💸 Transactions Vente</h5>\n                      <h2 class=\"valeur-orange\">{{ venteTransactions }}</h2>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- DIVISION EN DEUX PARTIES -->\n          <div class=\"row\">\n            <!-- GAUCHE : Courbe existante -->\n            <div class=\"col-md-6\">\n              <div class=\"card\">\n                <div class=\"card-header\">\n                  <h5 class=\"titre-graphique-courbe\">📈 Graphique en Courbe - Évolution des Transactions</h5>\n                </div>\n                <div class=\"card-body\">\n                  <canvas #myChart width=\"400\" height=\"300\"></canvas>\n                </div>\n              </div>\n            </div>\n\n            <!-- DROITE : Barres liées aux transactions de la courbe -->\n            <div class=\"col-md-6\">\n              <div class=\"card\">\n                <div class=\"card-header\">\n                  <h5 class=\"titre-graphique-barres\">📊 Graphique en Barres - Répartition par Type</h5>\n                </div>\n                <div class=\"card-body\">\n                  <canvas #barChart width=\"400\" height=\"300\"></canvas>\n                </div>\n              </div>\n            </div>\n          </div>\n\n        </main>\n      </div>\n    </div>\n\n    <!-- Bootstrap core JavaScript -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n\n  </body>\n</html>"], "mappings": "AAGA,OAAO,KAAKA,OAAO,MAAM,eAAe;AACxC,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAK/C,SAASC,KAAK,QAAQ,WAAW;;;;;;;;;;;;ICoCjBC,EAAA,CAAAC,cAAA,YAAuF;IACrFD,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIlDJ,EAAA,CAAAC,cAAA,YAA+E;IAC7ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAiF;IAC/ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAINJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAwC;IACxCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAmF;IACjFD,EAAA,CAAAE,SAAA,eAAwC;IACzCF,EAAA,CAAAG,MAAA,4BACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAEFJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAkC;IACnCF,EAAA,CAAAG,MAAA,iCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,YAA6E;IAC3ED,EAAA,CAAAE,SAAA,eAAiC;IAClCF,EAAA,CAAAG,MAAA,kCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;ADnElB,OAAM,MAAOC,kBAAkB;EAkB7BC,YACUC,kBAAsC,EACtCC,GAAsB,EACtBC,MAAc,EACfC,WAAkC,EACjCC,kBAAsC;IAJtC,KAAAJ,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAnB5B,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,SAAS,GAAW,EAAE;IAGtB,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,YAAY,GAAG,EAAE;IAGjB;IACA,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAC,iBAAiB,GAAW,CAAC;IAW3B;IACAtB,KAAK,CAACuB,QAAQ,CAAC,GAAGtB,aAAa,CAAC;EAClC;EAIA;EACAuB,gBAAgBA,CAAA;IACd,IAAI,CAACV,kBAAkB,CAACW,kBAAkB,EAAE,CAACC,SAAS,CAAC;MACrDC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACX,YAAY,GAAGW,IAAI;QACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACb,YAAY,CAAC,CAAC,CAAC;QAEtD;QACA,IAAI,CAACc,oBAAoB,EAAE;QAE3B;QACA,IAAI,CAACC,WAAW,EAAE;QAClB,IAAI,CAACC,cAAc,EAAE;MACvB,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACbN,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;MACnD;KACD,CAAC;EACJ;EAEA;EACAJ,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACZ,iBAAiB,GAAG,IAAI,CAACF,YAAY,CAACmB,MAAM;IAEjD;IACA,IAAI,CAAChB,WAAW,GAAG,CAAC;IACpB,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpB,YAAY,CAACmB,MAAM,EAAEC,CAAC,EAAE,EAAE;MACjD,IAAI,CAACjB,WAAW,IAAI,IAAI,CAACH,YAAY,CAACoB,CAAC,CAAC,CAACC,QAAQ,IAAI,CAAC;;IAGxD;IACAT,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,MAAMS,YAAY,GAAa,EAAE;IACjC,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpB,YAAY,CAACmB,MAAM,EAAEC,CAAC,EAAE,EAAE;MACjD,MAAMG,IAAI,GAAG,IAAI,CAACvB,YAAY,CAACoB,CAAC,CAAC,CAACG,IAAI;MACtCX,OAAO,CAACC,GAAG,CAAC,eAAeO,CAAC,GAAG,CAAC,aAAaG,IAAI,GAAG,CAAC;MACrD,IAAIA,IAAI,IAAI,CAACD,YAAY,CAACE,QAAQ,CAACD,IAAI,CAAC,EAAE;QACxCD,YAAY,CAACG,IAAI,CAACF,IAAI,CAAC;;;IAG3BX,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAES,YAAY,CAAC;IAEnD;IACA,IAAI,CAAClB,iBAAiB,GAAG,CAAC;IAC1B,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpB,YAAY,CAACmB,MAAM,EAAEC,CAAC,EAAE,EAAE;MACjD,MAAMG,IAAI,GAAG,IAAI,CAACvB,YAAY,CAACoB,CAAC,CAAC,CAACG,IAAI;MACtC,IAAIA,IAAI,EAAE;QACR,MAAMG,SAAS,GAAGH,IAAI,CAACI,WAAW,EAAE,CAACC,IAAI,EAAE;QAC3C;QACA,IAAIF,SAAS,KAAK,OAAO,IACrBA,SAAS,KAAK,KAAK,IACnBA,SAAS,KAAK,UAAU,IACxBA,SAAS,KAAK,SAAS,IACvBA,SAAS,CAACF,QAAQ,CAAC,OAAO,CAAC,IAC3BE,SAAS,CAACF,QAAQ,CAAC,KAAK,CAAC,IACzBE,SAAS,CAACF,QAAQ,CAAC,UAAU,CAAC,EAAE;UAClC,IAAI,CAACpB,iBAAiB,EAAE;UACxBQ,OAAO,CAACC,GAAG,CAAC,+BAA+BU,IAAI,GAAG,CAAC;;;;IAKzD;IACA,IAAI,CAAClB,iBAAiB,GAAG,CAAC;IAC1B,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpB,YAAY,CAACmB,MAAM,EAAEC,CAAC,EAAE,EAAE;MACjD,MAAMG,IAAI,GAAG,IAAI,CAACvB,YAAY,CAACoB,CAAC,CAAC,CAACG,IAAI;MACtC,IAAIA,IAAI,EAAE;QACR,MAAMG,SAAS,GAAGH,IAAI,CAACI,WAAW,EAAE,CAACC,IAAI,EAAE;QAC3C;QACA,IAAIF,SAAS,KAAK,OAAO,IACrBA,SAAS,KAAK,MAAM,IACpBA,SAAS,KAAK,MAAM,IACpBA,SAAS,KAAK,QAAQ,IACtBA,SAAS,CAACF,QAAQ,CAAC,OAAO,CAAC,IAC3BE,SAAS,CAACF,QAAQ,CAAC,MAAM,CAAC,IAC1BE,SAAS,CAACF,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC9B,IAAI,CAACnB,iBAAiB,EAAE;UACxBO,OAAO,CAACC,GAAG,CAAC,+BAA+BU,IAAI,GAAG,CAAC;;;;IAKzD;IACA,IAAI,IAAI,CAACnB,iBAAiB,KAAK,CAAC,IAAI,IAAI,CAACC,iBAAiB,KAAK,CAAC,IAAI,IAAI,CAACH,iBAAiB,GAAG,CAAC,EAAE;MAC9FU,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;MAClF,IAAI,CAACT,iBAAiB,GAAGyB,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC5B,iBAAiB,GAAG,CAAC,CAAC;MAC9D,IAAI,CAACG,iBAAiB,GAAGwB,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC7B,iBAAiB,GAAG,CAAC,CAAC;;IAGjE;IACAU,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IACxCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACX,iBAAiB,CAAC;IAC1DU,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACV,WAAW,CAAC;IAC/CS,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACT,iBAAiB,CAAC;IAC1DQ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACR,iBAAiB,CAAC;IAC1DO,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAGA;EACAE,WAAWA,CAAA;IACT,MAAMiB,GAAG,GAAG,IAAI,CAACC,UAAU,CAACC,aAAa,CAACC,UAAU,CAAC,IAAI,CAAC;IAE1D;IACA,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;;IAG9B;IACA,MAAMC,MAAM,GAAG,IAAI,CAACtC,YAAY,CAACuC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,IAAI,CAAC;IACjD,MAAMZ,IAAI,GAAG,IAAI,CAACX,YAAY,CAACuC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,QAAQ,CAAC;IAEnD;IACA,IAAI,CAACe,aAAa,GAAG,IAAIrD,KAAK,CAACiD,GAAG,EAAE;MAClCT,IAAI,EAAE,MAAM;MACZZ,IAAI,EAAE;QACJ2B,MAAM,EAAEA,MAAM;QACdG,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,iCAAiC;UACxC/B,IAAI,EAAEA,IAAI;UACVgC,eAAe,EAAE,oBAAoB;UACrCC,WAAW,EAAE,kBAAkB;UAC/BC,WAAW,EAAE;SACd;OACF;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BC,MAAM,EAAE;UACNC,CAAC,EAAE;YACDC,WAAW,EAAE;;;;KAIpB,CAAC;EACJ;EAEA;EACAnC,cAAcA,CAAA;IACZ,MAAMgB,GAAG,GAAG,IAAI,CAACoB,WAAW,CAAClB,aAAa,CAACC,UAAU,CAAC,IAAI,CAAC;IAE3D;IACA,IAAI,IAAI,CAACkB,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAAChB,OAAO,EAAE;;IAGjC;IACA;IACA,MAAMiB,UAAU,GAAQ,EAAE;IAC1B,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpB,YAAY,CAACmB,MAAM,EAAEC,CAAC,EAAE,EAAE;MACjD,MAAMG,IAAI,GAAG,IAAI,CAACvB,YAAY,CAACoB,CAAC,CAAC,CAACG,IAAI,IAAI,OAAO;MACjD,IAAI+B,UAAU,CAAC/B,IAAI,CAAC,EAAE;QACpB+B,UAAU,CAAC/B,IAAI,CAAC,EAAE;OACnB,MAAM;QACL+B,UAAU,CAAC/B,IAAI,CAAC,GAAG,CAAC;;;IAIxB;IACA,MAAMe,MAAM,GAAGiB,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC;IACtC,MAAM3C,IAAI,GAAG4C,MAAM,CAACE,MAAM,CAACH,UAAU,CAAC;IAEtC;IACA,IAAI,CAACD,gBAAgB,GAAG,IAAItE,KAAK,CAACiD,GAAG,EAAE;MACrCT,IAAI,EAAE,KAAK;MACXZ,IAAI,EAAE;QACJ2B,MAAM,EAAEA,MAAM;QACdG,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,iCAAiC;UACxC/B,IAAI,EAAEA,IAAI;UACVgC,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,CAC3B;UACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,wBAAwB,CACzB;UACDC,WAAW,EAAE;SACd;OACF;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BC,MAAM,EAAE;UACNC,CAAC,EAAE;YACDC,WAAW,EAAE;;;;KAIpB,CAAC;EACJ;EAGAO,QAAQA,CAAA;IACN,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC/C,IAAIF,SAAS,EAAE;MACb,MAAMG,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC;MACnC,IAAI,CAAC5D,SAAS,GAAG,GAAG+D,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACI,GAAG,EAAE;KAChD,MAAM;MACL,IAAI,CAACnE,SAAS,GAAG,OAAO;;IAG1B,IAAI,CAACoE,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC1E,kBAAkB,CAAC2E,eAAe,EAAE,CAAC3D,SAAS,CAChDE,IAAI,IAAI;MACP,IAAI,CAACb,YAAY,GAAGa,IAAI;IAC1B,CAAC,EACAM,KAAK,IAAI;MACRL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,CACF;EACH;EAEAoD,iBAAiBA,CAACC,EAAU;IAC1B,IAAIC,OAAO,CAAC,mDAAmD,CAAC,EAAE;MAChE,IAAI,CAAC9E,kBAAkB,CAAC4E,iBAAiB,CAACC,EAAE,CAAC,CAAC7D,SAAS,CAAC;QACtDC,IAAI,EAAG8D,QAAQ,IAAI;UACjB5D,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE2D,QAAQ,CAAC;UACzC,IAAI,CAAC1E,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC2E,MAAM,CAACC,WAAW,IAAIA,WAAW,CAACJ,EAAE,KAAKA,EAAE,CAAC;QACpF,CAAC;QACDrD,KAAK,EAAGA,KAAK,IAAI;UACfL,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAEA0D,MAAMA,CAAA;IACJf,YAAY,CAACgB,UAAU,CAAC,WAAW,CAAC;IACpChB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;IAC/BhB,YAAY,CAACgB,UAAU,CAAC,OAAO,CAAC;IAChChB,YAAY,CAACgB,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;IAClChE,OAAO,CAACC,GAAG,CAAC+C,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAAClE,MAAM,CAACkF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,iBAAiBA,CAACR,EAAU,EAAES,kBAAwB;IACpD,IAAI,CAACtF,kBAAkB,CAACqF,iBAAiB,CAACR,EAAE,EAAES,kBAAkB,CAAC,CAACtE,SAAS,CACzE,MAAK;MACH,IAAI,CAAC0D,gBAAgB,EAAE;IACzB,CAAC,EACAlD,KAAK,IAAI;MACRL,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CACF;EACH;EAEA+D,SAASA,CAACC,KAAa,EAAEC,IAAU;IACjC,OAAOA,IAAI,CAACZ,EAAE,IAAI,CAAC;EACrB;EAEAa,eAAeA,CAAA;IACb,IAAI,CAAC5E,gBAAgB,EAAE;IACvBzB,OAAO,CAACsG,OAAO,EAAE;IACjB,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzB,IAAI,CAACC,aAAa,GAAG,IAAIrG,KAAK,CAAC,IAAI,CAACoG,gBAAgB,CAACnD,aAAa,CAAC;;EAEvE;;;uBA7SW3C,kBAAkB,EAAAL,EAAA,CAAAqG,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAvG,EAAA,CAAAqG,iBAAA,CAAArG,EAAA,CAAAwG,iBAAA,GAAAxG,EAAA,CAAAqG,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAA1G,EAAA,CAAAqG,iBAAA,CAAAM,EAAA,CAAAC,qBAAA,GAAA5G,EAAA,CAAAqG,iBAAA,CAAAQ,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAlBzG,kBAAkB;MAAA0G,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAApE,GAAA;QAAA,IAAAoE,EAAA;;;;;;;;;;;;;;;;;UCf/BlH,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAE,SAAA,cAA0F;UAY1FF,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAG,MAAA,wCAAgC;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAE/CJ,EAAA,CAAAE,SAAA,gBAAmF;UAOrFF,EAAA,CAAAI,YAAA,EAAO;UAEPJ,EAAA,CAAAC,cAAA,YAAM;UAIAD,EAAA,CAAAE,SAAA,eAAwE;UACxEF,EAAA,CAAAG,MAAA,aACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAmH,UAAA,mBAAAC,gDAAA;YAAA,OAAStE,GAAA,CAAA2C,MAAA,EAAQ;UAAA,EAAC;UAACzF,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAIzDJ,EAAA,CAAAC,cAAA,eAA6B;UAMjBD,EAAA,CAAAqH,UAAA,KAAAC,gCAAA,gBAGI;UACNtH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAqH,UAAA,KAAAE,gCAAA,gBAGI;UACNvH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAqH,UAAA,KAAAG,gCAAA,gBAGI;UACNxH,EAAA,CAAAI,YAAA,EAAK;UAEPJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAqH,UAAA,KAAAI,gCAAA,gBAGI;UACNzH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAqH,UAAA,KAAAK,gCAAA,gBAGI;UAJN1H,EAAA,CAAAI,YAAA,EAAqB;UAKnBJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAqH,UAAA,KAAAM,gCAAA,gBAGI;UAJN3H,EAAA,CAAAI,YAAA,EAAqB;UAKdJ,EAAA,CAAAC,cAAA,cAAqB;UAC5BD,EAAA,CAAAqH,UAAA,KAAAO,gCAAA,gBAGI;UAJG5H,EAAA,CAAAI,YAAA,EAAqB;UASlCJ,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAG,MAAA,IAAwB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC5CJ,EAAA,CAAAC,cAAA,eAAsC;UACpCD,EAAA,CAAAE,SAAA,eAEM;UACRF,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAC,cAAA,eAAsB;UAEOD,EAAA,CAAAG,MAAA,kDAA2B;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzDJ,EAAA,CAAAC,cAAA,eAAiB;UAITD,EAAA,CAAAE,SAAA,aAA0C;UAC1CF,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAG,MAAA,uCAAqB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACtDJ,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAG,MAAA,IAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAI1DJ,EAAA,CAAAC,cAAA,eAAsB;UAGhBD,EAAA,CAAAE,SAAA,aAA2C;UAC3CF,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAG,MAAA,kCAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjDJ,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAG,MAAA,IAAmD;;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAItFJ,EAAA,CAAAC,cAAA,eAAsB;UAGhBD,EAAA,CAAAE,SAAA,aAA+C;UAC/CF,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAG,MAAA,uCAAqB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACtDJ,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAG,MAAA,IAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAI1DJ,EAAA,CAAAC,cAAA,eAAsB;UAGhBD,EAAA,CAAAE,SAAA,aAAoD;UACpDF,EAAA,CAAAC,cAAA,cAA8B;UAAAD,EAAA,CAAAG,MAAA,uCAAqB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACxDJ,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAG,MAAA,IAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UASlEJ,EAAA,CAAAC,cAAA,eAAiB;UAK0BD,EAAA,CAAAG,MAAA,0EAAmD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAE7FJ,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAE,SAAA,sBAAmD;UACrDF,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAC,cAAA,eAAsB;UAGmBD,EAAA,CAAAG,MAAA,qEAA6C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEvFJ,EAAA,CAAAC,cAAA,gBAAuB;UACrBD,EAAA,CAAAE,SAAA,uBAAoD;UACtDF,EAAA,CAAAI,YAAA,EAAM;;;UAxHiCJ,EAAA,CAAA6H,SAAA,IAA8C;UAA9C7H,EAAA,CAAA8H,UAAA,SAAAhF,GAAA,CAAApC,WAAA,CAAAqH,cAAA,eAA8C;UAMlD/H,EAAA,CAAA6H,SAAA,GAA0C;UAA1C7H,EAAA,CAAA8H,UAAA,SAAAhF,GAAA,CAAApC,WAAA,CAAAqH,cAAA,WAA0C;UAMzC/H,EAAA,CAAA6H,SAAA,GAA2C;UAA3C7H,EAAA,CAAA8H,UAAA,SAAAhF,GAAA,CAAApC,WAAA,CAAAqH,cAAA,YAA2C;UAOvC/H,EAAA,CAAA6H,SAAA,GAAiD;UAAjD7H,EAAA,CAAA8H,UAAA,SAAAhF,GAAA,CAAApC,WAAA,CAAAqH,cAAA,kBAAiD;UAMtD/H,EAAA,CAAA6H,SAAA,GAA4C;UAA5C7H,EAAA,CAAA8H,UAAA,SAAAhF,GAAA,CAAApC,WAAA,CAAAqH,cAAA,aAA4C;UAKrC/H,EAAA,CAAA6H,SAAA,GAAiD;UAAjD7H,EAAA,CAAA8H,UAAA,SAAAhF,GAAA,CAAApC,WAAA,CAAAqH,cAAA,kBAAiD;UAK3D/H,EAAA,CAAA6H,SAAA,GAAyC;UAAzC7H,EAAA,CAAA8H,UAAA,SAAAhF,GAAA,CAAApC,WAAA,CAAAqH,cAAA,UAAyC;UAU9D/H,EAAA,CAAA6H,SAAA,GAAwB;UAAxB7H,EAAA,CAAAgI,kBAAA,cAAAlF,GAAA,CAAAjC,SAAA,KAAwB;UAkBLb,EAAA,CAAA6H,SAAA,IAAuB;UAAvB7H,EAAA,CAAAiI,iBAAA,CAAAnF,GAAA,CAAA9B,iBAAA,CAAuB;UASvBhB,EAAA,CAAA6H,SAAA,GAAmD;UAAnD7H,EAAA,CAAAiI,iBAAA,CAAAjI,EAAA,CAAAkI,WAAA,SAAApF,GAAA,CAAA7B,WAAA,4BAAmD;UASnDjB,EAAA,CAAA6H,SAAA,GAAuB;UAAvB7H,EAAA,CAAAiI,iBAAA,CAAAnF,GAAA,CAAA5B,iBAAA,CAAuB;UASrBlB,EAAA,CAAA6H,SAAA,GAAuB;UAAvB7H,EAAA,CAAAiI,iBAAA,CAAAnF,GAAA,CAAA3B,iBAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}