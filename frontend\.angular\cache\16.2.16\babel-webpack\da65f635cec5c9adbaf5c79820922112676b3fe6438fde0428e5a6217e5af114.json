{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PortefeuilleService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = 'http://localhost:8000/api/v1/auth/Portefeuille';\n  }\n  getAllPortefeuilles() {\n    return this.http.get(`${this.baseUrl}/All_Portefeuilles`);\n  }\n  deletePortefeuille(id, options) {\n    return this.http.delete(`http://localhost:8000/api/v1/auth/Portefeuille/delete/${id}`, options);\n  }\n  createPortefeuille(portefeuille) {\n    const headers = new HttpHeaders().set('Content-Type', 'application/json');\n    return this.http.post('http://localhost:8000/api/v1/auth/Portefeuille/create', portefeuille, {\n      headers,\n      responseType: 'text' // 👈 tells <PERSON><PERSON> not to expect JSO<PERSON>\n    });\n  }\n\n  static {\n    this.ɵfac = function PortefeuilleService_Factory(t) {\n      return new (t || PortefeuilleService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PortefeuilleService,\n      factory: PortefeuilleService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "PortefeuilleService", "constructor", "http", "baseUrl", "getAllPortefeuilles", "get", "deletePortefeuille", "id", "options", "delete", "createPortefeuille", "portefeuille", "headers", "set", "post", "responseType", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Final - Copie\\frontend\\src\\app\\services\\portefeuille.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { Portefeuille } from '../model/portefeuille.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class PortefeuilleService {\n  private baseUrl = 'http://localhost:8000/api/v1/auth/Portefeuille';\n\n  constructor(private http: HttpClient) {}\n\n  getAllPortefeuilles(): Observable<Portefeuille[]> {\n    return this.http.get<Portefeuille[]>(`${this.baseUrl}/All_Portefeuilles`);\n  }\n\n  deletePortefeuille(id: number, options?: any): Observable<any> {\n    return this.http.delete(`http://localhost:8000/api/v1/auth/Portefeuille/delete/${id}`, options);\n  }\n  \n\ncreatePortefeuille(portefeuille: Portefeuille) {\n  const headers = new HttpHeaders().set('Content-Type', 'application/json');\n\n  return this.http.post('http://localhost:8000/api/v1/auth/Portefeuille/create', portefeuille, {\n    headers,\n    responseType: 'text' as 'json' // 👈 tells Angular not to expect JSON\n  });\n}\n}"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;;;AAO9D,OAAM,MAAOC,mBAAmB;EAG9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,OAAO,GAAG,gDAAgD;EAE3B;EAEvCC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAiB,GAAG,IAAI,CAACF,OAAO,oBAAoB,CAAC;EAC3E;EAEAG,kBAAkBA,CAACC,EAAU,EAAEC,OAAa;IAC1C,OAAO,IAAI,CAACN,IAAI,CAACO,MAAM,CAAC,yDAAyDF,EAAE,EAAE,EAAEC,OAAO,CAAC;EACjG;EAGFE,kBAAkBA,CAACC,YAA0B;IAC3C,MAAMC,OAAO,GAAG,IAAIb,WAAW,EAAE,CAACc,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAEzE,OAAO,IAAI,CAACX,IAAI,CAACY,IAAI,CAAC,uDAAuD,EAAEH,YAAY,EAAE;MAC3FC,OAAO;MACPG,YAAY,EAAE,MAAgB,CAAC;KAChC,CAAC;EACJ;;;;uBArBaf,mBAAmB,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnBnB,mBAAmB;MAAAoB,OAAA,EAAnBpB,mBAAmB,CAAAqB,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}