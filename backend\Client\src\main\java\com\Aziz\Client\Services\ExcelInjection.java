package com.Aziz.Client.Services;

import com.Aziz.Client.Entity.TransactionData;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ExcelInjection {

    public static String TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

    public static boolean hasExcelFormat(MultipartFile file) {
        return TYPE.equals(file.getContentType());
    }

    public static List<TransactionData> excelToTransactions(InputStream inputStream) {
        List<TransactionData> transactions = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rows = sheet.iterator();

            boolean firstRow = true;
            while (rows.hasNext()) {
                Row currentRow = rows.next();
                if (firstRow) {  // Skip header row
                    firstRow = false;
                    continue;
                }

                TransactionData transactionData = new TransactionData();
                transactionData.setIdTransaction((long) currentRow.getCell(0).getNumericCellValue());
                transactionData.setDateTransaction(currentRow.getCell(1).getLocalDateTimeCellValue());
                transactionData.setType(currentRow.getCell(2).getStringCellValue());
                transactionData.setIdActionnaire((long) currentRow.getCell(3).getNumericCellValue());
                transactionData.setNomActionnaire(currentRow.getCell(4).getStringCellValue());
                transactionData.setISINAction(currentRow.getCell(5).getStringCellValue());
                transactionData.setQuantite((int) currentRow.getCell(6).getNumericCellValue());



                String prix = currentRow.getCell(7).getStringCellValue();
                prix = prix.replace(".", "");
                transactionData.setPrixUnitaire(Double.parseDouble(prix));
                transactionData.setDateCreation(LocalDateTime.now());




                String montant=currentRow.getCell(8).getStringCellValue();
                montant = montant.replace(",", "");
                montant = montant.replace(".", "");

                transactionData.setMontatnt(Double.parseDouble(montant));

                transactionData.setObservations(currentRow.getCell(9).getStringCellValue());


                transactions.add(transactionData);
            }
        } catch (Exception e) {
            throw new RuntimeException("Erreur lors de la lecture du fichier Excel :"); //+ e.getMessage());
        }

        return transactions;
    }
}
