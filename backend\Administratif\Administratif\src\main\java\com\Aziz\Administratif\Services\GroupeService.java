package com.Aziz.Administratif.Services;

import com.Aziz.Administratif.Entity.Groupe;
import com.Aziz.Administratif.Entity.Habilitation;
import com.Aziz.Administratif.Entity.Ressource;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class GroupeService {

    private final com.Aziz.Administratif.Repositories.GroupeRepository GroupeRepository;
    private final com.Aziz.Administratif.Repositories.RessourceRepository RessourceRepository;
    private final com.Aziz.Administratif.Repositories.HabilitationRepository HabilitationRepository;

    private static final Logger logger = LoggerFactory.getLogger(GroupeService.class);

    // Save a Groupe
    public void saveGroupe(Groupe groupe) {
        GroupeRepository.save(groupe);
    }

    // Get all Groupes
    public List<Groupe> findAllGroupes() {
        return GroupeRepository.findAll();
    }

    // Delete a Groupe and related habilitations
    @Transactional
    public void deleteGroupe(Long idGroupe) {
        Groupe groupe = GroupeRepository.findById(idGroupe)
                .orElseThrow(() -> new RuntimeException("Group not found"));

        // ✅ Check if the group has linked users
        if (groupe.getUsers() != null && !groupe.getUsers().isEmpty()) {
            throw new RuntimeException("❌ Cannot delete group linked to users.");
        }

        // ✅ Safe to delete habilitations and then the group
        HabilitationRepository.deleteByIdGroupe(idGroupe);
        GroupeRepository.deleteById(idGroupe);
    }


    // Find a Groupe by id
    public Optional<Groupe> findGroupeById(Long idGroupe) {
        return GroupeRepository.findById(idGroupe);
    }

    // Update a Groupe
    @Transactional
    public Groupe updateGroupe(Long idGroupe, Groupe updatedGroupe) {
        return GroupeRepository.findById(idGroupe).map(existingGroupe -> {
            // Update only the group name (if needed)
            existingGroupe.setNomGroupe(updatedGroupe.getNomGroupe());
            // You can add more fields to update if needed

            // Save the updated group
            Groupe savedGroup = GroupeRepository.save(existingGroupe);
            logger.info("Updated Groupe: {}", savedGroup);

            // Optionally, update the habilitations (if needed)
            if (updatedGroupe.getHabilitations() != null) {
                // Update Habilitations for the groupe
                List<Habilitation> updatedHabilitations = updatedGroupe.getHabilitations();
                for (Habilitation habilitation : updatedHabilitations) {
                    habilitation.setIdGroupe(savedGroup.getIdGroupe());
                }
                HabilitationRepository.saveAll(updatedHabilitations);
            }

            return savedGroup;
        }).orElseThrow(() -> new RuntimeException("Groupe not found"));
    }

    // Get Ressources associated with the Groupe
    public List<Ressource> getRessourcesByGroupeId(Long idGroupe) {
        Optional<Groupe> groupeOptional = GroupeRepository.findById(idGroupe);
        if (groupeOptional.isPresent()) {
            Groupe groupe = groupeOptional.get();
            // Assuming you have a relationship in Groupe for Ressources or Habilitations
            return groupe.getRessources(); // This assumes the Groupe entity has a getRessources() method
        } else {
            throw new RuntimeException("Groupe not found with id: " + idGroupe);
        }
    }

    // Save a Groupe with associated Ressources (via Habilitation)
    public Groupe saveGroupeWithRessources(String nomGroupe, List<Long> ressourceIds) {
        // Step 1: Create and save the group first (to get its ID)
        Groupe groupe = new Groupe();
        groupe.setNomGroupe(nomGroupe);
        Groupe savedGroupe = GroupeRepository.save(groupe); // Save to get ID

        // Step 2: Create Habilitations for the ressources
        List<Habilitation> habilitations = new ArrayList<>();
        for (Long ressourceId : ressourceIds) {
            Ressource ressource = RessourceRepository.findById(ressourceId)
                    .orElseThrow(() -> new RuntimeException("Ressource not found with id: " + ressourceId));

            Habilitation habilitation = new Habilitation();
            habilitation.setRessource(ressource);
            habilitation.setIdGroupe(savedGroupe.getIdGroupe());
            habilitations.add(habilitation);
        }

        // Step 3: Save habilitations
        HabilitationRepository.saveAll(habilitations);

        // Step 4: Set the habilitations to the groupe (optional if you want it in response)
        savedGroupe.setHabilitations(habilitations);

        return savedGroupe;
    }




    @Transactional
    public Groupe updateGroupeWithRessources(Long idGroupe, String nomGroupe, List<Long> ressourceIds) {
        // Step 1: Fetch the existing group
        Groupe existingGroupe = GroupeRepository.findById(idGroupe)
                .orElseThrow(() -> new RuntimeException("Groupe not found with id: " + idGroupe));

        // Step 2: Update name
        existingGroupe.setNomGroupe(nomGroupe);
        GroupeRepository.save(existingGroupe);

        // Step 3: Delete old habilitations
        HabilitationRepository.deleteByIdGroupe(idGroupe);

        // Step 4: Create new habilitations
        List<Habilitation> newHabilitations = new ArrayList<>();
        for (Long ressourceId : ressourceIds) {
            Ressource ressource = RessourceRepository.findById(ressourceId)
                    .orElseThrow(() -> new RuntimeException("Ressource not found with id: " + ressourceId));

            Habilitation habilitation = new Habilitation();
            habilitation.setRessource(ressource);
            habilitation.setIdGroupe(idGroupe);
            newHabilitations.add(habilitation);
        }

        // Step 5: Save them
        HabilitationRepository.saveAll(newHabilitations);

        // Optional: Attach to group and return
        existingGroupe.setHabilitations(newHabilitations);
        return existingGroupe;
    }

}
