{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/groupe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = function (a1) {\n  return [\"/edit-groupe\", a1];\n};\nfunction GroupsComponent_tr_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"a\", 52)(7, \"i\", 53);\n    i0.ɵɵtext(8, \"\\uE254\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"a\", 54);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_tr_62_Template_a_click_9_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const groupe_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(groupe_r1.idGroupe ? ctx_r2.deleteGroupe(groupe_r1.idGroupe, $event) : null);\n    });\n    i0.ɵɵelementStart(10, \"i\", 55);\n    i0.ɵɵtext(11, \"\\uE872\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const groupe_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r1.idGroupe || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r1.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, groupe_r1.idGroupe));\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n  }\\n  \\n  .feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);\\n  }\\n  \\n  .sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #333;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n  }\\n  \\n  .sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, .25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .border-top[_ngcontent-%COMP%] { border-top: 1px solid #e5e5e5; }\\n  .border-bottom[_ngcontent-%COMP%] { border-bottom: 1px solid #e5e5e5; }\\n  \\n\\n  .btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n  }\\n  \\n\\n\\n\\n\\n  \\n\\n  body[_ngcontent-%COMP%] {\\n    font-family: 'Poppins', sans-serif;\\n    margin: 0;\\n    padding: 0;\\n    min-height: 100vh;\\n    background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n    background-size: 400% 400%;\\n    animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n    color: #fff;\\n  }\\n\\n  @keyframes _ngcontent-%COMP%_gradientMove {\\n    0% { background-position: 0% 50%; }\\n    50% { background-position: 100% 50%; }\\n    100% { background-position: 0% 50%; }\\n  }\\n\\n  .container-fluid[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 2rem;\\n    margin-top: 20px;\\n  }\\n\\n  .table-responsive[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 1.5rem;\\n    -webkit-backdrop-filter: blur(12px);\\n            backdrop-filter: blur(12px);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n  }\\n\\n  .table[_ngcontent-%COMP%] {\\n    color: #fff;\\n    border-radius: 15px;\\n    overflow: hidden;\\n    border-collapse: separate;\\n    border-spacing: 0 12px;\\n  }\\n\\n  .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.15);\\n    color: #ffffff;\\n    font-weight: 600;\\n    text-align: center;\\n    border: none;\\n    padding: 1rem;\\n  }\\n\\n  .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.1);\\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\\n    text-align: center;\\n    border-radius: 12px;\\n  }\\n\\n  .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.01);\\n    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n  }\\n\\n  .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    vertical-align: middle;\\n    padding: 0.9rem;\\n    font-size: 0.95rem;\\n  }\\n\\n  .btn-custom[_ngcontent-%COMP%] {\\n    background-color: #28a745;\\n    color: white;\\n    border: none;\\n    padding: 12px 25px;\\n    border-radius: 30px;\\n    text-transform: uppercase;\\n    font-weight: bold;\\n    transition: background 0.3s, transform 0.2s;\\n    box-shadow: 0 4px 15px rgba(0,0,0,0.2);\\n  }\\n\\n  .btn-custom[_ngcontent-%COMP%]:hover {\\n    background-color: #218838;\\n    transform: translateY(-2px);\\n  }\\n\\n  .edit-icon[_ngcontent-%COMP%] {\\n    color: #299216;\\n    cursor: pointer;\\n    font-size: 20px;\\n    margin: 0 10px;\\n    transition: color 0.3s;\\n  }\\n\\n  .edit-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n  }\\n\\n  .delete-icon[_ngcontent-%COMP%] {\\n    color: #d22d2d;\\n    cursor: pointer;\\n    font-size: 20px;\\n    transition: color 0.3s;\\n  }\\n\\n  .delete-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n  }\\n\\n  .navbar[_ngcontent-%COMP%] {\\n    background-color: #1c1c1c !important;\\n  }\\n\\n  h2[_ngcontent-%COMP%] {\\n    color: #fff;\\n  }\\n\\n  a.router-link[_ngcontent-%COMP%] {\\n    text-decoration: none;\\n  }\\n  \\n  .modal-header[_ngcontent-%COMP%] {\\nbackground-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\nbackground-color: #fff;\\npadding: 1.5rem;\\ncolor: #333;\\nfont-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\nbackground-color: #f1f1f1;\\npadding: 1rem;\\nborder-top: 1px solid #ddd;\\ndisplay: flex;\\njustify-content: center; \\n\\ngap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\npadding: 0.5rem 1rem;\\nfont-size: 1rem;\\nborder-radius: 0.3rem;\\ntransition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\nbackground-color: #6c757d;\\ncolor: #fff;\\nborder: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\nbackground-color: #5a6268;\\ntransform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\nbackground-color: #007bff;\\ncolor: #fff;\\nborder: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\nbackground-color: #0056b3;\\ntransform: translateY(-2px);\\n\\n\\n\\n.search-container {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    margin-bottom: 20px;\\n  }\\n\\n  .search-container .form-control {\\n    width: 70%;\\n  }\\n\\n  .search-container .btn-custom {\\n    width: 28%;\\n    padding: 10px;\\n    font-size: 16px;\\n    text-align: center;\\n    background-color: #007bff;\\n    color: white;\\n    border-radius: 5px;\\n    border: none;\\n    transition: background-color 0.3s ease;\\n  }\\n\\n  .search-container .btn-custom:hover {\\n    background-color: #0056b3;\\n  }\\n\\n  .search-container .btn-custom i {\\n    margin-right: 5px;\\n  }\\n\\n  \\n\\n  .table-hover tbody tr:hover {\\n    background-color: #f1f1f1;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class GroupsComponent {\n  constructor(groupeService, router) {\n    this.groupeService = groupeService;\n    this.router = router;\n    this.groupes = []; // List of groups\n    this.filteredGroupes = []; // Filtered groups based on search\n    this.newGroupe = {\n      nomGroupe: ''\n    }; // New group model\n    this.editGroupe = null; // Group being edited\n    this.searchQuery = ''; // Search query for filtering\n  }\n\n  ngOnInit() {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n  // Called after the view is initialized (ideal for initializing Feather icons)\n  ngAfterViewInit() {\n    feather.replace(); // Initialize Feather icons after the view is ready\n  }\n  // Load all groups\n  loadGroupes() {\n    this.groupeService.getAllGroupes().subscribe(data => {\n      this.groupes = data;\n      this.filteredGroupes = data; // Initially, show all groups\n    }, err => {\n      console.error('Error loading groups', err);\n    });\n  }\n  // Filter groups based on the search query\n  filterGroups() {\n    if (!this.searchQuery) {\n      this.filteredGroupes = this.groupes; // If no search, show all groups\n    } else {\n      this.filteredGroupes = this.groupes.filter(groupe => groupe.nomGroupe.toLowerCase().includes(this.searchQuery.toLowerCase()));\n    }\n  }\n  // Add a new group\n  addGroupe() {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(() => {\n        this.newGroupe = {\n          nomGroupe: ''\n        }; // Reset input\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error adding group', err);\n      });\n    }\n  }\n  // Set group to edit mode\n  setEditGroupe(groupe) {\n    this.editGroupe = {\n      ...groupe\n    }; // Clone object\n  }\n  // Update a group\n  updateGroupe() {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(() => {\n        this.editGroupe = null; // Reset edit mode\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error updating group', err);\n      });\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n  // Delete a group\n  deleteGroupe(id, event) {\n    event.preventDefault(); // 🔥 prevents <a> tag default behavior\n    this.groupeService.deleteGroupe(id).subscribe({\n      next: () => {\n        console.log('Group deleted successfully');\n        this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n        this.filterGroups(); // Reapply filter after deletion\n      },\n\n      error: err => {\n        console.error('Error deleting group', err);\n      }\n    });\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function GroupsComponent_Factory(t) {\n      return new (t || GroupsComponent)(i0.ɵɵdirectiveInject(i1.GroupeService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupsComponent,\n      selectors: [[\"app-groups\"]],\n      decls: 80,\n      vars: 3,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/css/bootstrap.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"href\", \"https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\", \"rel\", \"stylesheet\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-light\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"text-center\", \"mt-4\", \"mb-4\"], [1, \"mb-3\", \"d-flex\", \"justify-content-between\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"w-50\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#addGroupeModal\", 1, \"btn\", \"btn-custom\"], [1, \"material-icons\", \"align-middle\", 2, \"font-size\", \"20px\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"addGroupeModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"addGroupeModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"addGroupeModalLabel\", 1, \"modal-title\"], [1, \"modal-body\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"nomGroupe\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [1, \"edit\", 3, \"routerLink\"], [\"data-toggle\", \"tooltip\", \"title\", \"Modifier\", 1, \"material-icons\", \"edit-icon\"], [1, \"delete\", 3, \"click\"], [\"data-toggle\", \"tooltip\", \"title\", \"Supprimer\", 1, \"material-icons\", \"delete-icon\"]],\n      template: function GroupsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"link\", 6)(8, \"link\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10)(12, \"link\", 11);\n          i0.ɵɵelementStart(13, \"title\");\n          i0.ɵɵtext(14, \"Gestion des Groupes\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"body\")(16, \"nav\", 12)(17, \"a\", 13);\n          i0.ɵɵtext(18, \"GTI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"ul\", 14)(20, \"li\", 15)(21, \"a\", 16);\n          i0.ɵɵlistener(\"click\", function GroupsComponent_Template_a_click_21_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(22, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 17)(24, \"div\", 18)(25, \"nav\", 19)(26, \"div\", 20)(27, \"ul\", 21)(28, \"li\", 22)(29, \"a\", 23);\n          i0.ɵɵelement(30, \"span\", 24);\n          i0.ɵɵtext(31, \" Dashboard \");\n          i0.ɵɵelementStart(32, \"span\", 25);\n          i0.ɵɵtext(33, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"li\", 22)(35, \"a\", 26);\n          i0.ɵɵelement(36, \"span\", 27);\n          i0.ɵɵtext(37, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"li\", 22)(39, \"a\", 28);\n          i0.ɵɵelement(40, \"span\", 29);\n          i0.ɵɵtext(41, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(42, \"main\", 30)(43, \"h2\", 31);\n          i0.ɵɵtext(44, \"Gestion des groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 32)(46, \"input\", 33);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_46_listener($event) {\n            return ctx.searchQuery = $event;\n          })(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_46_listener() {\n            return ctx.filterGroups();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 34)(48, \"i\", 35);\n          i0.ɵɵtext(49, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50, \" Ajouter un groupe \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 36)(52, \"table\", 37)(53, \"thead\")(54, \"tr\")(55, \"th\");\n          i0.ɵɵtext(56, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"th\");\n          i0.ɵɵtext(58, \"Nom \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\");\n          i0.ɵɵtext(60, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(61, \"tbody\");\n          i0.ɵɵtemplate(62, GroupsComponent_tr_62_Template, 12, 5, \"tr\", 38);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(63, \"div\", 39)(64, \"div\", 40)(65, \"div\", 41)(66, \"div\", 42)(67, \"h5\", 43);\n          i0.ɵɵtext(68, \"Ajouter un groupe\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"div\", 44)(70, \"form\", 45);\n          i0.ɵɵlistener(\"ngSubmit\", function GroupsComponent_Template_form_ngSubmit_70_listener() {\n            return ctx.addGroupe();\n          });\n          i0.ɵɵelementStart(71, \"div\", 46)(72, \"label\", 47);\n          i0.ɵɵtext(73, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"input\", 48);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_74_listener($event) {\n            return ctx.newGroupe.nomGroupe = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 49)(76, \"button\", 50);\n          i0.ɵɵtext(77, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"button\", 51);\n          i0.ɵɵtext(79, \"Ajouter\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(46);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredGroupes);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngModel\", ctx.newGroupe.nomGroupe);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm, i2.RouterLink],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GroupsComponent_tr_62_Template_a_click_9_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r3", "groupe_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "idGroupe", "deleteGroupe", "ɵɵadvance", "ɵɵtextInterpolate", "nomGroupe", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "GroupsComponent", "constructor", "groupeService", "router", "groupes", "filteredGroupes", "newGroupe", "editGroupe", "searchQuery", "ngOnInit", "loadGroupes", "ngAfterViewInit", "replace", "getAllGroupes", "subscribe", "data", "err", "console", "error", "filterGroups", "filter", "groupe", "toLowerCase", "includes", "addGroupe", "trim", "setEditGroupe", "updateGroupe", "id", "event", "preventDefault", "next", "log", "logout", "localStorage", "removeItem", "getItem", "navigate", "ɵɵdirectiveInject", "i1", "GroupeService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "GroupsComponent_Template", "rf", "ctx", "ɵɵelement", "GroupsComponent_Template_a_click_21_listener", "GroupsComponent_Template_input_ngModelChange_46_listener", "ɵɵtemplate", "GroupsComponent_tr_62_Template", "GroupsComponent_Template_form_ngSubmit_70_listener", "GroupsComponent_Template_input_ngModelChange_74_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\groups\\groups.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\groups\\groups.component.html"], "sourcesContent": ["import { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Component, OnInit, AfterViewInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\n\n@Component({\n  selector: 'app-groups',\n  templateUrl: './groups.component.html',\n  styleUrls: ['./groups.component.css']\n})\nexport class GroupsComponent implements OnInit, AfterViewInit {\n  groupes: Groupe[] = []; // List of groups\n  filteredGroupes: Groupe[] = []; // Filtered groups based on search\n  newGroupe: Groupe = { nomGroupe: '' }; // New group model\n  editGroupe: Groupe | null = null; // Group being edited\n  searchQuery: string = ''; // Search query for filtering\n\n  constructor(private groupeService: GroupeService, private router: Router) {}\n\n  ngOnInit(): void {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n\n  // Called after the view is initialized (ideal for initializing Feather icons)\n  ngAfterViewInit(): void {\n    feather.replace(); // Initialize Feather icons after the view is ready\n  }\n\n  // Load all groups\n  loadGroupes(): void {\n    this.groupeService.getAllGroupes().subscribe(\n      (data) => {\n        this.groupes = data;\n        this.filteredGroupes = data; // Initially, show all groups\n      },\n      (err) => {\n        console.error('Error loading groups', err);\n      }\n    );\n  }\n\n  // Filter groups based on the search query\n  filterGroups(): void {\n    if (!this.searchQuery) {\n      this.filteredGroupes = this.groupes; // If no search, show all groups\n    } else {\n      this.filteredGroupes = this.groupes.filter(groupe =>\n        groupe.nomGroupe.toLowerCase().includes(this.searchQuery.toLowerCase())\n      );\n    }\n  }\n\n  // Add a new group\n  addGroupe(): void {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(\n        () => {\n          this.newGroupe = { nomGroupe: '' }; // Reset input\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error adding group', err);\n        }\n      );\n    }\n  }\n\n  // Set group to edit mode\n  setEditGroupe(groupe: Groupe): void {\n    this.editGroupe = { ...groupe }; // Clone object\n  }\n\n  // Update a group\n  updateGroupe(): void {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(\n        () => {\n          this.editGroupe = null; // Reset edit mode\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error updating group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n\n  // Delete a group\n  deleteGroupe(id: number, event: Event): void {\n    event.preventDefault(); // 🔥 prevents <a> tag default behavior\n  \n    this.groupeService.deleteGroupe(id).subscribe({\n      next: () => {\n        console.log('Group deleted successfully');\n        this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n        this.filterGroups(); // Reapply filter after deletion\n      },\n      error: (err) => {\n        console.error('Error deleting group', err);\n      }\n    });\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/css/bootstrap.min.css\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n    <link href=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\" rel=\"stylesheet\">\n    <link href=\"./groups.component.css\" rel=\"stylesheet\">\n    <title>Gestion des Groupes</title>\n  </head>\n\n  <body>\n    <!-- Navbar -->\n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"adminDash\">GTI</a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block bg-light sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"users\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <h2 class=\"text-center mt-4 mb-4\">Gestion des groupes</h2>\n\n          <!-- Search Bar and Add Button -->\n          <div class=\"mb-3 d-flex justify-content-between\">\n            <input class=\"form-control w-50\" \n                   type=\"text\" \n                   placeholder=\"Search\" \n                   aria-label=\"Search\" \n                   [(ngModel)]=\"searchQuery\" \n                   (ngModelChange)=\"filterGroups()\">\n\n            <button type=\"button\" class=\"btn btn-custom\" data-toggle=\"modal\" data-target=\"#addGroupeModal\">\n              <i class=\"material-icons align-middle\" style=\"font-size: 20px;\">add</i>\n              Ajouter un groupe\n            </button>\n          </div>\n\n          <!-- Table -->\n          <div class=\"table-responsive\">\n            <table class=\"table table-hover\">\n              <thead>\n                <tr>\n                  <th>ID</th>\n                  <th>Nom </th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let groupe of filteredGroupes\">\n                  <td>{{ groupe.idGroupe || 'N/A' }}</td>\n                  <td>{{ groupe.nomGroupe }}</td>\n                  <td>\n                    <a [routerLink]=\"['/edit-groupe', groupe.idGroupe]\" class=\"edit\">\n                      <i class=\"material-icons edit-icon\" data-toggle=\"tooltip\" title=\"Modifier\">&#xE254;</i>\n                    </a>\n                    <a  class=\"delete\" (click)=\"groupe.idGroupe ? deleteGroupe(groupe.idGroupe, $event) : null\">\n                      <i class=\"material-icons delete-icon\" data-toggle=\"tooltip\" title=\"Supprimer\">&#xE872;</i>\n                    </a>\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <!-- Modal -->\n    <div class=\"modal fade\" id=\"addGroupeModal\" tabindex=\"-1\" aria-labelledby=\"addGroupeModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"addGroupeModalLabel\">Ajouter un groupe</h5>\n          </div>\n          <div class=\"modal-body\">\n            <form (ngSubmit)=\"addGroupe()\">\n              <div class=\"form-group\">\n                <label for=\"nomGroupe\">Nom du groupe</label>\n                <input type=\"text\" id=\"nomGroupe\" [(ngModel)]=\"newGroupe.nomGroupe\" name=\"nomGroupe\" class=\"form-control\" required>\n              </div>\n              <div class=\"modal-footer\">\n                <button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\">Annuler</button>\n                <button type=\"submit\" class=\"btn btn-primary\">Ajouter</button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/@popperjs/core@2.10.2/dist/umd/popper.min.js\"></script>\n    <script src=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/js/bootstrap.min.js\"></script>\n  </body>\n</html>\n"], "mappings": "AAIA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;;;;;;;;;ICkFxBC,EAAA,CAAAC,cAAA,SAA2C;IACrCD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAE2ED,EAAA,CAAAE,MAAA,aAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEzFH,EAAA,CAAAC,cAAA,YAA4F;IAAzED,EAAA,CAAAI,UAAA,mBAAAC,kDAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAJ,SAAA,CAAAK,QAAA,GAAkBH,MAAA,CAAAI,YAAA,CAAAN,SAAA,CAAAK,QAAA,EAAAT,MAAA,CAAqC,GAAG,IAAI;IAAA,EAAC;IACzFN,EAAA,CAAAC,cAAA,aAA8E;IAAAD,EAAA,CAAAE,MAAA,cAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAP1FH,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAkB,iBAAA,CAAAR,SAAA,CAAAK,QAAA,UAA8B;IAC9Bf,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,iBAAA,CAAAR,SAAA,CAAAS,SAAA,CAAsB;IAErBnB,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAoB,UAAA,eAAApB,EAAA,CAAAqB,eAAA,IAAAC,GAAA,EAAAZ,SAAA,CAAAK,QAAA,EAAgD;;;;AD/EvE,OAAM,MAAOQ,eAAe;EAO1BC,YAAoBC,aAA4B,EAAUC,MAAc;IAApD,KAAAD,aAAa,GAAbA,aAAa;IAAyB,KAAAC,MAAM,GAANA,MAAM;IANhE,KAAAC,OAAO,GAAa,EAAE,CAAC,CAAC;IACxB,KAAAC,eAAe,GAAa,EAAE,CAAC,CAAC;IAChC,KAAAC,SAAS,GAAW;MAAEV,SAAS,EAAE;IAAE,CAAE,CAAC,CAAC;IACvC,KAAAW,UAAU,GAAkB,IAAI,CAAC,CAAC;IAClC,KAAAC,WAAW,GAAW,EAAE,CAAC,CAAC;EAEiD;;EAE3EC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;EACtB;EAEA;EACAC,eAAeA,CAAA;IACbnC,OAAO,CAACoC,OAAO,EAAE,CAAC,CAAC;EACrB;EAEA;EACAF,WAAWA,CAAA;IACT,IAAI,CAACR,aAAa,CAACW,aAAa,EAAE,CAACC,SAAS,CACzCC,IAAI,IAAI;MACP,IAAI,CAACX,OAAO,GAAGW,IAAI;MACnB,IAAI,CAACV,eAAe,GAAGU,IAAI,CAAC,CAAC;IAC/B,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;IAC5C,CAAC,CACF;EACH;EAEA;EACAG,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE;MACrB,IAAI,CAACH,eAAe,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC;KACtC,MAAM;MACL,IAAI,CAACC,eAAe,GAAG,IAAI,CAACD,OAAO,CAACgB,MAAM,CAACC,MAAM,IAC/CA,MAAM,CAACzB,SAAS,CAAC0B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACf,WAAW,CAACc,WAAW,EAAE,CAAC,CACxE;;EAEL;EAEA;EACAE,SAASA,CAAA;IACP,IAAI,IAAI,CAAClB,SAAS,CAACV,SAAS,CAAC6B,IAAI,EAAE,EAAE;MACnC,IAAI,CAACvB,aAAa,CAACsB,SAAS,CAAC,IAAI,CAAClB,SAAS,CAAC,CAACQ,SAAS,CACpD,MAAK;QACH,IAAI,CAACR,SAAS,GAAG;UAAEV,SAAS,EAAE;QAAE,CAAE,CAAC,CAAC;QACpC,IAAI,CAACc,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAM,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;MAC1C,CAAC,CACF;;EAEL;EAEA;EACAU,aAAaA,CAACL,MAAc;IAC1B,IAAI,CAACd,UAAU,GAAG;MAAE,GAAGc;IAAM,CAAE,CAAC,CAAC;EACnC;EAEA;EACAM,YAAYA,CAAA;IACV,IAAI,IAAI,CAACpB,UAAU,IAAI,IAAI,CAACA,UAAU,CAACf,QAAQ,EAAE;MAC/C,IAAI,CAACU,aAAa,CAACyB,YAAY,CAAC,IAAI,CAACpB,UAAU,CAACf,QAAQ,EAAE,IAAI,CAACe,UAAU,CAAC,CAACO,SAAS,CAClF,MAAK;QACH,IAAI,CAACP,UAAU,GAAG,IAAI,CAAC,CAAC;QACxB,IAAI,CAACG,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAM,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;;EAEtD;EAEA;EACAzB,YAAYA,CAACmC,EAAU,EAAEC,KAAY;IACnCA,KAAK,CAACC,cAAc,EAAE,CAAC,CAAC;IAExB,IAAI,CAAC5B,aAAa,CAACT,YAAY,CAACmC,EAAE,CAAC,CAACd,SAAS,CAAC;MAC5CiB,IAAI,EAAEA,CAAA,KAAK;QACTd,OAAO,CAACe,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAAC5B,OAAO,GAAG,IAAI,CAACA,OAAO,CAACgB,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC7B,QAAQ,KAAKoC,EAAE,CAAC;QACpE,IAAI,CAACT,YAAY,EAAE,CAAC,CAAC;MACvB,CAAC;;MACDD,KAAK,EAAGF,GAAG,IAAI;QACbC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C;KACD,CAAC;EACJ;EACAiB,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChClB,OAAO,CAACe,GAAG,CAACE,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACjC,MAAM,CAACkC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBApGWrC,eAAe,EAAAvB,EAAA,CAAA6D,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA/D,EAAA,CAAA6D,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAf1C,eAAe;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5BxE,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAA0E,SAAA,cAAsB;UAatB1E,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGpCH,EAAA,CAAAC,cAAA,YAAM;UAG8DD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAI,UAAA,mBAAAuE,6CAAA;YAAA,OAASF,GAAA,CAAAjB,MAAA,EAAQ;UAAA,EAAC;UAACxD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKzDH,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAA0E,SAAA,gBAAiC;UACjC1E,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGpDH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAA0E,SAAA,gBAAiC;UACjC1E,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAA0E,SAAA,gBAAkC;UAClC1E,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMZH,EAAA,CAAAC,cAAA,gBAAkE;UAC9BD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG1DH,EAAA,CAAAC,cAAA,eAAiD;UAKxCD,EAAA,CAAAI,UAAA,2BAAAwE,yDAAAtE,MAAA;YAAA,OAAAmE,GAAA,CAAA1C,WAAA,GAAAzB,MAAA;UAAA,EAAyB,2BAAAsE,yDAAA;YAAA,OACRH,GAAA,CAAA/B,YAAA,EAAc;UAAA,EADN;UAJhC1C,EAAA,CAAAG,YAAA,EAKwC;UAExCH,EAAA,CAAAC,cAAA,kBAA+F;UAC7BD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACXH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACbH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpBH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA6E,UAAA,KAAAC,8BAAA,kBAWK;UACP9E,EAAA,CAAAG,YAAA,EAAQ;UAQlBH,EAAA,CAAAC,cAAA,eAAmH;UAI1DD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEzEH,EAAA,CAAAC,cAAA,eAAwB;UAChBD,EAAA,CAAAI,UAAA,sBAAA2E,mDAAA;YAAA,OAAYN,GAAA,CAAA1B,SAAA,EAAW;UAAA,EAAC;UAC5B/C,EAAA,CAAAC,cAAA,eAAwB;UACCD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,iBAAmH;UAAjFD,EAAA,CAAAI,UAAA,2BAAA4E,yDAAA1E,MAAA;YAAA,OAAAmE,GAAA,CAAA5C,SAAA,CAAAV,SAAA,GAAAb,MAAA;UAAA,EAAiC;UAAnEN,EAAA,CAAAG,YAAA,EAAmH;UAErHH,EAAA,CAAAC,cAAA,eAA0B;UAC6CD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrFH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UAtD3DH,EAAA,CAAAiB,SAAA,IAAyB;UAAzBjB,EAAA,CAAAoB,UAAA,YAAAqD,GAAA,CAAA1C,WAAA,CAAyB;UAoBL/B,EAAA,CAAAiB,SAAA,IAAkB;UAAlBjB,EAAA,CAAAoB,UAAA,YAAAqD,GAAA,CAAA7C,eAAA,CAAkB;UA8BP5B,EAAA,CAAAiB,SAAA,IAAiC;UAAjCjB,EAAA,CAAAoB,UAAA,YAAAqD,GAAA,CAAA5C,SAAA,CAAAV,SAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}