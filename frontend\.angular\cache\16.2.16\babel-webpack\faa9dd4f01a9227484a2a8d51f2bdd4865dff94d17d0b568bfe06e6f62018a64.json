{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/forms\";\nfunction ForgotPasswordComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.message, \" \");\n  }\n}\nfunction ForgotPasswordComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nexport class ForgotPasswordComponent {\n  constructor(http) {\n    this.http = http;\n    this.message = '';\n    this.error = '';\n  }\n  submitRequest() {\n    const request = {\n      matricule: this.matricule,\n      email: this.email\n    };\n    this.http.post('http://localhost:8000/api/v1/auth/resetPassword/send_request_to_Admin', request).subscribe({\n      next: res => {\n        this.message = \"Votre demande a été envoyée à l'administrateur.\";\n        this.error = '';\n      },\n      error: err => {\n        console.error(err);\n        this.error = \"Erreur lors de l'envoi. Veuillez vérifier vos informations.\";\n        this.message = '';\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ForgotPasswordComponent_Factory(t) {\n      return new (t || ForgotPasswordComponent)(i0.ɵɵdirectiveInject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ForgotPasswordComponent,\n      selectors: [[\"app-forgot-password\"]],\n      decls: 31,\n      vars: 4,\n      consts: [[1, \"full-page-bg\"], [1, \"container\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"vh-100\"], [1, \"card\", \"p-4\", \"shadow-lg\", \"rounded-4\", \"custom-card\"], [1, \"text-center\", \"mb-4\"], [1, \"fw-bold\", \"text-primary\"], [1, \"text-muted\"], [3, \"ngSubmit\"], [1, \"mb-3\"], [1, \"form-label\", \"fw-semibold\"], [1, \"input-group\"], [1, \"input-group-text\", \"bg-light\"], [1, \"bi\", \"bi-person-badge\"], [\"type\", \"text\", \"name\", \"matricule\", \"required\", \"\", \"placeholder\", \"Votre matricule\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"bi\", \"bi-envelope\"], [\"type\", \"email\", \"name\", \"email\", \"required\", \"\", \"placeholder\", \"Votre email\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\", \"fw-bold\", \"rounded-pill\"], [1, \"bi\", \"bi-envelope-arrow-up\"], [\"class\", \"alert alert-success text-center mt-3 rounded-pill py-2\", 4, \"ngIf\"], [\"class\", \"alert alert-danger text-center mt-3 rounded-pill py-2\", 4, \"ngIf\"], [1, \"text-center\", \"mt-3\"], [\"routerLink\", \"/login\", 1, \"text-decoration-none\", \"text-secondary\"], [1, \"alert\", \"alert-success\", \"text-center\", \"mt-3\", \"rounded-pill\", \"py-2\"], [1, \"alert\", \"alert-danger\", \"text-center\", \"mt-3\", \"rounded-pill\", \"py-2\"]],\n      template: function ForgotPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h3\", 4);\n          i0.ɵɵtext(5, \"Mot de passe oubli\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Entrez votre matricule et votre adresse email pour envoyer une demande.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function ForgotPasswordComponent_Template_form_ngSubmit_8_listener() {\n            return ctx.submitRequest();\n          });\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"label\", 8);\n          i0.ɵɵtext(11, \"Matricule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"span\", 10);\n          i0.ɵɵelement(14, \"i\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function ForgotPasswordComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.matricule = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 7)(17, \"label\", 8);\n          i0.ɵɵtext(18, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"span\", 10);\n          i0.ɵɵelement(21, \"i\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function ForgotPasswordComponent_Template_input_ngModelChange_22_listener($event) {\n            return ctx.email = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"button\", 15);\n          i0.ɵɵelement(24, \"i\", 16);\n          i0.ɵɵtext(25, \" Envoyer la demande \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(26, ForgotPasswordComponent_div_26_Template, 2, 1, \"div\", 17);\n          i0.ɵɵtemplate(27, ForgotPasswordComponent_div_27_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 19)(29, \"a\", 20);\n          i0.ɵɵtext(30, \"\\u2190 Retour \\u00E0 la connexion\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngModel\", ctx.matricule);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n        }\n      },\n      dependencies: [i2.NgIf, i3.RouterLink, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "message", "ctx_r1", "error", "ForgotPasswordComponent", "constructor", "http", "submitRequest", "request", "matricule", "email", "post", "subscribe", "next", "res", "err", "console", "ɵɵdirectiveInject", "i1", "HttpClient", "selectors", "decls", "vars", "consts", "template", "ForgotPasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ForgotPasswordComponent_Template_form_ngSubmit_8_listener", "ɵɵelement", "ForgotPasswordComponent_Template_input_ngModelChange_15_listener", "$event", "ForgotPasswordComponent_Template_input_ngModelChange_22_listener", "ɵɵtemplate", "ForgotPasswordComponent_div_26_Template", "ForgotPasswordComponent_div_27_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\forgot-password\\forgot-password.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\forgot-password\\forgot-password.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\n\n@Component({\n  selector: 'app-forgot-password',\n  templateUrl: './forgot-password.component.html'\n})\nexport class ForgotPasswordComponent {\n  matricule!: number;\n  email!: string;\n  message = '';\n  error = '';\n\n  constructor(private http: HttpClient) {}\n\n  submitRequest() {\n    const request = {\n      matricule: this.matricule,\n      email: this.email\n    };\n\n    this.http.post('http://localhost:8000/api/v1/auth/resetPassword/send_request_to_Admin', request)\n      .subscribe({\n        next: (res: any) => {\n          this.message = \"Votre demande a été envoyée à l'administrateur.\";\n          this.error = '';\n        },\n        error: (err) => {\n          console.error(err);\n          this.error = \"Erreur lors de l'envoi. Veuillez vérifier vos informations.\";\n          this.message = '';\n        }\n      });\n  }\n}\n", "<!-- forgot-password.component.html -->\n<div class=\"full-page-bg\">\n    <div class=\"container d-flex justify-content-center align-items-center vh-100\">\n      <div class=\"card p-4 shadow-lg rounded-4 custom-card\">\n        <div class=\"text-center mb-4\">\n          <h3 class=\"fw-bold text-primary\">Mot de passe oublié</h3>\n          <p class=\"text-muted\">Entrez votre matricule et votre adresse email pour envoyer une demande.</p>\n        </div>\n  \n        <form (ngSubmit)=\"submitRequest()\">\n          <!-- Matricule -->\n          <div class=\"mb-3\">\n            <label class=\"form-label fw-semibold\">Matricule</label>\n            <div class=\"input-group\">\n              <span class=\"input-group-text bg-light\"><i class=\"bi bi-person-badge\"></i></span>\n              <input type=\"text\" [(ngModel)]=\"matricule\" name=\"matricule\" required class=\"form-control\" placeholder=\"Votre matricule\">\n            </div>\n          </div>\n  \n          <!-- Email -->\n          <div class=\"mb-3\">\n            <label class=\"form-label fw-semibold\">Email</label>\n            <div class=\"input-group\">\n              <span class=\"input-group-text bg-light\"><i class=\"bi bi-envelope\"></i></span>\n              <input type=\"email\" [(ngModel)]=\"email\" name=\"email\" required class=\"form-control\" placeholder=\"Votre email\">\n            </div>\n          </div>\n  \n          <!-- Submit -->\n          <button type=\"submit\" class=\"btn btn-primary w-100 fw-bold rounded-pill\">\n            <i class=\"bi bi-envelope-arrow-up\"></i> Envoyer la demande\n          </button>\n  \n          <!-- Messages -->\n          <div *ngIf=\"message\" class=\"alert alert-success text-center mt-3 rounded-pill py-2\">\n            {{ message }}\n          </div>\n          <div *ngIf=\"error\" class=\"alert alert-danger text-center mt-3 rounded-pill py-2\">\n            {{ error }}\n          </div>\n        </form>\n  \n        <!-- Back Link -->\n        <div class=\"text-center mt-3\">\n          <a routerLink=\"/login\" class=\"text-decoration-none text-secondary\">← Retour à la connexion</a>\n        </div>\n      </div>\n    </div>\n  </div>\n  "], "mappings": ";;;;;;;ICkCUA,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;IACAP,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAG,MAAA,CAAAC,KAAA,MACF;;;ADhCV,OAAM,MAAOC,uBAAuB;EAMlCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHxB,KAAAL,OAAO,GAAG,EAAE;IACZ,KAAAE,KAAK,GAAG,EAAE;EAE6B;EAEvCI,aAAaA,CAAA;IACX,MAAMC,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,KAAK,EAAE,IAAI,CAACA;KACb;IAED,IAAI,CAACJ,IAAI,CAACK,IAAI,CAAC,uEAAuE,EAAEH,OAAO,CAAC,CAC7FI,SAAS,CAAC;MACTC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACb,OAAO,GAAG,iDAAiD;QAChE,IAAI,CAACE,KAAK,GAAG,EAAE;MACjB,CAAC;MACDA,KAAK,EAAGY,GAAG,IAAI;QACbC,OAAO,CAACb,KAAK,CAACY,GAAG,CAAC;QAClB,IAAI,CAACZ,KAAK,GAAG,6DAA6D;QAC1E,IAAI,CAACF,OAAO,GAAG,EAAE;MACnB;KACD,CAAC;EACN;;;uBA1BWG,uBAAuB,EAAAV,EAAA,CAAAuB,iBAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAvBf,uBAAuB;MAAAgB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNpChC,EAAA,CAAAC,cAAA,aAA0B;UAIiBD,EAAA,CAAAE,MAAA,+BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,WAAsB;UAAAD,EAAA,CAAAE,MAAA,8EAAuE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGnGH,EAAA,CAAAC,cAAA,cAAmC;UAA7BD,EAAA,CAAAkC,UAAA,sBAAAC,0DAAA;YAAA,OAAYF,GAAA,CAAApB,aAAA,EAAe;UAAA,EAAC;UAEhCb,EAAA,CAAAC,cAAA,aAAkB;UACsBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,cAAyB;UACiBD,EAAA,CAAAoC,SAAA,aAAkC;UAAApC,EAAA,CAAAG,YAAA,EAAO;UACjFH,EAAA,CAAAC,cAAA,iBAAwH;UAArGD,EAAA,CAAAkC,UAAA,2BAAAG,iEAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAlB,SAAA,GAAAuB,MAAA;UAAA,EAAuB;UAA1CtC,EAAA,CAAAG,YAAA,EAAwH;UAK5HH,EAAA,CAAAC,cAAA,cAAkB;UACsBD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,cAAyB;UACiBD,EAAA,CAAAoC,SAAA,aAA8B;UAAApC,EAAA,CAAAG,YAAA,EAAO;UAC7EH,EAAA,CAAAC,cAAA,iBAA6G;UAAzFD,EAAA,CAAAkC,UAAA,2BAAAK,iEAAAD,MAAA;YAAA,OAAAL,GAAA,CAAAjB,KAAA,GAAAsB,MAAA;UAAA,EAAmB;UAAvCtC,EAAA,CAAAG,YAAA,EAA6G;UAKjHH,EAAA,CAAAC,cAAA,kBAAyE;UACvED,EAAA,CAAAoC,SAAA,aAAuC;UAACpC,EAAA,CAAAE,MAAA,4BAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAwC,UAAA,KAAAC,uCAAA,kBAEM;UACNzC,EAAA,CAAAwC,UAAA,KAAAE,uCAAA,kBAEM;UACR1C,EAAA,CAAAG,YAAA,EAAO;UAGPH,EAAA,CAAAC,cAAA,eAA8B;UACuCD,EAAA,CAAAE,MAAA,yCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UA7BvEH,EAAA,CAAAI,SAAA,IAAuB;UAAvBJ,EAAA,CAAA2C,UAAA,YAAAV,GAAA,CAAAlB,SAAA,CAAuB;UAStBf,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAA2C,UAAA,YAAAV,GAAA,CAAAjB,KAAA,CAAmB;UAUrChB,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAA2C,UAAA,SAAAV,GAAA,CAAA1B,OAAA,CAAa;UAGbP,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAA2C,UAAA,SAAAV,GAAA,CAAAxB,KAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}