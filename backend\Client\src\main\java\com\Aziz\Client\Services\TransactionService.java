package com.Aziz.Client.Services;

import com.Aziz.Client.Entity.*;
import com.Aziz.Client.Repositories.*;
import com.Aziz.Client.Entity.*;
import com.Aziz.Client.Repositories.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class TransactionService {


    private com.Aziz.Client.Repositories.TransactionRepository TransactionRepository;


    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private ActionnaireRepository actionnaireRepository;

    @Autowired
    private ActionRepository actionRepository;

    @Autowired
    private com.Aziz.Client.Repositories.CotationRepository CotationRepository;



    @Autowired
    private PortefeuilleService portefeuilleService;
    @Autowired
    private PortefeuilleRepository portefeuilleRepository;



//tous les find of transactions
    public Optional<Transaction> findById(Long idTransaction){
        return TransactionRepository.findById(idTransaction);
    }

    public List<Transaction> findByActionnaire(Long idActionnaire){

        return TransactionRepository.findByPortefeuille_Actionnaire_IdActionnaire(idActionnaire);


    }


    public List<Transaction> findByActionnaireId(Long idActionnaire) {
        return TransactionRepository.findByPortefeuille_Actionnaire_IdActionnaireAndTypeIgnoreCase(idActionnaire, "ACHAT");
    }

    public List<Transaction> findByPortefeuilleId(Long idPortefeuille) {
        return TransactionRepository.findByPortefeuille_IdPortefeuille(idPortefeuille);
    }

    public List<Transaction> findByType(String type) {
        return TransactionRepository.findByTypeIgnoreCase(type);
    }

    public List<Transaction> findByMontantRange(Double min, Double max) {
        return TransactionRepository.findByMontatntBetween(min, max);
    }

    public List<Transaction> findByUserId(Long userId) {
        return transactionRepository.findAllByUserId(userId);
    }


    //Create un transaction
    public void saveTransaction(Transaction transaction, Long idActionnaire, String nomComplet, String ISINAction, Double prixUnitaire) {
        Optional<Actionnaire> opActionnaire = actionnaireRepository.findById(idActionnaire);
        Actionnaire actionnaire;

        Action opAction = actionRepository.findByIsinAction(ISINAction);
        Action action;
        String type = transaction.getType().toLowerCase();

        if (type.contains("vente") || type.contains("achat")) {

            if (opActionnaire.isEmpty()) {
                actionnaire = new Actionnaire();
                actionnaire.setIdActionnaire(idActionnaire);
                String[] parts = nomComplet.split(" ", 2);
                actionnaire.setNomActionnaire(parts[0].replace("null", ""));
                actionnaire.setPrenomActionnaire(parts[1].replace("null", ""));//parts.length > 0 ? parts[1] : "");


                    actionnaire = actionnaireRepository.save(actionnaire);


            } else {

                actionnaire = opActionnaire.get();

            }

//       Portefeuille portefeuille=new Portefeuille();
////       portefeuille.setActionnaire(actionnaire);
//       PortefeuilleRepository.save(portefeuille);

            if (opAction == null) {
                action = new Action();
                action.setIsinAction(ISINAction);
                action.setPrix(prixUnitaire);
                action.setDateCreation(LocalDateTime.now());
                action.setPrix(prixUnitaire);
                action = actionRepository.save(action);
            } else {
                action = opAction;

                var cotation = new Cotation();
                cotation.setAction(action);
                cotation.setDateCotation(LocalDate.from(transaction.getDateTransaction()));
                cotation.setPrix((transaction.getMontatnt() / transaction.getQantite() * 0.001));



                    cotation = CotationRepository.save(cotation);


            }



//
            if (type.contains("vente")) {

                transaction.setDateCreation(LocalDateTime.now());
                portefeuilleService.withdraw(ISINAction, idActionnaire, transaction.getQantite(), actionnaire, action);
//                transaction.getPortefeuille().setIdPortefeuille(portefeuilleService.withdraw(ISINAction, idActionnaire, transaction.getQantite(), actionnaire, action).getIdPortefeuille());
                transaction.setPortefeuille(portefeuilleService.withdraw(ISINAction, idActionnaire, transaction.getQantite(), actionnaire, action));

            } else if (type.contains("achat")) {
                transaction.setDateCreation(LocalDateTime.now());
                portefeuilleService.deposit(ISINAction, idActionnaire, transaction.getQantite(), actionnaire, action, transaction);
//                transaction.getPortefeuille().setIdPortefeuille(portefeuilleService.deposit(ISINAction, idActionnaire, transaction.getQantite(), actionnaire, action, transaction).getIdPortefeuille());
                transaction.setPortefeuille(portefeuilleService.deposit(ISINAction, idActionnaire, transaction.getQantite(), actionnaire, action, transaction));
//            transaction.setPortefeuille(prot.get());

//        } else if (type.contains("attribution gratuite")) {
//            transaction.setDateCreation(LocalDateTime.now());
//            portefeuilleService.deposit(ISINAction, idActionnaire, transaction.getQantite(),actionnaire,action,transaction);
            } else {
                actionnaireRepository.deleteById(actionnaire.getIdActionnaire());
                throw new RuntimeException("Type de Transaction invalide !");
            }

            transactionRepository.save(transaction);
        }
        else {
            throw new RuntimeException("Type de Transaction invalide !");
        }

    }




    public void savewithExcel(MultipartFile file,Long userId) {
        try {
            List<TransactionData> transactionDataList = ExcelInjection.excelToTransactions(file.getInputStream());

            int lineNumber = 0; // Start from 1 assuming headers are skipped
            for (TransactionData data : transactionDataList) {
                try {
                    Transaction transaction = new Transaction();
                    transaction.setIdTransaction(data.getIdTransaction());
                    transaction.setType(data.getType());
                    transaction.setQantite(data.getQuantite());
                    transaction.setMontatnt(data.getMontatnt());
                    transaction.setObservations(data.getObservations());
                    transaction.setDateTransaction(data.getDateTransaction());
                    transaction.setUserId(userId);


                    saveTransaction(
                            transaction,
                            data.getIdActionnaire(),
                            //  0L,  // Replace with actual Actionnaire ID logic if needed
                            data.getNomActionnaire() + " " + data.getPrenomActionnaire(),
                            data.getISINAction(),
                            data.getPrixUnitaire()
                    );
                } catch (Exception e) {
                    System.err.println("Échec de l'enregistrement de la transaction sur la ligne" + lineNumber++ + ": " + e.getMessage());
                }

                lineNumber++;
            }

        } catch (Exception e) {
            throw new RuntimeException("Erreur lors de la lecture du fichier Excel :" + e.getMessage());
        }
    }




    public List<Transaction> findTransactionsbyUser(Long userId) {
        return  TransactionRepository.findAllByUserId(userId);
    }



}
