{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = [\"myChart\"];\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n  }\\n  \\n  .feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n  }\\n  \\n  .sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #000000;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n  }\\n  \\n  .sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, 0.25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .border-top[_ngcontent-%COMP%] {\\n    border-top: 1px solid #e5e5e5;\\n  }\\n  \\n  .border-bottom[_ngcontent-%COMP%] {\\n    border-bottom: 1px solid #e5e5e5;\\n  }\\n\\n  \\n  body[_ngcontent-%COMP%] {\\n    font-family: 'Poppins', sans-serif;\\n    margin: 0;\\n    padding: 0;\\n    min-height: 100vh;\\n    background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n    background-size: 400% 400%;\\n    animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n    color: #fff;\\n  }\\n  \\n  @keyframes _ngcontent-%COMP%_gradientMove {\\n    0% {\\n        background-position: 0% 50%;\\n    }\\n  \\n    50% {\\n        background-position: 100% 50%;\\n    }\\n  \\n    100% {\\n        background-position: 0% 50%;\\n    }\\n  }\\n  \\n  .container-fluid[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 2rem;\\n    margin-top: 20px;\\n  }\\n  \\n  \\n  \\n  \\n  \\n  .btn-custom[_ngcontent-%COMP%]:hover {\\n    background-color: #218838;\\n    transform: translateY(-2px);\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%] {\\n    background-color: #1c1c1c !important;\\n  }\\n  \\n  h2[_ngcontent-%COMP%] {\\n    color: #fff;\\n  }\\n  \\n  a.router-link[_ngcontent-%COMP%] {\\n    text-decoration: none;\\n  }\\n  \\n  .modal-header[_ngcontent-%COMP%] {\\n    background-color: #252528;\\n  }\\n  \\n  \\n\\n  .modal-body[_ngcontent-%COMP%] {\\n    background-color: #fff;\\n    padding: 1.5rem;\\n    color: #333;\\n    font-size: 1rem;\\n  }\\n  \\n  \\n\\n  .modal-footer[_ngcontent-%COMP%] {\\n    background-color: #f1f1f1;\\n    padding: 1rem;\\n    border-top: 1px solid #ddd;\\n    display: flex;\\n    justify-content: center; \\n\\n    gap: 1rem; \\n\\n  }\\n  \\n  .modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem;\\n    font-size: 1rem;\\n    border-radius: 0.3rem;\\n    transition: background-color 0.3s, transform 0.2s;\\n  }\\n  \\n  .modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    color: #fff;\\n    border: none;\\n  }\\n  \\n  .modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n    transform: translateY(-2px);\\n  }\\n  \\n  .modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n    background-color: #007bff;\\n    color: #fff;\\n    border: none;\\n  }\\n  \\n  .modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n    transform: translateY(-2px);\\n  }\\n  \\n  \\n\\n  .mb-3[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column; \\n\\n    align-items: flex-start; \\n\\n  }\\n  \\n  \\n  \\n  \\n  \\n  .custom-sidebar[_ngcontent-%COMP%] {\\n    background: rgba(20, 33, 59, 0.9); \\n\\n    color: #fff;\\n    min-height: 100vh;\\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\\n  }\\n  \\n  .custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #ccc;\\n    transition: color 0.3s ease, background 0.3s ease;\\n  }\\n  \\n  .custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, 0.1);\\n    border-radius: 4px;\\n  }\\n  \\n  \\n  .custom-topbar[_ngcontent-%COMP%] {\\n    background-color: #000 !important;\\n    color: #fff;\\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n  }\\n  \\n  .custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n  }\\n  \\n  .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    text-decoration: underline;\\n  }\\n  \\n  \\n  \\n  .navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n    position: relative;\\n    padding: 0.5rem 1rem;\\n    transition: color 0.3s ease;\\n    font-weight: 500;\\n    letter-spacing: 0.5px;\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n    content: '';\\n    position: absolute;\\n    left: 0;\\n    bottom: 0;\\n    height: 2px;\\n    width: 0;\\n    background: #ff4c60;\\n    transition: width 0.3s ease;\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    color: #ff4c60 !important;\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n    width: 100%;\\n  }\\n  \\n  .logo-img[_ngcontent-%COMP%] {\\n    height: 30px;\\n    width: 30px;\\n    object-fit: cover;\\n    border-radius: 50%; \\n\\n    margin-right: 8px;\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class ActionsComponent {\n  constructor(router // Inject Router for page navigation\n  ) {\n    this.router = router;\n  }\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  logout() {\n    // Clear token and other data\n    localStorage.removeItem('jwt_token'); // Fix: Remove 'jwt_token'\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    // Optionally, check that these items are removed:\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    // Navigate to login page\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function ActionsComponent_Factory(t) {\n      return new (t || ActionsComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionsComponent,\n      selectors: [[\"app-actions\"]],\n      viewQuery: function ActionsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 71,\n      vars: 0,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [\"width\", \"900\", \"height\", \"380\", 2, \"background-color\", \"lightgrey\"], [\"myChart\", \"\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"dropdown-toggle\"], [\"data-feather\", \"calendar\"], [\"id\", \"myChart\", \"width\", \"900\", \"height\", \"380\", 1, \"my-4\"]],\n      template: function ActionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"link\", 5);\n          i0.ɵɵelementStart(7, \"title\");\n          i0.ɵɵtext(8, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"link\", 6)(10, \"link\", 5)(11, \"link\", 7)(12, \"link\", 8)(13, \"link\", 9)(14, \"link\", 10)(15, \"canvas\", 11, 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"body\")(18, \"nav\", 13)(19, \"a\", 14);\n          i0.ɵɵelement(20, \"img\", 15);\n          i0.ɵɵtext(21, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"ul\", 16)(23, \"li\", 17)(24, \"a\", 18);\n          i0.ɵɵlistener(\"click\", function ActionsComponent_Template_a_click_24_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(25, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 19)(27, \"div\", 20)(28, \"nav\", 21)(29, \"div\", 22)(30, \"ul\", 23)(31, \"li\", 24)(32, \"a\", 25);\n          i0.ɵɵelement(33, \"span\", 26);\n          i0.ɵɵtext(34, \" Dashboard \");\n          i0.ɵɵelementStart(35, \"span\", 27);\n          i0.ɵɵtext(36, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"li\", 24)(38, \"a\", 28);\n          i0.ɵɵelement(39, \"span\", 29);\n          i0.ɵɵtext(40, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\", 24)(42, \"a\", 30);\n          i0.ɵɵelement(43, \"span\", 31);\n          i0.ɵɵtext(44, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"li\", 24)(46, \"a\", 32);\n          i0.ɵɵelement(47, \"span\", 33);\n          i0.ɵɵtext(48, \" Gestion des transactions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"li\", 24)(50, \"a\", 34);\n          i0.ɵɵelement(51, \"span\", 35);\n          i0.ɵɵtext(52, \" Gestion des actions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"li\", 24)(54, \"a\", 36);\n          i0.ɵɵelement(55, \"span\", 37);\n          i0.ɵɵtext(56, \" Gestion des actionnaires \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(57, \"main\", 38)(58, \"div\", 39)(59, \"h1\", 40);\n          i0.ɵɵtext(60, \"Actions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 41)(62, \"div\", 42)(63, \"button\", 43);\n          i0.ɵɵtext(64, \"Share\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"button\", 43);\n          i0.ɵɵtext(66, \"Export\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"button\", 44);\n          i0.ɵɵelement(68, \"span\", 45);\n          i0.ɵɵtext(69, \" This week \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(70, \"canvas\", 46);\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "ActionsComponent", "constructor", "router", "ngAfterViewInit", "replace", "logout", "localStorage", "removeItem", "console", "log", "getItem", "navigate", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "viewQuery", "ActionsComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ActionsComponent_Template_a_click_24_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\actions\\actions.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\actions\\actions.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, ViewChild } from '@angular/core';\nimport { Chart } from 'chart.js';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\n\n\n\n@Component({\n  selector: 'app-actions',\n  templateUrl: './actions.component.html',\n  styleUrls: ['./actions.component.css']\n})\n\nexport class ActionsComponent implements AfterViewInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n   constructor(\n      private router: Router // Inject Router for page navigation\n    ) {}\n\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  \n  }\n  logout() {\n    // Clear token and other data\n    localStorage.removeItem('jwt_token');  // Fix: Remove 'jwt_token'\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    \n    // Optionally, check that these items are removed:\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n  \n    // Navigate to login page\n    this.router.navigate(['/login']);\n  }\n}", "\n<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n\n    <!-- Custom styles for this template -->\n    <link href=\"./actions.component.css\" rel=\"stylesheet\">\n    <canvas #myChart width=\"900\" height=\"380\" style=\"background-color: lightgrey;\"></canvas>\n\n  </head>\n\n  <body>\n    \n    <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\" href=\"adminDash\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n   \n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            \n            <li class=\"nav-item\">\n              <a class=\"nav-link\"  href=\"/transactions\">\n                <span data-feather=\"dollar-sign\"></span>\n                Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/actions\">\n                <span data-feather=\"trending-up\"></span>\n               Gestion des actions\n              </a>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\">\n                  <span data-feather=\"users\"></span>\n                 Gestion des actionnaires\n                </a>\n            </ul>\n          </div>\n        </nav>\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Actions</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n                <button class=\"btn btn-sm btn-outline-secondary\">Share</button>\n                <button class=\"btn btn-sm btn-outline-secondary\">Export</button>\n              </div>\n              <button class=\"btn btn-sm btn-outline-secondary dropdown-toggle\">\n                <span data-feather=\"calendar\"></span>\n                This week\n              </button>\n            </div>\n          </div>\n\n          <canvas class=\"my-4\" id=\"myChart\" width=\"900\" height=\"380\"></canvas>\n          \n        \n        </main>\n      </div>\n    </div>\n\n    <!-- Bootstrap core JavaScript\n    ================================================== -->\n    <!-- Placed at the end of the document so the pages load faster -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>    \n\n    </script>\n  </body>\n</html>\n"], "mappings": "AAGA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;;AAUxC,OAAM,MAAOC,gBAAgB;EAE1BC,YACWC,MAAc,CAAC;EAAA,E;IAAf,KAAAA,MAAM,GAANA,MAAM;EACb;EAELC,eAAeA,CAAA;IACbJ,OAAO,CAACK,OAAO,EAAE,CAAC,CAAC;EAErB;;EACAC,MAAMA,CAAA;IACJ;IACAC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE;IACvCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAEhC;IACAC,OAAO,CAACC,GAAG,CAACH,YAAY,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAEhD;IACA,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBArBWX,gBAAgB,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBf,gBAAgB;MAAAgB,SAAA;MAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCX7BP,EAAA,CAAAS,cAAA,cAAgB;UAEZT,EAAA,CAAAU,SAAA,cAAsB;UAMtBV,EAAA,CAAAS,cAAA,YAAO;UAAAT,EAAA,CAAAW,MAAA,uCAAgC;UAAAX,EAAA,CAAAY,YAAA,EAAQ;UAE/CZ,EAAA,CAAAU,SAAA,cAAmF;UAYrFV,EAAA,CAAAY,YAAA,EAAO;UAEPZ,EAAA,CAAAS,cAAA,YAAM;UAIAT,EAAA,CAAAU,SAAA,eAAwE;UACxEV,EAAA,CAAAW,MAAA,aACF;UAAAX,EAAA,CAAAY,YAAA,EAAI;UACJZ,EAAA,CAAAS,cAAA,cAA4B;UAEJT,EAAA,CAAAa,UAAA,mBAAAC,8CAAA;YAAA,OAASN,GAAA,CAAAf,MAAA,EAAQ;UAAA,EAAC;UAACO,EAAA,CAAAW,MAAA,gBAAQ;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAKzDZ,EAAA,CAAAS,cAAA,eAA6B;UAOfT,EAAA,CAAAU,SAAA,gBAAiC;UACjCV,EAAA,CAAAW,MAAA,mBAAU;UAAAX,EAAA,CAAAS,cAAA,gBAAsB;UAAAT,EAAA,CAAAW,MAAA,iBAAS;UAAAX,EAAA,CAAAY,YAAA,EAAO;UAGpDZ,EAAA,CAAAS,cAAA,cAAqB;UAEjBT,EAAA,CAAAU,SAAA,gBAAiC;UACjCV,EAAA,CAAAW,MAAA,kCACF;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAENZ,EAAA,CAAAS,cAAA,cAAqB;UAEjBT,EAAA,CAAAU,SAAA,gBAAiC;UACjCV,EAAA,CAAAW,MAAA,6BACF;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAGRZ,EAAA,CAAAS,cAAA,cAAqB;UAEjBT,EAAA,CAAAU,SAAA,gBAAwC;UACxCV,EAAA,CAAAW,MAAA,kCACF;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAENZ,EAAA,CAAAS,cAAA,cAAqB;UAEjBT,EAAA,CAAAU,SAAA,gBAAwC;UACzCV,EAAA,CAAAW,MAAA,6BACD;UAAAX,EAAA,CAAAY,YAAA,EAAI;UACJZ,EAAA,CAAAS,cAAA,cAAqB;UAEjBT,EAAA,CAAAU,SAAA,gBAAkC;UACnCV,EAAA,CAAAW,MAAA,kCACD;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAIZZ,EAAA,CAAAS,cAAA,gBAAkE;UAE/CT,EAAA,CAAAW,MAAA,eAAO;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAC3BZ,EAAA,CAAAS,cAAA,eAAsC;UAEeT,EAAA,CAAAW,MAAA,aAAK;UAAAX,EAAA,CAAAY,YAAA,EAAS;UAC/DZ,EAAA,CAAAS,cAAA,kBAAiD;UAAAT,EAAA,CAAAW,MAAA,cAAM;UAAAX,EAAA,CAAAY,YAAA,EAAS;UAElEZ,EAAA,CAAAS,cAAA,kBAAiE;UAC/DT,EAAA,CAAAU,SAAA,gBAAqC;UACrCV,EAAA,CAAAW,MAAA,mBACF;UAAAX,EAAA,CAAAY,YAAA,EAAS;UAIbZ,EAAA,CAAAU,SAAA,kBAAoE;UAGtEV,EAAA,CAAAY,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}