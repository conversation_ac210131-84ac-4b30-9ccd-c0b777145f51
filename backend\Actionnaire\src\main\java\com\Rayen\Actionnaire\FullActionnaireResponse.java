package com.Rayen.Actionnaire;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.*;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FullActionnaireResponse {

    private String NomActionnaire;
    private String PrenomActionnaire;
    private String EmailActionnaire;
    private Long Telephone;
    private Date DateCreation;
    private String UserCreation;
    private Date DateModification;
    private String UserModification;


    List<com.Rayen.Actionnaire.PortefeuilleRelation.Portefeuille> Portefeuille;
}
