body {
  font-size: .875rem;
}

.feather {
  width: 16px;
  height: 16px;
  vertical-align: text-bottom;
}

/*
* Sidebar
*/

.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100; /* Behind the navbar */
  padding: 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
}

.sidebar-sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 48px; /* Height of navbar */
  height: calc(100vh - 48px);
  padding-top: .5rem;
  overflow-x: hidden;
  overflow-y: auto; /* Scrollable contents if viewport is shorter than content. */
}

.sidebar .nav-link {
  font-weight: 500;
  color: #000000;
}

.sidebar .nav-link .feather {
  margin-right: 4px;
  color: #999;
}

.sidebar .nav-link.active {
  color: #007bff;
}

.sidebar .nav-link:hover .feather,
.sidebar .nav-link.active .feather {
  color: inherit;
}

.sidebar-heading {
  font-size: .75rem;
  text-transform: uppercase;
}

/*
* Navbar
*/

.navbar-brand {
  padding-top: .75rem;
  padding-bottom: .75rem;
  font-size: 1rem;
  background-color: rgba(0, 0, 0, 0.25);
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);
}

.navbar .form-control {
  padding: .75rem 1rem;
  border-width: 0;
  border-radius: 0;
}

.form-control-dark {
  color: #fff;
  background-color: rgba(255, 255, 255, .1);
  border-color: rgba(255, 255, 255, .1);
}

.form-control-dark:focus {
  border-color: transparent;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);
}

/*
* Utilities
*/

.border-top {
  border-top: 1px solid #e5e5e5;
}

.border-bottom {
  border-bottom: 1px solid #e5e5e5;
}

/* Base button style */
.btn {
  padding: 8px 16px;
  font-size: 14px;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  font-weight: bold;
  transition: all 0.3s ease;
}

body {
  font-family: 'Poppins', sans-serif;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);
  background-size: 400% 400%;
  animation: gradientMove 20s ease infinite;
  color: #fff;
}

@keyframes gradientMove {
  0% {
      background-position: 0% 50%;
  }

  50% {
      background-position: 100% 50%;
  }

  100% {
      background-position: 0% 50%;
  }
}

.container-fluid {
  background: rgba(255, 255, 255, 0.06);
  border-radius: 15px;
  padding: 2rem;
  margin-top: 20px;
}





.btn-custom:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

.navbar {
  background-color: #1c1c1c !important;
}

h2 {
  color: #fff;
}

a.router-link {
  text-decoration: none;
}

.modal-header {
  background-color: #252528;
}

/* Modal Body */
.modal-body {
  background-color: #fff;
  padding: 1.5rem;
  color: #333;
  font-size: 1rem;
}

/* Modal Footer */
.modal-footer {
  background-color: #f1f1f1;
  padding: 1rem;
  border-top: 1px solid #ddd;
  display: flex;
  justify-content: center; /* Center buttons horizontally */
  gap: 1rem; /* Space between the buttons */
}

.modal-footer button {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 0.3rem;
  transition: background-color 0.3s, transform 0.2s;
}

.modal-footer .btn-secondary {
  background-color: #6c757d;
  color: #fff;
  border: none;
}

.modal-footer .btn-secondary:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

.modal-footer .btn-primary {
  background-color: #007bff;
  color: #fff;
  border: none;
}

.modal-footer .btn-primary:hover {
  background-color: #0056b3;
  transform: translateY(-2px);
}

/* Ensure search bar appears below the button */
.mb-3 {
  display: flex;
  flex-direction: column; /* Stack items vertically */
  align-items: flex-start; /* Align the items to the start */
}





.custom-sidebar {
  background: rgba(20, 33, 59, 0.9); /* dark, slightly transparent */
  color: #fff;
  min-height: 100vh;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-sidebar .nav-link {
  color: #ccc;
  transition: color 0.3s ease, background 0.3s ease;
}

.custom-sidebar .nav-link:hover,
.custom-sidebar .nav-link.active {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}


.custom-topbar {
  background-color: #000 !important;
  color: #fff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-topbar .navbar-brand,
.custom-topbar .nav-link {
  color: #fff !important;
}

.custom-topbar .nav-link:hover {
  text-decoration: underline;
}



.navbar .nav-link {
  color: #fff !important;
  position: relative;
  padding: 0.5rem 1rem;
  transition: color 0.3s ease;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.navbar .nav-link::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  height: 2px;
  width: 0;
  background: #ff4c60;
  transition: width 0.3s ease;
}

.navbar .nav-link:hover {
  color: #ff4c60 !important;
}

.navbar .nav-link:hover::after {
  width: 100%;
}

.logo-img {
  height: 30px;
  width: 30px;
  object-fit: cover;
  border-radius: 50%; /* Optional: makes it round */
  margin-right: 8px;
}

/* Styles colorés pour les noms des données des transactions */
.titre-colore {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.8rem;
}

/* Cartes colorées */
.card-bleu {
  border-left: 5px solid #007bff;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 123, 255, 0.05));
}

.card-vert {
  border-left: 5px solid #28a745;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
}

.card-cyan {
  border-left: 5px solid #17a2b8;
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
}

.card-orange {
  border-left: 5px solid #fd7e14;
  background: linear-gradient(135deg, rgba(253, 126, 20, 0.1), rgba(253, 126, 20, 0.05));
}

/* Icônes colorées */
.icone-bleu {
  color: #007bff;
  font-size: 2rem;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.icone-vert {
  color: #28a745;
  font-size: 2rem;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.icone-cyan {
  color: #17a2b8;
  font-size: 2rem;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

.icone-orange {
  color: #fd7e14;
  font-size: 2rem;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
}

/* Noms des données colorés */
.nom-donnee-bleu {
  color: #007bff;
  font-weight: bold;
  text-shadow: 0 1px 3px rgba(0, 123, 255, 0.3);
  margin-bottom: 1rem;
}

.nom-donnee-vert {
  color: #28a745;
  font-weight: bold;
  text-shadow: 0 1px 3px rgba(40, 167, 69, 0.3);
  margin-bottom: 1rem;
}

.nom-donnee-cyan {
  color: #17a2b8;
  font-weight: bold;
  text-shadow: 0 1px 3px rgba(23, 162, 184, 0.3);
  margin-bottom: 1rem;
}

.nom-donnee-orange {
  color: #fd7e14;
  font-weight: bold;
  text-shadow: 0 1px 3px rgba(253, 126, 20, 0.3);
  margin-bottom: 1rem;
}

/* Valeurs colorées */
.valeur-bleu {
  color: #0056b3;
  font-weight: bold;
  font-size: 2.5rem;
  text-shadow: 0 2px 6px rgba(0, 86, 179, 0.4);
  margin: 0;
}

.valeur-vert {
  color: #1e7e34;
  font-weight: bold;
  font-size: 2.5rem;
  text-shadow: 0 2px 6px rgba(30, 126, 52, 0.4);
  margin: 0;
}

.valeur-cyan {
  color: #138496;
  font-weight: bold;
  font-size: 2.5rem;
  text-shadow: 0 2px 6px rgba(19, 132, 150, 0.4);
  margin: 0;
}

.valeur-orange {
  color: #e0a800;
  font-weight: bold;
  font-size: 2.5rem;
  text-shadow: 0 2px 6px rgba(224, 168, 0, 0.4);
  margin: 0;
}

/* Effets hover pour les cartes colorées */
.card-bleu:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
}

.card-vert:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
}

.card-cyan:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(23, 162, 184, 0.3);
}

.card-orange:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(253, 126, 20, 0.3);
}

/* Responsive design pour les couleurs */
@media (max-width: 768px) {
  .titre-colore {
    font-size: 1.4rem;
  }

  .valeur-bleu, .valeur-vert, .valeur-cyan, .valeur-orange {
    font-size: 2rem;
  }

  .icone-bleu, .icone-vert, .icone-cyan, .icone-orange {
    font-size: 1.5rem;
  }
}
