body {
  font-size: .875rem;
}

.feather {
  width: 16px;
  height: 16px;
  vertical-align: text-bottom;
}

/*
* Sidebar
*/

.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100; /* Behind the navbar */
  padding: 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
}

.sidebar-sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 48px; /* Height of navbar */
  height: calc(100vh - 48px);
  padding-top: .5rem;
  overflow-x: hidden;
  overflow-y: auto; /* Scrollable contents if viewport is shorter than content. */
}

.sidebar .nav-link {
  font-weight: 500;
  color: #000000;
}

.sidebar .nav-link .feather {
  margin-right: 4px;
  color: #999;
}

.sidebar .nav-link.active {
  color: #007bff;
}

.sidebar .nav-link:hover .feather,
.sidebar .nav-link.active .feather {
  color: inherit;
}

.sidebar-heading {
  font-size: .75rem;
  text-transform: uppercase;
}

/*
* Navbar
*/

.navbar-brand {
  padding-top: .75rem;
  padding-bottom: .75rem;
  font-size: 1rem;
  background-color: rgba(0, 0, 0, 0.25);
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);
}

.navbar .form-control {
  padding: .75rem 1rem;
  border-width: 0;
  border-radius: 0;
}

.form-control-dark {
  color: #fff;
  background-color: rgba(255, 255, 255, .1);
  border-color: rgba(255, 255, 255, .1);
}

.form-control-dark:focus {
  border-color: transparent;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);
}

/*
* Utilities
*/

.border-top {
  border-top: 1px solid #e5e5e5;
}

.border-bottom {
  border-bottom: 1px solid #e5e5e5;
}

/* Base button style */
.btn {
  padding: 8px 16px;
  font-size: 14px;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  font-weight: bold;
  transition: all 0.3s ease;
}

body {
  font-family: 'Poppins', sans-serif;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);
  background-size: 400% 400%;
  animation: gradientMove 20s ease infinite;
  color: #fff;
}

@keyframes gradientMove {
  0% {
      background-position: 0% 50%;
  }

  50% {
      background-position: 100% 50%;
  }

  100% {
      background-position: 0% 50%;
  }
}

.container-fluid {
  background: rgba(255, 255, 255, 0.06);
  border-radius: 15px;
  padding: 2rem;
  margin-top: 20px;
}





.btn-custom:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

.navbar {
  background-color: #1c1c1c !important;
}

h2 {
  color: #fff;
}

a.router-link {
  text-decoration: none;
}

.modal-header {
  background-color: #252528;
}

/* Modal Body */
.modal-body {
  background-color: #fff;
  padding: 1.5rem;
  color: #333;
  font-size: 1rem;
}

/* Modal Footer */
.modal-footer {
  background-color: #f1f1f1;
  padding: 1rem;
  border-top: 1px solid #ddd;
  display: flex;
  justify-content: center; /* Center buttons horizontally */
  gap: 1rem; /* Space between the buttons */
}

.modal-footer button {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 0.3rem;
  transition: background-color 0.3s, transform 0.2s;
}

.modal-footer .btn-secondary {
  background-color: #6c757d;
  color: #fff;
  border: none;
}

.modal-footer .btn-secondary:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

.modal-footer .btn-primary {
  background-color: #007bff;
  color: #fff;
  border: none;
}

.modal-footer .btn-primary:hover {
  background-color: #0056b3;
  transform: translateY(-2px);
}

/* Ensure search bar appears below the button */
.mb-3 {
  display: flex;
  flex-direction: column; /* Stack items vertically */
  align-items: flex-start; /* Align the items to the start */
}





.custom-sidebar {
  background: rgba(20, 33, 59, 0.9); /* dark, slightly transparent */
  color: #fff;
  min-height: 100vh;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* Styles pour les cartes de statistiques */
.stats-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #fff;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.stats-card .card-body {
  padding: 1.5rem;
  text-align: center;
}

.stats-card .card-title {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.stats-card h3 {
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0;
}

/* Styles pour les cartes de graphiques */
.chart-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #fff;
  margin-bottom: 2rem;
}

.chart-card .card-header {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem 1.5rem;
  border-radius: 10px 10px 0 0;
}

.chart-card .card-header h5 {
  margin: 0;
  font-weight: 600;
  color: #fff;
}

.chart-card .card-body {
  padding: 1.5rem;
  height: 350px;
  position: relative;
}

/* Responsive design */
@media (max-width: 768px) {
  .stats-card {
    margin-bottom: 1rem;
  }

  .chart-card .card-body {
    height: 300px;
  }
}

.custom-sidebar .nav-link {
  color: #ccc;
  transition: color 0.3s ease, background 0.3s ease;
}

.custom-sidebar .nav-link:hover,
.custom-sidebar .nav-link.active {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}


.custom-topbar {
  background-color: #000 !important;
  color: #fff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-topbar .navbar-brand,
.custom-topbar .nav-link {
  color: #fff !important;
}

.custom-topbar .nav-link:hover {
  text-decoration: underline;
}



.navbar .nav-link {
  color: #fff !important;
  position: relative;
  padding: 0.5rem 1rem;
  transition: color 0.3s ease;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.navbar .nav-link::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  height: 2px;
  width: 0;
  background: #ff4c60;
  transition: width 0.3s ease;
}

.navbar .nav-link:hover {
  color: #ff4c60 !important;
}

.navbar .nav-link:hover::after {
  width: 100%;
}

.logo-img {
  height: 30px;
  width: 30px;
  object-fit: cover;
  border-radius: 50%; /* Optional: makes it round */
  margin-right: 8px;
}
