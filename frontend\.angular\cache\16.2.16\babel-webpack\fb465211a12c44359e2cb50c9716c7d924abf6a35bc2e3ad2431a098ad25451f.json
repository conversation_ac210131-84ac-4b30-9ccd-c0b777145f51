{"ast": null, "code": "import * as feather from 'feather-icons';\nimport { Modal } from 'bootstrap'; // ✅ Correct import\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../auth/authentication.service\";\nimport * as i3 from \"../services/portefeuille.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction PortefeuillesComponent_li_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 52)(1, \"a\", 53);\n    i0.ɵɵelement(2, \"span\", 54);\n    i0.ɵɵtext(3, \" Dashboard \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 52)(1, \"a\", 55);\n    i0.ɵɵelement(2, \"span\", 56);\n    i0.ɵɵtext(3, \" Gestion des utilisateurs \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 52)(1, \"a\", 57);\n    i0.ɵɵelement(2, \"span\", 58);\n    i0.ɵɵtext(3, \" Gestion des groupes \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 52)(1, \"a\", 59);\n    i0.ɵɵelement(2, \"span\", 60);\n    i0.ɵɵtext(3, \" Gestion des transactions \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 52)(1, \"a\", 61);\n    i0.ɵɵelement(2, \"span\", 62);\n    i0.ɵɵtext(3, \" Gestion des actions \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 52)(1, \"a\", 63);\n    i0.ɵɵelement(2, \"span\", 64);\n    i0.ɵɵtext(3, \" Gestion des actionnaires \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 52)(1, \"a\", 65);\n    i0.ɵɵelement(2, \"span\", 66);\n    i0.ɵɵtext(3, \" Gestion des Portefeuilles \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_tr_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function PortefeuillesComponent_tr_54_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const p_r9 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.deletePortefeuille(p_r9.idPortefeuille));\n    });\n    i0.ɵɵtext(14, \" Supprimer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function PortefeuillesComponent_tr_54_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.openModal());\n    });\n    i0.ɵɵtext(16, \"Ajouter\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const p_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(p_r9.idPortefeuille);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(p_r9.solde);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(p_r9.quantite);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 5, p_r9.dateCreation, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(p_r9.action == null ? null : p_r9.action.idAction);\n  }\n}\nconst _c0 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, 0.25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n    border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n    border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n    font-size: 14px;\\n    border: none;\\n    cursor: pointer;\\n    border-radius: 4px;\\n    font-weight: bold;\\n    transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n    font-family: 'Poppins', sans-serif;\\n    margin: 0;\\n    padding: 0;\\n    min-height: 100vh;\\n    background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n    background-size: 400% 400%;\\n    animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n    color: #fff;\\n}\\n\\n\\n.modal-content.border-danger[_ngcontent-%COMP%] {\\n    border: 2px solid #dc3545;\\n    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);\\n  }\\n  \\n  .modal-header.bg-danger[_ngcontent-%COMP%] {\\n    background-color: #dc3545 !important;\\n    color: white;\\n  }\\n  \\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n    0% {\\n        background-position: 0% 50%;\\n    }\\n\\n    50% {\\n        background-position: 100% 50%;\\n    }\\n\\n    100% {\\n        background-position: 0% 50%;\\n    }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 2rem;\\n    margin-top: 20px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 1.5rem;\\n    -webkit-backdrop-filter: blur(12px);\\n            backdrop-filter: blur(12px);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n    color: #fff;\\n    border-radius: 15px;\\n    overflow: hidden;\\n    border-collapse: separate;\\n    border-spacing: 0 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.15);\\n    color: #ffffff;\\n    font-weight: 600;\\n    text-align: center;\\n    border: none;\\n    padding: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.1);\\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\\n    text-align: center;\\n    border-radius: 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.01);\\n    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    vertical-align: middle;\\n    padding: 0.9rem;\\n    font-size: 0.95rem;\\n}\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n    background-color: #28a745;\\n    color: white;\\n    border: none;\\n    padding: 12px 25px;\\n    border-radius: 30px;\\n    text-transform: uppercase;\\n    font-weight: bold;\\n    transition: background 0.3s, transform 0.2s;\\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n    background-color: #218838;\\n    transform: translateY(-2px);\\n}\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n    color: #206ee1;\\n    cursor: pointer;\\n    font-size: 20px;\\n    margin: 0 10px;\\n    transition: color 0.3s;\\n}\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n    color: #d22d2d;\\n    cursor: pointer;\\n    font-size: 20px;\\n    transition: color 0.3s;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n    background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n    color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n    text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n    background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n    background-color: #fff;\\n    padding: 1.5rem;\\n    color: #333;\\n    font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n    background-color: #f1f1f1;\\n    padding: 1rem;\\n    border-top: 1px solid #ddd;\\n    display: flex;\\n    justify-content: center; \\n\\n    gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem;\\n    font-size: 1rem;\\n    border-radius: 0.3rem;\\n    transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    color: #fff;\\n    border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n    transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n    background-color: #007bff;\\n    color: #fff;\\n    border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n    transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column; \\n\\n    align-items: flex-start; \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n    margin-top: 10px;\\n    background-color: rgba(255, 255, 255, 0.1); \\n\\n    border: 1px solid rgba(255, 255, 255, 0.3); \\n\\n    border-radius: 30px; \\n\\n    color: #fff; \\n\\n    padding: 10px 20px; \\n\\n    font-size: 1rem; \\n\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); \\n\\n    width: 100%; \\n\\n    max-width: 400px; \\n\\n    transition: all 0.3s ease; \\n\\n}\\n\\n.search-bar[_ngcontent-%COMP%]:focus {\\n    outline: none; \\n\\n    border-color: #007bff; \\n\\n    box-shadow: 0 0 8px rgba(0, 123, 255, 0.6); \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%]::placeholder {\\n    color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n    background: rgba(20, 33, 59, 0.9); \\n\\n    color: #fff;\\n    min-height: 100vh;\\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #ccc;\\n    transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, 0.1);\\n    border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n    background-color: #000 !important;\\n    color: #fff;\\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n    position: relative;\\n    padding: 0.5rem 1rem;\\n    transition: color 0.3s ease;\\n    font-weight: 500;\\n    letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n    content: '';\\n    position: absolute;\\n    left: 0;\\n    bottom: 0;\\n    height: 2px;\\n    width: 0;\\n    background: #ff4c60;\\n    transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n    width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n    height: 30px;\\n    width: 30px;\\n    object-fit: cover;\\n    border-radius: 50%; \\n\\n    margin-right: 8px;\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class PortefeuillesComponent {\n  constructor(router, authService, portefeuilleService, cdr, ngZone) {\n    this.router = router;\n    this.authService = authService;\n    this.portefeuilleService = portefeuilleService;\n    this.cdr = cdr;\n    this.ngZone = ngZone;\n    this.portefeuilles = [];\n    this.portefeuilleToDelete = null;\n    // Ensure that all required properties are provided.\n    this.newPortefeuille = {\n      idPortefeuille: 0,\n      solde: 0,\n      quantite: 0,\n      dateCreation: '',\n      dateModification: '',\n      transactions: [],\n      action: {\n        idAction: 0,\n        isinAction: '',\n        nomSociete: '',\n        prix: 0,\n        dateCreation: '',\n        portefeuilles: [] // Will be handled by the backend\n      },\n\n      actionnaire: {\n        idActionnaire: 0,\n        nomActionnaire: '',\n        prenomActionnaire: '',\n        emailActionnaire: '',\n        telephone: 0,\n        dateCreation: '',\n        dateModification: '',\n        portefeuilles: [] // Will be handled by the backend\n      }\n    };\n\n    this.addModal = null;\n    this.confirmModal = null;\n  }\n  ngAfterViewInit() {\n    feather.replace();\n    const addModalEl = document.getElementById('addPortefeuilleModal');\n    const confirmModalEl = document.getElementById('confirmModal');\n    if (addModalEl) this.addModal = new Modal(addModalEl);\n    if (confirmModalEl) this.confirmModal = new Modal(confirmModalEl);\n  }\n  ngOnInit() {\n    this.portefeuilleService.getAllPortefeuilles().subscribe({\n      next: data => {\n        this.portefeuilles = data;\n        console.log('Portefeuilles:', this.portefeuilles);\n      },\n      error: err => {\n        console.error('Erreur lors du chargement des portefeuilles', err);\n      }\n    });\n  }\n  openModal() {\n    this.addModal?.show();\n  }\n  submitPortefeuille() {\n    // Log the data before sending it\n    console.log('Data being sent:', this.newPortefeuille); // Make sure the data matches the Postman request\n    this.portefeuilleService.createPortefeuille(this.newPortefeuille).subscribe({\n      next: () => {\n        alert('Portefeuille ajouté avec succès');\n        this.addModal?.hide();\n        this.ngOnInit();\n      },\n      error: err => {\n        console.error('Erreur lors de l\\'ajout du portefeuille', err);\n        alert('Erreur lors de l\\'ajout du portefeuille');\n      }\n    });\n  }\n  deletePortefeuille(id) {\n    this.portefeuilleToDelete = id;\n    this.confirmModal?.show();\n  }\n  deleteConfirmed() {\n    if (this.portefeuilleToDelete !== null) {\n      this.portefeuilleService.deletePortefeuille(this.portefeuilleToDelete, {\n        responseType: 'text'\n      }).subscribe({\n        next: () => {\n          this.portefeuilles = this.portefeuilles.filter(p => p.idPortefeuille !== this.portefeuilleToDelete);\n          this.portefeuilleToDelete = null;\n          this.cdr.detectChanges();\n          this.confirmModal?.hide();\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression du portefeuille', err);\n        }\n      });\n    }\n  }\n  closeModal() {\n    this.confirmModal?.hide();\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    this.router.navigate(['/login']);\n  }\n  trackById(index, item) {\n    return item.idPortefeuille;\n  }\n  static {\n    this.ɵfac = function PortefeuillesComponent_Factory(t) {\n      return new (t || PortefeuillesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthenticationService), i0.ɵɵdirectiveInject(i3.PortefeuilleService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PortefeuillesComponent,\n      selectors: [[\"app-portefeuilles\"]],\n      decls: 100,\n      vars: 13,\n      consts: [[\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [\"class\", \"nav-item\", 4, \"ngIf\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"thead-dark\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"id\", \"addPortefeuilleModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"addPortefeuilleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [3, \"ngSubmit\"], [\"portefeuilleForm\", \"ngForm\"], [1, \"modal-header\"], [\"id\", \"addPortefeuilleModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Fermer\", 1, \"btn-close\"], [1, \"modal-body\"], [1, \"mb-3\"], [\"for\", \"solde\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"solde\", \"name\", \"solde\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"quantite\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"quantite\", \"name\", \"quantite\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"idActionnaire\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"idActionnaire\", \"name\", \"idActionnaire\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"idAction\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"idAction\", \"name\", \"idAction\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"id\", \"confirmModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"confirmModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"id\", \"confirmModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\"], [\"data-feather\", \"home\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"book\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"]],\n      template: function PortefeuillesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"head\");\n          i0.ɵɵelement(1, \"meta\", 0)(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"link\", 5)(7, \"link\", 6)(8, \"link\", 7)(9, \"link\", 8);\n          i0.ɵɵelementStart(10, \"title\");\n          i0.ɵɵtext(11, \"Dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"body\")(13, \"nav\", 9)(14, \"a\", 10);\n          i0.ɵɵelement(15, \"img\", 11);\n          i0.ɵɵtext(16, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"ul\", 12)(18, \"li\", 13)(19, \"a\", 14);\n          i0.ɵɵlistener(\"click\", function PortefeuillesComponent_Template_a_click_19_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(20, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16)(23, \"nav\", 17)(24, \"div\", 18)(25, \"ul\", 19);\n          i0.ɵɵtemplate(26, PortefeuillesComponent_li_26_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(27, PortefeuillesComponent_li_27_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(28, PortefeuillesComponent_li_28_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(29, PortefeuillesComponent_li_29_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(30, PortefeuillesComponent_li_30_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(31, PortefeuillesComponent_li_31_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(32, PortefeuillesComponent_li_32_Template, 4, 0, \"li\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"main\", 21)(34, \"div\", 22)(35, \"h1\", 23);\n          i0.ɵɵtext(36, \"Gestion des portefeuilles\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 24)(38, \"table\", 25)(39, \"thead\", 26)(40, \"tr\")(41, \"th\");\n          i0.ɵɵtext(42, \"R\\u00E9f\\u00E9rence\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"th\");\n          i0.ɵɵtext(44, \"Solde\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"th\");\n          i0.ɵɵtext(46, \"Quantit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"th\");\n          i0.ɵɵtext(48, \"Date Cr\\u00E9ation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"th\");\n          i0.ɵɵtext(50, \"ID Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"th\");\n          i0.ɵɵtext(52, \"Action\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"tbody\");\n          i0.ɵɵtemplate(54, PortefeuillesComponent_tr_54_Template, 17, 8, \"tr\", 27);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(55, \"div\", 28)(56, \"div\", 29)(57, \"div\", 30)(58, \"form\", 31, 32);\n          i0.ɵɵlistener(\"ngSubmit\", function PortefeuillesComponent_Template_form_ngSubmit_58_listener() {\n            return ctx.submitPortefeuille();\n          });\n          i0.ɵɵelementStart(60, \"div\", 33)(61, \"h5\", 34);\n          i0.ɵɵtext(62, \"Ajouter un Portefeuille\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(63, \"button\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"div\", 36)(65, \"div\", 37)(66, \"label\", 38);\n          i0.ɵɵtext(67, \"Solde\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"input\", 39);\n          i0.ɵɵlistener(\"ngModelChange\", function PortefeuillesComponent_Template_input_ngModelChange_68_listener($event) {\n            return ctx.newPortefeuille.solde = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"div\", 37)(70, \"label\", 40);\n          i0.ɵɵtext(71, \"Quantit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"input\", 41);\n          i0.ɵɵlistener(\"ngModelChange\", function PortefeuillesComponent_Template_input_ngModelChange_72_listener($event) {\n            return ctx.newPortefeuille.quantite = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"div\", 37)(74, \"label\", 42);\n          i0.ɵɵtext(75, \"ID Actionnaire\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"input\", 43);\n          i0.ɵɵlistener(\"ngModelChange\", function PortefeuillesComponent_Template_input_ngModelChange_76_listener($event) {\n            return ctx.newPortefeuille.actionnaire.idActionnaire = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"div\", 37)(78, \"label\", 44);\n          i0.ɵɵtext(79, \"ID Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"input\", 45);\n          i0.ɵɵlistener(\"ngModelChange\", function PortefeuillesComponent_Template_input_ngModelChange_80_listener($event) {\n            return ctx.newPortefeuille.action.idAction = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(81, \"div\", 46)(82, \"button\", 47);\n          i0.ɵɵtext(83, \"Ajouter\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"button\", 48);\n          i0.ɵɵtext(85, \"Fermer\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(86, \"div\", 49)(87, \"div\", 29)(88, \"div\", 30)(89, \"div\", 33)(90, \"h5\", 50);\n          i0.ɵɵtext(91, \"Confirmer la suppression\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(92, \"button\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"div\", 36);\n          i0.ɵɵtext(94, \" Voulez-vous vraiment supprimer ce portefeuille ? \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"div\", 46)(96, \"button\", 48);\n          i0.ɵɵtext(97, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"button\", 51);\n          i0.ɵɵlistener(\"click\", function PortefeuillesComponent_Template_button_click_98_listener() {\n            return ctx.deleteConfirmed();\n          });\n          i0.ɵɵtext(99, \"Confirmer\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/adminDash\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/users\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/groups\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/transactions\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actions\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actionnaires\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/port\"));\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngForOf\", ctx.portefeuilles)(\"ngForTrackBy\", ctx.trackById);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.newPortefeuille.solde);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newPortefeuille.quantite);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newPortefeuille.actionnaire.idActionnaire);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newPortefeuille.action.idAction);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NumberValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm, i4.DatePipe],\n      styles: [_c0, _c0]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "Modal", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "PortefeuillesComponent_tr_54_Template_button_click_13_listener", "restoredCtx", "ɵɵrestoreView", "_r11", "p_r9", "$implicit", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "deletePortefeuille", "idPortefeuille", "PortefeuillesComponent_tr_54_Template_button_click_15_listener", "ctx_r12", "openModal", "ɵɵadvance", "ɵɵtextInterpolate", "solde", "quantite", "ɵɵpipeBind2", "dateCreation", "action", "idAction", "PortefeuillesComponent", "constructor", "router", "authService", "portefeuilleService", "cdr", "ngZone", "portefeuilles", "portefeuilleToDelete", "newPortefeuille", "dateModification", "transactions", "isinAction", "nomSociete", "prix", "actionnaire", "idActionnaire", "nomActionnaire", "prenomActionnaire", "emailActionnaire", "telephone", "addModal", "confirmModal", "ngAfterViewInit", "replace", "addModalEl", "document", "getElementById", "confirmModalEl", "ngOnInit", "getAllPortefeuilles", "subscribe", "next", "data", "console", "log", "error", "err", "show", "submitPortefeuille", "createPortefeuille", "alert", "hide", "id", "deleteConfirmed", "responseType", "filter", "p", "detectChanges", "closeModal", "logout", "localStorage", "removeItem", "navigate", "trackById", "index", "item", "ɵɵdirectiveInject", "i1", "Router", "i2", "AuthenticationService", "i3", "PortefeuilleService", "ChangeDetectorRef", "NgZone", "selectors", "decls", "vars", "consts", "template", "PortefeuillesComponent_Template", "rf", "ctx", "PortefeuillesComponent_Template_a_click_19_listener", "ɵɵtemplate", "PortefeuillesComponent_li_26_Template", "PortefeuillesComponent_li_27_Template", "PortefeuillesComponent_li_28_Template", "PortefeuillesComponent_li_29_Template", "PortefeuillesComponent_li_30_Template", "PortefeuillesComponent_li_31_Template", "PortefeuillesComponent_li_32_Template", "PortefeuillesComponent_tr_54_Template", "PortefeuillesComponent_Template_form_ngSubmit_58_listener", "PortefeuillesComponent_Template_input_ngModelChange_68_listener", "$event", "PortefeuillesComponent_Template_input_ngModelChange_72_listener", "PortefeuillesComponent_Template_input_ngModelChange_76_listener", "PortefeuillesComponent_Template_input_ngModelChange_80_listener", "PortefeuillesComponent_Template_button_click_98_listener", "ɵɵproperty", "isRouteAllowed"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\portefeuilles\\portefeuilles.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\portefeuilles\\portefeuilles.component.html"], "sourcesContent": ["import {\n  AfterViewInit,\n  Component,\n  ChangeDetector<PERSON>ef,\n  <PERSON><PERSON>one\n} from '@angular/core';\nimport { Router } from '@angular/router';\nimport { PortefeuilleService } from '../services/portefeuille.service';\nimport { Portefeuille } from '../model/portefeuille.model';\nimport { AuthenticationService } from '../auth/authentication.service';\nimport * as feather from 'feather-icons';\nimport { Modal } from 'bootstrap'; // ✅ Correct import\n\n@Component({\n  selector: 'app-portefeuilles',\n  templateUrl: './portefeuilles.component.html',\n  styleUrls: ['./portefeuilles.component.css']\n})\nexport class PortefeuillesComponent implements AfterViewInit {\n  portefeuilles: Portefeuille[] = [];\n  portefeuilleToDelete: number | null = null;\n\n  // Ensure that all required properties are provided.\n newPortefeuille: Portefeuille = {\n    idPortefeuille: 0, // Default value, will be assigned by the backend\n    solde: 0,\n    quantite: 0,\n    dateCreation: '', // Leave empty, will be handled by the backend\n    dateModification: '', // Leave empty, will be handled by the backend\n    transactions: [], // Empty array initially\n    action: {\n      idAction: 0, // Will be populated by the user\n      isinAction: '', // Will be populated by the user\n      nomSociete: '', // Will be populated by the user\n      prix: 0, // Will be populated by the user\n      dateCreation: '', // Will be handled by the backend\n      portefeuilles: [] // Will be handled by the backend\n    },\n    actionnaire: {\n      idActionnaire: 0, // Will be populated by the user\n      nomActionnaire: '', // Will be populated by the user\n      prenomActionnaire: '', // Will be populated by the user\n      emailActionnaire: '', // Will be populated by the user\n      telephone: 0, // Will be populated by the user\n      dateCreation: '', // Will be handled by the backend\n      dateModification: '', // Will be handled by the backend\n      portefeuilles: [] // Will be handled by the backend\n    }\n  };\n\n\n  private addModal: Modal | null = null;\n  private confirmModal: Modal | null = null;\nactions: any;\n\n  constructor(\n    private router: Router,\n    public authService: AuthenticationService,\n    private portefeuilleService: PortefeuilleService,\n    private cdr: ChangeDetectorRef,\n    private ngZone: NgZone\n  ) {}\n\n  ngAfterViewInit() {\n    feather.replace();\n    const addModalEl = document.getElementById('addPortefeuilleModal');\n    const confirmModalEl = document.getElementById('confirmModal');\n    if (addModalEl) this.addModal = new Modal(addModalEl);\n    if (confirmModalEl) this.confirmModal = new Modal(confirmModalEl);\n  }\n\n  ngOnInit(): void {\n    this.portefeuilleService.getAllPortefeuilles().subscribe({\n      next: (data) => {\n        this.portefeuilles = data;\n        console.log('Portefeuilles:', this.portefeuilles);\n      },\n      error: (err) => {\n        console.error('Erreur lors du chargement des portefeuilles', err);\n      }\n    });\n  }\n\n  openModal() {\n    this.addModal?.show();\n  }\n\nsubmitPortefeuille() {\n    // Log the data before sending it\n    console.log('Data being sent:', this.newPortefeuille); // Make sure the data matches the Postman request\n\n    this.portefeuilleService.createPortefeuille(this.newPortefeuille).subscribe({\n      next: () => {\n        alert('Portefeuille ajouté avec succès');\n        this.addModal?.hide();\n        this.ngOnInit();\n      },\n      error: (err) => {\n        console.error('Erreur lors de l\\'ajout du portefeuille', err);\n        alert('Erreur lors de l\\'ajout du portefeuille');\n      }\n    });\n  }\n\n\n  deletePortefeuille(id: number): void {\n    this.portefeuilleToDelete = id;\n    this.confirmModal?.show();\n  }\n\n  deleteConfirmed(): void {\n    if (this.portefeuilleToDelete !== null) {\n      this.portefeuilleService.deletePortefeuille(this.portefeuilleToDelete, { responseType: 'text' }).subscribe({\n        next: () => {\n          this.portefeuilles = this.portefeuilles.filter(\n            p => p.idPortefeuille !== this.portefeuilleToDelete\n          );\n          this.portefeuilleToDelete = null;\n          this.cdr.detectChanges();\n          this.confirmModal?.hide();\n        },\n        error: (err) => {\n          console.error('Erreur lors de la suppression du portefeuille', err);\n        }\n      });\n    }\n  }\n\n  closeModal(): void {\n    this.confirmModal?.hide();\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    this.router.navigate(['/login']);\n  }\n\n  trackById(index: number, item: Portefeuille): number {\n    return item.idPortefeuille;\n  }\n}\n", "<head>\n  <meta charset=\"utf-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n  <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n  <meta http-equiv=\"Pragma\" content=\"no-cache\">\n  <meta http-equiv=\"Expires\" content=\"0\">\n  <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n  <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n  <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n  <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n  <link href=\"./portefeuilles.component.css\" rel=\"stylesheet\">\n  <title>Dashboard</title>\n</head>\n\n<body>\n  <!-- Navbar -->\n  <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n    <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\">\n      <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n      GTI\n    </a>\n    <ul class=\"navbar-nav px-3\">\n      <li class=\"nav-item text-nowrap\">\n        <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n      </li>\n    </ul>\n  </nav>\n\n  <div class=\"container-fluid\">\n    <div class=\"row\">\n      <!-- Sidebar -->\n      <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n        <div class=\"sidebar-sticky\">\n          <ul class=\"nav flex-column\">\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/adminDash')\">\n              <a class=\"nav-link\" href=\"/adminDash\">\n                <span data-feather=\"home\"></span> Dashboard\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/users')\">\n              <a class=\"nav-link\" href=\"/users\">\n                <span data-feather=\"user\"></span> Gestion des utilisateurs\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/groups')\">\n              <a class=\"nav-link\" href=\"/groups\">\n                <span data-feather=\"grid\"></span> Gestion des groupes\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/transactions')\">\n              <a class=\"nav-link\" href=\"/transactions\">\n                <span data-feather=\"dollar-sign\"></span> Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/actions')\">\n              <a class=\"nav-link\" href=\"/actions\">\n                <span data-feather=\"trending-up\"></span> Gestion des actions\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/actionnaires')\">\n              <a class=\"nav-link\" href=\"/actionnaires\">\n                <span data-feather=\"users\"></span> Gestion des actionnaires\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/port')\">\n              <a class=\"nav-link\" href=\"/port\">\n                <span data-feather=\"book\"></span> Gestion des Portefeuilles\n              </a>\n            </li>\n          </ul>\n        </div>\n      </nav>\n\n      <!-- Main Content -->\n      <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n        <div class=\"d-flex justify-content-between align-items-center pb-2 mb-3 border-bottom\">\n          <h1 class=\"h2\">Gestion des portefeuilles</h1>\n        </div>\n\n        <div class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead class=\"thead-dark\">\n              <tr>\n                <th>Référence</th>\n                <th>Solde</th>\n                <th>Quantité</th>\n                <th>Date Création</th>\n                <th>ID Action</th>\n                <th>Action</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let p of portefeuilles; trackBy: trackById\">\n                <td>{{ p.idPortefeuille }}</td>\n                <td>{{ p.solde }}</td>\n                <td>{{ p.quantite }}</td>\n                <td>{{ p.dateCreation | date: 'short' }}</td>\n                <td>{{ p.action?.idAction }}</td>\n                <td>\n                  <button class=\"btn btn-danger btn-sm\" (click)=\"deletePortefeuille(p.idPortefeuille)\">\n                    Supprimer\n                  </button>\n                  <button class=\"btn btn-primary\" (click)=\"openModal()\">Ajouter</button>\n\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </main>\n    </div>\n  </div>\n\n\n <!-- Add Portefeuille Modal -->\n<div class=\"modal fade\" id=\"addPortefeuilleModal\" tabindex=\"-1\" aria-labelledby=\"addPortefeuilleModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <form (ngSubmit)=\"submitPortefeuille()\" #portefeuilleForm=\"ngForm\">\n        <div class=\"modal-header\">\n          <h5 class=\"modal-title\" id=\"addPortefeuilleModalLabel\">Ajouter un Portefeuille</h5>\n          <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\n        </div>\n        <div class=\"modal-body\">\n          <div class=\"mb-3\">\n            <label for=\"solde\" class=\"form-label\">Solde</label>\n            <input type=\"number\" id=\"solde\" class=\"form-control\" [(ngModel)]=\"newPortefeuille.solde\" name=\"solde\" required>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"quantite\" class=\"form-label\">Quantité</label>\n            <input type=\"number\" id=\"quantite\" class=\"form-control\" [(ngModel)]=\"newPortefeuille.quantite\" name=\"quantite\" required>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"idActionnaire\" class=\"form-label\">ID Actionnaire</label>\n            <input type=\"number\" id=\"idActionnaire\" class=\"form-control\" [(ngModel)]=\"newPortefeuille.actionnaire.idActionnaire\" name=\"idActionnaire\" required>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"idAction\" class=\"form-label\">ID Action</label>\n            <input type=\"number\" id=\"idAction\" class=\"form-control\" [(ngModel)]=\"newPortefeuille.action.idAction\" name=\"idAction\" required>\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button type=\"submit\" class=\"btn btn-success\">Ajouter</button>\n          <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Fermer</button>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n\n\n\n<!-- Confirmation Modal -->\n<div class=\"modal fade\" id=\"confirmModal\" tabindex=\"-1\" aria-labelledby=\"confirmModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"confirmModalLabel\">Confirmer la suppression</h5>\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\n      </div>\n      <div class=\"modal-body\">\n        Voulez-vous vraiment supprimer ce portefeuille ?\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Annuler</button>\n        <button type=\"button\" class=\"btn btn-danger\" (click)=\"deleteConfirmed()\">Confirmer</button>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n  <!-- Scripts -->\n  <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js\"></script>\n  <script src=\"https://unpkg.com/feather-icons\"></script>\n  <script>feather.replace();</script>\n</body>\n"], "mappings": "AAUA,OAAO,KAAKA,OAAO,MAAM,eAAe;AACxC,SAASC,KAAK,QAAQ,WAAW,CAAC,CAAC;;;;;;;;;ICwBvBC,EAAA,CAAAC,cAAA,aAAsE;IAElED,EAAA,CAAAE,SAAA,eAAiC;IAACF,EAAA,CAAAG,MAAA,kBACpC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAkE;IAE9DD,EAAA,CAAAE,SAAA,eAAiC;IAACF,EAAA,CAAAG,MAAA,iCACpC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAmE;IAE/DD,EAAA,CAAAE,SAAA,eAAiC;IAACF,EAAA,CAAAG,MAAA,4BACpC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAyE;IAErED,EAAA,CAAAE,SAAA,eAAwC;IAACF,EAAA,CAAAG,MAAA,iCAC3C;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAoE;IAEhED,EAAA,CAAAE,SAAA,eAAwC;IAACF,EAAA,CAAAG,MAAA,4BAC3C;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAyE;IAErED,EAAA,CAAAE,SAAA,eAAkC;IAACF,EAAA,CAAAG,MAAA,iCACrC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAiE;IAE7DD,EAAA,CAAAE,SAAA,eAAiC;IAACF,EAAA,CAAAG,MAAA,kCACpC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAyBJJ,EAAA,CAAAC,cAAA,SAAwD;IAClDD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAoC;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjCJ,EAAA,CAAAC,cAAA,UAAI;IACoCD,EAAA,CAAAK,UAAA,mBAAAC,+DAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,IAAA;MAAA,MAAAC,IAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,OAAA,CAAAG,kBAAA,CAAAL,IAAA,CAAAM,cAAA,CAAoC;IAAA,EAAC;IAClFhB,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAsD;IAAtBD,EAAA,CAAAK,UAAA,mBAAAY,+DAAA;MAAAjB,EAAA,CAAAQ,aAAA,CAAAC,IAAA;MAAA,MAAAS,OAAA,GAAAlB,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAI,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAACnB,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IATpEJ,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAqB,iBAAA,CAAAX,IAAA,CAAAM,cAAA,CAAsB;IACtBhB,EAAA,CAAAoB,SAAA,GAAa;IAAbpB,EAAA,CAAAqB,iBAAA,CAAAX,IAAA,CAAAY,KAAA,CAAa;IACbtB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAqB,iBAAA,CAAAX,IAAA,CAAAa,QAAA,CAAgB;IAChBvB,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAwB,WAAA,OAAAd,IAAA,CAAAe,YAAA,WAAoC;IACpCzB,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAAqB,iBAAA,CAAAX,IAAA,CAAAgB,MAAA,kBAAAhB,IAAA,CAAAgB,MAAA,CAAAC,QAAA,CAAwB;;;;ADhF5C,OAAM,MAAOC,sBAAsB;EAqCjCC,YACUC,MAAc,EACfC,WAAkC,EACjCC,mBAAwC,EACxCC,GAAsB,EACtBC,MAAc;IAJd,KAAAJ,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IAzChB,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,oBAAoB,GAAkB,IAAI;IAE1C;IACD,KAAAC,eAAe,GAAiB;MAC7BrB,cAAc,EAAE,CAAC;MACjBM,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE,CAAC;MACXE,YAAY,EAAE,EAAE;MAChBa,gBAAgB,EAAE,EAAE;MACpBC,YAAY,EAAE,EAAE;MAChBb,MAAM,EAAE;QACNC,QAAQ,EAAE,CAAC;QACXa,UAAU,EAAE,EAAE;QACdC,UAAU,EAAE,EAAE;QACdC,IAAI,EAAE,CAAC;QACPjB,YAAY,EAAE,EAAE;QAChBU,aAAa,EAAE,EAAE,CAAC;OACnB;;MACDQ,WAAW,EAAE;QACXC,aAAa,EAAE,CAAC;QAChBC,cAAc,EAAE,EAAE;QAClBC,iBAAiB,EAAE,EAAE;QACrBC,gBAAgB,EAAE,EAAE;QACpBC,SAAS,EAAE,CAAC;QACZvB,YAAY,EAAE,EAAE;QAChBa,gBAAgB,EAAE,EAAE;QACpBH,aAAa,EAAE,EAAE,CAAC;;KAErB;;IAGO,KAAAc,QAAQ,GAAiB,IAAI;IAC7B,KAAAC,YAAY,GAAiB,IAAI;EAStC;EAEHC,eAAeA,CAAA;IACbrD,OAAO,CAACsD,OAAO,EAAE;IACjB,MAAMC,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,CAAC;IAClE,MAAMC,cAAc,GAAGF,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;IAC9D,IAAIF,UAAU,EAAE,IAAI,CAACJ,QAAQ,GAAG,IAAIlD,KAAK,CAACsD,UAAU,CAAC;IACrD,IAAIG,cAAc,EAAE,IAAI,CAACN,YAAY,GAAG,IAAInD,KAAK,CAACyD,cAAc,CAAC;EACnE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACzB,mBAAmB,CAAC0B,mBAAmB,EAAE,CAACC,SAAS,CAAC;MACvDC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAAC1B,aAAa,GAAG0B,IAAI;QACzBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC5B,aAAa,CAAC;MACnD,CAAC;MACD6B,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,6CAA6C,EAAEC,GAAG,CAAC;MACnE;KACD,CAAC;EACJ;EAEA9C,SAASA,CAAA;IACP,IAAI,CAAC8B,QAAQ,EAAEiB,IAAI,EAAE;EACvB;EAEFC,kBAAkBA,CAAA;IACd;IACAL,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC1B,eAAe,CAAC,CAAC,CAAC;IAEvD,IAAI,CAACL,mBAAmB,CAACoC,kBAAkB,CAAC,IAAI,CAAC/B,eAAe,CAAC,CAACsB,SAAS,CAAC;MAC1EC,IAAI,EAAEA,CAAA,KAAK;QACTS,KAAK,CAAC,iCAAiC,CAAC;QACxC,IAAI,CAACpB,QAAQ,EAAEqB,IAAI,EAAE;QACrB,IAAI,CAACb,QAAQ,EAAE;MACjB,CAAC;MACDO,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;QAC7DI,KAAK,CAAC,yCAAyC,CAAC;MAClD;KACD,CAAC;EACJ;EAGAtD,kBAAkBA,CAACwD,EAAU;IAC3B,IAAI,CAACnC,oBAAoB,GAAGmC,EAAE;IAC9B,IAAI,CAACrB,YAAY,EAAEgB,IAAI,EAAE;EAC3B;EAEAM,eAAeA,CAAA;IACb,IAAI,IAAI,CAACpC,oBAAoB,KAAK,IAAI,EAAE;MACtC,IAAI,CAACJ,mBAAmB,CAACjB,kBAAkB,CAAC,IAAI,CAACqB,oBAAoB,EAAE;QAAEqC,YAAY,EAAE;MAAM,CAAE,CAAC,CAACd,SAAS,CAAC;QACzGC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACzB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACuC,MAAM,CAC5CC,CAAC,IAAIA,CAAC,CAAC3D,cAAc,KAAK,IAAI,CAACoB,oBAAoB,CACpD;UACD,IAAI,CAACA,oBAAoB,GAAG,IAAI;UAChC,IAAI,CAACH,GAAG,CAAC2C,aAAa,EAAE;UACxB,IAAI,CAAC1B,YAAY,EAAEoB,IAAI,EAAE;QAC3B,CAAC;QACDN,KAAK,EAAGC,GAAG,IAAI;UACbH,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEC,GAAG,CAAC;QACrE;OACD,CAAC;;EAEN;EAEAY,UAAUA,CAAA;IACR,IAAI,CAAC3B,YAAY,EAAEoB,IAAI,EAAE;EAC3B;EAEAQ,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAAClD,MAAM,CAACmD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,SAASA,CAACC,KAAa,EAAEC,IAAkB;IACzC,OAAOA,IAAI,CAACpE,cAAc;EAC5B;;;uBA3HWY,sBAAsB,EAAA5B,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAvF,EAAA,CAAAqF,iBAAA,CAAAG,EAAA,CAAAC,qBAAA,GAAAzF,EAAA,CAAAqF,iBAAA,CAAAK,EAAA,CAAAC,mBAAA,GAAA3F,EAAA,CAAAqF,iBAAA,CAAArF,EAAA,CAAA4F,iBAAA,GAAA5F,EAAA,CAAAqF,iBAAA,CAAArF,EAAA,CAAA6F,MAAA;IAAA;EAAA;;;YAAtBjE,sBAAsB;MAAAkE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBnCpG,EAAA,CAAAC,cAAA,WAAM;UACJD,EAAA,CAAAE,SAAA,cAAsB;UAWtBF,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAG1BJ,EAAA,CAAAC,cAAA,YAAM;UAIAD,EAAA,CAAAE,SAAA,eAAwE;UACxEF,EAAA,CAAAG,MAAA,aACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAK,UAAA,mBAAAiG,oDAAA;YAAA,OAASD,GAAA,CAAAvB,MAAA,EAAQ;UAAA,EAAC;UAAC9E,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAKzDJ,EAAA,CAAAC,cAAA,eAA6B;UAMnBD,EAAA,CAAAuG,UAAA,KAAAC,qCAAA,iBAIK;UACLxG,EAAA,CAAAuG,UAAA,KAAAE,qCAAA,iBAIK;UACLzG,EAAA,CAAAuG,UAAA,KAAAG,qCAAA,iBAIK;UACL1G,EAAA,CAAAuG,UAAA,KAAAI,qCAAA,iBAIK;UACL3G,EAAA,CAAAuG,UAAA,KAAAK,qCAAA,iBAIK;UACL5G,EAAA,CAAAuG,UAAA,KAAAM,qCAAA,iBAIK;UACL7G,EAAA,CAAAuG,UAAA,KAAAO,qCAAA,iBAIK;UACP9G,EAAA,CAAAI,YAAA,EAAK;UAKTJ,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAG,MAAA,iCAAyB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAG/CJ,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAG,MAAA,2BAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,qBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,0BAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACtBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGnBJ,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAuG,UAAA,KAAAQ,qCAAA,kBAaK;UACP/G,EAAA,CAAAI,YAAA,EAAQ;UASpBJ,EAAA,CAAAC,cAAA,eAA+H;UAGnHD,EAAA,CAAAK,UAAA,sBAAA2G,0DAAA;YAAA,OAAYX,GAAA,CAAAlC,kBAAA,EAAoB;UAAA,EAAC;UACrCnE,EAAA,CAAAC,cAAA,eAA0B;UAC+BD,EAAA,CAAAG,MAAA,+BAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnFJ,EAAA,CAAAE,SAAA,kBAA6F;UAC/FF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAwB;UAEkBD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACnDJ,EAAA,CAAAC,cAAA,iBAA+G;UAA1DD,EAAA,CAAAK,UAAA,2BAAA4G,gEAAAC,MAAA;YAAA,OAAAb,GAAA,CAAAhE,eAAA,CAAAf,KAAA,GAAA4F,MAAA;UAAA,EAAmC;UAAxFlH,EAAA,CAAAI,YAAA,EAA+G;UAEjHJ,EAAA,CAAAC,cAAA,eAAkB;UACyBD,EAAA,CAAAG,MAAA,qBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACzDJ,EAAA,CAAAC,cAAA,iBAAwH;UAAhED,EAAA,CAAAK,UAAA,2BAAA8G,gEAAAD,MAAA;YAAA,OAAAb,GAAA,CAAAhE,eAAA,CAAAd,QAAA,GAAA2F,MAAA;UAAA,EAAsC;UAA9FlH,EAAA,CAAAI,YAAA,EAAwH;UAE1HJ,EAAA,CAAAC,cAAA,eAAkB;UAC8BD,EAAA,CAAAG,MAAA,sBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACpEJ,EAAA,CAAAC,cAAA,iBAAmJ;UAAtFD,EAAA,CAAAK,UAAA,2BAAA+G,gEAAAF,MAAA;YAAA,OAAAb,GAAA,CAAAhE,eAAA,CAAAM,WAAA,CAAAC,aAAA,GAAAsE,MAAA;UAAA,EAAuD;UAApHlH,EAAA,CAAAI,YAAA,EAAmJ;UAErJJ,EAAA,CAAAC,cAAA,eAAkB;UACyBD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC1DJ,EAAA,CAAAC,cAAA,iBAA+H;UAAvED,EAAA,CAAAK,UAAA,2BAAAgH,gEAAAH,MAAA;YAAA,OAAAb,GAAA,CAAAhE,eAAA,CAAAX,MAAA,CAAAC,QAAA,GAAAuF,MAAA;UAAA,EAA6C;UAArGlH,EAAA,CAAAI,YAAA,EAA+H;UAGnIJ,EAAA,CAAAC,cAAA,eAA0B;UACsBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAC9DJ,EAAA,CAAAC,cAAA,kBAAwE;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAUjGJ,EAAA,CAAAC,cAAA,eAA+G;UAIxDD,EAAA,CAAAG,MAAA,gCAAwB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC5EJ,EAAA,CAAAE,SAAA,kBAA6F;UAC/FF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAG,MAAA,0DACF;UAAAH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAA0B;UACgDD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACxFJ,EAAA,CAAAC,cAAA,kBAAyE;UAA5BD,EAAA,CAAAK,UAAA,mBAAAiH,yDAAA;YAAA,OAASjB,GAAA,CAAA7B,eAAA,EAAiB;UAAA,EAAC;UAACxE,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAS;;;UAnIjEJ,EAAA,CAAAoB,SAAA,IAA8C;UAA9CpB,EAAA,CAAAuH,UAAA,SAAAlB,GAAA,CAAAtE,WAAA,CAAAyF,cAAA,eAA8C;UAK9CxH,EAAA,CAAAoB,SAAA,GAA0C;UAA1CpB,EAAA,CAAAuH,UAAA,SAAAlB,GAAA,CAAAtE,WAAA,CAAAyF,cAAA,WAA0C;UAK1CxH,EAAA,CAAAoB,SAAA,GAA2C;UAA3CpB,EAAA,CAAAuH,UAAA,SAAAlB,GAAA,CAAAtE,WAAA,CAAAyF,cAAA,YAA2C;UAK3CxH,EAAA,CAAAoB,SAAA,GAAiD;UAAjDpB,EAAA,CAAAuH,UAAA,SAAAlB,GAAA,CAAAtE,WAAA,CAAAyF,cAAA,kBAAiD;UAKjDxH,EAAA,CAAAoB,SAAA,GAA4C;UAA5CpB,EAAA,CAAAuH,UAAA,SAAAlB,GAAA,CAAAtE,WAAA,CAAAyF,cAAA,aAA4C;UAK5CxH,EAAA,CAAAoB,SAAA,GAAiD;UAAjDpB,EAAA,CAAAuH,UAAA,SAAAlB,GAAA,CAAAtE,WAAA,CAAAyF,cAAA,kBAAiD;UAKjDxH,EAAA,CAAAoB,SAAA,GAAyC;UAAzCpB,EAAA,CAAAuH,UAAA,SAAAlB,GAAA,CAAAtE,WAAA,CAAAyF,cAAA,UAAyC;UA4B3CxH,EAAA,CAAAoB,SAAA,IAAkB;UAAlBpB,EAAA,CAAAuH,UAAA,YAAAlB,GAAA,CAAAlE,aAAA,CAAkB,iBAAAkE,GAAA,CAAAnB,SAAA;UAkCelF,EAAA,CAAAoB,SAAA,IAAmC;UAAnCpB,EAAA,CAAAuH,UAAA,YAAAlB,GAAA,CAAAhE,eAAA,CAAAf,KAAA,CAAmC;UAIhCtB,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAuH,UAAA,YAAAlB,GAAA,CAAAhE,eAAA,CAAAd,QAAA,CAAsC;UAIjCvB,EAAA,CAAAoB,SAAA,GAAuD;UAAvDpB,EAAA,CAAAuH,UAAA,YAAAlB,GAAA,CAAAhE,eAAA,CAAAM,WAAA,CAAAC,aAAA,CAAuD;UAI5D5C,EAAA,CAAAoB,SAAA,GAA6C;UAA7CpB,EAAA,CAAAuH,UAAA,YAAAlB,GAAA,CAAAhE,eAAA,CAAAX,MAAA,CAAAC,QAAA,CAA6C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}