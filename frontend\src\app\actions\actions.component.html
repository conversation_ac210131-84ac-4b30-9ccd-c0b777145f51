
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
<link rel="icon" href="/docs/4.0/assets/img/favicons/favicon.ico">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <title>Dashboard Template for Bootstrap</title>

    <link rel="canonical" href="https://getbootstrap.com/docs/4.0/examples/dashboard/">
    <!-- Bootstrap core CSS -->

    <!-- Custom styles for this template -->
    <link href="./actions.component.css" rel="stylesheet">

  </head>

  <body>
    
     <!-- Navbar -->
     <nav class="navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0">
      <a class="navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center">
        <img src="assets/images/gti.jpg" alt="GTI Logo" class="logo-img mr-2" />
        GTI
      </a>
      <ul class="navbar-nav px-3">
        <li class="nav-item text-nowrap">
          <a class="nav-link" (click)="logout()">Sign out</a>
        </li>
      </ul>
    </nav>
    <div class="container-fluid">
      <div class="row">
        <nav class="col-md-2 d-none d-md-block custom-sidebar sidebar">
          <div class="sidebar-sticky">
            <ul class="nav flex-column">
              <li class="nav-item">
                <a class="nav-link" href="/adminDash" *ngIf="authService.isRouteAllowed('/adminDash')">
                  <span data-feather="home"></span>
                  Dashboard <span class="sr-only">(current)</span>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/users" *ngIf="authService.isRouteAllowed('/users')">
                  <span data-feather="user"></span>
                  Gestion des utilisateurs
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/groups" *ngIf="authService.isRouteAllowed('/groups')">
                  <span data-feather="grid"></span>
                  Gestion des groupes
                </a>
              </li>
            
            <li class="nav-item">
              <a class="nav-link" href="/transactions" *ngIf="authService.isRouteAllowed('/transactions')">
                <span data-feather="dollar-sign"></span>
                Gestion des transactions
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/actions" *ngIf="authService.isRouteAllowed('/actions')">
                <span data-feather="trending-up"></span>
               Gestion des actions
              </a>
              <li class="nav-item">
                <a class="nav-link" href="/actionnaires" *ngIf="authService.isRouteAllowed('/actionnaires')">
                  <span data-feather="users"></span>
                 Gestion des actionnaires
                </a>
                     <li class="nav-item">
              <a class="nav-link" href="/port" *ngIf="authService.isRouteAllowed('/port')">
                <span data-feather="book"></span>
               Gestion des Portefeuilles
              </a>
            </ul>
          </div>
        </nav>

        
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 pt-3 px-4">
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">Gestion des Actions</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <div class="btn-group mr-2">

              </div>
            </div>
          </div>
               <div class="d-flex justify-content-end mb-2">
<button type="button" class="btn btn-custom" data-toggle="modal" data-target="#addActionModal">
  Ajouter un Action
</button>
</div>
   <div class="table-responsive">

  <table class="table table-hover">

    
    <thead class="thead-dark">
      <tr>
        <th>ISIN</th>
        <th>Nom Société</th>
        <th>Prix</th>
        <th>Date de Création</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let action of actions">
        <td>{{ action.isinAction }}</td>
        <td>{{ action.nomSociete }}</td>
        <td>{{ action.prix }}</td>
        <td>{{ action.dateCreation | date: 'short' }}</td>
        <td>
          <button class="btn btn-danger btn-sm" (click)="deleteAction(action.idAction)" title="Supprimer cette action">
            <i class="fas fa-trash-alt"></i> Supprimer
          </button>
        </td>
      </tr>
    </tbody>
  </table>
</div>


          <canvas class="my-4" id="myChart" width="900" height="380"></canvas>
          
        
        </main>
      </div>
    </div>


<!-- Add Action Modal -->
<div class="modal fade" id="addActionModal" tabindex="-1" aria-labelledby="addActionModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <form (ngSubmit)="submitAction()" #actionForm="ngForm">
        <div class="modal-header">
          <h5 class="modal-title" id="addActionModalLabel">Ajouter une Action</h5>
        </div>
        <div class="modal-body">

          <div class="mb-3">
            <label for="isinAction" class="form-label">ISIN Action</label>
            <input type="text" id="isinAction" class="form-control" [(ngModel)]="newAction.isinAction" name="isinAction" required>
          </div>

          <div class="mb-3">
            <label for="nomSociete" class="form-label">Nom de la Société</label>
            <input type="text" id="nomSociete" class="form-control" [(ngModel)]="newAction.nomSociete" name="nomSociete" required>
          </div>

          <div class="mb-3">
            <label for="prix" class="form-label">Prix</label>
            <input type="number" id="prix" class="form-control" [(ngModel)]="newAction.prix" name="prix" required>
          </div>

        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-success">Ajouter</button>
        </div>
      </form>
    </div>
  </div>
</div>

    <!-- Bootstrap core JavaScript
    ================================================== -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
    <script>window.jQuery || document.write('<script src="../../assets/js/vendor/jquery-slim.min.js"><\/script>')</script>
    <script src="../../assets/js/vendor/popper.min.js"></script>
    <script src="../../dist/js/bootstrap.min.js"></script>

    <!-- Icons -->
    <script src="https://unpkg.com/feather-icons/dist/feather.min.js"></script>
    <script>    

    </script>
  </body>
</html>
