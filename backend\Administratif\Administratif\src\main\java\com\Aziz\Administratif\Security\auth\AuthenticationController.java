package com.Aziz.Administratif.Security.auth;


import com.Aziz.Administratif.ClientRelation.ClientClient;
import com.Aziz.Administratif.ClientRelation.Entity.Action;
import com.Aziz.Administratif.ClientRelation.Entity.Actionnaire;
import com.Aziz.Administratif.ClientRelation.Entity.Portefeuille;
import com.Aziz.Administratif.ClientRelation.Entity.Transaction;
import com.Aziz.Administratif.Services.*;
import com.Aziz.Administratif.Entity.Groupe;
import com.Aziz.Administratif.Entity.Ressource;
import com.Aziz.Administratif.Entity.User;
import com.Aziz.Administratif.Enum.Role;
import com.Aziz.Administratif.Services.*;
import feign.FeignException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
public class AuthenticationController {
    private final AuthenticationService service;
    private final UserService userService;
    private final com.Aziz.Administratif.Services.GroupeService GroupeService;
    private final com.Aziz.Administratif.Repositories.UserRepository UserRepository;
    private final com.Aziz.Administratif.Repositories.GroupeRepository GroupeRepository;
    private final EmailService emailService;
    private final IpLocationService ipLocationService;
    private final ClientClient clientClient;

    @Autowired
    private ExcelService excelService;


    private final com.Aziz.Administratif.Repositories.HabilitationRepository HabilitationRepository;

    @CrossOrigin(origins = "http://localhost:4200")
    @PostMapping("/register")
    public ResponseEntity<?> register(@RequestBody RegisterRequest request) {
        AuthenticationResponse response = service.register(request);

        if (response.getToken() == null || response.getToken().isEmpty()) {
            return ResponseEntity.badRequest().body("Échec de l'inscription : Token non généré !");
        } else {
            return ResponseEntity.ok(response);
        }
    }


    @CrossOrigin(origins = "http://localhost:4200")
    @PostMapping("/authenticate")
    public ResponseEntity<AuthenticationResponse> authenticate(
            @RequestBody AuthenticationRequest request, HttpServletRequest request1
    ) {
        User user = UserRepository.findByMatricule(request.getMatricule());

//         IpLocationService ipLocationService ;
        String ipAddress = ipLocationService.getIp();
        String location = ipLocationService.getLocation(ipAddress);
//        String ipAddress = request1.getRemoteAddr(); // Or getHeader("X-Forwarded-For") if behind proxy
//        String location = "N/A"; // Optional: Use an external API to resolve IP location
        String dateTime = LocalDateTime.now().toString();
        emailService.sendAuthentificationEmail(user.getEmail(), user.getNom(), ipAddress, dateTime, location);
        //.orElseThrow(() -> new UsernameNotFoundException("User not found"));
        return ResponseEntity.ok(service.authenticate(request, request1));
    }

    @CrossOrigin(origins = "http://localhost:4200")
    @PostMapping("/resetPassword/send_request_to_Admin")
    public ResponseEntity<?> resetPasswordRequestToAdmin(@RequestBody PasswordResetRequest requ) {

        // userService.ResetPasswordRequestToAdmin(requ.getMatricule(), requ.getEmail());

        return ResponseEntity
                .status(HttpStatus.OK)
                .body(userService.ResetPasswordRequestToAdmin(requ.getMatricule(), requ.getEmail()));
    }


    @CrossOrigin(origins = "http://localhost:4200")
    @PostMapping("/resetPassword/generate_new_password")
    public ResponseEntity<?> adminGeneratesPassword(@RequestBody PasswordResetRequest requ) {


        return ResponseEntity
                .status(HttpStatus.OK)
                .body(userService.adminGeneratesPassword(requ.getMatricule(), requ.getEmail()));
    }


    @CrossOrigin(origins = "http://localhost:4200")
    @PreAuthorize("hasRole('ROLE_ADMIN')")  // Only users with ADMIN role can add Responsables
    @PostMapping("/Users_Responsables/ajout")  // Changed the path to match other endpoints
    public ResponseEntity<User> addResponsable(@RequestBody User responsable, Long idGroupe, Long idRessource) {
        responsable.setRole(Role.RESPONSABLE);
        userService.saveUser(responsable, idGroupe, idRessource);
        return ResponseEntity.status(HttpStatus.CREATED).body(responsable);
    }

    @CrossOrigin(origins = "http://localhost:4200")
    @GetMapping("/Users")
    public ResponseEntity<List<Map<String, Object>>> findAllUsers(@RequestParam(required = false) String id) {
        try {
            List<User> users = userService.findAllUsers();

            List<Map<String, Object>> simplifiedUsers = users.stream().map(user -> {
                Map<String, Object> userMap = new HashMap<>();
                userMap.put("id", user.getId());
                userMap.put("nom", user.getNom());
                userMap.put("prenom", user.getPrenom());
                userMap.put("email", user.getEmail());
                userMap.put("telephone", user.getTelephone());
                userMap.put("role", user.getRole());
                userMap.put("Matricule", user.getMatricule());
                userMap.put("state", user.getState());
                userMap.put("flag", user.getFlag());


                if (user.getGroupe() != null) {
                    userMap.put("groupe", Map.of("nomGroupe", user.getGroupe().getNomGroupe()));
                }

                // Add idRessource if habilitation and ressource are not null
                if (user.getHabilitation() != null && user.getHabilitation().getRessource() != null) {
                    userMap.put("nom_ressource", user.getHabilitation().getRessource().getNomRessource());
                }

                return userMap;
            }).collect(Collectors.toList());

            return ResponseEntity.ok(simplifiedUsers);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }


    @CrossOrigin(origins = "http://localhost:4200")
    @GetMapping("/Users_Responsables")
    public ResponseEntity<List<Map<String, Object>>> findAllResponsables(@RequestParam(required = false) String id) {
        try {
            Role role = Role.RESPONSABLE;
            List<User> responsables = userService.findAllByRole(role);

            // Map the list of users to a simplified version containing only the required fields
            List<Map<String, Object>> simplifiedResponsables = responsables.stream().map(user -> {
                Map<String, Object> userMap = new HashMap<>();
                userMap.put("id", user.getId());
                userMap.put("nom", user.getNom());
                userMap.put("prenom", user.getPrenom());
                userMap.put("email", user.getEmail());
                userMap.put("telephone", user.getTelephone());
                userMap.put("role", user.getRole());
                userMap.put("Matricule", user.getMatricule());
                userMap.put("ressource", user.getHabilitation().getRessource());

                // Include only the nomGroupe from the Groupe
                if (user.getGroupe() != null) {
                    userMap.put("groupe", Map.of("nomGroupe", user.getGroupe().getNomGroupe()));
                }

                return userMap;
            }).collect(Collectors.toList());

            return ResponseEntity.ok(simplifiedResponsables);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);  // Handle any errors
        }
    }

    @CrossOrigin(origins = "http://localhost:4200")
    @PutMapping("/{id}/toggle-state")
    public ResponseEntity<User> toggleUserState(@PathVariable Long id) {
        User updatedUser = userService.toggleUserState(id);
        if (updatedUser != null) {
            Boolean S = updatedUser.getState();
            emailService.BlockEmail(updatedUser.getEmail(), updatedUser.getNom(), S);

            return ResponseEntity.ok(updatedUser);

        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);  // User not found
        }
    }


    @CrossOrigin(origins = "http://localhost:4200")
    @DeleteMapping("/Users_Responsables/delete/{id}")
    public ResponseEntity<String> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return ResponseEntity.ok("User deleted successfully");
    }

    @PutMapping("/Users_Responsables/edit/{id}")
    public ResponseEntity<Map<String, Object>> updateUser(@PathVariable Long id, @RequestBody User updatedUser, Principal principal) {
        try {
            User updated = userService.updateUser(id, updatedUser);

            // Wrap the response in a JSON object to avoid empty response issues
            Map<String, Object> response = new HashMap<>();
            response.put("message", "User updated successfully");
            response.put("user", updated);

            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Collections.singletonMap("error", "User not found"));
        }
    }


    @PutMapping("/Users_Responsables/editstate/{id}")
    public ResponseEntity<Map<String, Object>> updateUserState(@PathVariable Long id, @RequestBody User updatedUser, Principal principal) {
        try {
            User updated = userService.updateUserState(id, updatedUser);

            // Wrap the response in a JSON object to avoid empty response issues
            Map<String, Object> response = new HashMap<>();
            response.put("message", "User updated successfully");
            response.put("user", updated);

            return ResponseEntity.ok(response);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Collections.singletonMap("error", "User not found"));
        }
    }


    @CrossOrigin(origins = "http://localhost:4200")
    @GetMapping("/Users_Responsables/{id}")
    public ResponseEntity<Map<String, Object>> getResponsableById(@PathVariable Long id) {
        try {
            User responsable = userService.findById(id)
                    .orElseThrow(() -> new RuntimeException("Responsable not found"));

            Map<String, Object> userMap = new HashMap<>();
            userMap.put("id", responsable.getId());
            userMap.put("nom", responsable.getNom());
            userMap.put("prenom", responsable.getPrenom());
            userMap.put("email", responsable.getEmail());
            userMap.put("telephone", responsable.getTelephone());
            userMap.put("role", responsable.getRole());

            // Groupe info
            if (responsable.getGroupe() != null) {
                userMap.put("groupe", Map.of("nomGroupe", responsable.getGroupe().getNomGroupe()));
            }

            // Habilitation info
            if (responsable.getHabilitation() != null) {
                Map<String, Object> habMap = new HashMap<>();
                habMap.put("id", responsable.getHabilitation().getId());
                habMap.put("idGroupe", responsable.getHabilitation().getIdGroupe());

                // Optionally include resource info
                if (responsable.getHabilitation().getRessource() != null) {
                    habMap.put("ressource", Map.of(
                            "idRessource", responsable.getHabilitation().getRessource().getIdRessource(),
                            "nomRessource", responsable.getHabilitation().getRessource().getNomRessource(),
                            "link_path", responsable.getHabilitation().getRessource().getLink_path()
                    ));
                }

                userMap.put("habilitation", habMap);
            }

            return ResponseEntity.ok(userMap);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }


    // CRUD for Groupes
    @RestController
    @RequestMapping("/api/v1/auth/Groupes")
    @RequiredArgsConstructor
    public class XGroupeController {

        private final GroupeService groupeService;

        @CrossOrigin(origins = "http://localhost:4200")
        @GetMapping("/ALLGroupes")
        public ResponseEntity<List<Map<String, Object>>> findAllGroupes() {
            List<Groupe> groupes = groupeService.findAllGroupes();

            // Simplify the Groupes and include their Ressources
            List<Map<String, Object>> simplifiedGroupes = groupes.stream()
                    .map(groupe -> {
                        Map<String, Object> groupeMap = new HashMap<>();
                        groupeMap.put("idGroupe", groupe.getIdGroupe());
                        groupeMap.put("nomGroupe", groupe.getNomGroupe());
                        groupeMap.put("ressources", groupe.getRessources());  // Include associated Ressources
                        return groupeMap;
                    })
                    .collect(Collectors.toList());

            return ResponseEntity.ok(simplifiedGroupes);
        }

        @CrossOrigin(origins = "http://localhost:4200")
        @PostMapping("/ajout")
        public ResponseEntity<Groupe> addGroupe(@RequestBody GroupeRequest groupRequest) {
            // Call the service to save the group along with associated ressources
            Groupe groupe = groupeService.saveGroupeWithRessources(groupRequest.getNomGroupe(), groupRequest.getRessources());

            // Return the created group
            return ResponseEntity.status(HttpStatus.CREATED).body(groupe);
        }

        @CrossOrigin(origins = "http://localhost:4200")
        @DeleteMapping("/delete/{idGroupe}")
        public ResponseEntity<String> deleteGroupe(@PathVariable Long idGroupe) {
            try {
                groupeService.deleteGroupe(idGroupe);
                return ResponseEntity.ok("Groupe deleted successfully");
            } catch (RuntimeException e) {
                if (e.getMessage().contains("linked to users")) {
                    return ResponseEntity.status(HttpStatus.CONFLICT)
                            .body("❌ Cannot delete group: it is linked to users.");
                } else {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body("Error deleting Groupe: " + e.getMessage());
                }
            }
        }

        @PutMapping("/edit/{idGroupe}")
        public ResponseEntity<Groupe> updateGroupe(@PathVariable Long idGroupe, @RequestBody GroupeRequest request) {
            try {
                Groupe updatedGroupe = groupeService.updateGroupeWithRessources(
                        idGroupe,
                        request.getNomGroupe(),
                        request.getRessources()
                );
                return ResponseEntity.ok(updatedGroupe);
            } catch (RuntimeException e) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
            }
        }


        @CrossOrigin(origins = "http://localhost:4200")
        @GetMapping("/Groupes/find/{idGroupe}")
        public ResponseEntity<Map<String, Object>> findGroupeById(@PathVariable Long idGroupe) {
            Optional<Groupe> groupeOpt = groupeService.findGroupeById(idGroupe);

            if (groupeOpt.isPresent()) {
                Groupe groupe = groupeOpt.get();

                Map<String, Object> groupeMap = new HashMap<>();
                groupeMap.put("idGroupe", groupe.getIdGroupe());
                groupeMap.put("nomGroupe", groupe.getNomGroupe());
                groupeMap.put("ressources", groupe.getRessources());

                return ResponseEntity.ok(groupeMap);
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Collections.singletonMap("error", "Groupe not found"));
            }
        }
    }

// CRUD for Ressources

    @RestController
    @RequestMapping("/api/v1/auth/Ressources")
    @RequiredArgsConstructor
    public class RessourceController {

        private final RessourceService ressourceService;

        @CrossOrigin(origins = "http://localhost:4200")
        @GetMapping("/all")
        public ResponseEntity<List<Ressource>> findAllRessources() {
            List<Ressource> ressources = ressourceService.findAllRessources();

            // Return simplified list (no Habilitations)
            List<Ressource> simplifiedRessources = ressources.stream()
                    .map(ress -> Ressource.builder()
                            .idRessource(ress.getIdRessource())
                            .nomRessource(ress.getNomRessource())
                            .link_path(ress.getLink_path())
                            .build())
                    .collect(Collectors.toList());

            return ResponseEntity.ok(simplifiedRessources);
        }

        @CrossOrigin(origins = "http://localhost:4200")
        @GetMapping("/{id}")
        public ResponseEntity<Ressource> findRessourceById(@PathVariable("id") Long idRessource) {
            Ressource ressource = ressourceService.FindRessourceById(idRessource);
            return ResponseEntity.ok(ressource);
        }

    }


    @GetMapping("/Transaction/All_Transactions")
    public ResponseEntity<List<Transaction>> getAllTransactions() {
        return clientClient.getAllTransactions();
    }

    @PostMapping("/upload-excel")
    public ResponseEntity<String> uploadExcel(@RequestParam("file") MultipartFile file, Principal principal) {
        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body("File is empty.");
        }

        Long userId = service.getCurrentUserId();

        try {
            String result = excelService.sendExcelFile(file, userId);
            return ResponseEntity.ok(result + userId);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error while processing file: " + e.getMessage());
        }
    }


    @GetMapping("/{user-id}/transactions")
    public ResponseEntity<List<Transaction>> findTransactionsByUser(@PathVariable("user-id") Long userId) {
        List<Transaction> transactions = clientClient.findTransactionsByUser(userId);
        return ResponseEntity.ok(clientClient.findTransactionsByUser(userId));
        //  return ResponseEntity.ok(transactions);
    }


    //relation avec Action

    @GetMapping(value = "/Action/All_Actions")
    public ResponseEntity<List<Action>> getAllActions() {
        return clientClient.getAllActions();
    }

    @GetMapping("/Action/{idAction}")
    public ResponseEntity<Action> getActionById(@PathVariable Long idAction) {

        return clientClient.getActionById(idAction);
    }


    @PutMapping("/Action/update/{idAction}")
    ResponseEntity<Action> updateAction(@PathVariable Long idAction, @RequestBody Action updatedAction) {
        return clientClient.updateAction(idAction, updatedAction);
    }

    @DeleteMapping("/Action/{idAction}")
    public ResponseEntity<String> deleteAction(@PathVariable Long idAction) {
        return clientClient.deleteAction(idAction);
    }

    @GetMapping("/Action/Action_/{idAction}/Portefeuiles")
    public ResponseEntity<List<Portefeuille>> getPortefeuillesByActionId(@PathVariable Long idAction) {
        return clientClient.getPortefeuillesByActionId(idAction);
    }

    @GetMapping("/Action/Action_/{idAction}/Actionnaires")
    public ResponseEntity<List<Actionnaire>> getActionnairesByAction(@PathVariable Long idAction) {
        return clientClient.getActionnairesByAction(idAction);
    }

    @PostMapping("/Action/create")
    public ResponseEntity<String> createAction(@RequestBody Action action){
        return clientClient.createAction(action);
    }



//relation avec Actionnaire


    @GetMapping("/Actionnaire/All_Actionnaires")
    public ResponseEntity<List<Actionnaire>> getAllActionnaires() {
        return clientClient.getAllActionnaires();
    }

    @GetMapping("/Actionnaire/{idActionnaire}")
    public ResponseEntity<Actionnaire> getActionnaireById(@PathVariable Long idActionnaire) {
        return clientClient.getActionnaireById(idActionnaire);
    }

    @PostMapping("/Actionnaire/create")
    public ResponseEntity<String> createActionnaire(@RequestBody Actionnaire actionnaire) {

        try {
        return clientClient.createActionnaire(actionnaire);

        } catch (FeignException fe) {
            String errorMessage = fe.contentUTF8(); // FeignException provides full error body
            return ResponseEntity.badRequest().body(errorMessage);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/Actionnaire/update/{idActionnaire}")
    public ResponseEntity<Actionnaire> updateActionnaire(@PathVariable Long idActionnaire, @RequestBody Actionnaire updatedActionnaire) {
        return clientClient.updateActionnaire(idActionnaire, updatedActionnaire);
    }

    @DeleteMapping("/Actionnaire/{idActionnaire}")
    public ResponseEntity<String> deleteActionnaire(@PathVariable Long idActionnaire) {
        return clientClient.deleteActionnaire(idActionnaire);
    }

    @GetMapping("/Actionnaire/{idActionnaire}/portefeuilles")
    public ResponseEntity<List<Portefeuille>> getPortefeuilles(@PathVariable Long idActionnaire) {
        return clientClient.getPortefeuilles(idActionnaire);
    }

    @GetMapping("/Actionnaire/{idActionnaire}/actions")
    public ResponseEntity<List<Action>> getActions(@PathVariable Long idActionnaire) {
        return clientClient.getActions(idActionnaire);
    }


    //relation avec Portefeuille


    @GetMapping("/Portefeuille/{idPortfeuille}")
    public ResponseEntity<Portefeuille> getPortefeuilleById(@PathVariable Long idPortfeuille){
        return clientClient.getPortefeuilleById(idPortfeuille);
    }

    @GetMapping("/Portefeuille/actionnaire/{idActionnaire}")
    public ResponseEntity<List<Portefeuille>> getPortefeuillesByActionnaire(@PathVariable Long idActionnaire){
        return clientClient.getPortefeuillesByActionnaire(idActionnaire);
    }

    @GetMapping("/Portefeuille/search")
    public ResponseEntity<Portefeuille> getByISINAndActionnaire(@RequestParam String isinAction, @RequestParam Long idActionnaire){
        return clientClient.getByISINAndActionnaire(isinAction,idActionnaire);
    }

    @PostMapping("/Portefeuille/create")
    public ResponseEntity<String> createPortefeuille(@RequestBody Portefeuille portefeuille) {

        try {
            return clientClient.createPortefeuille(portefeuille);
        } catch (FeignException fe) {
            String errorMessage = fe.contentUTF8(); // FeignException provides full error body
            return ResponseEntity.badRequest().body(errorMessage);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }

    }

    @GetMapping("/Portefeuille/All_Portefeuilles")
    public ResponseEntity<List<Portefeuille>> getAllPortefeuilles(){
        return clientClient.getAllPortefeuilles();
    }

    @DeleteMapping("/Portefeuille/delete/{idPortefeuille}")
    public ResponseEntity<String> deletePortefeuille(@PathVariable Long idPortefeuille){
        return clientClient.deletePortefeuille(idPortefeuille);
    }



}

