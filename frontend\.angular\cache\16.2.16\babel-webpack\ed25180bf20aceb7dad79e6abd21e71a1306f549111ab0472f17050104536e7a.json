{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/groupe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = function (a1) {\n  return [\"/edit-groupe\", a1];\n};\nfunction GroupsComponent_tr_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"a\", 52);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_tr_62_Template_a_click_6_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const groupe_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(groupe_r1.idGroupe ? ctx_r2.deleteGroupe(groupe_r1.idGroupe, $event) : null);\n    });\n    i0.ɵɵelementStart(7, \"i\", 53);\n    i0.ɵɵtext(8, \"\\uE872\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"a\", 54)(10, \"i\", 55);\n    i0.ɵɵtext(11, \"\\uE254\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const groupe_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r1.idGroupe || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r1.nomGroupe);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, groupe_r1.idGroupe));\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n  }\\n  \\n  .feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);\\n  }\\n  \\n  .sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #333;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n  }\\n  \\n  .sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, .25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .border-top[_ngcontent-%COMP%] { border-top: 1px solid #e5e5e5; }\\n  .border-bottom[_ngcontent-%COMP%] { border-bottom: 1px solid #e5e5e5; }\\n  \\n\\n  .btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n  }\\n  \\n  .delete-btn[_ngcontent-%COMP%] {\\n  background-color: #ff4d4d; \\n\\n  color: white;\\n  }\\n  \\n  .delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #ff1a1a;\\n  }\\n  \\n  .modify-btn[_ngcontent-%COMP%] {\\n  background-color: #4d94ff; \\n\\n  color: white;\\n  }\\n  \\n  .modify-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #0066cc;\\n  }\\n  \\n  \\n\\n  \\n\\n  .edit-icon[_ngcontent-%COMP%] {\\n    color: #007bff;  \\n\\n  }\\n  \\n  \\n\\n  .delete-icon[_ngcontent-%COMP%] {\\n    color: #dc3545;  \\n\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZ3JvdXBzL2dyb3Vwcy5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0lBQ0ksa0JBQWtCO0VBQ3BCOztFQUVBO0lBQ0UsV0FBVztJQUNYLFlBQVk7SUFDWiwyQkFBMkI7RUFDN0I7O0VBRUE7O0lBRUU7O0VBRUY7SUFDRSxlQUFlO0lBQ2YsTUFBTTtJQUNOLFNBQVM7SUFDVCxPQUFPO0lBQ1AsWUFBWSxFQUFFLHNCQUFzQjtJQUNwQyxVQUFVO0lBQ1YsNENBQTRDO0VBQzlDOztFQUVBO0lBRUUsZ0JBQWdCO0lBQ2hCLFNBQVMsRUFBRSxxQkFBcUI7SUFDaEMsMEJBQTBCO0lBQzFCLGtCQUFrQjtJQUNsQixrQkFBa0I7SUFDbEIsZ0JBQWdCLEVBQUUsNkRBQTZEO0VBQ2pGOztFQUVBO0lBQ0UsZ0JBQWdCO0lBQ2hCLFdBQVc7RUFDYjs7RUFFQTtJQUNFLGlCQUFpQjtJQUNqQixXQUFXO0VBQ2I7O0VBRUE7SUFDRSxjQUFjO0VBQ2hCOztFQUVBOztJQUVFLGNBQWM7RUFDaEI7O0VBRUE7SUFDRSxpQkFBaUI7SUFDakIseUJBQXlCO0VBQzNCOztFQUVBOztJQUVFOztFQUVGO0lBQ0UsbUJBQW1CO0lBQ25CLHNCQUFzQjtJQUN0QixlQUFlO0lBQ2Ysb0NBQW9DO0lBQ3BDLDZDQUE2QztFQUMvQzs7RUFFQTtJQUNFLG9CQUFvQjtJQUNwQixlQUFlO0lBQ2YsZ0JBQWdCO0VBQ2xCOztFQUVBO0lBQ0UsV0FBVztJQUNYLHlDQUF5QztJQUN6QyxxQ0FBcUM7RUFDdkM7O0VBRUE7SUFDRSx5QkFBeUI7SUFDekIsOENBQThDO0VBQ2hEOztFQUVBOztJQUVFOztFQUVGLGNBQWMsNkJBQTZCLEVBQUU7RUFDN0MsaUJBQWlCLGdDQUFnQyxFQUFFO0VBQ25ELHNCQUFzQjtFQUN0QjtFQUNBLGlCQUFpQjtFQUNqQixlQUFlO0VBQ2YsWUFBWTtFQUNaLGVBQWU7RUFDZixrQkFBa0I7RUFDbEIsaUJBQWlCO0VBQ2pCLHlCQUF5QjtFQUN6Qjs7RUFFQTtFQUNBLHlCQUF5QixFQUFFLFFBQVE7RUFDbkMsWUFBWTtFQUNaOztFQUVBO0VBQ0EseUJBQXlCO0VBQ3pCOztFQUVBO0VBQ0EseUJBQXlCLEVBQUUsU0FBUztFQUNwQyxZQUFZO0VBQ1o7O0VBRUE7RUFDQSx5QkFBeUI7RUFDekI7O0VBRUEsMEVBQTBFO0VBQzFFLGtDQUFrQztFQUNsQztJQUNFLGNBQWMsR0FBRyxTQUFTO0VBQzVCOztFQUVBLG9DQUFvQztFQUNwQztJQUNFLGNBQWMsR0FBRyxRQUFRO0VBQzNCIiwic291cmNlc0NvbnRlbnQiOlsiYm9keSB7XHJcbiAgICBmb250LXNpemU6IC44NzVyZW07XHJcbiAgfVxyXG4gIFxyXG4gIC5mZWF0aGVyIHtcclxuICAgIHdpZHRoOiAxNnB4O1xyXG4gICAgaGVpZ2h0OiAxNnB4O1xyXG4gICAgdmVydGljYWwtYWxpZ246IHRleHQtYm90dG9tO1xyXG4gIH1cclxuICBcclxuICAvKlxyXG4gICAqIFNpZGViYXJcclxuICAgKi9cclxuICBcclxuICAuc2lkZWJhciB7XHJcbiAgICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgICB0b3A6IDA7XHJcbiAgICBib3R0b206IDA7XHJcbiAgICBsZWZ0OiAwO1xyXG4gICAgei1pbmRleDogMTAwOyAvKiBCZWhpbmQgdGhlIG5hdmJhciAqL1xyXG4gICAgcGFkZGluZzogMDtcclxuICAgIGJveC1zaGFkb3c6IGluc2V0IC0xcHggMCAwIHJnYmEoMCwgMCwgMCwgLjEpO1xyXG4gIH1cclxuICBcclxuICAuc2lkZWJhci1zdGlja3kge1xyXG4gICAgcG9zaXRpb246IC13ZWJraXQtc3RpY2t5O1xyXG4gICAgcG9zaXRpb246IHN0aWNreTtcclxuICAgIHRvcDogNDhweDsgLyogSGVpZ2h0IG9mIG5hdmJhciAqL1xyXG4gICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNDhweCk7XHJcbiAgICBwYWRkaW5nLXRvcDogLjVyZW07XHJcbiAgICBvdmVyZmxvdy14OiBoaWRkZW47XHJcbiAgICBvdmVyZmxvdy15OiBhdXRvOyAvKiBTY3JvbGxhYmxlIGNvbnRlbnRzIGlmIHZpZXdwb3J0IGlzIHNob3J0ZXIgdGhhbiBjb250ZW50LiAqL1xyXG4gIH1cclxuICBcclxuICAuc2lkZWJhciAubmF2LWxpbmsge1xyXG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgIGNvbG9yOiAjMzMzO1xyXG4gIH1cclxuICBcclxuICAuc2lkZWJhciAubmF2LWxpbmsgLmZlYXRoZXIge1xyXG4gICAgbWFyZ2luLXJpZ2h0OiA0cHg7XHJcbiAgICBjb2xvcjogIzk5OTtcclxuICB9XHJcbiAgXHJcbiAgLnNpZGViYXIgLm5hdi1saW5rLmFjdGl2ZSB7XHJcbiAgICBjb2xvcjogIzAwN2JmZjtcclxuICB9XHJcbiAgXHJcbiAgLnNpZGViYXIgLm5hdi1saW5rOmhvdmVyIC5mZWF0aGVyLFxyXG4gIC5zaWRlYmFyIC5uYXYtbGluay5hY3RpdmUgLmZlYXRoZXIge1xyXG4gICAgY29sb3I6IGluaGVyaXQ7XHJcbiAgfVxyXG4gIFxyXG4gIC5zaWRlYmFyLWhlYWRpbmcge1xyXG4gICAgZm9udC1zaXplOiAuNzVyZW07XHJcbiAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xyXG4gIH1cclxuICBcclxuICAvKlxyXG4gICAqIE5hdmJhclxyXG4gICAqL1xyXG4gIFxyXG4gIC5uYXZiYXItYnJhbmQge1xyXG4gICAgcGFkZGluZy10b3A6IC43NXJlbTtcclxuICAgIHBhZGRpbmctYm90dG9tOiAuNzVyZW07XHJcbiAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIC4yNSk7XHJcbiAgICBib3gtc2hhZG93OiBpbnNldCAtMXB4IDAgMCByZ2JhKDAsIDAsIDAsIC4yNSk7XHJcbiAgfVxyXG4gIFxyXG4gIC5uYXZiYXIgLmZvcm0tY29udHJvbCB7XHJcbiAgICBwYWRkaW5nOiAuNzVyZW0gMXJlbTtcclxuICAgIGJvcmRlci13aWR0aDogMDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDA7XHJcbiAgfVxyXG4gIFxyXG4gIC5mb3JtLWNvbnRyb2wtZGFyayB7XHJcbiAgICBjb2xvcjogI2ZmZjtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgLjEpO1xyXG4gICAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIC4xKTtcclxuICB9XHJcbiAgXHJcbiAgLmZvcm0tY29udHJvbC1kYXJrOmZvY3VzIHtcclxuICAgIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7XHJcbiAgICBib3gtc2hhZG93OiAwIDAgMCAzcHggcmdiYSgyNTUsIDI1NSwgMjU1LCAuMjUpO1xyXG4gIH1cclxuICBcclxuICAvKlxyXG4gICAqIFV0aWxpdGllc1xyXG4gICAqL1xyXG4gIFxyXG4gIC5ib3JkZXItdG9wIHsgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlNWU1ZTU7IH1cclxuICAuYm9yZGVyLWJvdHRvbSB7IGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTVlNWU1OyB9XHJcbiAgLyogQmFzZSBidXR0b24gc3R5bGUgKi9cclxuICAuYnRuIHtcclxuICBwYWRkaW5nOiA4cHggMTZweDtcclxuICBmb250LXNpemU6IDE0cHg7XHJcbiAgYm9yZGVyOiBub25lO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxuICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbiAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICB9XHJcbiAgXHJcbiAgLmRlbGV0ZS1idG4ge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZjRkNGQ7IC8qIFJlZCAqL1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxuICB9XHJcbiAgXHJcbiAgLmRlbGV0ZS1idG46aG92ZXIge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmZjFhMWE7XHJcbiAgfVxyXG4gIFxyXG4gIC5tb2RpZnktYnRuIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjNGQ5NGZmOyAvKiBCbHVlICovXHJcbiAgY29sb3I6IHdoaXRlO1xyXG4gIH1cclxuICBcclxuICAubW9kaWZ5LWJ0bjpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwNjZjYztcclxuICB9XHJcbiAgXHJcbiAgLyogT3B0aW9uYWw6IFlvdSBjYW4gYWRkIG1vcmUgYnV0dG9uIHN0eWxlcyBsaWtlICd2aWV3JywgJ2FyY2hpdmUnLCBldGMuICovXHJcbiAgLyogQ2hhbmdlIGNvbG9yIG9mIHRoZSBlZGl0IGljb24gKi9cclxuICAuZWRpdC1pY29uIHtcclxuICAgIGNvbG9yOiAjMDA3YmZmOyAgLyogQmx1ZSAqL1xyXG4gIH1cclxuICBcclxuICAvKiBDaGFuZ2UgY29sb3Igb2YgdGhlIGRlbGV0ZSBpY29uICovXHJcbiAgLmRlbGV0ZS1pY29uIHtcclxuICAgIGNvbG9yOiAjZGMzNTQ1OyAgLyogUmVkICovXHJcbiAgfVxyXG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\";\nexport class GroupsComponent {\n  constructor(groupeService, router) {\n    this.groupeService = groupeService;\n    this.router = router;\n    this.groupes = []; // List of groups\n    this.newGroupe = {\n      nomGroupe: ''\n    }; // New group model\n    this.editGroupe = null; // Group being edited\n  }\n\n  ngOnInit() {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n  // Called after the view is initialized (ideal for initializing Feather icons)\n  ngAfterViewInit() {\n    feather.replace(); // Initialize Feather icons after the view is ready\n  }\n  // Load all groups\n  loadGroupes() {\n    this.groupeService.getAllGroupes().subscribe(data => {\n      this.groupes = data;\n    }, err => {\n      console.error('Error loading groups', err);\n    });\n  }\n  // Add a new group\n  addGroupe() {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(() => {\n        this.newGroupe = {\n          nomGroupe: ''\n        }; // Reset input\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error adding group', err);\n      });\n    }\n  }\n  // Set group to edit mode\n  setEditGroupe(groupe) {\n    this.editGroupe = {\n      ...groupe\n    }; // Clone object\n  }\n  // Update a group\n  updateGroupe() {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(() => {\n        this.editGroupe = null; // Reset edit mode\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error updating group', err);\n      });\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n  // Delete a group\n  deleteGroupe(id, event) {\n    event.preventDefault(); // 🔥 prevents <a> tag default behavior\n    this.groupeService.deleteGroupe(id).subscribe({\n      next: () => {\n        console.log('Group deleted successfully');\n        this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n      },\n      error: err => {\n        console.error('Error deleting group:', err);\n      }\n    });\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function GroupsComponent_Factory(t) {\n      return new (t || GroupsComponent)(i0.ɵɵdirectiveInject(i1.GroupeService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupsComponent,\n      selectors: [[\"app-groups\"]],\n      decls: 80,\n      vars: 2,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/css/bootstrap.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"href\", \"https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\", \"rel\", \"stylesheet\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"form-control-dark\", \"w-100\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-light\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"text-center\", \"mt-4\", \"mb-4\"], [1, \"mb-3\", \"text-right\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#addGroupeModal\", 1, \"btn\", \"btn-custom\"], [1, \"material-icons\", \"align-middle\", 2, \"font-size\", \"20px\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"addGroupeModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"addGroupeModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"addGroupeModalLabel\", 1, \"modal-title\"], [1, \"modal-body\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"nomGroupe\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [\"href\", \"/groups\", 1, \"delete\", 3, \"click\"], [\"data-toggle\", \"tooltip\", \"title\", \"Supprimer\", 1, \"material-icons\", \"delete-icon\"], [1, \"edit\", 3, \"routerLink\"], [\"data-toggle\", \"tooltip\", \"title\", \"Modifier\", 1, \"material-icons\", \"edit-icon\"]],\n      template: function GroupsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"link\", 6)(8, \"link\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10)(12, \"link\", 11);\n          i0.ɵɵelementStart(13, \"title\");\n          i0.ɵɵtext(14, \"Gestion des Groupes\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"body\")(16, \"nav\", 12)(17, \"a\", 13);\n          i0.ɵɵtext(18, \"GTI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"input\", 14);\n          i0.ɵɵelementStart(20, \"ul\", 15)(21, \"li\", 16)(22, \"a\", 17);\n          i0.ɵɵlistener(\"click\", function GroupsComponent_Template_a_click_22_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(23, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(24, \"div\", 18)(25, \"div\", 19)(26, \"nav\", 20)(27, \"div\", 21)(28, \"ul\", 22)(29, \"li\", 23)(30, \"a\", 24);\n          i0.ɵɵelement(31, \"span\", 25);\n          i0.ɵɵtext(32, \" Dashboard \");\n          i0.ɵɵelementStart(33, \"span\", 26);\n          i0.ɵɵtext(34, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"li\", 23)(36, \"a\", 27);\n          i0.ɵɵelement(37, \"span\", 28);\n          i0.ɵɵtext(38, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"li\", 23)(40, \"a\", 29);\n          i0.ɵɵelement(41, \"span\", 30);\n          i0.ɵɵtext(42, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(43, \"main\", 31)(44, \"h2\", 32);\n          i0.ɵɵtext(45, \"Gestion des groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 33)(47, \"button\", 34)(48, \"i\", 35);\n          i0.ɵɵtext(49, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50, \" Ajouter un groupe \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 36)(52, \"table\", 37)(53, \"thead\")(54, \"tr\")(55, \"th\");\n          i0.ɵɵtext(56, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"th\");\n          i0.ɵɵtext(58, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\");\n          i0.ɵɵtext(60, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(61, \"tbody\");\n          i0.ɵɵtemplate(62, GroupsComponent_tr_62_Template, 12, 5, \"tr\", 38);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(63, \"div\", 39)(64, \"div\", 40)(65, \"div\", 41)(66, \"div\", 42)(67, \"h5\", 43);\n          i0.ɵɵtext(68, \"Ajouter un groupe\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"div\", 44)(70, \"form\", 45);\n          i0.ɵɵlistener(\"ngSubmit\", function GroupsComponent_Template_form_ngSubmit_70_listener() {\n            return ctx.addGroupe();\n          });\n          i0.ɵɵelementStart(71, \"div\", 46)(72, \"label\", 47);\n          i0.ɵɵtext(73, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"input\", 48);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_74_listener($event) {\n            return ctx.newGroupe.nomGroupe = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 49)(76, \"button\", 50);\n          i0.ɵɵtext(77, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"button\", 51);\n          i0.ɵɵtext(79, \"Ajouter\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(62);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groupes);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngModel\", ctx.newGroupe.nomGroupe);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm, i2.RouterLink],\n      styles: [_c1, _c1, \"body[_ngcontent-%COMP%] {\\n        font-family: 'Poppins', sans-serif;\\n        margin: 0;\\n        padding: 0;\\n        min-height: 100vh;\\n        background: linear-gradient(-45deg, #1e3c72, #2a5298, #1e3c72, #2a5298);\\n        background-size: 400% 400%;\\n        animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n        color: #fff;\\n      }\\n\\n      @keyframes _ngcontent-%COMP%_gradientMove {\\n        0% { background-position: 0% 50%; }\\n        50% { background-position: 100% 50%; }\\n        100% { background-position: 0% 50%; }\\n      }\\n\\n      .container-fluid[_ngcontent-%COMP%] {\\n        background: rgba(255, 255, 255, 0.06);\\n        border-radius: 15px;\\n        padding: 2rem;\\n        margin-top: 20px;\\n      }\\n\\n      .table-responsive[_ngcontent-%COMP%] {\\n        background: rgba(255, 255, 255, 0.06);\\n        border-radius: 15px;\\n        padding: 1.5rem;\\n        backdrop-filter: blur(12px);\\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n      }\\n\\n      .table[_ngcontent-%COMP%] {\\n        color: #fff;\\n        border-radius: 15px;\\n        overflow: hidden;\\n        border-collapse: separate;\\n        border-spacing: 0 12px;\\n      }\\n\\n      .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n        background-color: rgba(255, 255, 255, 0.15);\\n        color: #ffffff;\\n        font-weight: 600;\\n        text-align: center;\\n        border: none;\\n        padding: 1rem;\\n      }\\n\\n      .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n        background-color: rgba(255, 255, 255, 0.1);\\n        transition: transform 0.2s ease, box-shadow 0.2s ease;\\n        text-align: center;\\n        border-radius: 12px;\\n      }\\n\\n      .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n        transform: scale(1.01);\\n        box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n      }\\n\\n      .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n        vertical-align: middle;\\n        padding: 0.9rem;\\n        font-size: 0.95rem;\\n      }\\n\\n      .btn-custom[_ngcontent-%COMP%] {\\n        background-color: #28a745;\\n        color: white;\\n        border: none;\\n        padding: 12px 25px;\\n        border-radius: 30px;\\n        text-transform: uppercase;\\n        font-weight: bold;\\n        transition: background 0.3s, transform 0.2s;\\n        box-shadow: 0 4px 15px rgba(0,0,0,0.2);\\n      }\\n\\n      .btn-custom[_ngcontent-%COMP%]:hover {\\n        background-color: #218838;\\n        transform: translateY(-2px);\\n      }\\n\\n      .edit-icon[_ngcontent-%COMP%] {\\n        color: #17a2b8;\\n        cursor: pointer;\\n        font-size: 20px;\\n        margin: 0 10px;\\n        transition: color 0.3s;\\n      }\\n\\n      .edit-icon[_ngcontent-%COMP%]:hover {\\n        color: #0d6efd;\\n      }\\n\\n      .delete-icon[_ngcontent-%COMP%] {\\n        color: #dc3545;\\n        cursor: pointer;\\n        font-size: 20px;\\n        transition: color 0.3s;\\n      }\\n\\n      .delete-icon[_ngcontent-%COMP%]:hover {\\n        color: #bd2130;\\n      }\\n\\n      .navbar[_ngcontent-%COMP%] {\\n        background-color: #1c1c1c !important;\\n      }\\n\\n      h2[_ngcontent-%COMP%] {\\n        color: #fff;\\n      }\\n\\n      a.router-link[_ngcontent-%COMP%] {\\n        text-decoration: none;\\n      }\\n      \\n      .modal-header[_ngcontent-%COMP%] {\\n  background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 1.5rem;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n  background-color: #f1f1f1;\\n  padding: 1rem;\\n  border-top: 1px solid #ddd;\\n  display: flex;\\n  justify-content: center; \\n\\n  gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  font-size: 1rem;\\n  border-radius: 0.3rem;\\n  transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n  transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  transform: translateY(-2px);\\n}\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GroupsComponent_tr_62_Template_a_click_6_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r3", "groupe_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "idGroupe", "deleteGroupe", "ɵɵadvance", "ɵɵtextInterpolate", "nomGroupe", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "GroupsComponent", "constructor", "groupeService", "router", "groupes", "newGroupe", "editGroupe", "ngOnInit", "loadGroupes", "ngAfterViewInit", "replace", "getAllGroupes", "subscribe", "data", "err", "console", "error", "addGroupe", "trim", "setEditGroupe", "groupe", "updateGroupe", "id", "event", "preventDefault", "next", "log", "filter", "logout", "localStorage", "removeItem", "getItem", "navigate", "ɵɵdirectiveInject", "i1", "GroupeService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "GroupsComponent_Template", "rf", "ctx", "ɵɵelement", "GroupsComponent_Template_a_click_22_listener", "ɵɵtemplate", "GroupsComponent_tr_62_Template", "GroupsComponent_Template_form_ngSubmit_70_listener", "GroupsComponent_Template_input_ngModelChange_74_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\groups\\groups.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\groups\\groups.component.html"], "sourcesContent": ["import { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Component, OnInit, AfterViewInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\n\n@Component({\n  selector: 'app-groups',\n  templateUrl: './groups.component.html',\n  styleUrls: ['./groups.component.css']\n})\nexport class GroupsComponent implements OnInit, AfterViewInit {\n  groupes: Groupe[] = []; // List of groups\n  newGroupe: Groupe = { nomGroupe: '' }; // New group model\n  editGroupe: Groupe | null = null; // Group being edited\n\n  constructor(private groupeService: GroupeService, private router: Router) {}\n\n  ngOnInit(): void {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n\n  // Called after the view is initialized (ideal for initializing Feather icons)\n  ngAfterViewInit(): void {\n    feather.replace(); // Initialize Feather icons after the view is ready\n  }\n\n  // Load all groups\n  loadGroupes(): void {\n    this.groupeService.getAllGroupes().subscribe(\n      (data) => {\n        this.groupes = data;\n      },\n      (err) => {\n        console.error('Error loading groups', err);\n      }\n    );\n  }\n\n  // Add a new group\n  addGroupe(): void {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(\n        () => {\n          this.newGroupe = { nomGroupe: '' }; // Reset input\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error adding group', err);\n        }\n      );\n    }\n  }\n\n  // Set group to edit mode\n  setEditGroupe(groupe: Groupe): void {\n    this.editGroupe = { ...groupe }; // Clone object\n  }\n\n  // Update a group\n  updateGroupe(): void {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(\n        () => {\n          this.editGroupe = null; // Reset edit mode\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error updating group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n\n  // Delete a group\n  deleteGroupe(id: number, event: Event): void {\n    event.preventDefault(); // 🔥 prevents <a> tag default behavior\n  \n    this.groupeService.deleteGroupe(id).subscribe({\n      next: () => {\n        console.log('Group deleted successfully');\n        this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n      },\n      error: (err) => {\n        console.error('Error deleting group:', err);\n      }\n    });\n  }\n  \n\n  logout(): void {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/css/bootstrap.min.css\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n    <link href=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\" rel=\"stylesheet\">\n    <link href=\"./groups.component.css\" rel=\"stylesheet\">\n\n    <style>\n      \n      body {\n        font-family: 'Poppins', sans-serif;\n        margin: 0;\n        padding: 0;\n        min-height: 100vh;\n        background: linear-gradient(-45deg, #1e3c72, #2a5298, #1e3c72, #2a5298);\n        background-size: 400% 400%;\n        animation: gradientMove 20s ease infinite;\n        color: #fff;\n      }\n\n      @keyframes gradientMove {\n        0% { background-position: 0% 50%; }\n        50% { background-position: 100% 50%; }\n        100% { background-position: 0% 50%; }\n      }\n\n      .container-fluid {\n        background: rgba(255, 255, 255, 0.06);\n        border-radius: 15px;\n        padding: 2rem;\n        margin-top: 20px;\n      }\n\n      .table-responsive {\n        background: rgba(255, 255, 255, 0.06);\n        border-radius: 15px;\n        padding: 1.5rem;\n        backdrop-filter: blur(12px);\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\n      }\n\n      .table {\n        color: #fff;\n        border-radius: 15px;\n        overflow: hidden;\n        border-collapse: separate;\n        border-spacing: 0 12px;\n      }\n\n      .table thead th {\n        background-color: rgba(255, 255, 255, 0.15);\n        color: #ffffff;\n        font-weight: 600;\n        text-align: center;\n        border: none;\n        padding: 1rem;\n      }\n\n      .table tbody tr {\n        background-color: rgba(255, 255, 255, 0.1);\n        transition: transform 0.2s ease, box-shadow 0.2s ease;\n        text-align: center;\n        border-radius: 12px;\n      }\n\n      .table tbody tr:hover {\n        transform: scale(1.01);\n        box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\n      }\n\n      .table td {\n        vertical-align: middle;\n        padding: 0.9rem;\n        font-size: 0.95rem;\n      }\n\n      .btn-custom {\n        background-color: #28a745;\n        color: white;\n        border: none;\n        padding: 12px 25px;\n        border-radius: 30px;\n        text-transform: uppercase;\n        font-weight: bold;\n        transition: background 0.3s, transform 0.2s;\n        box-shadow: 0 4px 15px rgba(0,0,0,0.2);\n      }\n\n      .btn-custom:hover {\n        background-color: #218838;\n        transform: translateY(-2px);\n      }\n\n      .edit-icon {\n        color: #17a2b8;\n        cursor: pointer;\n        font-size: 20px;\n        margin: 0 10px;\n        transition: color 0.3s;\n      }\n\n      .edit-icon:hover {\n        color: #0d6efd;\n      }\n\n      .delete-icon {\n        color: #dc3545;\n        cursor: pointer;\n        font-size: 20px;\n        transition: color 0.3s;\n      }\n\n      .delete-icon:hover {\n        color: #bd2130;\n      }\n\n      .navbar {\n        background-color: #1c1c1c !important;\n      }\n\n      h2 {\n        color: #fff;\n      }\n\n      a.router-link {\n        text-decoration: none;\n      }\n      \n      .modal-header {\n  background-color: #252528;\n}\n\n/* Modal Body */\n.modal-body {\n  background-color: #fff;\n  padding: 1.5rem;\n  color: #333;\n  font-size: 1rem;\n}\n\n/* Modal Footer */\n.modal-footer {\n  background-color: #f1f1f1;\n  padding: 1rem;\n  border-top: 1px solid #ddd;\n  display: flex;\n  justify-content: center; /* Center buttons horizontally */\n  gap: 1rem; /* Space between the buttons */\n}\n\n.modal-footer button {\n  padding: 0.5rem 1rem;\n  font-size: 1rem;\n  border-radius: 0.3rem;\n  transition: background-color 0.3s, transform 0.2s;\n}\n\n.modal-footer .btn-secondary {\n  background-color: #6c757d;\n  color: #fff;\n  border: none;\n}\n\n.modal-footer .btn-secondary:hover {\n  background-color: #5a6268;\n  transform: translateY(-2px);\n}\n\n.modal-footer .btn-primary {\n  background-color: #007bff;\n  color: #fff;\n  border: none;\n}\n\n.modal-footer .btn-primary:hover {\n  background-color: #0056b3;\n  transform: translateY(-2px);\n}\n\n\n\n\n\n    </style>\n\n    <title>Gestion des Groupes</title>\n  </head>\n\n  <body>\n    <!-- Navbar -->\n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"adminDash\">GTI</a>\n      <input class=\"form-control form-control-dark w-100\" type=\"text\" placeholder=\"Search\" aria-label=\"Search\">\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n         <!-- Modal -->\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block bg-light sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"users\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n \n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <h2 class=\"text-center mt-4 mb-4\">Gestion des groupes</h2>\n          <div class=\"mb-3 text-right\">\n            <button type=\"button\" class=\"btn btn-custom\" data-toggle=\"modal\" data-target=\"#addGroupeModal\">\n              <i class=\"material-icons align-middle\" style=\"font-size: 20px;\">add</i>\n              Ajouter un groupe\n            </button>\n          </div>\n\n          <div class=\"table-responsive\">\n            <table class=\"table table-hover\">\n              <thead>\n                <tr>\n                  <th>ID</th>\n                  <th>Nom du groupe</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let groupe of groupes\">\n                  <td>{{ groupe.idGroupe || 'N/A' }}</td>\n                  <td>{{ groupe.nomGroupe }}</td>\n                  <td>\n                    <a href=\"/groups\" class=\"delete\" (click)=\"groupe.idGroupe ? deleteGroupe(groupe.idGroupe, $event) : null\">\n                      <i class=\"material-icons delete-icon\" data-toggle=\"tooltip\" title=\"Supprimer\">&#xE872;</i>\n                    </a>\n                    <a [routerLink]=\"['/edit-groupe', groupe.idGroupe]\" class=\"edit\">\n                      <i class=\"material-icons edit-icon\" data-toggle=\"tooltip\" title=\"Modifier\">&#xE254;</i>\n                    </a>\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <!-- Modal -->\n<div class=\"modal fade\" id=\"addGroupeModal\" tabindex=\"-1\" aria-labelledby=\"addGroupeModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"addGroupeModalLabel\">Ajouter un groupe</h5>\n      \n      </div>\n      <div class=\"modal-body\">\n        <form (ngSubmit)=\"addGroupe()\">\n          <div class=\"form-group\">\n            <label for=\"nomGroupe\">Nom du groupe</label>\n            <input type=\"text\" id=\"nomGroupe\" [(ngModel)]=\"newGroupe.nomGroupe\" name=\"nomGroupe\" class=\"form-control\" required>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\">Annuler</button>\n            <button type=\"submit\" class=\"btn btn-primary\">Ajouter</button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n\n    <script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/@popperjs/core@2.10.2/dist/umd/popper.min.js\"></script>\n    <script src=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/js/bootstrap.min.js\"></script>\n    \n  </body>\n</html>\n"], "mappings": "AAIA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;;;;;;;;;IC8PxBC,EAAA,CAAAC,cAAA,SAAmC;IAC7BD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAC+BD,EAAA,CAAAI,UAAA,mBAAAC,kDAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAJ,SAAA,CAAAK,QAAA,GAAkBH,MAAA,CAAAI,YAAA,CAAAN,SAAA,CAAAK,QAAA,EAAAT,MAAA,CAAqC,GAAG,IAAI;IAAA,EAAC;IACvGN,EAAA,CAAAC,cAAA,YAA8E;IAAAD,EAAA,CAAAE,MAAA,aAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE5FH,EAAA,CAAAC,cAAA,YAAiE;IACYD,EAAA,CAAAE,MAAA,cAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAPvFH,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAkB,iBAAA,CAAAR,SAAA,CAAAK,QAAA,UAA8B;IAC9Bf,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,iBAAA,CAAAR,SAAA,CAAAS,SAAA,CAAsB;IAKrBnB,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAoB,UAAA,eAAApB,EAAA,CAAAqB,eAAA,IAAAC,GAAA,EAAAZ,SAAA,CAAAK,QAAA,EAAgD;;;;AD9PvE,OAAM,MAAOQ,eAAe;EAK1BC,YAAoBC,aAA4B,EAAUC,MAAc;IAApD,KAAAD,aAAa,GAAbA,aAAa;IAAyB,KAAAC,MAAM,GAANA,MAAM;IAJhE,KAAAC,OAAO,GAAa,EAAE,CAAC,CAAC;IACxB,KAAAC,SAAS,GAAW;MAAET,SAAS,EAAE;IAAE,CAAE,CAAC,CAAC;IACvC,KAAAU,UAAU,GAAkB,IAAI,CAAC,CAAC;EAEyC;;EAE3EC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;EACtB;EAEA;EACAC,eAAeA,CAAA;IACbjC,OAAO,CAACkC,OAAO,EAAE,CAAC,CAAC;EACrB;EAEA;EACAF,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACS,aAAa,EAAE,CAACC,SAAS,CACzCC,IAAI,IAAI;MACP,IAAI,CAACT,OAAO,GAAGS,IAAI;IACrB,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;IAC5C,CAAC,CACF;EACH;EAEA;EACAG,SAASA,CAAA;IACP,IAAI,IAAI,CAACZ,SAAS,CAACT,SAAS,CAACsB,IAAI,EAAE,EAAE;MACnC,IAAI,CAAChB,aAAa,CAACe,SAAS,CAAC,IAAI,CAACZ,SAAS,CAAC,CAACO,SAAS,CACpD,MAAK;QACH,IAAI,CAACP,SAAS,GAAG;UAAET,SAAS,EAAE;QAAE,CAAE,CAAC,CAAC;QACpC,IAAI,CAACY,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAM,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;MAC1C,CAAC,CACF;;EAEL;EAEA;EACAK,aAAaA,CAACC,MAAc;IAC1B,IAAI,CAACd,UAAU,GAAG;MAAE,GAAGc;IAAM,CAAE,CAAC,CAAC;EACnC;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACf,UAAU,IAAI,IAAI,CAACA,UAAU,CAACd,QAAQ,EAAE;MAC/C,IAAI,CAACU,aAAa,CAACmB,YAAY,CAAC,IAAI,CAACf,UAAU,CAACd,QAAQ,EAAE,IAAI,CAACc,UAAU,CAAC,CAACM,SAAS,CAClF,MAAK;QACH,IAAI,CAACN,UAAU,GAAG,IAAI,CAAC,CAAC;QACxB,IAAI,CAACE,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAM,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;;EAEtD;EAEA;EACAvB,YAAYA,CAAC6B,EAAU,EAAEC,KAAY;IACnCA,KAAK,CAACC,cAAc,EAAE,CAAC,CAAC;IAExB,IAAI,CAACtB,aAAa,CAACT,YAAY,CAAC6B,EAAE,CAAC,CAACV,SAAS,CAAC;MAC5Ca,IAAI,EAAEA,CAAA,KAAK;QACTV,OAAO,CAACW,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAACtB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACuB,MAAM,CAACP,MAAM,IAAIA,MAAM,CAAC5B,QAAQ,KAAK8B,EAAE,CAAC;MACtE,CAAC;MACDN,KAAK,EAAGF,GAAG,IAAI;QACbC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,GAAG,CAAC;MAC7C;KACD,CAAC;EACJ;EAGAc,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCf,OAAO,CAACW,GAAG,CAACG,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC5B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAvFWhC,eAAe,EAAAvB,EAAA,CAAAwD,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA1D,EAAA,CAAAwD,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfrC,eAAe;MAAAsC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5BnE,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAqE,SAAA,cAAsB;UAgMtBrE,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGpCH,EAAA,CAAAC,cAAA,YAAM;UAG8DD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAqE,SAAA,iBAAyG;UACzGrE,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAI,UAAA,mBAAAkE,6CAAA;YAAA,OAASF,GAAA,CAAAjB,MAAA,EAAQ;UAAA,EAAC;UAACnD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMzDH,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAAqE,SAAA,gBAAiC;UACjCrE,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGpDH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAqE,SAAA,gBAAiC;UACjCrE,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAqE,SAAA,gBAAkC;UAClCrE,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAOZH,EAAA,CAAAC,cAAA,gBAAkE;UAC9BD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1DH,EAAA,CAAAC,cAAA,eAA6B;UAEuCD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACXH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpBH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAuE,UAAA,KAAAC,8BAAA,kBAWK;UACPxE,EAAA,CAAAG,YAAA,EAAQ;UAQtBH,EAAA,CAAAC,cAAA,eAAmH;UAI1DD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGzEH,EAAA,CAAAC,cAAA,eAAwB;UAChBD,EAAA,CAAAI,UAAA,sBAAAqE,mDAAA;YAAA,OAAYL,GAAA,CAAA5B,SAAA,EAAW;UAAA,EAAC;UAC5BxC,EAAA,CAAAC,cAAA,eAAwB;UACCD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,iBAAmH;UAAjFD,EAAA,CAAAI,UAAA,2BAAAsE,yDAAApE,MAAA;YAAA,OAAA8D,GAAA,CAAAxC,SAAA,CAAAT,SAAA,GAAAb,MAAA;UAAA,EAAiC;UAAnEN,EAAA,CAAAG,YAAA,EAAmH;UAErHH,EAAA,CAAAC,cAAA,eAA0B;UAC6CD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrFH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UAnCnCH,EAAA,CAAAiB,SAAA,IAAU;UAAVjB,EAAA,CAAAoB,UAAA,YAAAgD,GAAA,CAAAzC,OAAA,CAAU;UA+BH3B,EAAA,CAAAiB,SAAA,IAAiC;UAAjCjB,EAAA,CAAAoB,UAAA,YAAAgD,GAAA,CAAAxC,SAAA,CAAAT,SAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}