{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { SafeSubscriber } from '../Subscriber';\nimport { operate } from '../util/lift';\nexport function share(options = {}) {\n  const {\n    connector = () => new Subject(),\n    resetOnError = true,\n    resetOnComplete = true,\n    resetOnRefCountZero = true\n  } = options;\n  return wrapperSource => {\n    let connection;\n    let resetConnection;\n    let subject;\n    let refCount = 0;\n    let hasCompleted = false;\n    let hasErrored = false;\n    const cancelReset = () => {\n      resetConnection === null || resetConnection === void 0 ? void 0 : resetConnection.unsubscribe();\n      resetConnection = undefined;\n    };\n    const reset = () => {\n      cancelReset();\n      connection = subject = undefined;\n      hasCompleted = hasErrored = false;\n    };\n    const resetAndUnsubscribe = () => {\n      const conn = connection;\n      reset();\n      conn === null || conn === void 0 ? void 0 : conn.unsubscribe();\n    };\n    return operate((source, subscriber) => {\n      refCount++;\n      if (!hasErrored && !hasCompleted) {\n        cancelReset();\n      }\n      const dest = subject = subject !== null && subject !== void 0 ? subject : connector();\n      subscriber.add(() => {\n        refCount--;\n        if (refCount === 0 && !hasErrored && !hasCompleted) {\n          resetConnection = handleReset(resetAndUnsubscribe, resetOnRefCountZero);\n        }\n      });\n      dest.subscribe(subscriber);\n      if (!connection && refCount > 0) {\n        connection = new SafeSubscriber({\n          next: value => dest.next(value),\n          error: err => {\n            hasErrored = true;\n            cancelReset();\n            resetConnection = handleReset(reset, resetOnError, err);\n            dest.error(err);\n          },\n          complete: () => {\n            hasCompleted = true;\n            cancelReset();\n            resetConnection = handleReset(reset, resetOnComplete);\n            dest.complete();\n          }\n        });\n        innerFrom(source).subscribe(connection);\n      }\n    })(wrapperSource);\n  };\n}\nfunction handleReset(reset, on, ...args) {\n  if (on === true) {\n    reset();\n    return;\n  }\n  if (on === false) {\n    return;\n  }\n  const onSubscriber = new SafeSubscriber({\n    next: () => {\n      onSubscriber.unsubscribe();\n      reset();\n    }\n  });\n  return innerFrom(on(...args)).subscribe(onSubscriber);\n}", "map": {"version": 3, "names": ["innerFrom", "Subject", "SafeSubscriber", "operate", "share", "options", "connector", "resetOnError", "resetOnComplete", "resetOnRefCountZero", "wrapperSource", "connection", "resetConnection", "subject", "refCount", "hasCompleted", "hasErrored", "cancelReset", "unsubscribe", "undefined", "reset", "resetAndUnsubscribe", "conn", "source", "subscriber", "dest", "add", "handleReset", "subscribe", "next", "value", "error", "err", "complete", "on", "args", "onSubscriber"], "sources": ["C:/Users/<USER>/Desktop/Grafinalfront/GRA/frontend/node_modules/rxjs/dist/esm/internal/operators/share.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { SafeSubscriber } from '../Subscriber';\nimport { operate } from '../util/lift';\nexport function share(options = {}) {\n    const { connector = () => new Subject(), resetOnError = true, resetOnComplete = true, resetOnRefCountZero = true } = options;\n    return (wrapperSource) => {\n        let connection;\n        let resetConnection;\n        let subject;\n        let refCount = 0;\n        let hasCompleted = false;\n        let hasErrored = false;\n        const cancelReset = () => {\n            resetConnection === null || resetConnection === void 0 ? void 0 : resetConnection.unsubscribe();\n            resetConnection = undefined;\n        };\n        const reset = () => {\n            cancelReset();\n            connection = subject = undefined;\n            hasCompleted = hasErrored = false;\n        };\n        const resetAndUnsubscribe = () => {\n            const conn = connection;\n            reset();\n            conn === null || conn === void 0 ? void 0 : conn.unsubscribe();\n        };\n        return operate((source, subscriber) => {\n            refCount++;\n            if (!hasErrored && !hasCompleted) {\n                cancelReset();\n            }\n            const dest = (subject = subject !== null && subject !== void 0 ? subject : connector());\n            subscriber.add(() => {\n                refCount--;\n                if (refCount === 0 && !hasErrored && !hasCompleted) {\n                    resetConnection = handleReset(resetAndUnsubscribe, resetOnRefCountZero);\n                }\n            });\n            dest.subscribe(subscriber);\n            if (!connection &&\n                refCount > 0) {\n                connection = new SafeSubscriber({\n                    next: (value) => dest.next(value),\n                    error: (err) => {\n                        hasErrored = true;\n                        cancelReset();\n                        resetConnection = handleReset(reset, resetOnError, err);\n                        dest.error(err);\n                    },\n                    complete: () => {\n                        hasCompleted = true;\n                        cancelReset();\n                        resetConnection = handleReset(reset, resetOnComplete);\n                        dest.complete();\n                    },\n                });\n                innerFrom(source).subscribe(connection);\n            }\n        })(wrapperSource);\n    };\n}\nfunction handleReset(reset, on, ...args) {\n    if (on === true) {\n        reset();\n        return;\n    }\n    if (on === false) {\n        return;\n    }\n    const onSubscriber = new SafeSubscriber({\n        next: () => {\n            onSubscriber.unsubscribe();\n            reset();\n        },\n    });\n    return innerFrom(on(...args)).subscribe(onSubscriber);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,OAAO,QAAQ,cAAc;AACtC,OAAO,SAASC,KAAKA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;EAChC,MAAM;IAAEC,SAAS,GAAGA,CAAA,KAAM,IAAIL,OAAO,CAAC,CAAC;IAAEM,YAAY,GAAG,IAAI;IAAEC,eAAe,GAAG,IAAI;IAAEC,mBAAmB,GAAG;EAAK,CAAC,GAAGJ,OAAO;EAC5H,OAAQK,aAAa,IAAK;IACtB,IAAIC,UAAU;IACd,IAAIC,eAAe;IACnB,IAAIC,OAAO;IACX,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAIC,UAAU,GAAG,KAAK;IACtB,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACtBL,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACM,WAAW,CAAC,CAAC;MAC/FN,eAAe,GAAGO,SAAS;IAC/B,CAAC;IACD,MAAMC,KAAK,GAAGA,CAAA,KAAM;MAChBH,WAAW,CAAC,CAAC;MACbN,UAAU,GAAGE,OAAO,GAAGM,SAAS;MAChCJ,YAAY,GAAGC,UAAU,GAAG,KAAK;IACrC,CAAC;IACD,MAAMK,mBAAmB,GAAGA,CAAA,KAAM;MAC9B,MAAMC,IAAI,GAAGX,UAAU;MACvBS,KAAK,CAAC,CAAC;MACPE,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACJ,WAAW,CAAC,CAAC;IAClE,CAAC;IACD,OAAOf,OAAO,CAAC,CAACoB,MAAM,EAAEC,UAAU,KAAK;MACnCV,QAAQ,EAAE;MACV,IAAI,CAACE,UAAU,IAAI,CAACD,YAAY,EAAE;QAC9BE,WAAW,CAAC,CAAC;MACjB;MACA,MAAMQ,IAAI,GAAIZ,OAAO,GAAGA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGP,SAAS,CAAC,CAAE;MACvFkB,UAAU,CAACE,GAAG,CAAC,MAAM;QACjBZ,QAAQ,EAAE;QACV,IAAIA,QAAQ,KAAK,CAAC,IAAI,CAACE,UAAU,IAAI,CAACD,YAAY,EAAE;UAChDH,eAAe,GAAGe,WAAW,CAACN,mBAAmB,EAAEZ,mBAAmB,CAAC;QAC3E;MACJ,CAAC,CAAC;MACFgB,IAAI,CAACG,SAAS,CAACJ,UAAU,CAAC;MAC1B,IAAI,CAACb,UAAU,IACXG,QAAQ,GAAG,CAAC,EAAE;QACdH,UAAU,GAAG,IAAIT,cAAc,CAAC;UAC5B2B,IAAI,EAAGC,KAAK,IAAKL,IAAI,CAACI,IAAI,CAACC,KAAK,CAAC;UACjCC,KAAK,EAAGC,GAAG,IAAK;YACZhB,UAAU,GAAG,IAAI;YACjBC,WAAW,CAAC,CAAC;YACbL,eAAe,GAAGe,WAAW,CAACP,KAAK,EAAEb,YAAY,EAAEyB,GAAG,CAAC;YACvDP,IAAI,CAACM,KAAK,CAACC,GAAG,CAAC;UACnB,CAAC;UACDC,QAAQ,EAAEA,CAAA,KAAM;YACZlB,YAAY,GAAG,IAAI;YACnBE,WAAW,CAAC,CAAC;YACbL,eAAe,GAAGe,WAAW,CAACP,KAAK,EAAEZ,eAAe,CAAC;YACrDiB,IAAI,CAACQ,QAAQ,CAAC,CAAC;UACnB;QACJ,CAAC,CAAC;QACFjC,SAAS,CAACuB,MAAM,CAAC,CAACK,SAAS,CAACjB,UAAU,CAAC;MAC3C;IACJ,CAAC,CAAC,CAACD,aAAa,CAAC;EACrB,CAAC;AACL;AACA,SAASiB,WAAWA,CAACP,KAAK,EAAEc,EAAE,EAAE,GAAGC,IAAI,EAAE;EACrC,IAAID,EAAE,KAAK,IAAI,EAAE;IACbd,KAAK,CAAC,CAAC;IACP;EACJ;EACA,IAAIc,EAAE,KAAK,KAAK,EAAE;IACd;EACJ;EACA,MAAME,YAAY,GAAG,IAAIlC,cAAc,CAAC;IACpC2B,IAAI,EAAEA,CAAA,KAAM;MACRO,YAAY,CAAClB,WAAW,CAAC,CAAC;MAC1BE,KAAK,CAAC,CAAC;IACX;EACJ,CAAC,CAAC;EACF,OAAOpB,SAAS,CAACkC,EAAE,CAAC,GAAGC,IAAI,CAAC,CAAC,CAACP,SAAS,CAACQ,YAAY,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}