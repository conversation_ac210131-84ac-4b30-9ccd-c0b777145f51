{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ResponsableService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8060/api/v1/auth/Users_Responsables'; // Your API URL\n  }\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId) {\n    return this.http.get(`${this.apiUrl}/${responsableId}`); // Fetching a single user\n  }\n  // Get all responsibles\n  getResponsables() {\n    return this.http.get(this.apiUrl); // Fetching all users\n  }\n  // Delete a responsable\n  deleteResponsable(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`); // Deleting a user by ID\n  }\n  // Update a responsable\n  updateResponsable(id, responsable) {\n    return this.http.put(`${this.apiUrl}/${id}`, responsable); // Updating a user by ID\n  }\n\n  static {\n    this.ɵfac = function ResponsableService_Factory(t) {\n      return new (t || ResponsableService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ResponsableService,\n      factory: ResponsableService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["ResponsableService", "constructor", "http", "apiUrl", "getResponsableById", "responsableId", "get", "getResponsables", "deleteResponsable", "id", "delete", "updateResponsable", "responsable", "put", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\responsable.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { User } from './model/user.model'; // Import the User model\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ResponsableService {\n  private apiUrl = 'http://localhost:8060/api/v1/auth/Users_Responsables'; // Your API URL\n\n  constructor(private http: HttpClient) {}\n\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId: number): Observable<User> {\n    return this.http.get<User>(`${this.apiUrl}/${responsableId}`); // Fetching a single user\n  }\n\n  // Get all responsibles\n  getResponsables(): Observable<User[]> {\n    return this.http.get<User[]>(this.apiUrl); // Fetching all users\n  }\n\n  // Delete a responsable\n  deleteResponsable(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/${id}`); // Deleting a user by ID\n  }\n\n  // Update a responsable\n  updateResponsable(id: number, responsable: User): Observable<any> {\n    return this.http.put(`${this.apiUrl}/${id}`, responsable); // Updating a user by ID\n  }\n}\n"], "mappings": ";;AAQA,OAAM,MAAOA,kBAAkB;EAG7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,sDAAsD,CAAC,CAAC;EAElC;EAEvC;EACAC,kBAAkBA,CAACC,aAAqB;IACtC,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAO,GAAG,IAAI,CAACH,MAAM,IAAIE,aAAa,EAAE,CAAC,CAAC,CAAC;EACjE;EAEA;EACAE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACL,IAAI,CAACI,GAAG,CAAS,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC;EAC7C;EAEA;EACAK,iBAAiBA,CAACC,EAAU;IAC1B,OAAO,IAAI,CAACP,IAAI,CAACQ,MAAM,CAAC,GAAG,IAAI,CAACP,MAAM,IAAIM,EAAE,EAAE,CAAC,CAAC,CAAC;EACnD;EAEA;EACAE,iBAAiBA,CAACF,EAAU,EAAEG,WAAiB;IAC7C,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAAC,GAAG,IAAI,CAACV,MAAM,IAAIM,EAAE,EAAE,EAAEG,WAAW,CAAC,CAAC,CAAC;EAC7D;;;;uBAvBWZ,kBAAkB,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBjB,kBAAkB;MAAAkB,OAAA,EAAlBlB,kBAAkB,CAAAmB,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}