{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../authentication.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction LoginComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.loginError, \" \");\n  }\n}\nexport class LoginComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.email = '';\n    this.password = '';\n    this.loginError = '';\n  }\n  onLogin() {\n    this.authService.login(this.email, this.password).subscribe(response => {\n      this.authService.storeToken(response.token);\n      const {\n        role,\n        groupe\n      } = this.authService.getUserDetailsFromToken(response.token);\n      console.log(\"Login successful, User role:\", role, \"Group:\", groupe);\n      if (role === 'RESPONSABLE') {\n        if (groupe) {\n          this.redirectToGroupDashboard(groupe);\n        } else {\n          console.error(\"RESPONSABLE must have a group.\");\n          this.loginError = \"RESPONSABLE must have a group.\";\n        }\n      } else if (role === 'ADMIN') {\n        this.router.navigate(['/adminDash']);\n      } else {\n        this.loginError = \"Email ou mot de passe incorrect!\";\n      }\n    }, error => {\n      console.error(\"Login failed:\", error);\n      this.loginError = \"Email ou mot de passe incorrect!\";\n    });\n  }\n  redirectToGroupDashboard(groupe) {\n    const allowedGroups = {\n      'TRANSACTION': '/transactions',\n      'ACTION': '/actions',\n      'ACTIONNAIRE': '/actionnaires',\n      'PORTEFEUILLE': '/port',\n      'NOTIFICATION': '/reports'\n    };\n    const redirectUrl = allowedGroups[groupe];\n    if (redirectUrl) {\n      this.router.navigate([redirectUrl]);\n    } else {\n      console.error(\"Invalid group:\", groupe);\n      this.router.navigate(['/not-authorized']);\n    }\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 33,\n      vars: 3,\n      consts: [[1, \"container\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"vh-100\"], [1, \"card\", \"p-4\", \"shadow-lg\", \"rounded\", 2, \"width\", \"400px\"], [1, \"text-center\"], [1, \"mb-3\"], [1, \"text-muted\"], [3, \"ngSubmit\"], [\"for\", \"email\", 1, \"form-label\", \"fw-bold\"], [1, \"input-group\"], [1, \"input-group-text\"], [1, \"bi\", \"bi-envelope\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"placeholder\", \"Enter your email\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\", 1, \"form-label\", \"fw-bold\"], [1, \"bi\", \"bi-lock\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"placeholder\", \"Enter your password\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\", \"fw-bold\"], [1, \"bi\", \"bi-box-arrow-in-right\"], [\"class\", \"alert alert-danger text-center mt-3\", 4, \"ngIf\"], [1, \"text-center\", \"mt-3\"], [\"href\", \"#\", 1, \"text-decoration-none\"], [\"routerLink\", \"/signup\", 1, \"btn\", \"btn-outline-secondary\", \"w-100\"], [1, \"bi\", \"bi-person-plus\"], [1, \"alert\", \"alert-danger\", \"text-center\", \"mt-3\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵtext(4, \"Se connecter\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Connectez-vous \\u00E0 votre compte \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onLogin();\n          });\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"label\", 6);\n          i0.ɵɵtext(10, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"span\", 8);\n          i0.ɵɵelement(13, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_14_listener($event) {\n            return ctx.email = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 3)(16, \"label\", 11);\n          i0.ɵɵtext(17, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 7)(19, \"span\", 8);\n          i0.ɵɵelement(20, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.password = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"button\", 14);\n          i0.ɵɵelement(23, \"i\", 15);\n          i0.ɵɵtext(24, \" Se connecter \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, LoginComponent_div_25_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementStart(26, \"div\", 17)(27, \"a\", 18);\n          i0.ɵɵtext(28, \"Mot de passe oubli\\u00E9 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 17)(30, \"a\", 19);\n          i0.ɵɵelement(31, \"i\", 20);\n          i0.ɵɵtext(32, \" S'inscrire \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.email);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.password);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.loginError);\n        }\n      },\n      dependencies: [i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm, i2.RouterLink],\n      styles: [\"body[_ngcontent-%COMP%] {\\n    background-image: url('/assets/images/login.jpg');\\n    background-size: cover;\\n    background-position: center;\\n    background-repeat: no-repeat;\\n    background-attachment: fixed;\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXV0aC9sb2dpbi9sb2dpbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0lBQ0ksaURBQWlEO0lBQ2pELHNCQUFzQjtJQUN0QiwyQkFBMkI7SUFDM0IsNEJBQTRCO0lBQzVCLDRCQUE0QjtFQUM5QiIsInNvdXJjZXNDb250ZW50IjpbImJvZHkge1xyXG4gICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCcvYXNzZXRzL2ltYWdlcy9sb2dpbi5qcGcnKTtcclxuICAgIGJhY2tncm91bmQtc2l6ZTogY292ZXI7XHJcbiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBjZW50ZXI7XHJcbiAgICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xyXG4gICAgYmFja2dyb3VuZC1hdHRhY2htZW50OiBmaXhlZDtcclxuICB9XHJcbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "loginError", "LoginComponent", "constructor", "authService", "router", "email", "password", "onLogin", "login", "subscribe", "response", "storeToken", "token", "role", "groupe", "getUserDetailsFromToken", "console", "log", "redirectToGroupDashboard", "error", "navigate", "allowedGroups", "redirectUrl", "ɵɵdirectiveInject", "i1", "AuthenticationService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_7_listener", "ɵɵelement", "LoginComponent_Template_input_ngModelChange_14_listener", "$event", "LoginComponent_Template_input_ngModelChange_21_listener", "ɵɵtemplate", "LoginComponent_div_25_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\auth\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { AuthenticationService } from '../authentication.service';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent {\n  email = '';\n  password = '';\n  loginError: string = '';\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  onLogin() {\n    this.authService.login(this.email, this.password).subscribe(\n      response => {\n        this.authService.storeToken(response.token);\n\n        const { role, groupe } = this.authService.getUserDetailsFromToken(response.token);\n        console.log(\"Login successful, User role:\", role, \"Group:\", groupe);\n\n        if (role === 'RESPONSABLE') {\n          if (groupe) {\n            this.redirectToGroupDashboard(groupe);\n          } else {\n            console.error(\"RESPONSABLE must have a group.\");\n            this.loginError = \"RESPONSABLE must have a group.\";\n          }\n        } else if (role === 'ADMIN') {\n          this.router.navigate(['/adminDash']);\n        } else {\n          this.loginError = \"Email ou mot de passe incorrect!\";\n        }\n      },\n      error => {\n        console.error(\"Login failed:\", error);\n        this.loginError = \"Email ou mot de passe incorrect!\";\n      }\n    );\n  }\n\n  private redirectToGroupDashboard(groupe: string) {\n    const allowedGroups: Record<string, string> = {\n      'TRANSACTION': '/transactions',\n      'ACTION': '/actions',\n      'ACTIONNAIRE': '/actionnaires',\n      'PORTEFEUILLE': '/port',\n      'NOTIFICATION': '/reports'\n    };\n\n    const redirectUrl = allowedGroups[groupe];\n\n    if (redirectUrl) {\n      this.router.navigate([redirectUrl]);\n    } else {\n      console.error(\"Invalid group:\", groupe);\n      this.router.navigate(['/not-authorized']);\n    }\n  }\n}\n", "<div class=\"container d-flex justify-content-center align-items-center vh-100\">\n  <div class=\"card p-4 shadow-lg rounded\" style=\"width: 400px;\">\n    <div class=\"text-center\">\n      <h2 class=\"mb-3\">Se connecter</h2>\n      <p class=\"text-muted\">Connectez-vous à votre compte\n      </p>\n    </div>\n\n    <form (ngSubmit)=\"onLogin()\">\n      <!-- Email Field -->\n      <div class=\"mb-3\">\n        <label for=\"email\" class=\"form-label fw-bold\">Email</label>\n        <div class=\"input-group\">\n          <span class=\"input-group-text\"><i class=\"bi bi-envelope\"></i></span>\n          <input \n            type=\"email\" \n            id=\"email\" \n            class=\"form-control\" \n            [(ngModel)]=\"email\" \n            name=\"email\" \n            required \n            placeholder=\"Enter your email\" />\n        </div>\n      </div>\n\n      <!-- Password Field -->\n      <div class=\"mb-3\">\n        <label for=\"password\" class=\"form-label fw-bold\">Password</label>\n        <div class=\"input-group\">\n          <span class=\"input-group-text\"><i class=\"bi bi-lock\"></i></span>\n          <input \n            type=\"password\" \n            id=\"password\" \n            class=\"form-control\" \n            [(ngModel)]=\"password\" \n            name=\"password\" \n            required \n            placeholder=\"Enter your password\" />\n        </div>\n      </div>\n\n      <!-- Login Button -->\n      <button type=\"submit\" class=\"btn btn-primary w-100 fw-bold\">\n        <i class=\"bi bi-box-arrow-in-right\"></i> Se connecter\n\n      </button>\n\n      <!-- Error Message -->\n      <div *ngIf=\"loginError\" class=\"alert alert-danger text-center mt-3\">\n        {{ loginError }}\n      </div>\n\n      <!-- Additional Links -->\n      <div class=\"text-center mt-3\">\n        <a href=\"#\" class=\"text-decoration-none\">Mot de passe oublié </a>\n      </div>\n\n      <!-- Sign Up Button -->\n      <div class=\"text-center mt-3\">\n        <a routerLink=\"/signup\" class=\"btn btn-outline-secondary w-100\">\n          <i class=\"bi bi-person-plus\"></i> S'inscrire\n\n        </a>\n      </div>\n    </form>\n  </div>\n</div>\n"], "mappings": ";;;;;;;ICgDMA,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,UAAA,MACF;;;ADzCN,OAAM,MAAOC,cAAc;EAKzBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;IAJtE,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAN,UAAU,GAAW,EAAE;EAE0D;EAEjFO,OAAOA,CAAA;IACL,IAAI,CAACJ,WAAW,CAACK,KAAK,CAAC,IAAI,CAACH,KAAK,EAAE,IAAI,CAACC,QAAQ,CAAC,CAACG,SAAS,CACzDC,QAAQ,IAAG;MACT,IAAI,CAACP,WAAW,CAACQ,UAAU,CAACD,QAAQ,CAACE,KAAK,CAAC;MAE3C,MAAM;QAAEC,IAAI;QAAEC;MAAM,CAAE,GAAG,IAAI,CAACX,WAAW,CAACY,uBAAuB,CAACL,QAAQ,CAACE,KAAK,CAAC;MACjFI,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEJ,IAAI,EAAE,QAAQ,EAAEC,MAAM,CAAC;MAEnE,IAAID,IAAI,KAAK,aAAa,EAAE;QAC1B,IAAIC,MAAM,EAAE;UACV,IAAI,CAACI,wBAAwB,CAACJ,MAAM,CAAC;SACtC,MAAM;UACLE,OAAO,CAACG,KAAK,CAAC,gCAAgC,CAAC;UAC/C,IAAI,CAACnB,UAAU,GAAG,gCAAgC;;OAErD,MAAM,IAAIa,IAAI,KAAK,OAAO,EAAE;QAC3B,IAAI,CAACT,MAAM,CAACgB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;OACrC,MAAM;QACL,IAAI,CAACpB,UAAU,GAAG,kCAAkC;;IAExD,CAAC,EACDmB,KAAK,IAAG;MACNH,OAAO,CAACG,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,IAAI,CAACnB,UAAU,GAAG,kCAAkC;IACtD,CAAC,CACF;EACH;EAEQkB,wBAAwBA,CAACJ,MAAc;IAC7C,MAAMO,aAAa,GAA2B;MAC5C,aAAa,EAAE,eAAe;MAC9B,QAAQ,EAAE,UAAU;MACpB,aAAa,EAAE,eAAe;MAC9B,cAAc,EAAE,OAAO;MACvB,cAAc,EAAE;KACjB;IAED,MAAMC,WAAW,GAAGD,aAAa,CAACP,MAAM,CAAC;IAEzC,IAAIQ,WAAW,EAAE;MACf,IAAI,CAAClB,MAAM,CAACgB,QAAQ,CAAC,CAACE,WAAW,CAAC,CAAC;KACpC,MAAM;MACLN,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAEL,MAAM,CAAC;MACvC,IAAI,CAACV,MAAM,CAACgB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;;EAE7C;;;uBApDWnB,cAAc,EAAAR,EAAA,CAAA8B,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAhC,EAAA,CAAA8B,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAd1B,cAAc;MAAA2B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT3BzC,EAAA,CAAAC,cAAA,aAA+E;UAGxDD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,WAAsB;UAAAD,EAAA,CAAAE,MAAA,0CACtB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,cAA6B;UAAvBD,EAAA,CAAA2C,UAAA,sBAAAC,iDAAA;YAAA,OAAYF,GAAA,CAAA5B,OAAA,EAAS;UAAA,EAAC;UAE1Bd,EAAA,CAAAC,cAAA,aAAkB;UAC8BD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3DH,EAAA,CAAAC,cAAA,cAAyB;UACQD,EAAA,CAAA6C,SAAA,YAA8B;UAAA7C,EAAA,CAAAG,YAAA,EAAO;UACpEH,EAAA,CAAAC,cAAA,iBAOmC;UAHjCD,EAAA,CAAA2C,UAAA,2BAAAG,wDAAAC,MAAA;YAAA,OAAAL,GAAA,CAAA9B,KAAA,GAAAmC,MAAA;UAAA,EAAmB;UAJrB/C,EAAA,CAAAG,YAAA,EAOmC;UAKvCH,EAAA,CAAAC,cAAA,cAAkB;UACiCD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjEH,EAAA,CAAAC,cAAA,cAAyB;UACQD,EAAA,CAAA6C,SAAA,aAA0B;UAAA7C,EAAA,CAAAG,YAAA,EAAO;UAChEH,EAAA,CAAAC,cAAA,iBAOsC;UAHpCD,EAAA,CAAA2C,UAAA,2BAAAK,wDAAAD,MAAA;YAAA,OAAAL,GAAA,CAAA7B,QAAA,GAAAkC,MAAA;UAAA,EAAsB;UAJxB/C,EAAA,CAAAG,YAAA,EAOsC;UAK1CH,EAAA,CAAAC,cAAA,kBAA4D;UAC1DD,EAAA,CAAA6C,SAAA,aAAwC;UAAC7C,EAAA,CAAAE,MAAA,sBAE3C;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAiD,UAAA,KAAAC,8BAAA,kBAEM;UAGNlD,EAAA,CAAAC,cAAA,eAA8B;UACaD,EAAA,CAAAE,MAAA,iCAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAInEH,EAAA,CAAAC,cAAA,eAA8B;UAE1BD,EAAA,CAAA6C,SAAA,aAAiC;UAAC7C,EAAA,CAAAE,MAAA,oBAEpC;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UA5CAH,EAAA,CAAAI,SAAA,IAAmB;UAAnBJ,EAAA,CAAAmD,UAAA,YAAAT,GAAA,CAAA9B,KAAA,CAAmB;UAgBnBZ,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAmD,UAAA,YAAAT,GAAA,CAAA7B,QAAA,CAAsB;UActBb,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAmD,UAAA,SAAAT,GAAA,CAAAnC,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}