{"ast": null, "code": "/*!\n * toidentifier\n * Copyright(c) 2016 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\nmodule.exports = toIdentifier;\n\n/**\n * Trasform the given string into a JavaScript identifier\n *\n * @param {string} str\n * @returns {string}\n * @public\n */\n\nfunction toIdentifier(str) {\n  return str.split(' ').map(function (token) {\n    return token.slice(0, 1).toUpperCase() + token.slice(1);\n  }).join('').replace(/[^ _0-9a-z]/gi, '');\n}", "map": {"version": 3, "names": ["module", "exports", "toIdentifier", "str", "split", "map", "token", "slice", "toUpperCase", "join", "replace"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/toidentifier/index.js"], "sourcesContent": ["/*!\n * toidentifier\n * Copyright(c) 2016 <PERSON>\n * MIT Licensed\n */\n\n'use strict'\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = toIdentifier\n\n/**\n * Trasform the given string into a JavaScript identifier\n *\n * @param {string} str\n * @returns {string}\n * @public\n */\n\nfunction toIdentifier (str) {\n  return str\n    .split(' ')\n    .map(function (token) {\n      return token.slice(0, 1).toUpperCase() + token.slice(1)\n    })\n    .join('')\n    .replace(/[^ _0-9a-z]/gi, '')\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AAEAA,MAAM,CAACC,OAAO,GAAGC,YAAY;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,YAAYA,CAAEC,GAAG,EAAE;EAC1B,OAAOA,GAAG,CACPC,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAC,UAAUC,KAAK,EAAE;IACpB,OAAOA,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;EACzD,CAAC,CAAC,CACDE,IAAI,CAAC,EAAE,CAAC,CACRC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}