import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoginComponent } from './auth/login/login.component';
import { SignupComponent } from './auth/signup/signup.component';
import { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';
import { NotAuthorizedComponent } from './not-authorized/not-authorized.component';
import { AuthGuard } from './auth/auth.guard';
import { TransactionsComponent } from './transactions/transactions.component';
import { ActionsComponent } from './actions/actions.component';
import { ActionnairesComponent } from './actionnaires/actionnaires.component';
import { ReportsComponent } from './reports/reports.component';
import { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';
import { AdminDashComponent } from './admin-dash/admin-dash.component';
import { ResponsableEditComponent } from './responsable-edit/responsable-edit.component';
import { UsersComponent } from './users/users.component';
import { GroupsComponent } from './groups/groups.component';
import { ModifyGroupComponent } from './modify-group/modify-group.component';
import { AddResponsableComponent } from './add-responsable/add-responsable.component';  // Import AddResponsableComponent
import { HabilitationComponent } from './habilitation/habilitation.component';  // Import the component
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';

const routes: Routes = [
  { path: '', redirectTo: 'login', pathMatch: 'full' },
  { path: 'login', component: LoginComponent },
  { path: 'signup', component: SignupComponent },
  { path: 'edit-responsable/:id', component: ResponsableEditComponent },
  { path: 'adminDash', component: AdminDashComponent, canActivate: [AuthGuard] },
  { path: 'transactions', component: TransactionsComponent, canActivate: [AuthGuard] },
  { path: 'actions', component: ActionsComponent, canActivate: [AuthGuard] },
  { path: 'actionnaires', component: ActionnairesComponent, canActivate: [AuthGuard] },
  { path: 'reports', component: ReportsComponent, canActivate: [AuthGuard] },
  { path: 'port', component: PortefeuillesComponent, canActivate: [AuthGuard] },
  { path: 'ResDash', component: ResponDashboardComponent, canActivate: [AuthGuard] },
  { path: 'users', component: UsersComponent, canActivate: [AuthGuard] }, // Admin only
  { path: 'groups', component: GroupsComponent, canActivate: [AuthGuard] }, // Admin only
  { path: 'edit-groupe/:id', component: ModifyGroupComponent }, // Added route for editing a group
  { path: 'add-responsable', component: AddResponsableComponent, canActivate: [AuthGuard] },  // Added route for AddResponsableComponent
  { path: 'not-authorized', component: NotAuthorizedComponent },
  { path: 'habilitation/:id', component: HabilitationComponent, canActivate: [AuthGuard] },  // Route with the dynamic id
  { path: 'forgot-password', component: ForgotPasswordComponent },

];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
