{"ast": null, "code": "import { Role } from '../model/role.enum';\nimport { Groupe } from '../model/groupe.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../responsable.service\";\nimport * as i3 from \"@angular/forms\";\nexport class ResponsableEditComponent {\n  constructor(route, responsableService, router) {\n    this.route = route;\n    this.responsableService = responsableService;\n    this.router = router;\n    this.responsableId = 0;\n    this.responsable = {\n      id: 0,\n      nom: '',\n      prenom: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE'\n    };\n    this.roles = Object.values(Role); // Dynamically get roles from the Role enum\n    this.groupes = Object.values(Groupe); // Dynamically get groupes from the Groupe enum\n  }\n\n  ngOnInit() {\n    // Get the ID from the route\n    this.responsableId = Number(this.route.snapshot.paramMap.get('id'));\n    this.getResponsable(this.responsableId); // Fetch user data\n  }\n\n  getResponsable(id) {\n    this.responsableService.getResponsableById(id).subscribe(data => {\n      this.responsable = data; // Bind the fetched data to the responsable object\n    }, error => {\n      console.error('Error fetching responsable:', error);\n    });\n  }\n  updateResponsable() {\n    // Send updated data to the backend\n    this.responsableService.updateResponsable(this.responsableId, this.responsable).subscribe(response => {\n      console.log('Responsable updated successfully', response);\n      this.router.navigate(['/users']); // Navigate after successful update\n    }, error => {\n      console.error('Error updating responsable:', error);\n    });\n  }\n  static {\n    this.ɵfac = function ResponsableEditComponent_Factory(t) {\n      return new (t || ResponsableEditComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ResponsableService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResponsableEditComponent,\n      selectors: [[\"app-responsable-edit\"]],\n      decls: 22,\n      vars: 4,\n      consts: [[1, \"container\", \"mt-4\"], [3, \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"nom\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"nom\", \"name\", \"nom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"prenom\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"prenom\", \"name\", \"prenom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"telephone\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"telephone\", \"name\", \"telephone\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"]],\n      template: function ResponsableEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Edit Responsable\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"form\", 1);\n          i0.ɵɵlistener(\"ngSubmit\", function ResponsableEditComponent_Template_form_ngSubmit_3_listener() {\n            return ctx.updateResponsable();\n          });\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"input\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_7_listener($event) {\n            return ctx.responsable.nom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 2)(9, \"label\", 5);\n          i0.ɵɵtext(10, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"input\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.responsable.prenom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 2)(13, \"label\", 7);\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 8);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.responsable.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 2)(17, \"label\", 9);\n          i0.ɵɵtext(18, \"Telephone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"input\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_19_listener($event) {\n            return ctx.responsable.telephone = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"button\", 11);\n          i0.ɵɵtext(21, \"Update Responsable\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.nom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.prenom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.telephone);\n        }\n      },\n      dependencies: [i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Role", "Groupe", "ResponsableEditComponent", "constructor", "route", "responsableService", "router", "responsableId", "responsable", "id", "nom", "prenom", "email", "telephone", "role", "roles", "Object", "values", "groupes", "ngOnInit", "Number", "snapshot", "paramMap", "get", "getResponsable", "getResponsableById", "subscribe", "data", "error", "console", "updateResponsable", "response", "log", "navigate", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ResponsableService", "Router", "selectors", "decls", "vars", "consts", "template", "ResponsableEditComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ResponsableEditComponent_Template_form_ngSubmit_3_listener", "ResponsableEditComponent_Template_input_ngModelChange_7_listener", "$event", "ResponsableEditComponent_Template_input_ngModelChange_11_listener", "ResponsableEditComponent_Template_input_ngModelChange_15_listener", "ResponsableEditComponent_Template_input_ngModelChange_19_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\responsable-edit\\responsable-edit.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\responsable-edit\\responsable-edit.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport { Role } from '../model/role.enum';\nimport { Groupe } from '../model/groupe.enum';\n\n@Component({\n  selector: 'app-responsable-edit',\n  templateUrl: './responsable-edit.component.html',\n  styleUrls: ['./responsable-edit.component.css']\n})\nexport class ResponsableEditComponent implements OnInit {\n  responsableId: number = 0;\n  responsable: User = { id: 0, nom: '', prenom: '', email: '', telephone: '', role: 'RESPONSABLE' };\n  roles = Object.values(Role); // Dynamically get roles from the Role enum\n  groupes = Object.values(Groupe); // Dynamically get groupes from the Groupe enum\n\n  constructor(\n    private route: ActivatedRoute,\n    private responsableService: ResponsableService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    // Get the ID from the route\n    this.responsableId = Number(this.route.snapshot.paramMap.get('id'));\n    this.getResponsable(this.responsableId); // Fetch user data\n  }\n\n  getResponsable(id: number) {\n    this.responsableService.getResponsableById(id).subscribe(\n      (data) => {\n        this.responsable = data; // Bind the fetched data to the responsable object\n      },\n      (error) => {\n        console.error('Error fetching responsable:', error);\n      }\n    );\n  }\n\n  updateResponsable() {\n    // Send updated data to the backend\n    this.responsableService.updateResponsable(this.responsableId, this.responsable).subscribe(\n      (response) => {\n        console.log('Responsable updated successfully', response);\n        this.router.navigate(['/users']); // Navigate after successful update\n      },\n      (error) => {\n        console.error('Error updating responsable:', error);\n      }\n    );\n  }\n}\n", "<div class=\"container mt-4\">\n  <h2>Edit Responsable</h2>\n  <form (ngSubmit)=\"updateResponsable()\">\n    <!-- First Name -->\n    <div class=\"mb-3\">\n      <label for=\"nom\" class=\"form-label\">First Name</label>\n      <input type=\"text\" id=\"nom\" [(ngModel)]=\"responsable.nom\" name=\"nom\" class=\"form-control\" required />\n    </div>\n\n    <!-- Last Name -->\n    <div class=\"mb-3\">\n      <label for=\"prenom\" class=\"form-label\">Last Name</label>\n      <input type=\"text\" id=\"prenom\" [(ngModel)]=\"responsable.prenom\" name=\"prenom\" class=\"form-control\" required />\n    </div>\n\n    <!-- Email -->\n    <div class=\"mb-3\">\n      <label for=\"email\" class=\"form-label\">Email</label>\n      <input type=\"email\" id=\"email\" [(ngModel)]=\"responsable.email\" name=\"email\" class=\"form-control\" required />\n    </div>\n\n    <!-- Telephone -->\n    <div class=\"mb-3\">\n      <label for=\"telephone\" class=\"form-label\">Telephone</label>\n      <input type=\"number\" id=\"telephone\" [(ngModel)]=\"responsable.telephone\" name=\"telephone\" class=\"form-control\" required />\n    </div>\n\n    <!-- Submit Button -->\n    <button type=\"submit\" class=\"btn btn-primary\">Update Responsable</button>\n  </form>\n</div>\n"], "mappings": "AAIA,SAASA,IAAI,QAAQ,oBAAoB;AACzC,SAASC,MAAM,QAAQ,sBAAsB;;;;;AAO7C,OAAM,MAAOC,wBAAwB;EAMnCC,YACUC,KAAqB,EACrBC,kBAAsC,EACtCC,MAAc;IAFd,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAC,aAAa,GAAW,CAAC;IACzB,KAAAC,WAAW,GAAS;MAAEC,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAa,CAAE;IACjG,KAAAC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACjB,IAAI,CAAC,CAAC,CAAC;IAC7B,KAAAkB,OAAO,GAAGF,MAAM,CAACC,MAAM,CAAChB,MAAM,CAAC,CAAC,CAAC;EAM9B;;EAEHkB,QAAQA,CAAA;IACN;IACA,IAAI,CAACZ,aAAa,GAAGa,MAAM,CAAC,IAAI,CAAChB,KAAK,CAACiB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnE,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjB,aAAa,CAAC,CAAC,CAAC;EAC3C;;EAEAiB,cAAcA,CAACf,EAAU;IACvB,IAAI,CAACJ,kBAAkB,CAACoB,kBAAkB,CAAChB,EAAE,CAAC,CAACiB,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAACnB,WAAW,GAAGmB,IAAI,CAAC,CAAC;IAC3B,CAAC,EACAC,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CACF;EACH;EAEAE,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACzB,kBAAkB,CAACyB,iBAAiB,CAAC,IAAI,CAACvB,aAAa,EAAE,IAAI,CAACC,WAAW,CAAC,CAACkB,SAAS,CACtFK,QAAQ,IAAI;MACXF,OAAO,CAACG,GAAG,CAAC,kCAAkC,EAAED,QAAQ,CAAC;MACzD,IAAI,CAACzB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,EACAL,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CACF;EACH;;;uBAxCW1B,wBAAwB,EAAAgC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAI,MAAA;IAAA;EAAA;;;YAAxBtC,wBAAwB;MAAAuC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZrCb,EAAA,CAAAe,cAAA,aAA4B;UACtBf,EAAA,CAAAgB,MAAA,uBAAgB;UAAAhB,EAAA,CAAAiB,YAAA,EAAK;UACzBjB,EAAA,CAAAe,cAAA,cAAuC;UAAjCf,EAAA,CAAAkB,UAAA,sBAAAC,2DAAA;YAAA,OAAYL,GAAA,CAAAlB,iBAAA,EAAmB;UAAA,EAAC;UAEpCI,EAAA,CAAAe,cAAA,aAAkB;UACoBf,EAAA,CAAAgB,MAAA,iBAAU;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UACtDjB,EAAA,CAAAe,cAAA,eAAqG;UAAzEf,EAAA,CAAAkB,UAAA,2BAAAE,iEAAAC,MAAA;YAAA,OAAAP,GAAA,CAAAxC,WAAA,CAAAE,GAAA,GAAA6C,MAAA;UAAA,EAA6B;UAAzDrB,EAAA,CAAAiB,YAAA,EAAqG;UAIvGjB,EAAA,CAAAe,cAAA,aAAkB;UACuBf,EAAA,CAAAgB,MAAA,iBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UACxDjB,EAAA,CAAAe,cAAA,gBAA8G;UAA/Ef,EAAA,CAAAkB,UAAA,2BAAAI,kEAAAD,MAAA;YAAA,OAAAP,GAAA,CAAAxC,WAAA,CAAAG,MAAA,GAAA4C,MAAA;UAAA,EAAgC;UAA/DrB,EAAA,CAAAiB,YAAA,EAA8G;UAIhHjB,EAAA,CAAAe,cAAA,cAAkB;UACsBf,EAAA,CAAAgB,MAAA,aAAK;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UACnDjB,EAAA,CAAAe,cAAA,gBAA4G;UAA7Ef,EAAA,CAAAkB,UAAA,2BAAAK,kEAAAF,MAAA;YAAA,OAAAP,GAAA,CAAAxC,WAAA,CAAAI,KAAA,GAAA2C,MAAA;UAAA,EAA+B;UAA9DrB,EAAA,CAAAiB,YAAA,EAA4G;UAI9GjB,EAAA,CAAAe,cAAA,cAAkB;UAC0Bf,EAAA,CAAAgB,MAAA,iBAAS;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UAC3DjB,EAAA,CAAAe,cAAA,iBAAyH;UAArFf,EAAA,CAAAkB,UAAA,2BAAAM,kEAAAH,MAAA;YAAA,OAAAP,GAAA,CAAAxC,WAAA,CAAAK,SAAA,GAAA0C,MAAA;UAAA,EAAmC;UAAvErB,EAAA,CAAAiB,YAAA,EAAyH;UAI3HjB,EAAA,CAAAe,cAAA,kBAA8C;UAAAf,EAAA,CAAAgB,MAAA,0BAAkB;UAAAhB,EAAA,CAAAiB,YAAA,EAAS;;;UAtB3CjB,EAAA,CAAAyB,SAAA,GAA6B;UAA7BzB,EAAA,CAAA0B,UAAA,YAAAZ,GAAA,CAAAxC,WAAA,CAAAE,GAAA,CAA6B;UAM1BwB,EAAA,CAAAyB,SAAA,GAAgC;UAAhCzB,EAAA,CAAA0B,UAAA,YAAAZ,GAAA,CAAAxC,WAAA,CAAAG,MAAA,CAAgC;UAMhCuB,EAAA,CAAAyB,SAAA,GAA+B;UAA/BzB,EAAA,CAAA0B,UAAA,YAAAZ,GAAA,CAAAxC,WAAA,CAAAI,KAAA,CAA+B;UAM1BsB,EAAA,CAAAyB,SAAA,GAAmC;UAAnCzB,EAAA,CAAA0B,UAAA,YAAAZ,GAAA,CAAAxC,WAAA,CAAAK,SAAA,CAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}