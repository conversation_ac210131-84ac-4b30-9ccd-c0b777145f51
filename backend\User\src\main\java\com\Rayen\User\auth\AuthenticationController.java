package com.Rayen.User.auth;


import com.Rayen.User.Entity.User;
import com.Rayen.User.Enum.Role;
import com.Rayen.User.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;

@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
public class AuthenticationController {
    private final AuthenticationService service;
    private final UserService UserService;

    @CrossOrigin(origins = "http://localhost:4200")
    @PostMapping("/register")
    public ResponseEntity<?> register(@RequestBody RegisterRequest request) {
        AuthenticationResponse response = service.register(request);

        if (response.getToken() == null || response.getToken().isEmpty()) {
            return ResponseEntity.badRequest().body("Échec de l'inscription : Token non généré !");
        } else {
            return ResponseEntity.ok(response);
        }


    }

    @CrossOrigin(origins = "http://localhost:4200")
    @PostMapping("/authenticate")
    public ResponseEntity<AuthenticationResponse> authenticate(
            @RequestBody AuthenticationRequest request
    ) {
        return ResponseEntity.ok(service.authenticate(request));
    }


    @CrossOrigin(origins = "http://localhost:4200")
    @GetMapping("/Users")
    public ResponseEntity<List<User>> findAllUsers(@RequestParam(required = false) String id) {

        return ResponseEntity.ok(UserService.findAllUsers());


    }


    @CrossOrigin(origins = "http://localhost:4200")
    @GetMapping("/Users_Responsables")
    public ResponseEntity<List<User>> findAllResponsables(@RequestParam(required = false) String id) {
        Role role = Role.RESPONSABLE;
        return ResponseEntity.ok(UserService.findAllByRole(role));


    }


    @CrossOrigin(origins = "http://localhost:4200")
    @DeleteMapping("/Users_Responsables/delete/{id}")
    public ResponseEntity<String> deleteUser(@PathVariable Long id) {
        UserService.deleteUser(id);
        return ResponseEntity.ok("User deleted successfully welyee");
    }


    // **Update a user**
    @CrossOrigin(origins = "http://localhost:4200")
    @PutMapping("/Users_Responsables/edit/{id}")
    public ResponseEntity<User> updateUser(@PathVariable Long id, @RequestBody User updatedUser, Principal principal) {
        try {
            // This should work as your previous GET request works
            User updated = UserService.updateUser(id, updatedUser);
            return ResponseEntity.ok(updated);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);  // User not found
        }
    }
}
