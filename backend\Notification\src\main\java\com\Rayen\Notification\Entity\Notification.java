package com.Rayen.Notification.Entity;


import jakarta.persistence.*;

import java.util.Date;

@Entity
@Table(name="Notification")
public class Notification {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idNotification;
    private Date DateNotification;
    private Boolean status;

    public Long getIdNotification() {
        return idNotification;
    }

    public void setIdNotification(Long idNotification) {
        this.idNotification = idNotification;
    }

    public Date getDateNotification() {
        return DateNotification;
    }

    public void setDateNotification(Date dateNotification) {
        DateNotification = dateNotification;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }
}
