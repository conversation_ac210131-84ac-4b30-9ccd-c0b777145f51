{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/groupe.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction ModifyGroupComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModifyGroupComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h2\", 2);\n    i0.ɵɵtext(2, \"Group not found or an error occurred\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ModifyGroupComponent_div_2_form_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 4);\n    i0.ɵɵlistener(\"ngSubmit\", function ModifyGroupComponent_div_2_form_3_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.updateGroupe());\n    });\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"label\", 6);\n    i0.ɵɵtext(3, \"Nom du groupe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 7);\n    i0.ɵɵlistener(\"ngModelChange\", function ModifyGroupComponent_div_2_form_3_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.groupe.nomGroupe = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 5)(6, \"button\", 8);\n    i0.ɵɵtext(7, \"Mettre \\u00E0 jour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ModifyGroupComponent_div_2_form_3_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.cancel());\n    });\n    i0.ɵɵtext(9, \"Annuler\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.groupe.nomGroupe);\n  }\n}\nfunction ModifyGroupComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"h2\", 2);\n    i0.ɵɵtext(2, \"Modifier le groupe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ModifyGroupComponent_div_2_form_3_Template, 10, 1, \"form\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.groupe);\n  }\n}\nexport class ModifyGroupComponent {\n  constructor(route, groupeService, router) {\n    this.route = route;\n    this.groupeService = groupeService;\n    this.router = router;\n    this.groupe = null; // Group to modify\n    this.idGroupe = null; // Group ID from route\n    this.loading = true; // Loading state for UI\n  }\n\n  ngOnInit() {\n    console.log(\"ModifyGroupComponent initialized\");\n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.idGroupe = +id; // Convert to number\n      console.log(\"Fetching group with ID:\", this.idGroupe);\n      this.getGroupe(this.idGroupe); // Fetch group data\n    }\n  }\n\n  getGroupe(id) {\n    this.groupeService.getGroupeById(id).subscribe(data => {\n      console.log(\"Group data:\", data);\n      this.groupe = data; // Fill group data\n      this.loading = false; // Stop loading\n    }, err => {\n      console.error('Error loading group data', err);\n      this.loading = false;\n    });\n  }\n  // Save changes to the group\n  updateGroupe() {\n    if (this.groupe && this.groupe.idGroupe) {\n      this.groupeService.updateGroupe(this.groupe.idGroupe, this.groupe).subscribe(() => {\n        console.log(\"Group updated successfully\");\n        this.router.navigate(['/groups']); // Navigate after successful update\n      }, err => {\n        console.error('Error updating group', err);\n      });\n    }\n  }\n  // Handle cancel\n  cancel() {\n    this.router.navigate(['/groups']); // Navigate back to groups page\n  }\n\n  static {\n    this.ɵfac = function ModifyGroupComponent_Factory(t) {\n      return new (t || ModifyGroupComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.GroupeService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModifyGroupComponent,\n      selectors: [[\"app-modify-group\"]],\n      decls: 3,\n      vars: 3,\n      consts: [[\"class\", \"text-center\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\"], [3, \"ngSubmit\", 4, \"ngIf\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"nomGroupe\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", \"placeholder\", \"Enter group name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"]],\n      template: function ModifyGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ModifyGroupComponent_div_0_Template, 2, 0, \"div\", 0);\n          i0.ɵɵtemplate(1, ModifyGroupComponent_div_1_Template, 3, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, ModifyGroupComponent_div_2_Template, 4, 1, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.groupe);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.groupe);\n        }\n      },\n      dependencies: [i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ModifyGroupComponent_div_2_form_3_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "updateGroupe", "ModifyGroupComponent_div_2_form_3_Template_input_ngModelChange_4_listener", "$event", "ctx_r6", "groupe", "nomGroupe", "ModifyGroupComponent_div_2_form_3_Template_button_click_8_listener", "ctx_r7", "cancel", "ɵɵadvance", "ɵɵproperty", "ctx_r3", "ɵɵtemplate", "ModifyGroupComponent_div_2_form_3_Template", "ctx_r2", "ModifyGroupComponent", "constructor", "route", "groupeService", "router", "idGroupe", "loading", "ngOnInit", "console", "log", "id", "snapshot", "paramMap", "get", "getGroupe", "getGroupeById", "subscribe", "data", "err", "error", "navigate", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "GroupeService", "Router", "selectors", "decls", "vars", "consts", "template", "ModifyGroupComponent_Template", "rf", "ctx", "ModifyGroupComponent_div_0_Template", "ModifyGroupComponent_div_1_Template", "ModifyGroupComponent_div_2_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\modify-group\\modify-group.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\modify-group\\modify-group.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\n\n@Component({\n  selector: 'app-modify-group',\n  templateUrl: './modify-group.component.html',\n  styleUrls: ['./modify-group.component.css']\n})\nexport class ModifyGroupComponent implements OnInit {\n  groupe: Groupe | null = null; // Group to modify\n  idGroupe: number | null = null; // Group ID from route\n  loading: boolean = true; // Loading state for UI\n\n  constructor(\n    private route: ActivatedRoute,\n    private groupeService: GroupeService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    console.log(\"ModifyGroupComponent initialized\");\n  \n    const id = this.route.snapshot.paramMap.get('id');\n    if (id) {\n      this.idGroupe = +id; // Convert to number\n      console.log(\"Fetching group with ID:\", this.idGroupe);\n      this.getGroupe(this.idGroupe); // Fetch group data\n    }\n  }\n\n  getGroupe(id: number): void {\n    this.groupeService.getGroupeById(id).subscribe(\n      (data: Groupe) => {\n        console.log(\"Group data:\", data);\n        this.groupe = data; // Fill group data\n        this.loading = false; // Stop loading\n      },\n      (err) => {\n        console.error('Error loading group data', err);\n        this.loading = false;\n      }\n    );\n  }\n\n  // Save changes to the group\n  updateGroupe(): void {\n    if (this.groupe && this.groupe.idGroupe) {\n      this.groupeService.updateGroupe(this.groupe.idGroupe, this.groupe).subscribe(\n        () => {\n          console.log(\"Group updated successfully\");\n          this.router.navigate(['/groups']); // Navigate after successful update\n        },\n        (err) => {\n          console.error('Error updating group', err);\n        }\n      );\n    }\n  }\n\n  // Handle cancel\n  cancel(): void {\n    this.router.navigate(['/groups']); // Navigate back to groups page\n  }\n}\n", "<div *ngIf=\"loading\" class=\"text-center\">Loading...</div>\n\n<div *ngIf=\"!loading && !groupe\">\n  <h2 class=\"text-center\">Group not found or an error occurred</h2>\n</div>\n\n<div *ngIf=\"!loading && groupe\">\n  <h2 class=\"text-center\">Modifier le groupe</h2>\n\n  <form (ngSubmit)=\"updateGroupe()\" *ngIf=\"groupe\">\n    <div class=\"form-group\">\n      <label for=\"nomGroupe\">Nom du groupe</label>\n      <input\n        type=\"text\"\n        id=\"nomGroupe\"\n        [(ngModel)]=\"groupe.nomGroupe\"\n        name=\"nomGroupe\"\n        class=\"form-control\"\n        required\n        placeholder=\"Enter group name\"\n      />\n    </div>\n\n    <!-- You can add more fields for the group if needed -->\n    \n    <div class=\"form-group\">\n      <button type=\"submit\" class=\"btn btn-primary\">Mettre à jour</button>\n      <button type=\"button\" class=\"btn btn-secondary\" (click)=\"cancel()\">Annuler</button>\n    </div>\n  </form>\n</div>\n"], "mappings": ";;;;;;;ICAAA,EAAA,CAAAC,cAAA,aAAyC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAEzDH,EAAA,CAAAC,cAAA,UAAiC;IACPD,EAAA,CAAAE,MAAA,2CAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAMjEH,EAAA,CAAAC,cAAA,cAAiD;IAA3CD,EAAA,CAAAI,UAAA,sBAAAC,oEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAYT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAC/BX,EAAA,CAAAC,cAAA,aAAwB;IACCD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5CH,EAAA,CAAAC,cAAA,eAQE;IALAD,EAAA,CAAAI,UAAA,2BAAAQ,0EAAAC,MAAA;MAAAb,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAd,EAAA,CAAAS,aAAA;MAAA,OAAaT,EAAA,CAAAU,WAAA,CAAAI,MAAA,CAAAC,MAAA,CAAAC,SAAA,GAAAH,MAAA,CACf;IAAA,EADgC;IAHhCb,EAAA,CAAAG,YAAA,EAQE;IAKJH,EAAA,CAAAC,cAAA,aAAwB;IACwBD,EAAA,CAAAE,MAAA,yBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,gBAAmE;IAAnBD,EAAA,CAAAI,UAAA,mBAAAa,mEAAA;MAAAjB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAW,MAAA,GAAAlB,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAQ,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAACnB,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAZjFH,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAAqB,UAAA,YAAAC,MAAA,CAAAP,MAAA,CAAAC,SAAA,CAA8B;;;;;IATtChB,EAAA,CAAAC,cAAA,UAAgC;IACND,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE/CH,EAAA,CAAAuB,UAAA,IAAAC,0CAAA,mBAoBO;IACTxB,EAAA,CAAAG,YAAA,EAAM;;;;IArB+BH,EAAA,CAAAoB,SAAA,GAAY;IAAZpB,EAAA,CAAAqB,UAAA,SAAAI,MAAA,CAAAV,MAAA,CAAY;;;ADCjD,OAAM,MAAOW,oBAAoB;EAK/BC,YACUC,KAAqB,EACrBC,aAA4B,EAC5BC,MAAc;IAFd,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAf,MAAM,GAAkB,IAAI,CAAC,CAAC;IAC9B,KAAAgB,QAAQ,GAAkB,IAAI,CAAC,CAAC;IAChC,KAAAC,OAAO,GAAY,IAAI,CAAC,CAAC;EAMtB;;EAEHC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAE/C,MAAMC,EAAE,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAIH,EAAE,EAAE;MACN,IAAI,CAACL,QAAQ,GAAG,CAACK,EAAE,CAAC,CAAC;MACrBF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACJ,QAAQ,CAAC;MACrD,IAAI,CAACS,SAAS,CAAC,IAAI,CAACT,QAAQ,CAAC,CAAC,CAAC;;EAEnC;;EAEAS,SAASA,CAACJ,EAAU;IAClB,IAAI,CAACP,aAAa,CAACY,aAAa,CAACL,EAAE,CAAC,CAACM,SAAS,CAC3CC,IAAY,IAAI;MACfT,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEQ,IAAI,CAAC;MAChC,IAAI,CAAC5B,MAAM,GAAG4B,IAAI,CAAC,CAAC;MACpB,IAAI,CAACX,OAAO,GAAG,KAAK,CAAC,CAAC;IACxB,CAAC,EACAY,GAAG,IAAI;MACNV,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAED,GAAG,CAAC;MAC9C,IAAI,CAACZ,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEA;EACArB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACI,MAAM,IAAI,IAAI,CAACA,MAAM,CAACgB,QAAQ,EAAE;MACvC,IAAI,CAACF,aAAa,CAAClB,YAAY,CAAC,IAAI,CAACI,MAAM,CAACgB,QAAQ,EAAE,IAAI,CAAChB,MAAM,CAAC,CAAC2B,SAAS,CAC1E,MAAK;QACHR,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAACL,MAAM,CAACgB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,EACAF,GAAG,IAAI;QACNV,OAAO,CAACW,KAAK,CAAC,sBAAsB,EAAED,GAAG,CAAC;MAC5C,CAAC,CACF;;EAEL;EAEA;EACAzB,MAAMA,CAAA;IACJ,IAAI,CAACW,MAAM,CAACgB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;EACrC;;;;uBAtDWpB,oBAAoB,EAAA1B,EAAA,CAAA+C,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjD,EAAA,CAAA+C,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAnD,EAAA,CAAA+C,iBAAA,CAAAC,EAAA,CAAAI,MAAA;IAAA;EAAA;;;YAApB1B,oBAAoB;MAAA2B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVjC3D,EAAA,CAAAuB,UAAA,IAAAsC,mCAAA,iBAAyD;UAEzD7D,EAAA,CAAAuB,UAAA,IAAAuC,mCAAA,iBAEM;UAEN9D,EAAA,CAAAuB,UAAA,IAAAwC,mCAAA,iBAwBM;;;UA9BA/D,EAAA,CAAAqB,UAAA,SAAAuC,GAAA,CAAA5B,OAAA,CAAa;UAEbhC,EAAA,CAAAoB,SAAA,GAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAAuC,GAAA,CAAA5B,OAAA,KAAA4B,GAAA,CAAA7C,MAAA,CAAyB;UAIzBf,EAAA,CAAAoB,SAAA,GAAwB;UAAxBpB,EAAA,CAAAqB,UAAA,UAAAuC,GAAA,CAAA5B,OAAA,IAAA4B,GAAA,CAAA7C,MAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}