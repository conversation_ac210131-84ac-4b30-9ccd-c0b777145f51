{"ast": null, "code": "import * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js';\nimport { Modal } from 'bootstrap';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../auth/authentication.service\";\nimport * as i4 from \"../services/transaction.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"myChart\"];\nconst _c1 = [\"barChart\"];\nconst _c2 = [\"feedbackModal\"];\nfunction AdminDashComponent_a_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 53);\n    i0.ɵɵelement(1, \"span\", 54);\n    i0.ɵɵtext(2, \" Dashboard \");\n    i0.ɵɵelementStart(3, \"span\", 55);\n    i0.ɵɵtext(4, \"(current)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminDashComponent_a_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 56);\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵtext(2, \" Gestion des utilisateurs \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 58);\n    i0.ɵɵelement(1, \"span\", 59);\n    i0.ɵɵtext(2, \" Gestion des groupes \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 60);\n    i0.ɵɵelement(1, \"span\", 61);\n    i0.ɵɵtext(2, \" Gestion des transactions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 62);\n    i0.ɵɵelement(1, \"span\", 63);\n    i0.ɵɵtext(2, \" Gestion des actions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 64);\n    i0.ɵɵelement(1, \"span\", 65);\n    i0.ɵɵtext(2, \" Gestion des actionnaires \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminDashComponent_a_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 66);\n    i0.ɵɵelement(1, \"span\", 67);\n    i0.ɵɵtext(2, \" Gestion des Portefeuilles \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = \"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, 0.25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: 'Poppins', sans-serif;\\n  margin: 0;\\n  padding: 0;\\n  min-height: 100vh;\\n  background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n  background-size: 400% 400%;\\n  animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n  color: #fff;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n  0% {\\n      background-position: 0% 50%;\\n  }\\n\\n  50% {\\n      background-position: 100% 50%;\\n  }\\n\\n  100% {\\n      background-position: 0% 50%;\\n  }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  margin-top: 20px;\\n}\\n\\n\\n\\n\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n  background-color: #218838;\\n  transform: translateY(-2px);\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 1.5rem;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n  background-color: #f1f1f1;\\n  padding: 1rem;\\n  border-top: 1px solid #ddd;\\n  display: flex;\\n  justify-content: center; \\n\\n  gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  font-size: 1rem;\\n  border-radius: 0.3rem;\\n  transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n  transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column; \\n\\n  align-items: flex-start; \\n\\n}\\n\\n\\n\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n  background: rgba(20, 33, 59, 0.9); \\n\\n  color: #fff;\\n  min-height: 100vh;\\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n\\n\\n.stats-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 10px;\\n  color: #fff;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n}\\n\\n.stats-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\\n}\\n\\n.stats-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  text-align: center;\\n}\\n\\n.stats-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  margin-bottom: 0.5rem;\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n\\n.stats-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: bold;\\n  margin: 0;\\n}\\n\\n\\n\\n.chart-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 10px;\\n  color: #fff;\\n  margin-bottom: 2rem;\\n}\\n\\n.chart-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.05);\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n  padding: 1rem 1.5rem;\\n  border-radius: 10px 10px 0 0;\\n}\\n\\n.chart-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-weight: 600;\\n  color: #fff;\\n}\\n\\n.chart-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  height: 350px;\\n  position: relative;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .stats-card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n\\n  .chart-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n    height: 300px;\\n  }\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #ccc;\\n  transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n  background-color: #000 !important;\\n  color: #fff;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n  position: relative;\\n  padding: 0.5rem 1rem;\\n  transition: color 0.3s ease;\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  width: 0;\\n  background: #ff4c60;\\n  transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 30px;\\n  object-fit: cover;\\n  border-radius: 50%; \\n\\n  margin-right: 8px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class AdminDashComponent {\n  constructor(responsableService, cdr, router, authService, transactionService) {\n    this.responsableService = responsableService;\n    this.cdr = cdr;\n    this.router = router;\n    this.authService = authService;\n    this.transactionService = transactionService;\n    this.responsables = [];\n    this.adminName = '';\n    this.transactions = [];\n    this.modalMessage = '';\n    // Propriétés pour les statistiques\n    this.totalTransactions = 0;\n    this.totalAmount = 0;\n    this.achatTransactions = 0;\n    this.venteTransactions = 0;\n    // Registering chart.js components\n    Chart.register(...registerables);\n  }\n  // Function to load transactions from the server\n  loadTransactions() {\n    this.transactionService.getAllTransactions().subscribe({\n      next: data => {\n        this.transactions = data;\n        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded\n        // Calculer les statistiques\n        this.calculateStatistics();\n        // Now render the charts\n        this.renderChart();\n        this.renderBarChart();\n      },\n      error: err => {\n        console.error('Error fetching transactions', err);\n      }\n    });\n  }\n  // Fonction pour calculer les statistiques\n  calculateStatistics() {\n    this.totalTransactions = this.transactions.length;\n    this.totalAmount = this.transactions.reduce((sum, t) => sum + (t.montatnt || 0), 0);\n    this.achatTransactions = this.transactions.filter(t => t.type?.toLowerCase() === 'achat').length;\n    this.venteTransactions = this.transactions.filter(t => t.type?.toLowerCase() === 'vente').length;\n  }\n  // Function to render the line chart using the transaction data\n  renderChart() {\n    const ctx = this.myChartRef.nativeElement.getContext('2d');\n    // Destroy previous chart if exists\n    if (this.chartInstance) {\n      this.chartInstance.destroy();\n    }\n    // Grouper les transactions par date pour la courbe\n    const transactionsByDate = this.groupTransactionsByDate();\n    const labels = Object.keys(transactionsByDate);\n    const data = Object.values(transactionsByDate);\n    // Create a new chart instance - COULEURS ORIGINALES CONSERVÉES\n    this.chartInstance = new Chart(ctx, {\n      type: 'line',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Montant des Transactions par Jour',\n          data: data,\n          backgroundColor: 'rgba(54, 162, 235, 0.6)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 2,\n          fill: true,\n          tension: 0.4\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n  // Function to render the bar chart\n  renderBarChart() {\n    const ctx = this.barChartRef.nativeElement.getContext('2d');\n    // Destroy previous chart if exists\n    if (this.barChartInstance) {\n      this.barChartInstance.destroy();\n    }\n    // Grouper les transactions par type\n    const transactionsByType = this.groupTransactionsByType();\n    const labels = Object.keys(transactionsByType);\n    const data = Object.values(transactionsByType);\n    // Create a new bar chart instance avec des couleurs attrayantes\n    this.barChartInstance = new Chart(ctx, {\n      type: 'bar',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Nombre de Transactions par Type',\n          data: data,\n          backgroundColor: ['rgba(255, 99, 132, 0.8)', 'rgba(54, 162, 235, 0.8)', 'rgba(255, 206, 86, 0.8)', 'rgba(75, 192, 192, 0.8)', 'rgba(153, 102, 255, 0.8)', 'rgba(255, 159, 64, 0.8)', 'rgba(199, 199, 199, 0.8)', 'rgba(83, 102, 255, 0.8)' // Bleu indigo\n          ],\n\n          borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)', 'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)', 'rgba(199, 199, 199, 1)', 'rgba(83, 102, 255, 1)'],\n          borderWidth: 2,\n          borderRadius: 8,\n          borderSkipped: false\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            display: true,\n            labels: {\n              color: '#fff',\n              font: {\n                size: 12\n              }\n            }\n          }\n        },\n        scales: {\n          y: {\n            beginAtZero: true,\n            ticks: {\n              color: '#fff'\n            },\n            grid: {\n              color: 'rgba(255, 255, 255, 0.1)'\n            }\n          },\n          x: {\n            ticks: {\n              color: '#fff'\n            },\n            grid: {\n              color: 'rgba(255, 255, 255, 0.1)'\n            }\n          }\n        }\n      }\n    });\n  }\n  // Grouper les transactions par date\n  groupTransactionsByDate() {\n    const grouped = {};\n    this.transactions.forEach(transaction => {\n      const date = new Date(transaction.dateTransaction).toLocaleDateString();\n      if (grouped[date]) {\n        grouped[date] += transaction.montatnt || 0;\n      } else {\n        grouped[date] = transaction.montatnt || 0;\n      }\n    });\n    return grouped;\n  }\n  // Grouper les transactions par type\n  groupTransactionsByType() {\n    const grouped = {};\n    this.transactions.forEach(transaction => {\n      const type = transaction.type || 'Autre';\n      if (grouped[type]) {\n        grouped[type]++;\n      } else {\n        grouped[type] = 1;\n      }\n    });\n    return grouped;\n  }\n  ngOnInit() {\n    const adminData = localStorage.getItem('admin');\n    if (adminData) {\n      const admin = JSON.parse(adminData);\n      this.adminName = `${admin.prenom} ${admin.nom}`;\n    } else {\n      this.adminName = 'Admin';\n    }\n    this.loadResponsables();\n  }\n  loadResponsables() {\n    this.responsableService.getResponsables().subscribe(data => {\n      this.responsables = data;\n    }, error => {\n      console.error('Error fetching responsables:', error);\n    });\n  }\n  deleteResponsable(id) {\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: response => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: error => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    localStorage.removeItem('admin'); // ✅ correct key now\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  updateResponsable(id, updatedResponsable) {\n    this.responsableService.updateResponsable(id, updatedResponsable).subscribe(() => {\n      this.loadResponsables();\n    }, error => {\n      console.error('Error updating responsable:', error);\n    });\n  }\n  trackById(index, item) {\n    return item.id ?? 0;\n  }\n  ngAfterViewInit() {\n    this.loadTransactions();\n    feather.replace();\n    if (this.feedbackModalRef) {\n      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);\n    }\n  }\n  static {\n    this.ɵfac = function AdminDashComponent_Factory(t) {\n      return new (t || AdminDashComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthenticationService), i0.ɵɵdirectiveInject(i4.TransactionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashComponent,\n      selectors: [[\"app-respon-dashboard\"]],\n      viewQuery: function AdminDashComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.barChartRef = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedbackModalRef = _t.first);\n        }\n      },\n      decls: 98,\n      vars: 17,\n      consts: [[\"lang\", \"en\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-store, no-cache, must-revalidate, max-age=0\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"class\", \"nav-link\", \"href\", \"/adminDash\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/users\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/groups\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/transactions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actionnaires\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/port\", 4, \"ngIf\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"row\", \"mb-4\"], [1, \"col-md-3\"], [1, \"card\", \"stats-card\"], [1, \"card-body\"], [1, \"card-title\"], [1, \"text-primary\"], [1, \"text-success\"], [1, \"text-info\"], [1, \"text-warning\"], [1, \"col-md-6\"], [1, \"card\", \"chart-card\"], [1, \"card-header\"], [\"width\", \"400\", \"height\", \"300\"], [\"myChart\", \"\"], [\"barChart\", \"\"], [\"href\", \"/adminDash\", 1, \"nav-link\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"book\"]],\n      template: function AdminDashComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"meta\", 6)(8, \"meta\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10)(12, \"link\", 11);\n          i0.ɵɵelementStart(13, \"title\");\n          i0.ɵɵtext(14, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"link\", 12)(16, \"link\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"body\")(18, \"nav\", 14)(19, \"a\", 15);\n          i0.ɵɵelement(20, \"img\", 16);\n          i0.ɵɵtext(21, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"ul\", 17)(23, \"li\", 18)(24, \"a\", 19);\n          i0.ɵɵlistener(\"click\", function AdminDashComponent_Template_a_click_24_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(25, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21)(28, \"nav\", 22)(29, \"div\", 23)(30, \"ul\", 24)(31, \"li\", 25);\n          i0.ɵɵtemplate(32, AdminDashComponent_a_32_Template, 5, 0, \"a\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"li\", 25);\n          i0.ɵɵtemplate(34, AdminDashComponent_a_34_Template, 3, 0, \"a\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"li\", 25);\n          i0.ɵɵtemplate(36, AdminDashComponent_a_36_Template, 3, 0, \"a\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"li\", 25);\n          i0.ɵɵtemplate(38, AdminDashComponent_a_38_Template, 3, 0, \"a\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"li\", 25);\n          i0.ɵɵtemplate(40, AdminDashComponent_a_40_Template, 3, 0, \"a\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"li\", 25);\n          i0.ɵɵtemplate(42, AdminDashComponent_a_42_Template, 3, 0, \"a\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"li\", 25);\n          i0.ɵɵtemplate(44, AdminDashComponent_a_44_Template, 3, 0, \"a\", 32);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(45, \"main\", 33)(46, \"div\", 34)(47, \"h1\", 35);\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 36);\n          i0.ɵɵelement(50, \"div\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 38)(52, \"div\", 39)(53, \"div\", 40)(54, \"div\", 41)(55, \"h5\", 42);\n          i0.ɵɵtext(56, \"Total Transactions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"h3\", 43);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(59, \"div\", 39)(60, \"div\", 40)(61, \"div\", 41)(62, \"h5\", 42);\n          i0.ɵɵtext(63, \"Montant Total\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"h3\", 44);\n          i0.ɵɵtext(65);\n          i0.ɵɵpipe(66, \"currency\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(67, \"div\", 39)(68, \"div\", 40)(69, \"div\", 41)(70, \"h5\", 42);\n          i0.ɵɵtext(71, \"Transactions Achat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"h3\", 45);\n          i0.ɵɵtext(73);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(74, \"div\", 39)(75, \"div\", 40)(76, \"div\", 41)(77, \"h5\", 42);\n          i0.ɵɵtext(78, \"Transactions Vente\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"h3\", 46);\n          i0.ɵɵtext(80);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(81, \"div\", 21)(82, \"div\", 47)(83, \"div\", 48)(84, \"div\", 49)(85, \"h5\");\n          i0.ɵɵtext(86, \"\\u00C9volution des Transactions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 41);\n          i0.ɵɵelement(88, \"canvas\", 50, 51);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(90, \"div\", 47)(91, \"div\", 48)(92, \"div\", 49)(93, \"h5\");\n          i0.ɵɵtext(94, \"R\\u00E9partition par Type\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"div\", 41);\n          i0.ɵɵelement(96, \"canvas\", 50, 52);\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(32);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/adminDash\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/users\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/groups\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/transactions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actionnaires\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/port\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"Welcome, \", ctx.adminName, \"\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.totalTransactions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(66, 12, ctx.totalAmount, \"EUR\", \"symbol\", \"1.2-2\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.achatTransactions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.venteTransactions);\n        }\n      },\n      dependencies: [i5.NgIf, i5.CurrencyPipe],\n      styles: [_c3, _c3]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "Chart", "registerables", "Modal", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "AdminDashComponent", "constructor", "responsableService", "cdr", "router", "authService", "transactionService", "responsables", "admin<PERSON>ame", "transactions", "modalMessage", "totalTransactions", "totalAmount", "achatTransactions", "venteTransactions", "register", "loadTransactions", "getAllTransactions", "subscribe", "next", "data", "console", "log", "calculateStatistics", "<PERSON><PERSON><PERSON>", "renderBarChart", "error", "err", "length", "reduce", "sum", "t", "montatnt", "filter", "type", "toLowerCase", "ctx", "myChartRef", "nativeElement", "getContext", "chartInstance", "destroy", "transactionsByDate", "groupTransactionsByDate", "labels", "Object", "keys", "values", "datasets", "label", "backgroundColor", "borderColor", "borderWidth", "fill", "tension", "options", "responsive", "maintainAspectRatio", "scales", "y", "beginAtZero", "barChartRef", "barChartInstance", "transactionsByType", "groupTransactionsByType", "borderRadius", "borderSkipped", "plugins", "legend", "display", "color", "font", "size", "ticks", "grid", "x", "grouped", "for<PERSON>ach", "transaction", "date", "Date", "dateTransaction", "toLocaleDateString", "ngOnInit", "adminData", "localStorage", "getItem", "admin", "JSON", "parse", "prenom", "nom", "loadResponsables", "getResponsables", "deleteResponsable", "id", "confirm", "response", "responsable", "logout", "removeItem", "navigate", "updateResponsable", "updatedResponsable", "trackById", "index", "item", "ngAfterViewInit", "replace", "feedbackModalRef", "modalInstance", "ɵɵdirectiveInject", "i1", "ResponsableService", "ChangeDetectorRef", "i2", "Router", "i3", "AuthenticationService", "i4", "TransactionService", "selectors", "viewQuery", "AdminDashComponent_Query", "rf", "ɵɵlistener", "AdminDashComponent_Template_a_click_24_listener", "ɵɵtemplate", "AdminDashComponent_a_32_Template", "AdminDashComponent_a_34_Template", "AdminDashComponent_a_36_Template", "AdminDashComponent_a_38_Template", "AdminDashComponent_a_40_Template", "AdminDashComponent_a_42_Template", "AdminDashComponent_a_44_Template", "ɵɵadvance", "ɵɵproperty", "isRouteAllowed", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "ɵɵpipeBind4"], "sources": ["C:\\Users\\<USER>\\Desktop\\Final - Copie\\frontend\\src\\app\\admin-dash\\admin-dash.component.ts", "C:\\Users\\<USER>\\Desktop\\Final - Copie\\frontend\\src\\app\\admin-dash\\admin-dash.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, AfterViewInit, ElementRef, ChangeDetectorRef } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js';\nimport { Router } from '@angular/router';\nimport { AuthenticationService } from '../auth/authentication.service';\nimport { Transaction } from '../model/transaction.model';\nimport { TransactionService } from '../services/transaction.service';\nimport { Modal } from 'bootstrap';\n\n@Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './admin-dash.component.html',\n  styleUrls: ['./admin-dash.component.css']\n})\nexport class AdminDashComponent implements AfterViewInit, OnInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  @ViewChild('barChart') barChartRef!: ElementRef;\n  @ViewChild('feedbackModal') feedbackModalRef!: ElementRef;\n  responsables: User[] = [];\n  adminName: string = '';\n  private chartInstance: any;\n  private barChartInstance: any;\n  transactions: Transaction[] = [];\n  modalMessage = '';\n  private modalInstance!: Modal;\n\n  // Propriétés pour les statistiques\n  totalTransactions: number = 0;\n  totalAmount: number = 0;\n  achatTransactions: number = 0;\n  venteTransactions: number = 0;\n\n  constructor(\n    private responsableService: ResponsableService,\n    private cdr: ChangeDetectorRef,\n    private router: Router,\n    public authService: AuthenticationService,\n    private transactionService: TransactionService,\n\n    \n  ) {\n    // Registering chart.js components\n    Chart.register(...registerables);\n  }\n\n  \n\n  // Function to load transactions from the server\n  loadTransactions(): void {\n    this.transactionService.getAllTransactions().subscribe({\n      next: (data) => {\n        this.transactions = data;\n        console.log('Transactions data:', this.transactions); // Log the data to ensure it's loaded\n\n        // Calculer les statistiques\n        this.calculateStatistics();\n\n        // Now render the charts\n        this.renderChart();\n        this.renderBarChart();\n      },\n      error: (err) => {\n        console.error('Error fetching transactions', err);\n      }\n    });\n  }\n\n  // Fonction pour calculer les statistiques\n  calculateStatistics(): void {\n    this.totalTransactions = this.transactions.length;\n    this.totalAmount = this.transactions.reduce((sum, t) => sum + (t.montatnt || 0), 0);\n    this.achatTransactions = this.transactions.filter(t => t.type?.toLowerCase() === 'achat').length;\n    this.venteTransactions = this.transactions.filter(t => t.type?.toLowerCase() === 'vente').length;\n  }\n\n\n  // Function to render the line chart using the transaction data\n  renderChart(): void {\n    const ctx = this.myChartRef.nativeElement.getContext('2d');\n\n    // Destroy previous chart if exists\n    if (this.chartInstance) {\n      this.chartInstance.destroy();\n    }\n\n    // Grouper les transactions par date pour la courbe\n    const transactionsByDate = this.groupTransactionsByDate();\n    const labels = Object.keys(transactionsByDate);\n    const data = Object.values(transactionsByDate);\n\n    // Create a new chart instance - COULEURS ORIGINALES CONSERVÉES\n    this.chartInstance = new Chart(ctx, {\n      type: 'line',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Montant des Transactions par Jour',\n          data: data,\n          backgroundColor: 'rgba(54, 162, 235, 0.6)',\n          borderColor: 'rgba(54, 162, 235, 1)',\n          borderWidth: 2,\n          fill: true,\n          tension: 0.4\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        scales: {\n          y: {\n            beginAtZero: true\n          }\n        }\n      }\n    });\n  }\n\n  // Function to render the bar chart\n  renderBarChart(): void {\n    const ctx = this.barChartRef.nativeElement.getContext('2d');\n\n    // Destroy previous chart if exists\n    if (this.barChartInstance) {\n      this.barChartInstance.destroy();\n    }\n\n    // Grouper les transactions par type\n    const transactionsByType = this.groupTransactionsByType();\n    const labels = Object.keys(transactionsByType);\n    const data = Object.values(transactionsByType);\n\n    // Create a new bar chart instance avec des couleurs attrayantes\n    this.barChartInstance = new Chart(ctx, {\n      type: 'bar',\n      data: {\n        labels: labels,\n        datasets: [{\n          label: 'Nombre de Transactions par Type',\n          data: data,\n          backgroundColor: [\n            'rgba(255, 99, 132, 0.8)',   // Rouge rose\n            'rgba(54, 162, 235, 0.8)',   // Bleu\n            'rgba(255, 206, 86, 0.8)',   // Jaune doré\n            'rgba(75, 192, 192, 0.8)',   // Turquoise\n            'rgba(153, 102, 255, 0.8)',  // Violet\n            'rgba(255, 159, 64, 0.8)',   // Orange\n            'rgba(199, 199, 199, 0.8)',  // Gris\n            'rgba(83, 102, 255, 0.8)'    // Bleu indigo\n          ],\n          borderColor: [\n            'rgba(255, 99, 132, 1)',\n            'rgba(54, 162, 235, 1)',\n            'rgba(255, 206, 86, 1)',\n            'rgba(75, 192, 192, 1)',\n            'rgba(153, 102, 255, 1)',\n            'rgba(255, 159, 64, 1)',\n            'rgba(199, 199, 199, 1)',\n            'rgba(83, 102, 255, 1)'\n          ],\n          borderWidth: 2,\n          borderRadius: 8,\n          borderSkipped: false,\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            display: true,\n            labels: {\n              color: '#fff',\n              font: {\n                size: 12\n              }\n            }\n          }\n        },\n        scales: {\n          y: {\n            beginAtZero: true,\n            ticks: {\n              color: '#fff'\n            },\n            grid: {\n              color: 'rgba(255, 255, 255, 0.1)'\n            }\n          },\n          x: {\n            ticks: {\n              color: '#fff'\n            },\n            grid: {\n              color: 'rgba(255, 255, 255, 0.1)'\n            }\n          }\n        }\n      }\n    });\n  }\n\n  // Grouper les transactions par date\n  groupTransactionsByDate(): { [key: string]: number } {\n    const grouped: { [key: string]: number } = {};\n\n    this.transactions.forEach(transaction => {\n      const date = new Date(transaction.dateTransaction).toLocaleDateString();\n      if (grouped[date]) {\n        grouped[date] += transaction.montatnt || 0;\n      } else {\n        grouped[date] = transaction.montatnt || 0;\n      }\n    });\n\n    return grouped;\n  }\n\n  // Grouper les transactions par type\n  groupTransactionsByType(): { [key: string]: number } {\n    const grouped: { [key: string]: number } = {};\n\n    this.transactions.forEach(transaction => {\n      const type = transaction.type || 'Autre';\n      if (grouped[type]) {\n        grouped[type]++;\n      } else {\n        grouped[type] = 1;\n      }\n    });\n\n    return grouped;\n  }\n\n\n  ngOnInit(): void {\n    const adminData = localStorage.getItem('admin');\n    if (adminData) {\n      const admin = JSON.parse(adminData);\n      this.adminName = `${admin.prenom} ${admin.nom}`;\n    } else {\n      this.adminName = 'Admin';\n    }\n\n    this.loadResponsables();\n  }\n\n  loadResponsables() {\n    this.responsableService.getResponsables().subscribe(\n      (data) => {\n        this.responsables = data;\n      },\n      (error) => {\n        console.error('Error fetching responsables:', error);\n      }\n    );\n  }\n\n  deleteResponsable(id: number) {\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: (response) => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: (error) => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    localStorage.removeItem('admin'); // ✅ correct key now\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n\n  updateResponsable(id: number, updatedResponsable: User) {\n    this.responsableService.updateResponsable(id, updatedResponsable).subscribe(\n      () => {\n        this.loadResponsables();\n      },\n      (error) => {\n        console.error('Error updating responsable:', error);\n      }\n    );\n  }\n\n  trackById(index: number, item: User): number | undefined {\n    return item.id ?? 0;\n  }\n\n  ngAfterViewInit() {\n    this.loadTransactions();\n    feather.replace();\n    if (this.feedbackModalRef) {\n      this.modalInstance = new Modal(this.feedbackModalRef.nativeElement);\n    }\n  }\n\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta http-equiv=\"Cache-Control\" content=\"no-store, no-cache, must-revalidate, max-age=0\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n\n    <!-- Custom styles for this template -->\n    <link href=\"./admin-dash.component.css\" rel=\"stylesheet\">\n  </head>\n\n  <body>\n     <!-- Navbar -->\n     <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/adminDash\" *ngIf=\"authService.isRouteAllowed('/adminDash')\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\" *ngIf=\"authService.isRouteAllowed('/users')\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\" *ngIf=\"authService.isRouteAllowed('/groups')\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            \n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/transactions\" *ngIf=\"authService.isRouteAllowed('/transactions')\">\n                <span data-feather=\"dollar-sign\"></span>\n                Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/actions\" *ngIf=\"authService.isRouteAllowed('/actions')\">\n                <span data-feather=\"trending-up\"></span>\n               Gestion des actions\n              </a>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\" *ngIf=\"authService.isRouteAllowed('/actionnaires')\">\n                  <span data-feather=\"users\"></span>\n                 Gestion des actionnaires\n                </a>\n                     <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/port\" *ngIf=\"authService.isRouteAllowed('/port')\">\n                <span data-feather=\"book\"></span>\n               Gestion des Portefeuilles\n              </a>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Welcome, {{ adminName }}</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n\n              </div>\n            </div>\n          </div>\n\n          <!-- Section des statistiques en haut -->\n          <div class=\"row mb-4\">\n            <div class=\"col-md-3\">\n              <div class=\"card stats-card\">\n                <div class=\"card-body\">\n                  <h5 class=\"card-title\">Total Transactions</h5>\n                  <h3 class=\"text-primary\">{{ totalTransactions }}</h3>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3\">\n              <div class=\"card stats-card\">\n                <div class=\"card-body\">\n                  <h5 class=\"card-title\">Montant Total</h5>\n                  <h3 class=\"text-success\">{{ totalAmount | currency:'EUR':'symbol':'1.2-2' }}</h3>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3\">\n              <div class=\"card stats-card\">\n                <div class=\"card-body\">\n                  <h5 class=\"card-title\">Transactions Achat</h5>\n                  <h3 class=\"text-info\">{{ achatTransactions }}</h3>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3\">\n              <div class=\"card stats-card\">\n                <div class=\"card-body\">\n                  <h5 class=\"card-title\">Transactions Vente</h5>\n                  <h3 class=\"text-warning\">{{ venteTransactions }}</h3>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Section des graphiques -->\n          <div class=\"row\">\n            <!-- Graphique en courbe à gauche -->\n            <div class=\"col-md-6\">\n              <div class=\"card chart-card\">\n                <div class=\"card-header\">\n                  <h5>Évolution des Transactions</h5>\n                </div>\n                <div class=\"card-body\">\n                  <canvas #myChart width=\"400\" height=\"300\"></canvas>\n                </div>\n              </div>\n            </div>\n\n            <!-- Graphique en barres à droite -->\n            <div class=\"col-md-6\">\n              <div class=\"card chart-card\">\n                <div class=\"card-header\">\n                  <h5>Répartition par Type</h5>\n                </div>\n                <div class=\"card-body\">\n                  <canvas #barChart width=\"400\" height=\"300\"></canvas>\n                </div>\n              </div>\n            </div>\n          </div>\n\n        </main>\n      </div>\n    </div>\n\n    <!-- Bootstrap core JavaScript -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n\n  </body>\n</html>"], "mappings": "AAGA,OAAO,KAAKA,OAAO,MAAM,eAAe;AACxC,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAK/C,SAASC,KAAK,QAAQ,WAAW;;;;;;;;;;;;ICoCjBC,EAAA,CAAAC,cAAA,YAAuF;IACrFD,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIlDJ,EAAA,CAAAC,cAAA,YAA+E;IAC7ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAiF;IAC/ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAINJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAwC;IACxCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAmF;IACjFD,EAAA,CAAAE,SAAA,eAAwC;IACzCF,EAAA,CAAAG,MAAA,4BACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAEFJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAkC;IACnCF,EAAA,CAAAG,MAAA,iCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,YAA6E;IAC3ED,EAAA,CAAAE,SAAA,eAAiC;IAClCF,EAAA,CAAAG,MAAA,kCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;ADnElB,OAAM,MAAOC,kBAAkB;EAkB7BC,YACUC,kBAAsC,EACtCC,GAAsB,EACtBC,MAAc,EACfC,WAAkC,EACjCC,kBAAsC;IAJtC,KAAAJ,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAnB5B,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,SAAS,GAAW,EAAE;IAGtB,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,YAAY,GAAG,EAAE;IAGjB;IACA,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAC,iBAAiB,GAAW,CAAC;IAW3B;IACAtB,KAAK,CAACuB,QAAQ,CAAC,GAAGtB,aAAa,CAAC;EAClC;EAIA;EACAuB,gBAAgBA,CAAA;IACd,IAAI,CAACV,kBAAkB,CAACW,kBAAkB,EAAE,CAACC,SAAS,CAAC;MACrDC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACX,YAAY,GAAGW,IAAI;QACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACb,YAAY,CAAC,CAAC,CAAC;QAEtD;QACA,IAAI,CAACc,mBAAmB,EAAE;QAE1B;QACA,IAAI,CAACC,WAAW,EAAE;QAClB,IAAI,CAACC,cAAc,EAAE;MACvB,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACbN,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;MACnD;KACD,CAAC;EACJ;EAEA;EACAJ,mBAAmBA,CAAA;IACjB,IAAI,CAACZ,iBAAiB,GAAG,IAAI,CAACF,YAAY,CAACmB,MAAM;IACjD,IAAI,CAAChB,WAAW,GAAG,IAAI,CAACH,YAAY,CAACoB,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,IAAIC,CAAC,CAACC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACnF,IAAI,CAACnB,iBAAiB,GAAG,IAAI,CAACJ,YAAY,CAACwB,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACG,IAAI,EAAEC,WAAW,EAAE,KAAK,OAAO,CAAC,CAACP,MAAM;IAChG,IAAI,CAACd,iBAAiB,GAAG,IAAI,CAACL,YAAY,CAACwB,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACG,IAAI,EAAEC,WAAW,EAAE,KAAK,OAAO,CAAC,CAACP,MAAM;EAClG;EAGA;EACAJ,WAAWA,CAAA;IACT,MAAMY,GAAG,GAAG,IAAI,CAACC,UAAU,CAACC,aAAa,CAACC,UAAU,CAAC,IAAI,CAAC;IAE1D;IACA,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;;IAG9B;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAACC,uBAAuB,EAAE;IACzD,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACJ,kBAAkB,CAAC;IAC9C,MAAMtB,IAAI,GAAGyB,MAAM,CAACE,MAAM,CAACL,kBAAkB,CAAC;IAE9C;IACA,IAAI,CAACF,aAAa,GAAG,IAAIhD,KAAK,CAAC4C,GAAG,EAAE;MAClCF,IAAI,EAAE,MAAM;MACZd,IAAI,EAAE;QACJwB,MAAM,EAAEA,MAAM;QACdI,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,mCAAmC;UAC1C7B,IAAI,EAAEA,IAAI;UACV8B,eAAe,EAAE,yBAAyB;UAC1CC,WAAW,EAAE,uBAAuB;UACpCC,WAAW,EAAE,CAAC;UACdC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE;SACV;OACF;MACDC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BC,MAAM,EAAE;UACNC,CAAC,EAAE;YACDC,WAAW,EAAE;;;;KAIpB,CAAC;EACJ;EAEA;EACAnC,cAAcA,CAAA;IACZ,MAAMW,GAAG,GAAG,IAAI,CAACyB,WAAW,CAACvB,aAAa,CAACC,UAAU,CAAC,IAAI,CAAC;IAE3D;IACA,IAAI,IAAI,CAACuB,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACrB,OAAO,EAAE;;IAGjC;IACA,MAAMsB,kBAAkB,GAAG,IAAI,CAACC,uBAAuB,EAAE;IACzD,MAAMpB,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACiB,kBAAkB,CAAC;IAC9C,MAAM3C,IAAI,GAAGyB,MAAM,CAACE,MAAM,CAACgB,kBAAkB,CAAC;IAE9C;IACA,IAAI,CAACD,gBAAgB,GAAG,IAAItE,KAAK,CAAC4C,GAAG,EAAE;MACrCF,IAAI,EAAE,KAAK;MACXd,IAAI,EAAE;QACJwB,MAAM,EAAEA,MAAM;QACdI,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,iCAAiC;UACxC7B,IAAI,EAAEA,IAAI;UACV8B,eAAe,EAAE,CACf,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,yBAAyB,EACzB,0BAA0B,EAC1B,yBAAyB,EACzB,0BAA0B,EAC1B,yBAAyB,CAAI;UAAA,CAC9B;;UACDC,WAAW,EAAE,CACX,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,CACxB;UACDC,WAAW,EAAE,CAAC;UACda,YAAY,EAAE,CAAC;UACfC,aAAa,EAAE;SAChB;OACF;MACDX,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BU,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE,IAAI;YACbzB,MAAM,EAAE;cACN0B,KAAK,EAAE,MAAM;cACbC,IAAI,EAAE;gBACJC,IAAI,EAAE;;;;SAIb;QACDd,MAAM,EAAE;UACNC,CAAC,EAAE;YACDC,WAAW,EAAE,IAAI;YACjBa,KAAK,EAAE;cACLH,KAAK,EAAE;aACR;YACDI,IAAI,EAAE;cACJJ,KAAK,EAAE;;WAEV;UACDK,CAAC,EAAE;YACDF,KAAK,EAAE;cACLH,KAAK,EAAE;aACR;YACDI,IAAI,EAAE;cACJJ,KAAK,EAAE;;;;;KAKhB,CAAC;EACJ;EAEA;EACA3B,uBAAuBA,CAAA;IACrB,MAAMiC,OAAO,GAA8B,EAAE;IAE7C,IAAI,CAACnE,YAAY,CAACoE,OAAO,CAACC,WAAW,IAAG;MACtC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,WAAW,CAACG,eAAe,CAAC,CAACC,kBAAkB,EAAE;MACvE,IAAIN,OAAO,CAACG,IAAI,CAAC,EAAE;QACjBH,OAAO,CAACG,IAAI,CAAC,IAAID,WAAW,CAAC9C,QAAQ,IAAI,CAAC;OAC3C,MAAM;QACL4C,OAAO,CAACG,IAAI,CAAC,GAAGD,WAAW,CAAC9C,QAAQ,IAAI,CAAC;;IAE7C,CAAC,CAAC;IAEF,OAAO4C,OAAO;EAChB;EAEA;EACAZ,uBAAuBA,CAAA;IACrB,MAAMY,OAAO,GAA8B,EAAE;IAE7C,IAAI,CAACnE,YAAY,CAACoE,OAAO,CAACC,WAAW,IAAG;MACtC,MAAM5C,IAAI,GAAG4C,WAAW,CAAC5C,IAAI,IAAI,OAAO;MACxC,IAAI0C,OAAO,CAAC1C,IAAI,CAAC,EAAE;QACjB0C,OAAO,CAAC1C,IAAI,CAAC,EAAE;OAChB,MAAM;QACL0C,OAAO,CAAC1C,IAAI,CAAC,GAAG,CAAC;;IAErB,CAAC,CAAC;IAEF,OAAO0C,OAAO;EAChB;EAGAO,QAAQA,CAAA;IACN,MAAMC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC/C,IAAIF,SAAS,EAAE;MACb,MAAMG,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC;MACnC,IAAI,CAAC5E,SAAS,GAAG,GAAG+E,KAAK,CAACG,MAAM,IAAIH,KAAK,CAACI,GAAG,EAAE;KAChD,MAAM;MACL,IAAI,CAACnF,SAAS,GAAG,OAAO;;IAG1B,IAAI,CAACoF,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC1F,kBAAkB,CAAC2F,eAAe,EAAE,CAAC3E,SAAS,CAChDE,IAAI,IAAI;MACP,IAAI,CAACb,YAAY,GAAGa,IAAI;IAC1B,CAAC,EACAM,KAAK,IAAI;MACRL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,CACF;EACH;EAEAoE,iBAAiBA,CAACC,EAAU;IAC1B,IAAIC,OAAO,CAAC,mDAAmD,CAAC,EAAE;MAChE,IAAI,CAAC9F,kBAAkB,CAAC4F,iBAAiB,CAACC,EAAE,CAAC,CAAC7E,SAAS,CAAC;QACtDC,IAAI,EAAG8E,QAAQ,IAAI;UACjB5E,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE2E,QAAQ,CAAC;UACzC,IAAI,CAAC1F,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC0B,MAAM,CAACiE,WAAW,IAAIA,WAAW,CAACH,EAAE,KAAKA,EAAE,CAAC;QACpF,CAAC;QACDrE,KAAK,EAAGA,KAAK,IAAI;UACfL,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAEAyE,MAAMA,CAAA;IACJd,YAAY,CAACe,UAAU,CAAC,WAAW,CAAC;IACpCf,YAAY,CAACe,UAAU,CAAC,MAAM,CAAC;IAC/Bf,YAAY,CAACe,UAAU,CAAC,OAAO,CAAC;IAChCf,YAAY,CAACe,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;IAClC/E,OAAO,CAACC,GAAG,CAAC+D,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAAClF,MAAM,CAACiG,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,iBAAiBA,CAACP,EAAU,EAAEQ,kBAAwB;IACpD,IAAI,CAACrG,kBAAkB,CAACoG,iBAAiB,CAACP,EAAE,EAAEQ,kBAAkB,CAAC,CAACrF,SAAS,CACzE,MAAK;MACH,IAAI,CAAC0E,gBAAgB,EAAE;IACzB,CAAC,EACAlE,KAAK,IAAI;MACRL,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CACF;EACH;EAEA8E,SAASA,CAACC,KAAa,EAAEC,IAAU;IACjC,OAAOA,IAAI,CAACX,EAAE,IAAI,CAAC;EACrB;EAEAY,eAAeA,CAAA;IACb,IAAI,CAAC3F,gBAAgB,EAAE;IACvBzB,OAAO,CAACqH,OAAO,EAAE;IACjB,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACzB,IAAI,CAACC,aAAa,GAAG,IAAIpH,KAAK,CAAC,IAAI,CAACmH,gBAAgB,CAACvE,aAAa,CAAC;;EAEvE;;;uBA/RWtC,kBAAkB,EAAAL,EAAA,CAAAoH,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAtH,EAAA,CAAAoH,iBAAA,CAAApH,EAAA,CAAAuH,iBAAA,GAAAvH,EAAA,CAAAoH,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAAzH,EAAA,CAAAoH,iBAAA,CAAAM,EAAA,CAAAC,qBAAA,GAAA3H,EAAA,CAAAoH,iBAAA,CAAAQ,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAlBxH,kBAAkB;MAAAyH,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAxF,GAAA;QAAA,IAAAwF,EAAA;;;;;;;;;;;;;;;;;UCf/BjI,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAE,SAAA,cAA0F;UAY1FF,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAG,MAAA,wCAAgC;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAE/CJ,EAAA,CAAAE,SAAA,gBAAmF;UAOrFF,EAAA,CAAAI,YAAA,EAAO;UAEPJ,EAAA,CAAAC,cAAA,YAAM;UAIAD,EAAA,CAAAE,SAAA,eAAwE;UACxEF,EAAA,CAAAG,MAAA,aACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAkI,UAAA,mBAAAC,gDAAA;YAAA,OAAS1F,GAAA,CAAA+D,MAAA,EAAQ;UAAA,EAAC;UAACxG,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAIzDJ,EAAA,CAAAC,cAAA,eAA6B;UAMjBD,EAAA,CAAAoI,UAAA,KAAAC,gCAAA,gBAGI;UACNrI,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAoI,UAAA,KAAAE,gCAAA,gBAGI;UACNtI,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAoI,UAAA,KAAAG,gCAAA,gBAGI;UACNvI,EAAA,CAAAI,YAAA,EAAK;UAEPJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAoI,UAAA,KAAAI,gCAAA,gBAGI;UACNxI,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAoI,UAAA,KAAAK,gCAAA,gBAGI;UAJNzI,EAAA,CAAAI,YAAA,EAAqB;UAKnBJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAoI,UAAA,KAAAM,gCAAA,gBAGI;UAJN1I,EAAA,CAAAI,YAAA,EAAqB;UAKdJ,EAAA,CAAAC,cAAA,cAAqB;UAC5BD,EAAA,CAAAoI,UAAA,KAAAO,gCAAA,gBAGI;UAJG3I,EAAA,CAAAI,YAAA,EAAqB;UASlCJ,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAG,MAAA,IAAwB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC5CJ,EAAA,CAAAC,cAAA,eAAsC;UACpCD,EAAA,CAAAE,SAAA,eAEM;UACRF,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAC,cAAA,eAAsB;UAISD,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC9CJ,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAG,MAAA,IAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAI3DJ,EAAA,CAAAC,cAAA,eAAsB;UAGOD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzCJ,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAG,MAAA,IAAmD;;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAIvFJ,EAAA,CAAAC,cAAA,eAAsB;UAGOD,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC9CJ,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAG,MAAA,IAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAIxDJ,EAAA,CAAAC,cAAA,eAAsB;UAGOD,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC9CJ,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAG,MAAA,IAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAO7DJ,EAAA,CAAAC,cAAA,eAAiB;UAKLD,EAAA,CAAAG,MAAA,uCAA0B;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAErCJ,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAE,SAAA,sBAAmD;UACrDF,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAC,cAAA,eAAsB;UAGZD,EAAA,CAAAG,MAAA,iCAAoB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAE/BJ,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAE,SAAA,sBAAoD;UACtDF,EAAA,CAAAI,YAAA,EAAM;;;UA/GiCJ,EAAA,CAAA4I,SAAA,IAA8C;UAA9C5I,EAAA,CAAA6I,UAAA,SAAApG,GAAA,CAAA/B,WAAA,CAAAoI,cAAA,eAA8C;UAMlD9I,EAAA,CAAA4I,SAAA,GAA0C;UAA1C5I,EAAA,CAAA6I,UAAA,SAAApG,GAAA,CAAA/B,WAAA,CAAAoI,cAAA,WAA0C;UAMzC9I,EAAA,CAAA4I,SAAA,GAA2C;UAA3C5I,EAAA,CAAA6I,UAAA,SAAApG,GAAA,CAAA/B,WAAA,CAAAoI,cAAA,YAA2C;UAOvC9I,EAAA,CAAA4I,SAAA,GAAiD;UAAjD5I,EAAA,CAAA6I,UAAA,SAAApG,GAAA,CAAA/B,WAAA,CAAAoI,cAAA,kBAAiD;UAMtD9I,EAAA,CAAA4I,SAAA,GAA4C;UAA5C5I,EAAA,CAAA6I,UAAA,SAAApG,GAAA,CAAA/B,WAAA,CAAAoI,cAAA,aAA4C;UAKrC9I,EAAA,CAAA4I,SAAA,GAAiD;UAAjD5I,EAAA,CAAA6I,UAAA,SAAApG,GAAA,CAAA/B,WAAA,CAAAoI,cAAA,kBAAiD;UAK3D9I,EAAA,CAAA4I,SAAA,GAAyC;UAAzC5I,EAAA,CAAA6I,UAAA,SAAApG,GAAA,CAAA/B,WAAA,CAAAoI,cAAA,UAAyC;UAU9D9I,EAAA,CAAA4I,SAAA,GAAwB;UAAxB5I,EAAA,CAAA+I,kBAAA,cAAAtG,GAAA,CAAA5B,SAAA,KAAwB;UAcRb,EAAA,CAAA4I,SAAA,IAAuB;UAAvB5I,EAAA,CAAAgJ,iBAAA,CAAAvG,GAAA,CAAAzB,iBAAA,CAAuB;UAQvBhB,EAAA,CAAA4I,SAAA,GAAmD;UAAnD5I,EAAA,CAAAgJ,iBAAA,CAAAhJ,EAAA,CAAAiJ,WAAA,SAAAxG,GAAA,CAAAxB,WAAA,4BAAmD;UAQtDjB,EAAA,CAAA4I,SAAA,GAAuB;UAAvB5I,EAAA,CAAAgJ,iBAAA,CAAAvG,GAAA,CAAAvB,iBAAA,CAAuB;UAQpBlB,EAAA,CAAA4I,SAAA,GAAuB;UAAvB5I,EAAA,CAAAgJ,iBAAA,CAAAvG,GAAA,CAAAtB,iBAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}