{"ast": null, "code": "/*!\n * body-parser\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module dependencies.\n */\nvar bytes = require('bytes');\nvar contentType = require('content-type');\nvar debug = require('debug')('body-parser:text');\nvar read = require('../read');\nvar typeis = require('type-is');\n\n/**\n * Module exports.\n */\n\nmodule.exports = text;\n\n/**\n * Create a middleware to parse text bodies.\n *\n * @param {object} [options]\n * @return {function}\n * @api public\n */\n\nfunction text(options) {\n  var opts = options || {};\n  var defaultCharset = opts.defaultCharset || 'utf-8';\n  var inflate = opts.inflate !== false;\n  var limit = typeof opts.limit !== 'number' ? bytes.parse(opts.limit || '100kb') : opts.limit;\n  var type = opts.type || 'text/plain';\n  var verify = opts.verify || false;\n  if (verify !== false && typeof verify !== 'function') {\n    throw new TypeError('option verify must be function');\n  }\n\n  // create the appropriate type checking function\n  var shouldParse = typeof type !== 'function' ? typeChecker(type) : type;\n  function parse(buf) {\n    return buf;\n  }\n  return function textParser(req, res, next) {\n    if (req._body) {\n      debug('body already parsed');\n      next();\n      return;\n    }\n    req.body = req.body || {};\n\n    // skip requests without bodies\n    if (!typeis.hasBody(req)) {\n      debug('skip empty body');\n      next();\n      return;\n    }\n    debug('content-type %j', req.headers['content-type']);\n\n    // determine if request should be parsed\n    if (!shouldParse(req)) {\n      debug('skip parsing');\n      next();\n      return;\n    }\n\n    // get charset\n    var charset = getCharset(req) || defaultCharset;\n\n    // read\n    read(req, res, next, parse, debug, {\n      encoding: charset,\n      inflate: inflate,\n      limit: limit,\n      verify: verify\n    });\n  };\n}\n\n/**\n * Get the charset of a request.\n *\n * @param {object} req\n * @api private\n */\n\nfunction getCharset(req) {\n  try {\n    return (contentType.parse(req).parameters.charset || '').toLowerCase();\n  } catch (e) {\n    return undefined;\n  }\n}\n\n/**\n * Get the simple type checker.\n *\n * @param {string} type\n * @return {function}\n */\n\nfunction typeChecker(type) {\n  return function checkType(req) {\n    return Boolean(typeis(req, type));\n  };\n}", "map": {"version": 3, "names": ["bytes", "require", "contentType", "debug", "read", "typeis", "module", "exports", "text", "options", "opts", "defaultCharset", "inflate", "limit", "parse", "type", "verify", "TypeError", "<PERSON><PERSON><PERSON><PERSON>", "typeC<PERSON>cker", "buf", "text<PERSON><PERSON><PERSON>", "req", "res", "next", "_body", "body", "hasBody", "headers", "charset", "getCharset", "encoding", "parameters", "toLowerCase", "e", "undefined", "checkType", "Boolean"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/body-parser/lib/types/text.js"], "sourcesContent": ["/*!\n * body-parser\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict'\n\n/**\n * Module dependencies.\n */\n\nvar bytes = require('bytes')\nvar contentType = require('content-type')\nvar debug = require('debug')('body-parser:text')\nvar read = require('../read')\nvar typeis = require('type-is')\n\n/**\n * Module exports.\n */\n\nmodule.exports = text\n\n/**\n * Create a middleware to parse text bodies.\n *\n * @param {object} [options]\n * @return {function}\n * @api public\n */\n\nfunction text (options) {\n  var opts = options || {}\n\n  var defaultCharset = opts.defaultCharset || 'utf-8'\n  var inflate = opts.inflate !== false\n  var limit = typeof opts.limit !== 'number'\n    ? bytes.parse(opts.limit || '100kb')\n    : opts.limit\n  var type = opts.type || 'text/plain'\n  var verify = opts.verify || false\n\n  if (verify !== false && typeof verify !== 'function') {\n    throw new TypeError('option verify must be function')\n  }\n\n  // create the appropriate type checking function\n  var shouldParse = typeof type !== 'function'\n    ? typeChecker(type)\n    : type\n\n  function parse (buf) {\n    return buf\n  }\n\n  return function textParser (req, res, next) {\n    if (req._body) {\n      debug('body already parsed')\n      next()\n      return\n    }\n\n    req.body = req.body || {}\n\n    // skip requests without bodies\n    if (!typeis.hasBody(req)) {\n      debug('skip empty body')\n      next()\n      return\n    }\n\n    debug('content-type %j', req.headers['content-type'])\n\n    // determine if request should be parsed\n    if (!shouldParse(req)) {\n      debug('skip parsing')\n      next()\n      return\n    }\n\n    // get charset\n    var charset = getCharset(req) || defaultCharset\n\n    // read\n    read(req, res, next, parse, debug, {\n      encoding: charset,\n      inflate: inflate,\n      limit: limit,\n      verify: verify\n    })\n  }\n}\n\n/**\n * Get the charset of a request.\n *\n * @param {object} req\n * @api private\n */\n\nfunction getCharset (req) {\n  try {\n    return (contentType.parse(req).parameters.charset || '').toLowerCase()\n  } catch (e) {\n    return undefined\n  }\n}\n\n/**\n * Get the simple type checker.\n *\n * @param {string} type\n * @return {function}\n */\n\nfunction typeChecker (type) {\n  return function checkType (req) {\n    return Boolean(typeis(req, type))\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AAEA,IAAIA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIC,WAAW,GAAGD,OAAO,CAAC,cAAc,CAAC;AACzC,IAAIE,KAAK,GAAGF,OAAO,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC;AAChD,IAAIG,IAAI,GAAGH,OAAO,CAAC,SAAS,CAAC;AAC7B,IAAII,MAAM,GAAGJ,OAAO,CAAC,SAAS,CAAC;;AAE/B;AACA;AACA;;AAEAK,MAAM,CAACC,OAAO,GAAGC,IAAI;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAAEC,OAAO,EAAE;EACtB,IAAIC,IAAI,GAAGD,OAAO,IAAI,CAAC,CAAC;EAExB,IAAIE,cAAc,GAAGD,IAAI,CAACC,cAAc,IAAI,OAAO;EACnD,IAAIC,OAAO,GAAGF,IAAI,CAACE,OAAO,KAAK,KAAK;EACpC,IAAIC,KAAK,GAAG,OAAOH,IAAI,CAACG,KAAK,KAAK,QAAQ,GACtCb,KAAK,CAACc,KAAK,CAACJ,IAAI,CAACG,KAAK,IAAI,OAAO,CAAC,GAClCH,IAAI,CAACG,KAAK;EACd,IAAIE,IAAI,GAAGL,IAAI,CAACK,IAAI,IAAI,YAAY;EACpC,IAAIC,MAAM,GAAGN,IAAI,CAACM,MAAM,IAAI,KAAK;EAEjC,IAAIA,MAAM,KAAK,KAAK,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IACpD,MAAM,IAAIC,SAAS,CAAC,gCAAgC,CAAC;EACvD;;EAEA;EACA,IAAIC,WAAW,GAAG,OAAOH,IAAI,KAAK,UAAU,GACxCI,WAAW,CAACJ,IAAI,CAAC,GACjBA,IAAI;EAER,SAASD,KAAKA,CAAEM,GAAG,EAAE;IACnB,OAAOA,GAAG;EACZ;EAEA,OAAO,SAASC,UAAUA,CAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAE;IAC1C,IAAIF,GAAG,CAACG,KAAK,EAAE;MACbtB,KAAK,CAAC,qBAAqB,CAAC;MAC5BqB,IAAI,CAAC,CAAC;MACN;IACF;IAEAF,GAAG,CAACI,IAAI,GAAGJ,GAAG,CAACI,IAAI,IAAI,CAAC,CAAC;;IAEzB;IACA,IAAI,CAACrB,MAAM,CAACsB,OAAO,CAACL,GAAG,CAAC,EAAE;MACxBnB,KAAK,CAAC,iBAAiB,CAAC;MACxBqB,IAAI,CAAC,CAAC;MACN;IACF;IAEArB,KAAK,CAAC,iBAAiB,EAAEmB,GAAG,CAACM,OAAO,CAAC,cAAc,CAAC,CAAC;;IAErD;IACA,IAAI,CAACV,WAAW,CAACI,GAAG,CAAC,EAAE;MACrBnB,KAAK,CAAC,cAAc,CAAC;MACrBqB,IAAI,CAAC,CAAC;MACN;IACF;;IAEA;IACA,IAAIK,OAAO,GAAGC,UAAU,CAACR,GAAG,CAAC,IAAIX,cAAc;;IAE/C;IACAP,IAAI,CAACkB,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEV,KAAK,EAAEX,KAAK,EAAE;MACjC4B,QAAQ,EAAEF,OAAO;MACjBjB,OAAO,EAAEA,OAAO;MAChBC,KAAK,EAAEA,KAAK;MACZG,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASc,UAAUA,CAAER,GAAG,EAAE;EACxB,IAAI;IACF,OAAO,CAACpB,WAAW,CAACY,KAAK,CAACQ,GAAG,CAAC,CAACU,UAAU,CAACH,OAAO,IAAI,EAAE,EAAEI,WAAW,CAAC,CAAC;EACxE,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAOC,SAAS;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAShB,WAAWA,CAAEJ,IAAI,EAAE;EAC1B,OAAO,SAASqB,SAASA,CAAEd,GAAG,EAAE;IAC9B,OAAOe,OAAO,CAAChC,MAAM,CAACiB,GAAG,EAAEP,IAAI,CAAC,CAAC;EACnC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}