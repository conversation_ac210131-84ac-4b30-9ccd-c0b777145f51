spring.application.name=Actionnaire


spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.hibernate.ddl-auto=update


spring.jpa.show-sql=true

debug=true

server.port=8020

spring.config.import=optional:configserver:http://localhost:8888


eureka.client.service-url.defaultZone=http://localhost:8761/eureka/


management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always




spring.datasource.url=*************************************
spring.datasource.username=C##rayen
spring.datasource.password=rayouna123
spring.datasource.driver-class-name=oracle.jdbc.driver.OracleDriver


application.config.portefeuille-url=http://localhost:8040/GRA/Portefeuille
