package com.Aziz.Administratif.ClientRelation.Entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;



@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Action {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idAction;
    private String isinAction;
    private String nomSociete;
    private Double prix;
    // private String proprietaire;
    @CreationTimestamp
    private LocalDateTime DateCreation;


//    @LastModifiedDate
//    private LocalDateTime DateModification;


    @OneToMany(mappedBy = "action")
    @JsonBackReference
    @JsonIgnore
    private List<Portefeuille> portefeuilles;

    @OneToMany(mappedBy = "action")
    @JsonBackReference
    private List<Cotation> cotations;


    @JsonIgnore
    public List<Actionnaire> getActionnaires() {
        return portefeuilles.stream()
                .map(Portefeuille::getActionnaire)
                .distinct()
                .collect(Collectors.toList());
    }

}
