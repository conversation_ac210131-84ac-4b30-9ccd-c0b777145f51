package com.Aziz.Client.Controllers;

import com.Aziz.Client.Entity.Transaction;
import com.Aziz.Client.Repositories.PortefeuilleRepository;
import com.Aziz.Client.Repositories.TransactionRepository;
import com.Aziz.Client.Services.ExcelInjection;
import com.Aziz.Client.Services.TransactionService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@RestController
@RequestMapping("GRA/Client/Transaction")
@RequiredArgsConstructor
public class TransactionController {

    @Autowired
    private TransactionService transactionService;
    @Autowired
    private TransactionRepository TransactionRepository;
    private PortefeuilleRepository PortefeuilleRepository;




    @GetMapping("/All_Transactions")
    public ResponseEntity<List<Transaction>> getAllTransactions() {
        return ResponseEntity.ok(TransactionRepository.findAll());
    }

    @CrossOrigin(origins = "http://localhost:4200")
    @PostMapping("/ExelInjecter_Transaction")
    public ResponseEntity<String> uploadFile(@RequestParam("file") MultipartFile file,Long userId) {

        if (!ExcelInjection.hasExcelFormat(file)) {
            return ResponseEntity.badRequest().body("Format de fichier non valide. Veuillez télécharger un fichier Excel.");
        }

        transactionService.savewithExcel(file,userId);
        return ResponseEntity.ok("Fichier téléchargé et données enregistrées avec succès par vour et votre referenciel est: ");
    }










    //relation avec User
    @CrossOrigin(origins = "http://localhost:4200")
    @GetMapping("/{user-id}/transactions")
    public ResponseEntity<List<Transaction>>findAllUsers(@PathVariable("user-id")Long userId){
        return ResponseEntity.ok(transactionService.findByUserId(userId));
    }

    @CrossOrigin(origins = "http://localhost:4200")
    @GetMapping("/{idActionnaire}/Actionnaire/transactions")
    public ResponseEntity<List<Transaction>>findByActionnaireId(@PathVariable("idActionnaire")Long idActionnaire) {
        return ResponseEntity.ok(transactionService.findByPortefeuilleId(idActionnaire));


    }


}

