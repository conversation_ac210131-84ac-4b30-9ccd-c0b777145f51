package com.Aziz.Client.Entity;


import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name="Cotation")
public class Cotation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idCotation;

    private Double prix;

    private LocalDate DateCotation;

    @ManyToOne
    @JoinColumn(name = "idAction")
    private Action action;
}
