<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="Discovery" />
        <module name="Config" />
        <module name="Gateway" />
      </profile>
      <profile name="Annotation profile for security" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar" />
        </processorPath>
        <module name="security" />
      </profile>
      <profile name="Annotation profile for User" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$MAVEN_REPOSITORY$/org/projectlombok/lombok/1.18.36/lombok-1.18.36.jar" />
        </processorPath>
        <module name="Action" />
        <module name="User" />
        <module name="Transaction" />
        <module name="Client (1)" />
        <module name="Administratif" />
        <module name="Actionnaire" />
        <module name="Client" />
        <module name="Notification" />
        <module name="Portefeuille" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="Action" options="-parameters" />
      <module name="Actionnaire" options="-parameters" />
      <module name="Administratif" options="-parameters" />
      <module name="Client" options="-parameters" />
      <module name="Client (1)" options="-parameters" />
      <module name="Config" options="-parameters" />
      <module name="Discovery" options="-parameters" />
      <module name="Gateway" options="-parameters" />
      <module name="Notification" options="-parameters" />
      <module name="Portefeuille" options="-parameters" />
      <module name="Transaction" options="-parameters" />
      <module name="User" options="-parameters" />
      <module name="security" options="-parameters" />
    </option>
  </component>
</project>