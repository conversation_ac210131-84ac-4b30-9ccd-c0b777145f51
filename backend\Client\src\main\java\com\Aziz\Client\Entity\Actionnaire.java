package com.Aziz.Client.Entity;


import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;
import java.util.List;


@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name="Actionnaire")
public class Actionnaire {

    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idActionnaire;
    private String nomActionnaire;
    private String PrenomActionnaire;
    private String emailActionnaire;
    private Long Telephone;

    @CreationTimestamp
    private LocalDateTime DateCreation;


    @LastModifiedDate
    private LocalDateTime DateModification;



    @OneToMany(mappedBy = "actionnaire")
    @JsonBackReference
    private List<Portefeuille> portefeuilles;



//    public List<Portefeuille> getActions() {
//        return portefeuilles.stream().toList();
//////                .map(Portefeuille::getIdPortefeuille)
//////                .distinct()
////                .collect(Collectors.toList());
//    }

}
