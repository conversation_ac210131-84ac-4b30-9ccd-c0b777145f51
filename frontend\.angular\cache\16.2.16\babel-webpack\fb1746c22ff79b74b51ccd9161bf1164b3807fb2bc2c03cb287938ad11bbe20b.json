{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ResponDashboardComponent {\n  static {\n    this.ɵfac = function ResponDashboardComponent_Factory(t) {\n      return new (t || ResponDashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResponDashboardComponent,\n      selectors: [[\"app-respon-dashboard\"]],\n      decls: 2,\n      vars: 0,\n      template: function ResponDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"respon-dashboard works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ResponDashboardComponent", "selectors", "decls", "vars", "template", "ResponDashboardComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\frontend\\src\\app\\respon-dashboard\\respon-dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\frontend\\src\\app\\respon-dashboard\\respon-dashboard.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './respon-dashboard.component.html',\n  styleUrls: ['./respon-dashboard.component.css']\n})\nexport class ResponDashboardComponent {\n\n}\n", "<p>respon-dashboard works!</p>\n"], "mappings": ";AAOA,OAAM,MAAOA,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPrCE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,8BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}