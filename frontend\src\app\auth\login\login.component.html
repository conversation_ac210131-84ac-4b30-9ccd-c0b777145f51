<!-- Wrapper for background -->
<div class="full-page-bg">
  <div class="container d-flex justify-content-center align-items-center vh-100">
    <div class="card p-4 shadow-lg rounded" style="width: 400px;">
      <div class="text-center">
        <h2 class="mb-3">Se connecter</h2>
        <p class="text-muted">Connectez-vous à votre compte</p>
      </div>

      <form (ngSubmit)="oonLogin()">
        <!-- Matricule Field -->
        <div class="mb-3">
          <label for="matricule" class="form-label fw-bold">Matricule</label>
          <div class="input-group">
            <span class="input-group-text"><i class="bi bi-person-badge"></i></span>
            <input 
              type="text" 
              id="matricule" 
              class="form-control" 
              [(ngModel)]="matricule" 
              name="matricule" 
              required 
              placeholder="Entrez votre matricule" />
          </div>
        </div>

        <!-- Password Field -->
        <div class="mb-3">
          <label for="password" class="form-label fw-bold">Mot de passe</label>
          <div class="input-group">
            <span class="input-group-text"><i class="bi bi-lock"></i></span>
            <input 
              type="password" 
              id="password" 
              class="form-control" 
              [(ngModel)]="password" 
              name="password" 
              required 
              placeholder="Entrez votre mot de passe" />
          </div>
        </div>

        <!-- Login Button -->
        <button type="submit" class="btn btn-primary w-100 fw-bold">
          <i class="bi bi-box-arrow-in-right"></i> Se connecter
        </button>

        <!-- Error Message -->
        <div *ngIf="loginError" class="alert alert-danger text-center mt-3">
          {{ loginError }}
        </div>

        <!-- Additional Links -->
        <div class="text-center mt-3">
          <a routerLink="/forgot-password" class="text-decoration-none">Mot de passe oublié</a>
        </div>

        
      </form>
    </div>
  </div>
</div>
