{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/actionnaire.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../auth/authentication.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"myChart\"];\nfunction ActionnairesComponent_a_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 60);\n    i0.ɵɵelement(1, \"span\", 61);\n    i0.ɵɵtext(2, \" Dashboard \");\n    i0.ɵɵelementStart(3, \"span\", 62);\n    i0.ɵɵtext(4, \"(current)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActionnairesComponent_a_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 63);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Gestion des utilisateurs \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionnairesComponent_a_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 65);\n    i0.ɵɵelement(1, \"span\", 66);\n    i0.ɵɵtext(2, \" Gestion des groupes \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionnairesComponent_a_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 67);\n    i0.ɵɵelement(1, \"span\", 68);\n    i0.ɵɵtext(2, \" Gestion des transactions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionnairesComponent_a_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 69);\n    i0.ɵɵelement(1, \"span\", 70);\n    i0.ɵɵtext(2, \" Gestion des actions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionnairesComponent_a_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 71);\n    i0.ɵɵelement(1, \"span\", 72);\n    i0.ɵɵtext(2, \" Gestion des actionnaires \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionnairesComponent_a_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 73);\n    i0.ɵɵelement(1, \"span\", 74);\n    i0.ɵɵtext(2, \" Gestion des Portefeuilles \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionnairesComponent_tr_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const a_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(a_r9.nomActionnaire);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(a_r9.prenomActionnaire);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(a_r9.emailActionnaire);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(a_r9.telephone);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 5, a_r9.dateCreation, \"short\"));\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, 0.25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: 'Poppins', sans-serif;\\n  margin: 0;\\n  padding: 0;\\n  min-height: 100vh;\\n  background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n  background-size: 400% 400%;\\n  animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n  color: #fff;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n  0% {\\n      background-position: 0% 50%;\\n  }\\n\\n  50% {\\n      background-position: 100% 50%;\\n  }\\n\\n  100% {\\n      background-position: 0% 50%;\\n  }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  margin-top: 20px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 1.5rem;\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  color: #fff;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  border-collapse: separate;\\n  border-spacing: 0 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.15);\\n  color: #ffffff;\\n  font-weight: 600;\\n  text-align: center;\\n  border: none;\\n  padding: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  text-align: center;\\n  border-radius: 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.01);\\n  box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  vertical-align: middle;\\n  padding: 0.9rem;\\n  font-size: 0.95rem;\\n}\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n  border: none;\\n  padding: 12px 25px;\\n  border-radius: 30px;\\n  text-transform: uppercase;\\n  font-weight: bold;\\n  transition: background 0.3s, transform 0.2s;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n  background-color: #218838;\\n  transform: translateY(-2px);\\n}\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n  color: #206ee1;\\n  cursor: pointer;\\n  font-size: 20px;\\n  margin: 0 10px;\\n  transition: color 0.3s;\\n}\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffffff;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n  color: #d22d2d;\\n  cursor: pointer;\\n  font-size: 20px;\\n  transition: color 0.3s;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffffff;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 1.5rem;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n  background-color: #f1f1f1;\\n  padding: 1rem;\\n  border-top: 1px solid #ddd;\\n  display: flex;\\n  justify-content: center; \\n\\n  gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  font-size: 1rem;\\n  border-radius: 0.3rem;\\n  transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n  transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column; \\n\\n  align-items: flex-start; \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  background-color: rgba(255, 255, 255, 0.1); \\n\\n  border: 1px solid rgba(255, 255, 255, 0.3); \\n\\n  border-radius: 30px; \\n\\n  color: #fff; \\n\\n  padding: 10px 20px; \\n\\n  font-size: 1rem; \\n\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); \\n\\n  width: 100%; \\n\\n  max-width: 400px; \\n\\n  transition: all 0.3s ease; \\n\\n}\\n\\n.search-bar[_ngcontent-%COMP%]:focus {\\n  outline: none; \\n\\n  border-color: #007bff; \\n\\n  box-shadow: 0 0 8px rgba(0, 123, 255, 0.6); \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n  background: rgba(20, 33, 59, 0.9); \\n\\n  color: #fff;\\n  min-height: 100vh;\\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #ccc;\\n  transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n  background-color: #000 !important;\\n  color: #fff;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n  position: relative;\\n  padding: 0.5rem 1rem;\\n  transition: color 0.3s ease;\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  width: 0;\\n  background: #ff4c60;\\n  transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 30px;\\n  object-fit: cover;\\n  border-radius: 50%; \\n\\n  margin-right: 8px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class ActionnairesComponent {\n  constructor(actionnaireService, router, authService) {\n    this.actionnaireService = actionnaireService;\n    this.router = router;\n    this.authService = authService;\n    this.newActionnaire = {\n      idActionnaire: undefined,\n      nomActionnaire: '',\n      prenomActionnaire: '',\n      emailActionnaire: '',\n      telephone: undefined\n    };\n    this.actionnaires = [];\n  }\n  ngOnInit() {\n    this.fetchActionnaires();\n    ;\n  }\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  fetchActionnaires() {\n    this.actionnaireService.getAllActionnaires().subscribe({\n      next: data => {\n        this.actionnaires = data;\n        console.log('Fetched actionnaires:', data);\n      },\n      error: err => {\n        console.error('Error fetching actionnaires:', err);\n      }\n    });\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    this.router.navigate(['/login']);\n  }\n  submitActionnaire() {\n    const payload = {\n      idActionnaire: this.newActionnaire.idActionnaire,\n      nomActionnaire: this.newActionnaire.nomActionnaire,\n      prenomActionnaire: this.newActionnaire.prenomActionnaire,\n      emailActionnaire: this.newActionnaire.emailActionnaire,\n      telephone: this.newActionnaire.telephone\n    };\n    this.actionnaireService.createActionnaire(payload).subscribe({\n      next: response => {\n        console.log('Actionnaire ajouté:', response);\n        this.fetchActionnaires(); // refresh table\n        this.newActionnaire = {}; // ✅ clear form\n      },\n\n      error: err => {\n        console.error('Erreur lors de l\\'ajout de l\\'Actionnaire:', err);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ActionnairesComponent_Factory(t) {\n      return new (t || ActionnairesComponent)(i0.ɵɵdirectiveInject(i1.ActionnaireService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthenticationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionnairesComponent,\n      selectors: [[\"app-actions\"]],\n      viewQuery: function ActionnairesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 98,\n      vars: 13,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"class\", \"nav-link\", \"href\", \"/adminDash\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/users\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/groups\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/transactions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actionnaires\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/port\", 4, \"ngIf\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"table-responsive\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#addActionnaireModal\", 1, \"btn\", \"btn-primary\"], [1, \"table\", \"table-hover\"], [1, \"thead-dark\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"addActionnaireModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"addActionnaireModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [3, \"ngSubmit\"], [\"actionnaireForm\", \"ngForm\"], [1, \"modal-header\"], [\"id\", \"addActionnaireModalLabel\", 1, \"modal-title\"], [1, \"modal-body\"], [1, \"form-group\"], [\"type\", \"number\", \"name\", \"idActionnaire\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"mb-3\"], [\"for\", \"nomActionnaire\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"nomActionnaire\", \"name\", \"nomActionnaire\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"prenomActionnaire\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"prenomActionnaire\", \"name\", \"prenomActionnaire\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"emailActionnaire\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"emailActionnaire\", \"name\", \"emailActionnaire\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"telephone\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"telephone\", \"name\", \"telephone\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\"], [\"href\", \"/adminDash\", 1, \"nav-link\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"book\"]],\n      template: function ActionnairesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"link\", 5)(7, \"link\", 5)(8, \"link\", 6)(9, \"link\", 7)(10, \"link\", 8);\n          i0.ɵɵelementStart(11, \"title\");\n          i0.ɵɵtext(12, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"link\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"body\")(15, \"nav\", 10)(16, \"a\", 11);\n          i0.ɵɵelement(17, \"img\", 12);\n          i0.ɵɵtext(18, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"ul\", 13)(20, \"li\", 14)(21, \"a\", 15);\n          i0.ɵɵlistener(\"click\", function ActionnairesComponent_Template_a_click_21_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(22, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 16)(24, \"div\", 17)(25, \"nav\", 18)(26, \"div\", 19)(27, \"ul\", 20)(28, \"li\", 21);\n          i0.ɵɵtemplate(29, ActionnairesComponent_a_29_Template, 5, 0, \"a\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"li\", 21);\n          i0.ɵɵtemplate(31, ActionnairesComponent_a_31_Template, 3, 0, \"a\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"li\", 21);\n          i0.ɵɵtemplate(33, ActionnairesComponent_a_33_Template, 3, 0, \"a\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"li\", 21);\n          i0.ɵɵtemplate(35, ActionnairesComponent_a_35_Template, 3, 0, \"a\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"li\", 21);\n          i0.ɵɵtemplate(37, ActionnairesComponent_a_37_Template, 3, 0, \"a\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"li\", 21);\n          i0.ɵɵtemplate(39, ActionnairesComponent_a_39_Template, 3, 0, \"a\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"li\", 21);\n          i0.ɵɵtemplate(41, ActionnairesComponent_a_41_Template, 3, 0, \"a\", 28);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(42, \"main\", 29)(43, \"div\", 30)(44, \"h1\", 31);\n          i0.ɵɵtext(45, \"Gestion des Actionnaires\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 32);\n          i0.ɵɵelement(47, \"div\", 33);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 34)(49, \"button\", 35);\n          i0.ɵɵtext(50, \" Ajouter un Actionnaire\\n\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"table\", 36)(52, \"thead\", 37)(53, \"tr\")(54, \"th\");\n          i0.ɵɵtext(55, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"th\");\n          i0.ɵɵtext(57, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"th\");\n          i0.ɵɵtext(59, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"th\");\n          i0.ɵɵtext(61, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"th\");\n          i0.ɵɵtext(63, \"date de creation\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(64, \"tbody\");\n          i0.ɵɵtemplate(65, ActionnairesComponent_tr_65_Template, 12, 8, \"tr\", 38);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(66, \"div\", 39)(67, \"div\", 40)(68, \"div\", 41)(69, \"form\", 42, 43);\n          i0.ɵɵlistener(\"ngSubmit\", function ActionnairesComponent_Template_form_ngSubmit_69_listener() {\n            return ctx.submitActionnaire();\n          });\n          i0.ɵɵelementStart(71, \"div\", 44)(72, \"h5\", 45);\n          i0.ɵɵtext(73, \"Ajouter un Actionnaire\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"div\", 46)(75, \"div\", 47)(76, \"label\");\n          i0.ɵɵtext(77, \"ID Actionnaire\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"input\", 48);\n          i0.ɵɵlistener(\"ngModelChange\", function ActionnairesComponent_Template_input_ngModelChange_78_listener($event) {\n            return ctx.newActionnaire.idActionnaire = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"div\", 49)(80, \"label\", 50);\n          i0.ɵɵtext(81, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"input\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function ActionnairesComponent_Template_input_ngModelChange_82_listener($event) {\n            return ctx.newActionnaire.nomActionnaire = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"div\", 49)(84, \"label\", 52);\n          i0.ɵɵtext(85, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"input\", 53);\n          i0.ɵɵlistener(\"ngModelChange\", function ActionnairesComponent_Template_input_ngModelChange_86_listener($event) {\n            return ctx.newActionnaire.prenomActionnaire = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 49)(88, \"label\", 54);\n          i0.ɵɵtext(89, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"input\", 55);\n          i0.ɵɵlistener(\"ngModelChange\", function ActionnairesComponent_Template_input_ngModelChange_90_listener($event) {\n            return ctx.newActionnaire.emailActionnaire = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"div\", 49)(92, \"label\", 56);\n          i0.ɵɵtext(93, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"input\", 57);\n          i0.ɵɵlistener(\"ngModelChange\", function ActionnairesComponent_Template_input_ngModelChange_94_listener($event) {\n            return ctx.newActionnaire.telephone = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(95, \"div\", 58)(96, \"button\", 59);\n          i0.ɵɵtext(97, \"Ajouter\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/adminDash\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/users\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/groups\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/transactions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actionnaires\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/port\"));\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngForOf\", ctx.actionnaires);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngModel\", ctx.newActionnaire.idActionnaire);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newActionnaire.nomActionnaire);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newActionnaire.prenomActionnaire);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newActionnaire.emailActionnaire);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newActionnaire.telephone);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NumberValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm, i4.DatePipe],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "a_r9", "nomActionnaire", "prenomActionnaire", "emailActionnaire", "telephone", "ɵɵpipeBind2", "dateCreation", "ActionnairesComponent", "constructor", "actionnaireService", "router", "authService", "newActionnaire", "idActionnaire", "undefined", "actionnaires", "ngOnInit", "fetchActionnaires", "ngAfterViewInit", "replace", "getAllActionnaires", "subscribe", "next", "data", "console", "log", "error", "err", "logout", "localStorage", "removeItem", "navigate", "submitActionnaire", "payload", "createActionnaire", "response", "ɵɵdirectiveInject", "i1", "ActionnaireService", "i2", "Router", "i3", "AuthenticationService", "selectors", "viewQuery", "ActionnairesComponent_Query", "rf", "ctx", "ɵɵlistener", "ActionnairesComponent_Template_a_click_21_listener", "ɵɵtemplate", "ActionnairesComponent_a_29_Template", "ActionnairesComponent_a_31_Template", "ActionnairesComponent_a_33_Template", "ActionnairesComponent_a_35_Template", "ActionnairesComponent_a_37_Template", "ActionnairesComponent_a_39_Template", "ActionnairesComponent_a_41_Template", "ActionnairesComponent_tr_65_Template", "ActionnairesComponent_Template_form_ngSubmit_69_listener", "ActionnairesComponent_Template_input_ngModelChange_78_listener", "$event", "ActionnairesComponent_Template_input_ngModelChange_82_listener", "ActionnairesComponent_Template_input_ngModelChange_86_listener", "ActionnairesComponent_Template_input_ngModelChange_90_listener", "ActionnairesComponent_Template_input_ngModelChange_94_listener", "ɵɵproperty", "isRouteAllowed"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\actionnaires\\actionnaires.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\actionnaires\\actionnaires.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, ElementRef, ViewChild } from '@angular/core';\nimport { ActionnaireService } from '../services/actionnaire.service';\nimport { Actionnaire } from '../model/actionnaire.model';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\nimport { AuthenticationService } from '../auth/authentication.service';\n\n@Component({\n  selector: 'app-actions',\n  templateUrl: './actionnaires.component.html',\n  styleUrls: ['./actionnaires.component.css']\n})\nexport class ActionnairesComponent implements OnInit, AfterViewInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\nnewActionnaire: Partial<Actionnaire> = {\n    idActionnaire: undefined,   // ✅ now included\n    nomActionnaire: '',\n    prenomActionnaire: '',\n    emailActionnaire: '',\n    telephone: undefined\n  };\n  actionnaires: Actionnaire[] = [];\n\n  constructor(\n    private actionnaireService: ActionnaireService,\n    private router: Router,\n    public authService: AuthenticationService,\n  ) {}\n\n  ngOnInit(): void {\n    this.fetchActionnaires();\n;\n  }\n\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  fetchActionnaires(): void {\n    this.actionnaireService.getAllActionnaires().subscribe({\n      next: (data) => {\n        this.actionnaires = data;\n        console.log('Fetched actionnaires:', data);\n      },\n      error: (err) => {\n        console.error('Error fetching actionnaires:', err);\n      }\n    });\n  }\n\n  logout(): void {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    this.router.navigate(['/login']);\n  }\n\n\n\nsubmitActionnaire(): void {\n  const payload = {\n    idActionnaire: this.newActionnaire.idActionnaire,\n    nomActionnaire: this.newActionnaire.nomActionnaire,\n    prenomActionnaire: this.newActionnaire.prenomActionnaire,\n    emailActionnaire: this.newActionnaire.emailActionnaire,\n    telephone: this.newActionnaire.telephone\n  };\n\n  this.actionnaireService.createActionnaire(payload).subscribe({\n    next: (response) => {\n      console.log('Actionnaire ajouté:', response);\n      this.fetchActionnaires(); // refresh table\n      this.newActionnaire = {}; // ✅ clear form\n    },\n    error: (err) => {\n      console.error('Erreur lors de l\\'ajout de l\\'Actionnaire:', err);\n    }\n  });\n}\n\n}", "\n<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n\n    <!-- Custom styles for this template -->\n    <link href=\"./actionnaires.component.css\" rel=\"stylesheet\">\n\n  </head>\n\n  <body>\n     <!-- Navbar -->\n     <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/adminDash\" *ngIf=\"authService.isRouteAllowed('/adminDash')\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\" *ngIf=\"authService.isRouteAllowed('/users')\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\" *ngIf=\"authService.isRouteAllowed('/groups')\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            \n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/transactions\" *ngIf=\"authService.isRouteAllowed('/transactions')\">\n                <span data-feather=\"dollar-sign\"></span>\n                Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/actions\" *ngIf=\"authService.isRouteAllowed('/actions')\">\n                <span data-feather=\"trending-up\"></span>\n               Gestion des actions\n              </a>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\" *ngIf=\"authService.isRouteAllowed('/actionnaires')\">\n                  <span data-feather=\"users\"></span>\n                 Gestion des actionnaires\n                </a>\n                     <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/port\" *ngIf=\"authService.isRouteAllowed('/port')\">\n                <span data-feather=\"book\"></span>\n               Gestion des Portefeuilles\n              </a>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Gestion des Actionnaires</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n\n              </div>\n\n              \n            </div>\n          </div>\n\n          <div class=\"table-responsive\">\n<button type=\"button\" class=\"btn btn-primary\" data-toggle=\"modal\" data-target=\"#addActionnaireModal\">\n  Ajouter un Actionnaire\n</button>\n            <table class=\"table table-hover\">\n              <thead class=\"thead-dark\">\n                <tr>\n                  <th>Nom</th>\n                  <th>Prénom</th>\n                  <th>Email</th>\n                  <th>Téléphone</th>\n                  <th>date de creation</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let a of actionnaires\">\n                  <td>{{ a.nomActionnaire }}</td>\n                  <td>{{ a.prenomActionnaire }}</td>\n                  <td>{{ a.emailActionnaire }}</td>\n                  <td>{{ a.telephone }}</td>\n                  <td>{{ a.dateCreation| date:'short' }}</td>\n\n                </tr>\n              </tbody>\n            </table>\n          </div>\n\n        \n        </main>\n      </div>\n    </div>\n\n<!-- Add Actionnaire Modal -->\n<div class=\"modal fade\" id=\"addActionnaireModal\" tabindex=\"-1\" aria-labelledby=\"addActionnaireModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <form (ngSubmit)=\"submitActionnaire()\" #actionnaireForm=\"ngForm\">\n        <div class=\"modal-header\">\n          <h5 class=\"modal-title\" id=\"addActionnaireModalLabel\">Ajouter un Actionnaire</h5>\n        </div>\n        <div class=\"modal-body\">\n\n  <div class=\"form-group\">\n  <label>ID Actionnaire</label>\n<input type=\"number\" class=\"form-control\" [(ngModel)]=\"newActionnaire.idActionnaire\" name=\"idActionnaire\">\n</div>\n          <div class=\"mb-3\">\n            <label for=\"nomActionnaire\" class=\"form-label\">Nom</label>\n            <input type=\"text\" id=\"nomActionnaire\" class=\"form-control\" [(ngModel)]=\"newActionnaire.nomActionnaire\" name=\"nomActionnaire\" required>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"prenomActionnaire\" class=\"form-label\">Prénom</label>\n            <input type=\"text\" id=\"prenomActionnaire\" class=\"form-control\" [(ngModel)]=\"newActionnaire.prenomActionnaire\" name=\"prenomActionnaire\" required>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"emailActionnaire\" class=\"form-label\">Email</label>\n            <input type=\"email\" id=\"emailActionnaire\" class=\"form-control\" [(ngModel)]=\"newActionnaire.emailActionnaire\" name=\"emailActionnaire\" required>\n          </div>\n          <div class=\"mb-3\">\n            <label for=\"telephone\" class=\"form-label\">Téléphone</label>\n            <input type=\"number\" id=\"telephone\" class=\"form-control\" [(ngModel)]=\"newActionnaire.telephone\" name=\"telephone\" required>\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button type=\"submit\" class=\"btn btn-success\">Ajouter</button>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n\n\n\n\n    <!-- Placed at the end of the document so the pages load faster -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>    \n\n    </script>\n  </body>\n</html>\n"], "mappings": "AAIA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;;;;;;;ICwCxBC,EAAA,CAAAC,cAAA,YAAuF;IACrFD,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIlDJ,EAAA,CAAAC,cAAA,YAA+E;IAC7ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAiF;IAC/ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAINJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAwC;IACxCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAmF;IACjFD,EAAA,CAAAE,SAAA,eAAwC;IACzCF,EAAA,CAAAG,MAAA,4BACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAEFJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAkC;IACnCF,EAAA,CAAAG,MAAA,iCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,YAA6E;IAC3ED,EAAA,CAAAE,SAAA,eAAiC;IAClCF,EAAA,CAAAG,MAAA,kCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAgCFJ,EAAA,CAAAC,cAAA,SAAmC;IAC7BD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,IAAkC;;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAJvCJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,iBAAA,CAAAC,IAAA,CAAAC,cAAA,CAAsB;IACtBR,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAM,iBAAA,CAAAC,IAAA,CAAAE,iBAAA,CAAyB;IACzBT,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAAC,IAAA,CAAAG,gBAAA,CAAwB;IACxBV,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAM,iBAAA,CAAAC,IAAA,CAAAI,SAAA,CAAiB;IACjBX,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAY,WAAA,QAAAL,IAAA,CAAAM,YAAA,WAAkC;;;;AD3GxD,OAAM,MAAOC,qBAAqB;EAWhCC,YACUC,kBAAsC,EACtCC,MAAc,EACfC,WAAkC;IAFjC,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IAZtB,KAAAC,cAAc,GAAyB;MACnCC,aAAa,EAAEC,SAAS;MACxBb,cAAc,EAAE,EAAE;MAClBC,iBAAiB,EAAE,EAAE;MACrBC,gBAAgB,EAAE,EAAE;MACpBC,SAAS,EAAEU;KACZ;IACD,KAAAC,YAAY,GAAkB,EAAE;EAM7B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IAC5B;EACE;EAEAC,eAAeA,CAAA;IACb1B,OAAO,CAAC2B,OAAO,EAAE,CAAC,CAAC;EACrB;;EAEAF,iBAAiBA,CAAA;IACf,IAAI,CAACR,kBAAkB,CAACW,kBAAkB,EAAE,CAACC,SAAS,CAAC;MACrDC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACR,YAAY,GAAGQ,IAAI;QACxBC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,IAAI,CAAC;MAC5C,CAAC;MACDG,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;MACpD;KACD,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAIFC,iBAAiBA,CAAA;IACf,MAAMC,OAAO,GAAG;MACdpB,aAAa,EAAE,IAAI,CAACD,cAAc,CAACC,aAAa;MAChDZ,cAAc,EAAE,IAAI,CAACW,cAAc,CAACX,cAAc;MAClDC,iBAAiB,EAAE,IAAI,CAACU,cAAc,CAACV,iBAAiB;MACxDC,gBAAgB,EAAE,IAAI,CAACS,cAAc,CAACT,gBAAgB;MACtDC,SAAS,EAAE,IAAI,CAACQ,cAAc,CAACR;KAChC;IAED,IAAI,CAACK,kBAAkB,CAACyB,iBAAiB,CAACD,OAAO,CAAC,CAACZ,SAAS,CAAC;MAC3DC,IAAI,EAAGa,QAAQ,IAAI;QACjBX,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEU,QAAQ,CAAC;QAC5C,IAAI,CAAClB,iBAAiB,EAAE,CAAC,CAAC;QAC1B,IAAI,CAACL,cAAc,GAAG,EAAE,CAAC,CAAC;MAC5B,CAAC;;MACDc,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,4CAA4C,EAAEC,GAAG,CAAC;MAClE;KACD,CAAC;EACJ;;;uBAlEapB,qBAAqB,EAAAd,EAAA,CAAA2C,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA7C,EAAA,CAAA2C,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA/C,EAAA,CAAA2C,iBAAA,CAAAK,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAArBnC,qBAAqB;MAAAoC,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCVlCrD,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAE,SAAA,cAAsB;UAUtBF,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAG,MAAA,wCAAgC;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAE/CJ,EAAA,CAAAE,SAAA,eAAmF;UAOrFF,EAAA,CAAAI,YAAA,EAAO;UAEPJ,EAAA,CAAAC,cAAA,YAAM;UAIAD,EAAA,CAAAE,SAAA,eAAwE;UACxEF,EAAA,CAAAG,MAAA,aACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAuD,UAAA,mBAAAC,mDAAA;YAAA,OAASF,GAAA,CAAAnB,MAAA,EAAQ;UAAA,EAAC;UAACnC,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAIzDJ,EAAA,CAAAC,cAAA,eAA6B;UAMjBD,EAAA,CAAAyD,UAAA,KAAAC,mCAAA,gBAGI;UACN1D,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAyD,UAAA,KAAAE,mCAAA,gBAGI;UACN3D,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAyD,UAAA,KAAAG,mCAAA,gBAGI;UACN5D,EAAA,CAAAI,YAAA,EAAK;UAEPJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAyD,UAAA,KAAAI,mCAAA,gBAGI;UACN7D,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAyD,UAAA,KAAAK,mCAAA,gBAGI;UAJN9D,EAAA,CAAAI,YAAA,EAAqB;UAKnBJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAyD,UAAA,KAAAM,mCAAA,gBAGI;UAJN/D,EAAA,CAAAI,YAAA,EAAqB;UAKdJ,EAAA,CAAAC,cAAA,cAAqB;UAC5BD,EAAA,CAAAyD,UAAA,KAAAO,mCAAA,gBAGI;UAJGhE,EAAA,CAAAI,YAAA,EAAqB;UASlCJ,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAG,MAAA,gCAAwB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC5CJ,EAAA,CAAAC,cAAA,eAAsC;UACpCD,EAAA,CAAAE,SAAA,eAEM;UAGRF,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAC,cAAA,eAA8B;UAEtCD,EAAA,CAAAG,MAAA,iCACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACGJ,EAAA,CAAAC,cAAA,iBAAiC;UAGvBD,EAAA,CAAAG,MAAA,WAAG;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACZJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,mBAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACfJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,2BAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAG7BJ,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAyD,UAAA,KAAAQ,oCAAA,kBAOK;UACPjE,EAAA,CAAAI,YAAA,EAAQ;UAUtBJ,EAAA,CAAAC,cAAA,eAA6H;UAGjHD,EAAA,CAAAuD,UAAA,sBAAAW,yDAAA;YAAA,OAAYZ,GAAA,CAAAf,iBAAA,EAAmB;UAAA,EAAC;UACpCvC,EAAA,CAAAC,cAAA,eAA0B;UAC8BD,EAAA,CAAAG,MAAA,8BAAsB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEnFJ,EAAA,CAAAC,cAAA,eAAwB;UAGvBD,EAAA,CAAAG,MAAA,sBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC/BJ,EAAA,CAAAC,cAAA,iBAA0G;UAAhED,EAAA,CAAAuD,UAAA,2BAAAY,+DAAAC,MAAA;YAAA,OAAAd,GAAA,CAAAnC,cAAA,CAAAC,aAAA,GAAAgD,MAAA;UAAA,EAA0C;UAApFpE,EAAA,CAAAI,YAAA,EAA0G;UAEhGJ,EAAA,CAAAC,cAAA,eAAkB;UAC+BD,EAAA,CAAAG,MAAA,WAAG;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC1DJ,EAAA,CAAAC,cAAA,iBAAuI;UAA3ED,EAAA,CAAAuD,UAAA,2BAAAc,+DAAAD,MAAA;YAAA,OAAAd,GAAA,CAAAnC,cAAA,CAAAX,cAAA,GAAA4D,MAAA;UAAA,EAA2C;UAAvGpE,EAAA,CAAAI,YAAA,EAAuI;UAEzIJ,EAAA,CAAAC,cAAA,eAAkB;UACkCD,EAAA,CAAAG,MAAA,mBAAM;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAChEJ,EAAA,CAAAC,cAAA,iBAAgJ;UAAjFD,EAAA,CAAAuD,UAAA,2BAAAe,+DAAAF,MAAA;YAAA,OAAAd,GAAA,CAAAnC,cAAA,CAAAV,iBAAA,GAAA2D,MAAA;UAAA,EAA8C;UAA7GpE,EAAA,CAAAI,YAAA,EAAgJ;UAElJJ,EAAA,CAAAC,cAAA,eAAkB;UACiCD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC9DJ,EAAA,CAAAC,cAAA,iBAA8I;UAA/ED,EAAA,CAAAuD,UAAA,2BAAAgB,+DAAAH,MAAA;YAAA,OAAAd,GAAA,CAAAnC,cAAA,CAAAT,gBAAA,GAAA0D,MAAA;UAAA,EAA6C;UAA5GpE,EAAA,CAAAI,YAAA,EAA8I;UAEhJJ,EAAA,CAAAC,cAAA,eAAkB;UAC0BD,EAAA,CAAAG,MAAA,2BAAS;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC3DJ,EAAA,CAAAC,cAAA,iBAA0H;UAAjED,EAAA,CAAAuD,UAAA,2BAAAiB,+DAAAJ,MAAA;YAAA,OAAAd,GAAA,CAAAnC,cAAA,CAAAR,SAAA,GAAAyD,MAAA;UAAA,EAAsC;UAA/FpE,EAAA,CAAAI,YAAA,EAA0H;UAG9HJ,EAAA,CAAAC,cAAA,eAA0B;UACsBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAS;;;UAvHjBJ,EAAA,CAAAK,SAAA,IAA8C;UAA9CL,EAAA,CAAAyE,UAAA,SAAAnB,GAAA,CAAApC,WAAA,CAAAwD,cAAA,eAA8C;UAMlD1E,EAAA,CAAAK,SAAA,GAA0C;UAA1CL,EAAA,CAAAyE,UAAA,SAAAnB,GAAA,CAAApC,WAAA,CAAAwD,cAAA,WAA0C;UAMzC1E,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAyE,UAAA,SAAAnB,GAAA,CAAApC,WAAA,CAAAwD,cAAA,YAA2C;UAOvC1E,EAAA,CAAAK,SAAA,GAAiD;UAAjDL,EAAA,CAAAyE,UAAA,SAAAnB,GAAA,CAAApC,WAAA,CAAAwD,cAAA,kBAAiD;UAMtD1E,EAAA,CAAAK,SAAA,GAA4C;UAA5CL,EAAA,CAAAyE,UAAA,SAAAnB,GAAA,CAAApC,WAAA,CAAAwD,cAAA,aAA4C;UAKrC1E,EAAA,CAAAK,SAAA,GAAiD;UAAjDL,EAAA,CAAAyE,UAAA,SAAAnB,GAAA,CAAApC,WAAA,CAAAwD,cAAA,kBAAiD;UAK3D1E,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAyE,UAAA,SAAAnB,GAAA,CAAApC,WAAA,CAAAwD,cAAA,UAAyC;UAmCvD1E,EAAA,CAAAK,SAAA,IAAe;UAAfL,EAAA,CAAAyE,UAAA,YAAAnB,GAAA,CAAAhC,YAAA,CAAe;UA6BPtB,EAAA,CAAAK,SAAA,IAA0C;UAA1CL,EAAA,CAAAyE,UAAA,YAAAnB,GAAA,CAAAnC,cAAA,CAAAC,aAAA,CAA0C;UAIZpB,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAyE,UAAA,YAAAnB,GAAA,CAAAnC,cAAA,CAAAX,cAAA,CAA2C;UAIxCR,EAAA,CAAAK,SAAA,GAA8C;UAA9CL,EAAA,CAAAyE,UAAA,YAAAnB,GAAA,CAAAnC,cAAA,CAAAV,iBAAA,CAA8C;UAI9CT,EAAA,CAAAK,SAAA,GAA6C;UAA7CL,EAAA,CAAAyE,UAAA,YAAAnB,GAAA,CAAAnC,cAAA,CAAAT,gBAAA,CAA6C;UAInDV,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAyE,UAAA,YAAAnB,GAAA,CAAAnC,cAAA,CAAAR,SAAA,CAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}