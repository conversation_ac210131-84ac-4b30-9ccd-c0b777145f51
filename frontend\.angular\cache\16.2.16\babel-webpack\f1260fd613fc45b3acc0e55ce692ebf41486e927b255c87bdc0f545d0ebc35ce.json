{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth/authentication.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nexport class TransactionsComponent {\n  constructor(authService, router, http) {\n    this.authService = authService;\n    this.router = router;\n    this.http = http;\n  }\n  ngAfterViewInit() {\n    throw new Error('Method not implemented.');\n  }\n  onFileSelected(event) {\n    this.selectedFile = event.target.files[0];\n  }\n  uploadExcel() {\n    if (!this.selectedFile) {\n      alert(\"Veuillez sélectionner un fichier Excel.\");\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"file\", this.selectedFile);\n    this.http.post('http://localhost:8000/api/v1/auth/upload-excel', formData, {\n      responseType: 'text'\n    }).subscribe({\n      next: response => {\n        alert(\"Fichier envoyé avec succès : \" + response);\n      },\n      error: error => {\n        alert(\"Erreur lors de l'envoi du fichier : \" + error.error);\n      }\n    });\n  }\n  logout() {\n    // Clear token and other data\n    localStorage.removeItem('jwt_token'); // Fix: Remove 'jwt_token'\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    // Optionally, check that these items are removed:\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    // Navigate to login page\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function TransactionsComponent_Factory(t) {\n      return new (t || TransactionsComponent)(i0.ɵɵdirectiveInject(i1.AuthenticationService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TransactionsComponent,\n      selectors: [[\"app-transactions\"]],\n      decls: 70,\n      vars: 0,\n      consts: [[\"lang\", \"en\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-store, no-cache, must-revalidate, max-age=0\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"card\", \"mb-4\"], [1, \"card-body\"], [1, \"card-title\"], [1, \"form-group\"], [\"type\", \"file\", \"id\", \"excelFile\", 1, \"form-control-file\", 3, \"change\"], [1, \"btn\", \"btn-primary\", \"mt-2\", 3, \"click\"], [\"id\", \"myChart\", \"width\", \"900\", \"height\", \"380\", 1, \"my-4\", 2, \"background-color\", \"lightgrey\"], [\"myChart\", \"\"]],\n      template: function TransactionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"meta\", 6)(8, \"meta\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10)(12, \"link\", 11);\n          i0.ɵɵelementStart(13, \"title\");\n          i0.ɵɵtext(14, \"Dashboard - Transactions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"link\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"body\")(17, \"nav\", 13)(18, \"a\", 14);\n          i0.ɵɵelement(19, \"img\", 15);\n          i0.ɵɵtext(20, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"ul\", 16)(22, \"li\", 17)(23, \"a\", 18);\n          i0.ɵɵlistener(\"click\", function TransactionsComponent_Template_a_click_23_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(24, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 19)(26, \"div\", 20)(27, \"nav\", 21)(28, \"div\", 22)(29, \"ul\", 23)(30, \"li\", 24)(31, \"a\", 25);\n          i0.ɵɵelement(32, \"span\", 26);\n          i0.ɵɵtext(33, \" Dashboard \");\n          i0.ɵɵelementStart(34, \"span\", 27);\n          i0.ɵɵtext(35, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"li\", 24)(37, \"a\", 28);\n          i0.ɵɵelement(38, \"span\", 29);\n          i0.ɵɵtext(39, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"li\", 24)(41, \"a\", 30);\n          i0.ɵɵelement(42, \"span\", 31);\n          i0.ɵɵtext(43, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 24)(45, \"a\", 32);\n          i0.ɵɵelement(46, \"span\", 33);\n          i0.ɵɵtext(47, \" Gestion des transactions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"li\", 24)(49, \"a\", 34);\n          i0.ɵɵelement(50, \"span\", 35);\n          i0.ɵɵtext(51, \" Gestion des actions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"li\", 24)(53, \"a\", 36);\n          i0.ɵɵelement(54, \"span\", 37);\n          i0.ɵɵtext(55, \" Gestion des actionnaires \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(56, \"main\", 38)(57, \"div\", 39)(58, \"h1\", 40);\n          i0.ɵɵtext(59, \"Transactions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 41)(61, \"div\", 42)(62, \"h5\", 43);\n          i0.ɵɵtext(63, \"Importer un fichier Excel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"div\", 44)(65, \"input\", 45);\n          i0.ɵɵlistener(\"change\", function TransactionsComponent_Template_input_change_65_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"button\", 46);\n          i0.ɵɵlistener(\"click\", function TransactionsComponent_Template_button_click_66_listener() {\n            return ctx.uploadExcel();\n          });\n          i0.ɵɵtext(67, \"Envoyer\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(68, \"canvas\", 47, 48);\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      styles: [\"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n  }\\n  \\n  .feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n  }\\n  \\n  .sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #000000;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n  }\\n  \\n  .sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, 0.25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .border-top[_ngcontent-%COMP%] {\\n    border-top: 1px solid #e5e5e5;\\n  }\\n  \\n  .border-bottom[_ngcontent-%COMP%] {\\n    border-bottom: 1px solid #e5e5e5;\\n  }\\n\\n  \\n  body[_ngcontent-%COMP%] {\\n    font-family: 'Poppins', sans-serif;\\n    margin: 0;\\n    padding: 0;\\n    min-height: 100vh;\\n    background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n    background-size: 400% 400%;\\n    animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n    color: #fff;\\n  }\\n  \\n  @keyframes _ngcontent-%COMP%_gradientMove {\\n    0% {\\n        background-position: 0% 50%;\\n    }\\n  \\n    50% {\\n        background-position: 100% 50%;\\n    }\\n  \\n    100% {\\n        background-position: 0% 50%;\\n    }\\n  }\\n  \\n  .container-fluid[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 2rem;\\n    margin-top: 20px;\\n  }\\n  \\n  \\n  \\n  \\n  \\n  .btn-custom[_ngcontent-%COMP%]:hover {\\n    background-color: #218838;\\n    transform: translateY(-2px);\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%] {\\n    background-color: #1c1c1c !important;\\n  }\\n  \\n  h2[_ngcontent-%COMP%] {\\n    color: #fff;\\n  }\\n  \\n  a.router-link[_ngcontent-%COMP%] {\\n    text-decoration: none;\\n  }\\n  \\n  .modal-header[_ngcontent-%COMP%] {\\n    background-color: #252528;\\n  }\\n  \\n  \\n\\n  .modal-body[_ngcontent-%COMP%] {\\n    background-color: #fff;\\n    padding: 1.5rem;\\n    color: #333;\\n    font-size: 1rem;\\n  }\\n  \\n  \\n\\n  .modal-footer[_ngcontent-%COMP%] {\\n    background-color: #f1f1f1;\\n    padding: 1rem;\\n    border-top: 1px solid #ddd;\\n    display: flex;\\n    justify-content: center; \\n\\n    gap: 1rem; \\n\\n  }\\n  \\n  .modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem;\\n    font-size: 1rem;\\n    border-radius: 0.3rem;\\n    transition: background-color 0.3s, transform 0.2s;\\n  }\\n  \\n  .modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    color: #fff;\\n    border: none;\\n  }\\n  \\n  .modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n    transform: translateY(-2px);\\n  }\\n  \\n  .modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n    background-color: #007bff;\\n    color: #fff;\\n    border: none;\\n  }\\n  \\n  .modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n    transform: translateY(-2px);\\n  }\\n  \\n  \\n\\n  .mb-3[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column; \\n\\n    align-items: flex-start; \\n\\n  }\\n  \\n  \\n  \\n  \\n  \\n  .custom-sidebar[_ngcontent-%COMP%] {\\n    background: rgba(20, 33, 59, 0.9); \\n\\n    color: #fff;\\n    min-height: 100vh;\\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\\n  }\\n  \\n  .custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #ccc;\\n    transition: color 0.3s ease, background 0.3s ease;\\n  }\\n  \\n  .custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, 0.1);\\n    border-radius: 4px;\\n  }\\n  \\n  \\n  .custom-topbar[_ngcontent-%COMP%] {\\n    background-color: #000 !important;\\n    color: #fff;\\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n  }\\n  \\n  .custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n  }\\n  \\n  .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    text-decoration: underline;\\n  }\\n  \\n  \\n  \\n  .navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n    position: relative;\\n    padding: 0.5rem 1rem;\\n    transition: color 0.3s ease;\\n    font-weight: 500;\\n    letter-spacing: 0.5px;\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n    content: '';\\n    position: absolute;\\n    left: 0;\\n    bottom: 0;\\n    height: 2px;\\n    width: 0;\\n    background: #ff4c60;\\n    transition: width 0.3s ease;\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    color: #ff4c60 !important;\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n    width: 100%;\\n  }\\n  \\n  .logo-img[_ngcontent-%COMP%] {\\n    height: 30px;\\n    width: 30px;\\n    object-fit: cover;\\n    border-radius: 50%; \\n\\n    margin-right: 8px;\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["TransactionsComponent", "constructor", "authService", "router", "http", "ngAfterViewInit", "Error", "onFileSelected", "event", "selectedFile", "target", "files", "uploadExcel", "alert", "formData", "FormData", "append", "post", "responseType", "subscribe", "next", "response", "error", "logout", "localStorage", "removeItem", "console", "log", "getItem", "navigate", "i0", "ɵɵdirectiveInject", "i1", "AuthenticationService", "i2", "Router", "i3", "HttpClient", "selectors", "decls", "vars", "consts", "template", "TransactionsComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "TransactionsComponent_Template_a_click_23_listener", "TransactionsComponent_Template_input_change_65_listener", "$event", "TransactionsComponent_Template_button_click_66_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\transactions\\transactions.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\transactions\\transactions.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, ViewChild } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthenticationService } from '../auth/authentication.service';\nimport * as feather from 'feather-icons';\nimport { HttpClient } from '@angular/common/http';\n\n@Component({\n  selector: 'app-transactions',\n  templateUrl: './transactions.component.html',\n  styleUrls: ['./transactions.component.css']\n})\nexport class TransactionsComponent implements AfterViewInit {\n  selectedFile!: File;\n\n  constructor(\n    private authService: AuthenticationService,\n    private router: Router,\n    private http: HttpClient\n  ) {}\n  ngAfterViewInit(): void {\n    throw new Error('Method not implemented.');\n  }\n\n  onFileSelected(event: any): void {\n    this.selectedFile = event.target.files[0];\n  }\n\n  uploadExcel(): void {\n    if (!this.selectedFile) {\n      alert(\"Veuillez sélectionner un fichier Excel.\");\n      return;\n    }\n\n    const formData = new FormData();\n    formData.append(\"file\", this.selectedFile);\n\n    this.http.post('http://localhost:8000/api/v1/auth/upload-excel', formData, {\n      responseType: 'text'\n    }).subscribe({\n      next: (response) => {\n        alert(\"Fichier envoyé avec succès : \" + response);\n      },\n      error: (error) => {\n        alert(\"Erreur lors de l'envoi du fichier : \" + error.error);\n      }\n    });\n  }\n  logout() {\n    // Clear token and other data\n    localStorage.removeItem('jwt_token');  // Fix: Remove 'jwt_token'\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    \n    // Optionally, check that these items are removed:\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n  \n    // Navigate to login page\n    this.router.navigate(['/login']);\n  }\n}", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta http-equiv=\"Cache-Control\" content=\"no-store, no-cache, must-revalidate, max-age=0\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n    <title>Dashboard - Transactions</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n  </head>\n\n  <body>\n    <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\" href=\"adminDash\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/transactions\">\n                  <span data-feather=\"dollar-sign\"></span>\n                  Gestion des transactions\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actions\">\n                  <span data-feather=\"trending-up\"></span>\n                  Gestion des actions\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\">\n                  <span data-feather=\"users\"></span>\n                  Gestion des actionnaires\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Transactions</h1>\n          </div>\n\n          <!-- Upload Excel Section -->\n          <div class=\"card mb-4\">\n            <div class=\"card-body\">\n              <h5 class=\"card-title\">Importer un fichier Excel</h5>\n              <div class=\"form-group\">\n                <input type=\"file\" class=\"form-control-file\" id=\"excelFile\" (change)=\"onFileSelected($event)\">\n              </div>\n              <button class=\"btn btn-primary mt-2\" (click)=\"uploadExcel()\">Envoyer</button>\n            </div>\n          </div>\n\n          <!-- Chart -->\n          <canvas class=\"my-4\" #myChart id=\"myChart\" width=\"900\" height=\"380\" style=\"background-color: lightgrey;\"></canvas>\n        </main>\n      </div>\n    </div>\n\n    <!-- JavaScript -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n  </body>\n</html>\n"], "mappings": ";;;;AAWA,OAAM,MAAOA,qBAAqB;EAGhCC,YACUC,WAAkC,EAClCC,MAAc,EACdC,IAAgB;IAFhB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;EACX;EACHC,eAAeA,CAAA;IACb,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EAEAC,cAAcA,CAACC,KAAU;IACvB,IAAI,CAACC,YAAY,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EAC3C;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACH,YAAY,EAAE;MACtBI,KAAK,CAAC,yCAAyC,CAAC;MAChD;;IAGF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,IAAI,CAACP,YAAY,CAAC;IAE1C,IAAI,CAACL,IAAI,CAACa,IAAI,CAAC,gDAAgD,EAAEH,QAAQ,EAAE;MACzEI,YAAY,EAAE;KACf,CAAC,CAACC,SAAS,CAAC;MACXC,IAAI,EAAGC,QAAQ,IAAI;QACjBR,KAAK,CAAC,+BAA+B,GAAGQ,QAAQ,CAAC;MACnD,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfT,KAAK,CAAC,sCAAsC,GAAGS,KAAK,CAACA,KAAK,CAAC;MAC7D;KACD,CAAC;EACJ;EACAC,MAAMA,CAAA;IACJ;IACAC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE;IACvCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAEhC;IACAC,OAAO,CAACC,GAAG,CAACH,YAAY,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAEhD;IACA,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBA/CW7B,qBAAqB,EAAA8B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAArBrC,qBAAqB;MAAAsC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVlCd,EAAA,CAAAgB,cAAA,cAAgB;UAEZhB,EAAA,CAAAiB,SAAA,cAA0F;UAW1FjB,EAAA,CAAAgB,cAAA,aAAO;UAAAhB,EAAA,CAAAkB,MAAA,gCAAwB;UAAAlB,EAAA,CAAAmB,YAAA,EAAQ;UAEvCnB,EAAA,CAAAiB,SAAA,gBAAmF;UACrFjB,EAAA,CAAAmB,YAAA,EAAO;UAEPnB,EAAA,CAAAgB,cAAA,YAAM;UAGAhB,EAAA,CAAAiB,SAAA,eAAwE;UACxEjB,EAAA,CAAAkB,MAAA,aACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACJnB,EAAA,CAAAgB,cAAA,cAA4B;UAEJhB,EAAA,CAAAoB,UAAA,mBAAAC,mDAAA;YAAA,OAASN,GAAA,CAAAtB,MAAA,EAAQ;UAAA,EAAC;UAACO,EAAA,CAAAkB,MAAA,gBAAQ;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAKzDnB,EAAA,CAAAgB,cAAA,eAA6B;UAOfhB,EAAA,CAAAiB,SAAA,gBAAiC;UACjCjB,EAAA,CAAAkB,MAAA,mBAAU;UAAAlB,EAAA,CAAAgB,cAAA,gBAAsB;UAAAhB,EAAA,CAAAkB,MAAA,iBAAS;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAGpDnB,EAAA,CAAAgB,cAAA,cAAqB;UAEjBhB,EAAA,CAAAiB,SAAA,gBAAiC;UACjCjB,EAAA,CAAAkB,MAAA,kCACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAENnB,EAAA,CAAAgB,cAAA,cAAqB;UAEjBhB,EAAA,CAAAiB,SAAA,gBAAiC;UACjCjB,EAAA,CAAAkB,MAAA,6BACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAENnB,EAAA,CAAAgB,cAAA,cAAqB;UAEjBhB,EAAA,CAAAiB,SAAA,gBAAwC;UACxCjB,EAAA,CAAAkB,MAAA,kCACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAENnB,EAAA,CAAAgB,cAAA,cAAqB;UAEjBhB,EAAA,CAAAiB,SAAA,gBAAwC;UACxCjB,EAAA,CAAAkB,MAAA,6BACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAENnB,EAAA,CAAAgB,cAAA,cAAqB;UAEjBhB,EAAA,CAAAiB,SAAA,gBAAkC;UAClCjB,EAAA,CAAAkB,MAAA,kCACF;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UAMZnB,EAAA,CAAAgB,cAAA,gBAAkE;UAE/ChB,EAAA,CAAAkB,MAAA,oBAAY;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UAIlCnB,EAAA,CAAAgB,cAAA,eAAuB;UAEIhB,EAAA,CAAAkB,MAAA,iCAAyB;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACrDnB,EAAA,CAAAgB,cAAA,eAAwB;UACsChB,EAAA,CAAAoB,UAAA,oBAAAE,wDAAAC,MAAA;YAAA,OAAUR,GAAA,CAAAtC,cAAA,CAAA8C,MAAA,CAAsB;UAAA,EAAC;UAA7FvB,EAAA,CAAAmB,YAAA,EAA8F;UAEhGnB,EAAA,CAAAgB,cAAA,kBAA6D;UAAxBhB,EAAA,CAAAoB,UAAA,mBAAAI,wDAAA;YAAA,OAAST,GAAA,CAAAjC,WAAA,EAAa;UAAA,EAAC;UAACkB,EAAA,CAAAkB,MAAA,eAAO;UAAAlB,EAAA,CAAAmB,YAAA,EAAS;UAKjFnB,EAAA,CAAAiB,SAAA,sBAAkH;UACpHjB,EAAA,CAAAmB,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}