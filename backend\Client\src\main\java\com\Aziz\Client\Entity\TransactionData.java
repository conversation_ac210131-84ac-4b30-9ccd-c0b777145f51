package com.Aziz.Client.Entity;

import jakarta.persistence.Id;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;


@Getter
@Setter
public class TransactionData {
    @Id
    private Long idTransaction;
    private String nomActionnaire;
    private String prenomActionnaire;
    private String ISINAction;
    private Double prixUnitaire;
    private String type;
    private Integer quantite;
    private Long idActionnaire;
    private Double montatnt;
    private String observations;


    @CreationTimestamp
    private LocalDateTime DateCreation;

    private LocalDateTime DateTransaction;


}
