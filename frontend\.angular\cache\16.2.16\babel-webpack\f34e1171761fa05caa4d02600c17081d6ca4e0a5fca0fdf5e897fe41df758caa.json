{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ResponsableService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth/Users_Responsables'; // Your API URL\n  }\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId) {\n    return this.http.get(`${this.apiUrl}/${responsableId}`); // Fetching a single user\n  }\n  // Get all responsibles\n  getResponsables() {\n    const headers = new HttpHeaders().set('Authorization', `Bearer ${localStorage.getItem('token')}`);\n    return this.http.get(this.apiUrl, {\n      headers\n    });\n  }\n  // Delete a responsable\n  deleteResponsable(id) {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, {\n      responseType: 'text'\n    }) // Force response as text\n    .pipe(catchError(error => {\n      console.error('Delete error:', error);\n      return throwError(error); // Re-throw the error so we can handle it in the component\n    }));\n  }\n  // Update a responsable\n  updateResponsable(id, responsable) {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.error(\"No token found!\");\n      return throwError(() => new Error(\"No token found\"));\n    }\n    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);\n    console.log(\"Sending request with headers:\", headers);\n    return this.http.put(`${this.apiUrl}/edit/${id}`, responsable, {\n      headers\n    });\n  }\n  addResponsable(responsable) {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.error(\"No token found!\");\n      return throwError(() => new Error(\"No token found\"));\n    }\n    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);\n    console.log(\"Sending request with headers:\", headers);\n    // Update the URL to match the new backend path\n    return this.http.post('http://localhost:8000/api/v1/auth/Users_Responsables/ajout', responsable, {\n      headers\n    }).pipe(catchError(error => {\n      console.error('Add responsable error:', error);\n      return throwError(error); // Re-throw the error to handle it in the component\n    }));\n  }\n\n  static {\n    this.ɵfac = function ResponsableService_Factory(t) {\n      return new (t || ResponsableService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ResponsableService,\n      factory: ResponsableService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "catchError", "throwError", "ResponsableService", "constructor", "http", "apiUrl", "getResponsableById", "responsableId", "get", "getResponsables", "headers", "set", "localStorage", "getItem", "deleteResponsable", "id", "delete", "responseType", "pipe", "error", "console", "updateResponsable", "responsable", "token", "Error", "log", "put", "addResponsable", "post", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\responsable.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { catchError, Observable, throwError } from 'rxjs';\nimport { User } from './model/user.model'; // Import the User model\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ResponsableService {\n  private apiUrl = 'http://localhost:8000/api/v1/auth/Users_Responsables'; // Your API URL\n\n  constructor(private http: HttpClient) {}\n\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId: number): Observable<User> {\n    return this.http.get<User>(`${this.apiUrl}/${responsableId}`); // Fetching a single user\n  }\n  \n  // Get all responsibles\n  getResponsables(): Observable<User[]> {\n    const headers = new HttpHeaders().set(\n      'Authorization',\n      `Bearer ${localStorage.getItem('token')}`\n    );\n\n    return this.http.get<User[]>(this.apiUrl, { headers });\n  }\n\n  // Delete a responsable\n  deleteResponsable(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, { responseType: 'text' })  // Force response as text\n      .pipe(\n        catchError((error) => {\n          console.error('Delete error:', error);\n          return throwError(error); // Re-throw the error so we can handle it in the component\n        })\n      );\n  }\n\n\n  \n  \n\n  // Update a responsable\n  updateResponsable(id: number, responsable: User): Observable<any> {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.error(\"No token found!\");\n      return throwError(() => new Error(\"No token found\"));\n    }\n  \n    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);\n    console.log(\"Sending request with headers:\", headers);\n  \n    return this.http.put(`${this.apiUrl}/edit/${id}`, responsable, { headers });\n  }\n\n\n  \n  addResponsable(responsable: User): Observable<User> {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.error(\"No token found!\");\n      return throwError(() => new Error(\"No token found\"));\n    }\n\n    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);\n    console.log(\"Sending request with headers:\", headers);\n\n    // Update the URL to match the new backend path\n    return this.http.post<User>('http://localhost:8000/api/v1/auth/Users_Responsables/ajout', responsable, { headers }).pipe(\n      catchError((error) => {\n        console.error('Add responsable error:', error);\n        return throwError(error); // Re-throw the error to handle it in the component\n      })\n    );\n}\n\n\n}\n  \n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAcC,UAAU,QAAQ,MAAM;;;AAMzD,OAAM,MAAOC,kBAAkB;EAG7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,sDAAsD,CAAC,CAAC;EAElC;EAEvC;EACAC,kBAAkBA,CAACC,aAAqB;IACtC,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAO,GAAG,IAAI,CAACH,MAAM,IAAIE,aAAa,EAAE,CAAC,CAAC,CAAC;EACjE;EAEA;EACAE,eAAeA,CAAA;IACb,MAAMC,OAAO,GAAG,IAAIX,WAAW,EAAE,CAACY,GAAG,CACnC,eAAe,EACf,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE,CAC1C;IAED,OAAO,IAAI,CAACT,IAAI,CAACI,GAAG,CAAS,IAAI,CAACH,MAAM,EAAE;MAAEK;IAAO,CAAE,CAAC;EACxD;EAEA;EACAI,iBAAiBA,CAACC,EAAU;IAC1B,OAAO,IAAI,CAACX,IAAI,CAACY,MAAM,CAAC,GAAG,IAAI,CAACX,MAAM,WAAWU,EAAE,EAAE,EAAE;MAAEE,YAAY,EAAE;IAAM,CAAE,CAAC,CAAE;IAAA,CAC/EC,IAAI,CACHlB,UAAU,CAAEmB,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAOlB,UAAU,CAACkB,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CACH;EACL;EAMA;EACAE,iBAAiBA,CAACN,EAAU,EAAEO,WAAiB;IAC7C,MAAMC,KAAK,GAAGX,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACU,KAAK,EAAE;MACVH,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAOlB,UAAU,CAAC,MAAM,IAAIuB,KAAK,CAAC,gBAAgB,CAAC,CAAC;;IAGtD,MAAMd,OAAO,GAAG,IAAIX,WAAW,EAAE,CAACY,GAAG,CAAC,eAAe,EAAE,UAAUY,KAAK,EAAE,CAAC;IACzEH,OAAO,CAACK,GAAG,CAAC,+BAA+B,EAAEf,OAAO,CAAC;IAErD,OAAO,IAAI,CAACN,IAAI,CAACsB,GAAG,CAAC,GAAG,IAAI,CAACrB,MAAM,SAASU,EAAE,EAAE,EAAEO,WAAW,EAAE;MAAEZ;IAAO,CAAE,CAAC;EAC7E;EAIAiB,cAAcA,CAACL,WAAiB;IAC9B,MAAMC,KAAK,GAAGX,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACU,KAAK,EAAE;MACVH,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAOlB,UAAU,CAAC,MAAM,IAAIuB,KAAK,CAAC,gBAAgB,CAAC,CAAC;;IAGtD,MAAMd,OAAO,GAAG,IAAIX,WAAW,EAAE,CAACY,GAAG,CAAC,eAAe,EAAE,UAAUY,KAAK,EAAE,CAAC;IACzEH,OAAO,CAACK,GAAG,CAAC,+BAA+B,EAAEf,OAAO,CAAC;IAErD;IACA,OAAO,IAAI,CAACN,IAAI,CAACwB,IAAI,CAAO,4DAA4D,EAAEN,WAAW,EAAE;MAAEZ;IAAO,CAAE,CAAC,CAACQ,IAAI,CACtHlB,UAAU,CAAEmB,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOlB,UAAU,CAACkB,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CACH;EACL;;;;uBApEajB,kBAAkB,EAAA2B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlB9B,kBAAkB;MAAA+B,OAAA,EAAlB/B,kBAAkB,CAAAgC,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}