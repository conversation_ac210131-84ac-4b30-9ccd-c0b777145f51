{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction AddResponsableComponent_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r1.idGroupe);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(group_r1.nomGroupe);\n  }\n}\nexport class AddResponsableComponent {\n  constructor(responsableService) {\n    this.responsableService = responsableService;\n    this.newResponsable = {\n      id: 0,\n      nom: '',\n      prenom: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      groupe: {\n        idGroupe: 0,\n        nomGroupe: ''\n      } // Initialize with empty group\n    };\n\n    this.groups = [];\n  }\n  ngOnInit() {\n    // Fetch the list of groups\n    this.responsableService.getGroups().subscribe(data => {\n      this.groups = data; // Store the groups to bind to the dropdown\n    });\n  }\n  // Method to add the new responsable\n  addResponsable() {\n    // Ensure the groupeId is a number\n    this.newResponsable.groupe.idGroupe = Number(this.newResponsable.groupe.idGroupe);\n    // Check if the group ID is a valid number\n    if (this.newResponsable.groupe.idGroupe === 0) {\n      alert('Please select a valid group');\n      return;\n    }\n    console.log('Adding Responsable:', this.newResponsable); // Check what is being sent\n    this.responsableService.addResponsable(this.newResponsable).subscribe(response => {\n      console.log('Responsable added successfully:', response);\n    }, error => {\n      console.error('Error adding responsable:', error);\n    });\n  }\n  static {\n    this.ɵfac = function AddResponsableComponent_Factory(t) {\n      return new (t || AddResponsableComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddResponsableComponent,\n      selectors: [[\"app-add-responsable\"]],\n      decls: 27,\n      vars: 6,\n      consts: [[1, \"container\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"nom\"], [\"type\", \"text\", \"id\", \"nom\", \"name\", \"nom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"prenom\"], [\"type\", \"text\", \"id\", \"prenom\", \"name\", \"prenom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"telephone\"], [\"type\", \"text\", \"id\", \"telephone\", \"name\", \"telephone\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"groupe\"], [\"id\", \"groupe\", \"name\", \"groupe\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\"], [3, \"value\"]],\n      template: function AddResponsableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Add New Responsable\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"form\", 1);\n          i0.ɵɵlistener(\"ngSubmit\", function AddResponsableComponent_Template_form_ngSubmit_3_listener() {\n            return ctx.addResponsable();\n          });\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"input\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_7_listener($event) {\n            return ctx.newResponsable.nom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 2)(9, \"label\", 5);\n          i0.ɵɵtext(10, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"input\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.newResponsable.prenom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 2)(13, \"label\", 7);\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 8);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.newResponsable.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 2)(17, \"label\", 9);\n          i0.ɵɵtext(18, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"input\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_19_listener($event) {\n            return ctx.newResponsable.telephone = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 2)(21, \"label\", 11);\n          i0.ɵɵtext(22, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"select\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_select_ngModelChange_23_listener($event) {\n            return ctx.newResponsable.groupe.idGroupe = $event;\n          });\n          i0.ɵɵtemplate(24, AddResponsableComponent_option_24_Template, 2, 2, \"option\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"button\", 14);\n          i0.ɵɵtext(26, \"Add Responsable\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.nom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.prenom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.telephone);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.groupe.idGroupe);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groups);\n        }\n      },\n      dependencies: [i2.NgForOf, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "group_r1", "idGroupe", "ɵɵadvance", "ɵɵtextInterpolate", "nomGroupe", "AddResponsableComponent", "constructor", "responsableService", "newResponsable", "id", "nom", "prenom", "email", "telephone", "role", "groupe", "groups", "ngOnInit", "getGroups", "subscribe", "data", "addResponsable", "Number", "alert", "console", "log", "response", "error", "ɵɵdirectiveInject", "i1", "ResponsableService", "selectors", "decls", "vars", "consts", "template", "AddResponsableComponent_Template", "rf", "ctx", "ɵɵlistener", "AddResponsableComponent_Template_form_ngSubmit_3_listener", "AddResponsableComponent_Template_input_ngModelChange_7_listener", "$event", "AddResponsableComponent_Template_input_ngModelChange_11_listener", "AddResponsableComponent_Template_input_ngModelChange_15_listener", "AddResponsableComponent_Template_input_ngModelChange_19_listener", "AddResponsableComponent_Template_select_ngModelChange_23_listener", "ɵɵtemplate", "AddResponsableComponent_option_24_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport { Groupe } from '../model/groupe.model';\n\n@Component({\n  selector: 'app-add-responsable',\n  templateUrl: './add-responsable.component.html',\n  styleUrls: ['./add-responsable.component.css']\n})\nexport class AddResponsableComponent implements OnInit {\n  newResponsable: User = {\n    id: 0,\n    nom: '',\n    prenom: '',\n    email: '',\n    telephone: '',\n    role: 'RESPONSABLE',\n    groupe: { idGroupe: 0, nomGroupe: '' }  // Initialize with empty group\n  };\n\n  groups: Groupe[] = [];\nconsole: any;\n\n  constructor(private responsableService: ResponsableService) {}\n\n  ngOnInit(): void {\n    // Fetch the list of groups\n    this.responsableService.getGroups().subscribe((data) => {\n      this.groups = data;  // Store the groups to bind to the dropdown\n    });\n  }\n\n  // Method to add the new responsable\n  addResponsable(): void {\n    // Ensure the groupeId is a number\n    this.newResponsable.groupe.idGroupe = Number(this.newResponsable.groupe.idGroupe);\n    \n    // Check if the group ID is a valid number\n    if (this.newResponsable.groupe.idGroupe === 0) {\n      alert('Please select a valid group');\n      return;\n    }\n  \n    console.log('Adding Responsable:', this.newResponsable);  // Check what is being sent\n    this.responsableService.addResponsable(this.newResponsable).subscribe(\n      (response) => {\n        console.log('Responsable added successfully:', response);\n      },\n      (error) => {\n        console.error('Error adding responsable:', error);\n      }\n    );\n  }\n  \n  \n}\n", "<div class=\"container\">\n    <h2>Add New Responsable</h2>\n    <form (ngSubmit)=\"addResponsable()\">\n      <div class=\"form-group\">\n        <label for=\"nom\">Nom</label>\n        <input type=\"text\" class=\"form-control\" id=\"nom\" [(ngModel)]=\"newResponsable.nom\" name=\"nom\" required />\n      </div>\n      <div class=\"form-group\">\n        <label for=\"prenom\">Prénom</label>\n        <input type=\"text\" class=\"form-control\" id=\"prenom\" [(ngModel)]=\"newResponsable.prenom\" name=\"prenom\" required />\n      </div>\n      <div class=\"form-group\">\n        <label for=\"email\">Email</label>\n        <input type=\"email\" class=\"form-control\" id=\"email\" [(ngModel)]=\"newResponsable.email\" name=\"email\" required />\n      </div>\n      <div class=\"form-group\">\n        <label for=\"telephone\">Téléphone</label>\n        <input type=\"text\" class=\"form-control\" id=\"telephone\" [(ngModel)]=\"newResponsable.telephone\" name=\"telephone\" required />\n      </div>\n  \n      <!-- Dropdown for selecting the group -->\n      <div class=\"form-group\">\n        <label for=\"groupe\">Groupe</label>\n        <select id=\"groupe\" class=\"form-control\" [(ngModel)]=\"newResponsable.groupe.idGroupe\" name=\"groupe\" required>\n          <option *ngFor=\"let group of groups\" [value]=\"group.idGroupe\">{{ group.nomGroupe }}</option>\n        </select>\n        \n        \n      </div>\n      \n      \n  \n      <button type=\"submit\" class=\"btn btn-success\">Add Responsable</button>\n    </form>\n  </div>\n  "], "mappings": ";;;;;;ICwBUA,EAAA,CAAAC,cAAA,iBAA8D;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAvDH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAAC,QAAA,CAAwB;IAACN,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,iBAAA,CAAAH,QAAA,CAAAI,SAAA,CAAqB;;;ADd7F,OAAM,MAAOC,uBAAuB;EAclCC,YAAoBC,kBAAsC;IAAtC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAbtC,KAAAC,cAAc,GAAS;MACrBC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa;MACnBC,MAAM,EAAE;QAAEd,QAAQ,EAAE,CAAC;QAAEG,SAAS,EAAE;MAAE,CAAE,CAAE;KACzC;;IAED,KAAAY,MAAM,GAAa,EAAE;EAGwC;EAE7DC,QAAQA,CAAA;IACN;IACA,IAAI,CAACV,kBAAkB,CAACW,SAAS,EAAE,CAACC,SAAS,CAAEC,IAAI,IAAI;MACrD,IAAI,CAACJ,MAAM,GAAGI,IAAI,CAAC,CAAE;IACvB,CAAC,CAAC;EACJ;EAEA;EACAC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACb,cAAc,CAACO,MAAM,CAACd,QAAQ,GAAGqB,MAAM,CAAC,IAAI,CAACd,cAAc,CAACO,MAAM,CAACd,QAAQ,CAAC;IAEjF;IACA,IAAI,IAAI,CAACO,cAAc,CAACO,MAAM,CAACd,QAAQ,KAAK,CAAC,EAAE;MAC7CsB,KAAK,CAAC,6BAA6B,CAAC;MACpC;;IAGFC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACjB,cAAc,CAAC,CAAC,CAAE;IAC1D,IAAI,CAACD,kBAAkB,CAACc,cAAc,CAAC,IAAI,CAACb,cAAc,CAAC,CAACW,SAAS,CAClEO,QAAQ,IAAI;MACXF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAAC;IAC1D,CAAC,EACAC,KAAK,IAAI;MACRH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,CACF;EACH;;;uBA3CWtB,uBAAuB,EAAAV,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAvBzB,uBAAuB;MAAA0B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVpC1C,EAAA,CAAAC,cAAA,aAAuB;UACfD,EAAA,CAAAE,MAAA,0BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,cAAoC;UAA9BD,EAAA,CAAA4C,UAAA,sBAAAC,0DAAA;YAAA,OAAYF,GAAA,CAAAjB,cAAA,EAAgB;UAAA,EAAC;UACjC1B,EAAA,CAAAC,cAAA,aAAwB;UACLD,EAAA,CAAAE,MAAA,UAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5BH,EAAA,CAAAC,cAAA,eAAwG;UAAvDD,EAAA,CAAA4C,UAAA,2BAAAE,gEAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAA9B,cAAA,CAAAE,GAAA,GAAAgC,MAAA;UAAA,EAAgC;UAAjF/C,EAAA,CAAAG,YAAA,EAAwG;UAE1GH,EAAA,CAAAC,cAAA,aAAwB;UACFD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,gBAAiH;UAA7DD,EAAA,CAAA4C,UAAA,2BAAAI,iEAAAD,MAAA;YAAA,OAAAJ,GAAA,CAAA9B,cAAA,CAAAG,MAAA,GAAA+B,MAAA;UAAA,EAAmC;UAAvF/C,EAAA,CAAAG,YAAA,EAAiH;UAEnHH,EAAA,CAAAC,cAAA,cAAwB;UACHD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,gBAA+G;UAA3DD,EAAA,CAAA4C,UAAA,2BAAAK,iEAAAF,MAAA;YAAA,OAAAJ,GAAA,CAAA9B,cAAA,CAAAI,KAAA,GAAA8B,MAAA;UAAA,EAAkC;UAAtF/C,EAAA,CAAAG,YAAA,EAA+G;UAEjHH,EAAA,CAAAC,cAAA,cAAwB;UACCD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,iBAA0H;UAAnED,EAAA,CAAA4C,UAAA,2BAAAM,iEAAAH,MAAA;YAAA,OAAAJ,GAAA,CAAA9B,cAAA,CAAAK,SAAA,GAAA6B,MAAA;UAAA,EAAsC;UAA7F/C,EAAA,CAAAG,YAAA,EAA0H;UAI5HH,EAAA,CAAAC,cAAA,cAAwB;UACFD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,kBAA6G;UAApED,EAAA,CAAA4C,UAAA,2BAAAO,kEAAAJ,MAAA;YAAA,OAAAJ,GAAA,CAAA9B,cAAA,CAAAO,MAAA,CAAAd,QAAA,GAAAyC,MAAA;UAAA,EAA4C;UACnF/C,EAAA,CAAAoD,UAAA,KAAAC,0CAAA,qBAA4F;UAC9FrD,EAAA,CAAAG,YAAA,EAAS;UAOXH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UA3BnBH,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAI,UAAA,YAAAuC,GAAA,CAAA9B,cAAA,CAAAE,GAAA,CAAgC;UAI7Bf,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAI,UAAA,YAAAuC,GAAA,CAAA9B,cAAA,CAAAG,MAAA,CAAmC;UAInChB,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAI,UAAA,YAAAuC,GAAA,CAAA9B,cAAA,CAAAI,KAAA,CAAkC;UAI/BjB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAI,UAAA,YAAAuC,GAAA,CAAA9B,cAAA,CAAAK,SAAA,CAAsC;UAMpDlB,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAI,UAAA,YAAAuC,GAAA,CAAA9B,cAAA,CAAAO,MAAA,CAAAd,QAAA,CAA4C;UACzDN,EAAA,CAAAO,SAAA,GAAS;UAATP,EAAA,CAAAI,UAAA,YAAAuC,GAAA,CAAAtB,MAAA,CAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}