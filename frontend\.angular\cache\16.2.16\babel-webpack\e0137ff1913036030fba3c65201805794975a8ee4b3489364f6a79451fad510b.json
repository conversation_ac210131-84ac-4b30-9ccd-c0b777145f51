{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction AddResponsableComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtext(1, \" Nom is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtext(1, \" Pr\\u00E9nom is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtext(1, \" A valid email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_47_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_47_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone must be a number and up to 8 digits.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, AddResponsableComponent_div_47_div_1_Template, 2, 0, \"div\", 40);\n    i0.ɵɵtemplate(2, AddResponsableComponent_div_47_div_2_Template, 2, 0, \"div\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r7 = i0.ɵɵreference(46);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r7.errors == null ? null : _r7.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r7.errors == null ? null : _r7.errors[\"pattern\"]);\n  }\n}\nfunction AddResponsableComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtext(1, \" Mot de passe is required (minimum 8 characters). \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_option_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r16.idGroupe);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", group_r16.nomGroupe, \" \");\n  }\n}\nfunction AddResponsableComponent_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtext(1, \" Groupe is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class AddResponsableComponent {\n  constructor(responsableService, router) {\n    this.responsableService = responsableService;\n    this.router = router;\n    this.newResponsable = {\n      id: 0,\n      firstName: '',\n      lastName: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupeId: 0 // Use groupeId instead of groupe object\n    };\n\n    this.groups = [];\n  }\n  ngOnInit() {\n    // Fetch the list of groups\n    this.responsableService.getGroups().subscribe(data => {\n      this.groups = data; // Store the groups to bind to the dropdown\n    });\n  }\n  // Method to add the new responsable\n  addResponsable() {\n    if (!this.newResponsable.groupeId) {\n      alert('Please select a valid group');\n      return;\n    }\n    if (!this.newResponsable.password) {\n      alert('Please enter a password');\n      return;\n    }\n    // Log the object to verify\n    console.log('Adding Responsable:', this.newResponsable);\n    // Send the request to the backend\n    this.responsableService.addResponsable(this.newResponsable).subscribe(response => {\n      console.log('Responsable added successfully:', response);\n      // Reset the form after successful creation\n      this.newResponsable = {\n        firstName: '',\n        lastName: '',\n        email: '',\n        telephone: '',\n        role: 'RESPONSABLE',\n        password: '',\n        groupeId: 0 // Reset selected group\n      };\n\n      this.router.navigate(['/users']);\n    }, error => {\n      console.error('Error adding responsable:', error);\n    });\n  }\n  cancelEdit() {\n    // Reset form data (optional, if you want to clear the form fields on cancel)\n    this.newResponsable = {\n      id: 0,\n      firstName: '',\n      lastName: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupeId: 0\n    };\n    // Navigate to the users list or a different page (replace '/users' with your desired route)\n    this.router.navigateByUrl('/users');\n  }\n  static {\n    this.ɵfac = function AddResponsableComponent_Factory(t) {\n      return new (t || AddResponsableComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddResponsableComponent,\n      selectors: [[\"app-add-responsable\"]],\n      decls: 77,\n      vars: 14,\n      consts: [[1, \"container\", \"mt-5\"], [1, \"form-container\", \"mx-auto\", 2, \"max-width\", \"900px\"], [1, \"text-center\", \"mb-4\"], [1, \"form-group\", 3, \"ngSubmit\"], [\"responsableForm\", \"ngForm\"], [1, \"form-row\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"nom\"], [1, \"input-group\"], [1, \"input-group-prepend\"], [1, \"input-group-text\"], [1, \"fa\", \"fa-user\"], [\"type\", \"text\", \"id\", \"nom\", \"name\", \"nom\", \"required\", \"\", \"placeholder\", \"Entrez le nom\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"nom\", \"ngModel\"], [\"class\", \"text-danger small\", 4, \"ngIf\"], [\"for\", \"prenom\"], [\"type\", \"text\", \"id\", \"prenom\", \"name\", \"prenom\", \"required\", \"\", \"placeholder\", \"Entrez le pr\\u00E9nom\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"prenom\", \"ngModel\"], [\"for\", \"email\"], [1, \"fa\", \"fa-envelope\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"Entrez l'email\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"email\", \"ngModel\"], [\"for\", \"telephone\"], [1, \"fa\", \"fa-phone\"], [\"type\", \"tel\", \"id\", \"telephone\", \"name\", \"telephone\", \"required\", \"\", \"pattern\", \"^[0-9]{1,8}$\", \"placeholder\", \"Entrez le t\\u00E9l\\u00E9phone\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"telephone\", \"ngModel\"], [\"for\", \"password\"], [1, \"fa\", \"fa-lock\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"minlength\", \"8\", \"placeholder\", \"Entrez un mot de passe\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"password\", \"ngModel\"], [\"for\", \"groupe\"], [1, \"fa\", \"fa-users\"], [\"id\", \"groupe\", \"name\", \"groupeId\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"groupe\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-row\", \"mt-4\"], [1, \"col-md-12\", \"text-center\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"ml-3\", 3, \"click\"], [1, \"text-danger\", \"small\"], [4, \"ngIf\"], [3, \"value\"]],\n      template: function AddResponsableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n          i0.ɵɵtext(3, \"Ajouter un Nouvel Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"form\", 3, 4);\n          i0.ɵɵlistener(\"ngSubmit\", function AddResponsableComponent_Template_form_ngSubmit_4_listener() {\n            return ctx.addResponsable();\n          });\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"label\", 7);\n          i0.ɵɵtext(9, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"span\", 10);\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"input\", 12, 13);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_14_listener($event) {\n            return ctx.newResponsable.firstName = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(16, AddResponsableComponent_div_16_Template, 2, 0, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 6)(18, \"label\", 15);\n          i0.ɵɵtext(19, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 8)(21, \"div\", 9)(22, \"span\", 10);\n          i0.ɵɵelement(23, \"i\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"input\", 16, 17);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_24_listener($event) {\n            return ctx.newResponsable.lastName = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(26, AddResponsableComponent_div_26_Template, 2, 0, \"div\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 5)(28, \"div\", 6)(29, \"label\", 18);\n          i0.ɵɵtext(30, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 8)(32, \"div\", 9)(33, \"span\", 10);\n          i0.ɵɵelement(34, \"i\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"input\", 20, 21);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_35_listener($event) {\n            return ctx.newResponsable.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(37, AddResponsableComponent_div_37_Template, 2, 0, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 6)(39, \"label\", 22);\n          i0.ɵɵtext(40, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 8)(42, \"div\", 9)(43, \"span\", 10);\n          i0.ɵɵelement(44, \"i\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"input\", 24, 25);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_45_listener($event) {\n            return ctx.newResponsable.telephone = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(47, AddResponsableComponent_div_47_Template, 3, 2, \"div\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 5)(49, \"div\", 6)(50, \"label\", 26);\n          i0.ɵɵtext(51, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\", 8)(53, \"div\", 9)(54, \"span\", 10);\n          i0.ɵɵelement(55, \"i\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"input\", 28, 29);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_56_listener($event) {\n            return ctx.newResponsable.password = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(58, AddResponsableComponent_div_58_Template, 2, 0, \"div\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"div\", 5)(60, \"div\", 6)(61, \"label\", 30);\n          i0.ɵɵtext(62, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 8)(64, \"div\", 9)(65, \"span\", 10);\n          i0.ɵɵelement(66, \"i\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"select\", 32, 33);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_select_ngModelChange_67_listener($event) {\n            return ctx.newResponsable.groupeId = $event;\n          });\n          i0.ɵɵtemplate(69, AddResponsableComponent_option_69_Template, 2, 2, \"option\", 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(70, AddResponsableComponent_div_70_Template, 2, 0, \"div\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 35)(72, \"div\", 36)(73, \"button\", 37);\n          i0.ɵɵtext(74, \" Ajouter \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function AddResponsableComponent_Template_button_click_75_listener() {\n            return ctx.cancelEdit();\n          });\n          i0.ɵɵtext(76, \" Annuler \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(5);\n          const _r1 = i0.ɵɵreference(15);\n          const _r3 = i0.ɵɵreference(25);\n          const _r5 = i0.ɵɵreference(36);\n          const _r7 = i0.ɵɵreference(46);\n          const _r9 = i0.ɵɵreference(57);\n          const _r11 = i0.ɵɵreference(68);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.firstName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r1.invalid && _r1.touched);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.lastName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r3.invalid && _r3.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r5.invalid && _r5.touched);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.telephone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r7.invalid && _r7.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r9.invalid && _r9.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.groupeId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groups);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r11.invalid && _r11.touched);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", _r0.invalid);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.ɵNgNoValidate, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.DefaultValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.MinLengthValidator, i4.PatternValidator, i4.EmailValidator, i4.NgModel, i4.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "AddResponsableComponent_div_47_div_1_Template", "AddResponsableComponent_div_47_div_2_Template", "ɵɵadvance", "ɵɵproperty", "_r7", "errors", "group_r16", "idGroupe", "ɵɵtextInterpolate1", "nomGroupe", "AddResponsableComponent", "constructor", "responsableService", "router", "newResponsable", "id", "firstName", "lastName", "email", "telephone", "role", "password", "groupeId", "groups", "ngOnInit", "getGroups", "subscribe", "data", "addResponsable", "alert", "console", "log", "response", "navigate", "error", "cancelEdit", "navigateByUrl", "ɵɵdirectiveInject", "i1", "ResponsableService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AddResponsableComponent_Template", "rf", "ctx", "ɵɵlistener", "AddResponsableComponent_Template_form_ngSubmit_4_listener", "ɵɵelement", "AddResponsableComponent_Template_input_ngModelChange_14_listener", "$event", "AddResponsableComponent_div_16_Template", "AddResponsableComponent_Template_input_ngModelChange_24_listener", "AddResponsableComponent_div_26_Template", "AddResponsableComponent_Template_input_ngModelChange_35_listener", "AddResponsableComponent_div_37_Template", "AddResponsableComponent_Template_input_ngModelChange_45_listener", "AddResponsableComponent_div_47_Template", "AddResponsableComponent_Template_input_ngModelChange_56_listener", "AddResponsableComponent_div_58_Template", "AddResponsableComponent_Template_select_ngModelChange_67_listener", "AddResponsableComponent_option_69_Template", "AddResponsableComponent_div_70_Template", "AddResponsableComponent_Template_button_click_75_listener", "_r1", "invalid", "touched", "_r3", "_r5", "_r9", "_r11", "_r0"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { Responsable } from '../model/respons.model';  // Import Responsable model\nimport { Groupe } from '../model/groupe.model';\nimport { Router } from '@angular/router';  // Import Router\n\n\n@Component({\n  selector: 'app-add-responsable',\n  templateUrl: './add-responsable.component.html',\n  styleUrls: ['./add-responsable.component.css']\n})\nexport class AddResponsableComponent implements OnInit {\n  newResponsable: Responsable = {  \n    id: 0, \n    firstName: '', \n    lastName: '', \n    email: '', \n    telephone: '', \n    role: 'RESPONSABLE',  \n    password: '',\n    groupeId: 0,  // Use groupeId instead of groupe object\n  };\n\n  groups: Groupe[] = [];\n  console: any;\n\n  constructor(private responsableService: ResponsableService ,private router: Router) {}\n\n  ngOnInit(): void {\n    // Fetch the list of groups\n    this.responsableService.getGroups().subscribe((data) => {\n      this.groups = data;  // Store the groups to bind to the dropdown\n    });\n  }\n\n  // Method to add the new responsable\n  addResponsable(): void {\n    if (!this.newResponsable.groupeId) {\n      alert('Please select a valid group');\n      return;\n    }\n\n    if (!this.newResponsable.password) {\n      alert('Please enter a password');\n      return;\n    }\n\n    // Log the object to verify\n    console.log('Adding Responsable:', this.newResponsable);\n\n    // Send the request to the backend\n    this.responsableService.addResponsable(this.newResponsable).subscribe(\n      (response) => {\n        console.log('Responsable added successfully:', response);\n\n        // Reset the form after successful creation\n        this.newResponsable = {\n          firstName: '',\n          lastName: '',\n          email: '',\n          telephone: '',\n          role: 'RESPONSABLE',\n          password: '',\n          groupeId: 0,  // Reset selected group\n        };\n        this.router.navigate(['/users']);\n\n      },\n      (error) => {\n        console.error('Error adding responsable:', error);\n      }\n    );\n  }\n\n  cancelEdit() {\n    // Reset form data (optional, if you want to clear the form fields on cancel)\n    this.newResponsable = {\n      id: 0,\n      firstName: '',\n      lastName: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupeId: 0,\n    };\n\n    // Navigate to the users list or a different page (replace '/users' with your desired route)\n    this.router.navigateByUrl('/users');\n  }\n}\n", "<div class=\"container mt-5\">\n  <div class=\"form-container mx-auto\" style=\"max-width: 900px;\">\n    <h2 class=\"text-center mb-4\">Ajouter un Nouvel Utilisateur</h2>\n    <form (ngSubmit)=\"addResponsable()\" #responsableForm=\"ngForm\" class=\"form-group\">\n\n      <!-- Nom + Prénom -->\n      <div class=\"form-row\">\n        <div class=\"col-md-6 mb-3\">\n          <label for=\"nom\">Nom</label>\n          <div class=\"input-group\">\n            <div class=\"input-group-prepend\">\n              <span class=\"input-group-text\"><i class=\"fa fa-user\"></i></span>\n            </div>\n            <input type=\"text\" class=\"form-control\" id=\"nom\" [(ngModel)]=\"newResponsable.firstName\" name=\"nom\" required #nom=\"ngModel\" placeholder=\"Entrez le nom\" />\n          </div>\n          <div *ngIf=\"nom.invalid && nom.touched\" class=\"text-danger small\">\n            Nom is required.\n          </div>\n        </div>\n\n        <div class=\"col-md-6 mb-3\">\n          <label for=\"prenom\">Prénom</label>\n          <div class=\"input-group\">\n            <div class=\"input-group-prepend\">\n              <span class=\"input-group-text\"><i class=\"fa fa-user\"></i></span>\n            </div>\n            <input type=\"text\" class=\"form-control\" id=\"prenom\" [(ngModel)]=\"newResponsable.lastName\" name=\"prenom\" required #prenom=\"ngModel\" placeholder=\"Entrez le prénom\" />\n          </div>\n          <div *ngIf=\"prenom.invalid && prenom.touched\" class=\"text-danger small\">\n            Prénom is required.\n          </div>\n        </div>\n      </div>\n\n      <!-- Email + Téléphone -->\n      <div class=\"form-row\">\n        <div class=\"col-md-6 mb-3\">\n          <label for=\"email\">Email</label>\n          <div class=\"input-group\">\n            <div class=\"input-group-prepend\">\n              <span class=\"input-group-text\"><i class=\"fa fa-envelope\"></i></span>\n            </div>\n            <input type=\"email\" class=\"form-control\" id=\"email\" [(ngModel)]=\"newResponsable.email\" name=\"email\" required email #email=\"ngModel\" placeholder=\"Entrez l'email\" />\n          </div>\n          <div *ngIf=\"email.invalid && email.touched\" class=\"text-danger small\">\n            A valid email is required.\n          </div>\n        </div>\n\n        <div class=\"col-md-6 mb-3\">\n          <label for=\"telephone\">Téléphone</label>\n          <div class=\"input-group\">\n            <div class=\"input-group-prepend\">\n              <span class=\"input-group-text\"><i class=\"fa fa-phone\"></i></span>\n            </div>\n            <input type=\"tel\" class=\"form-control\" id=\"telephone\" [(ngModel)]=\"newResponsable.telephone\" name=\"telephone\" required #telephone=\"ngModel\" pattern=\"^[0-9]{1,8}$\" placeholder=\"Entrez le téléphone\" />\n          </div>\n          <div *ngIf=\"telephone.invalid && telephone.touched\" class=\"text-danger small\">\n            <div *ngIf=\"telephone.errors?.['required']\">Téléphone is required.</div>\n            <div *ngIf=\"telephone.errors?.['pattern']\">Téléphone must be a number and up to 8 digits.</div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Mot de passe -->\n      <div class=\"form-row\">\n        <div class=\"col-md-6 mb-3\">\n          <label for=\"password\">Mot de passe</label>\n          <div class=\"input-group\">\n            <div class=\"input-group-prepend\">\n              <span class=\"input-group-text\"><i class=\"fa fa-lock\"></i></span>\n            </div>\n            <input type=\"password\" class=\"form-control\" id=\"password\" [(ngModel)]=\"newResponsable.password\" name=\"password\" required minlength=\"8\" #password=\"ngModel\" placeholder=\"Entrez un mot de passe\" />\n          </div>\n          <div *ngIf=\"password.invalid && password.touched\" class=\"text-danger small\">\n            Mot de passe is required (minimum 8 characters).\n          </div>\n        </div>\n      </div>\n\n      <!-- Groupe -->\n      <div class=\"form-row\">\n        <div class=\"col-md-6 mb-3\">\n          <label for=\"groupe\">Groupe</label>\n          <div class=\"input-group\">\n            <div class=\"input-group-prepend\">\n              <span class=\"input-group-text\"><i class=\"fa fa-users\"></i></span>\n            </div>\n            <select id=\"groupe\" class=\"form-control\" [(ngModel)]=\"newResponsable.groupeId\" name=\"groupeId\" required #groupe=\"ngModel\">\n              <option *ngFor=\"let group of groups\" [value]=\"group.idGroupe\">\n                {{ group.nomGroupe }}\n              </option>\n            </select>\n          </div>\n          <div *ngIf=\"groupe.invalid && groupe.touched\" class=\"text-danger small\">\n            Groupe is required.\n          </div>\n        </div>\n      </div>\n\n      <!-- Buttons -->\n      <div class=\"form-row mt-4\">\n        <div class=\"col-md-12 text-center\">\n          <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"responsableForm.invalid\">\n            Ajouter\n          </button>\n          <button type=\"button\" class=\"btn btn-secondary ml-3\" (click)=\"cancelEdit()\">\n            Annuler\n          </button>\n        </div>\n      </div>\n    </form>\n  </div>\n</div>\n"], "mappings": ";;;;;;;ICeUA,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWNH,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAcNH,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYJH,EAAA,CAAAC,cAAA,UAA4C;IAAAD,EAAA,CAAAE,MAAA,uCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACxEH,EAAA,CAAAC,cAAA,UAA2C;IAAAD,EAAA,CAAAE,MAAA,+DAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFjGH,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAI,UAAA,IAAAC,6CAAA,kBAAwE;IACxEL,EAAA,CAAAI,UAAA,IAAAE,6CAAA,kBAA+F;IACjGN,EAAA,CAAAG,YAAA,EAAM;;;;;IAFEH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAoC;IACpCV,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,YAAmC;;;;;IAe3CV,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAaFH,EAAA,CAAAC,cAAA,iBAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF4BH,EAAA,CAAAQ,UAAA,UAAAG,SAAA,CAAAC,QAAA,CAAwB;IAC3DZ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAa,kBAAA,MAAAF,SAAA,CAAAG,SAAA,MACF;;;;;IAGJd,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;ADpFhB,OAAM,MAAOY,uBAAuB;EAelCC,YAAoBC,kBAAsC,EAAUC,MAAc;IAA9D,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAA8B,KAAAC,MAAM,GAANA,MAAM;IAd1E,KAAAC,cAAc,GAAgB;MAC5BC,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,CAAC,CAAG;KACf;;IAED,KAAAC,MAAM,GAAa,EAAE;EAGgE;EAErFC,QAAQA,CAAA;IACN;IACA,IAAI,CAACZ,kBAAkB,CAACa,SAAS,EAAE,CAACC,SAAS,CAAEC,IAAI,IAAI;MACrD,IAAI,CAACJ,MAAM,GAAGI,IAAI,CAAC,CAAE;IACvB,CAAC,CAAC;EACJ;EAEA;EACAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACd,cAAc,CAACQ,QAAQ,EAAE;MACjCO,KAAK,CAAC,6BAA6B,CAAC;MACpC;;IAGF,IAAI,CAAC,IAAI,CAACf,cAAc,CAACO,QAAQ,EAAE;MACjCQ,KAAK,CAAC,yBAAyB,CAAC;MAChC;;IAGF;IACAC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACjB,cAAc,CAAC;IAEvD;IACA,IAAI,CAACF,kBAAkB,CAACgB,cAAc,CAAC,IAAI,CAACd,cAAc,CAAC,CAACY,SAAS,CAClEM,QAAQ,IAAI;MACXF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAAC;MAExD;MACA,IAAI,CAAClB,cAAc,GAAG;QACpBE,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,CAAC,CAAG;OACf;;MACD,IAAI,CAACT,MAAM,CAACoB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAElC,CAAC,EACAC,KAAK,IAAI;MACRJ,OAAO,CAACI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,CACF;EACH;EAEAC,UAAUA,CAAA;IACR;IACA,IAAI,CAACrB,cAAc,GAAG;MACpBC,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX;IAED;IACA,IAAI,CAACT,MAAM,CAACuB,aAAa,CAAC,QAAQ,CAAC;EACrC;;;uBA9EW1B,uBAAuB,EAAAf,EAAA,CAAA0C,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA5C,EAAA,CAAA0C,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB/B,uBAAuB;MAAAgC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZpCrD,EAAA,CAAAC,cAAA,aAA4B;UAEKD,EAAA,CAAAE,MAAA,oCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/DH,EAAA,CAAAC,cAAA,iBAAiF;UAA3ED,EAAA,CAAAuD,UAAA,sBAAAC,0DAAA;YAAA,OAAYF,GAAA,CAAArB,cAAA,EAAgB;UAAA,EAAC;UAGjCjC,EAAA,CAAAC,cAAA,aAAsB;UAEDD,EAAA,CAAAE,MAAA,UAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5BH,EAAA,CAAAC,cAAA,cAAyB;UAEUD,EAAA,CAAAyD,SAAA,aAA0B;UAAAzD,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,qBAAyJ;UAAxGD,EAAA,CAAAuD,UAAA,2BAAAG,iEAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAnC,cAAA,CAAAE,SAAA,GAAAsC,MAAA;UAAA,EAAsC;UAAvF3D,EAAA,CAAAG,YAAA,EAAyJ;UAE3JH,EAAA,CAAAI,UAAA,KAAAwD,uCAAA,kBAEM;UACR5D,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAA2B;UACLD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,cAAyB;UAEUD,EAAA,CAAAyD,SAAA,aAA0B;UAAAzD,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,qBAAoK;UAAhHD,EAAA,CAAAuD,UAAA,2BAAAM,iEAAAF,MAAA;YAAA,OAAAL,GAAA,CAAAnC,cAAA,CAAAG,QAAA,GAAAqC,MAAA;UAAA,EAAqC;UAAzF3D,EAAA,CAAAG,YAAA,EAAoK;UAEtKH,EAAA,CAAAI,UAAA,KAAA0D,uCAAA,kBAEM;UACR9D,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAAsB;UAECD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,cAAyB;UAEUD,EAAA,CAAAyD,SAAA,aAA8B;UAAAzD,EAAA,CAAAG,YAAA,EAAO;UAEtEH,EAAA,CAAAC,cAAA,qBAAmK;UAA/GD,EAAA,CAAAuD,UAAA,2BAAAQ,iEAAAJ,MAAA;YAAA,OAAAL,GAAA,CAAAnC,cAAA,CAAAI,KAAA,GAAAoC,MAAA;UAAA,EAAkC;UAAtF3D,EAAA,CAAAG,YAAA,EAAmK;UAErKH,EAAA,CAAAI,UAAA,KAAA4D,uCAAA,kBAEM;UACRhE,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAA2B;UACFD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,cAAyB;UAEUD,EAAA,CAAAyD,SAAA,aAA2B;UAAAzD,EAAA,CAAAG,YAAA,EAAO;UAEnEH,EAAA,CAAAC,cAAA,qBAAuM;UAAjJD,EAAA,CAAAuD,UAAA,2BAAAU,iEAAAN,MAAA;YAAA,OAAAL,GAAA,CAAAnC,cAAA,CAAAK,SAAA,GAAAmC,MAAA;UAAA,EAAsC;UAA5F3D,EAAA,CAAAG,YAAA,EAAuM;UAEzMH,EAAA,CAAAI,UAAA,KAAA8D,uCAAA,kBAGM;UACRlE,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAAsB;UAEID,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,cAAyB;UAEUD,EAAA,CAAAyD,SAAA,aAA0B;UAAAzD,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,qBAAkM;UAAxID,EAAA,CAAAuD,UAAA,2BAAAY,iEAAAR,MAAA;YAAA,OAAAL,GAAA,CAAAnC,cAAA,CAAAO,QAAA,GAAAiC,MAAA;UAAA,EAAqC;UAA/F3D,EAAA,CAAAG,YAAA,EAAkM;UAEpMH,EAAA,CAAAI,UAAA,KAAAgE,uCAAA,kBAEM;UACRpE,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAAsB;UAEED,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,cAAyB;UAEUD,EAAA,CAAAyD,SAAA,aAA2B;UAAAzD,EAAA,CAAAG,YAAA,EAAO;UAEnEH,EAAA,CAAAC,cAAA,sBAA0H;UAAjFD,EAAA,CAAAuD,UAAA,2BAAAc,kEAAAV,MAAA;YAAA,OAAAL,GAAA,CAAAnC,cAAA,CAAAQ,QAAA,GAAAgC,MAAA;UAAA,EAAqC;UAC5E3D,EAAA,CAAAI,UAAA,KAAAkE,0CAAA,qBAES;UACXtE,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAI,UAAA,KAAAmE,uCAAA,kBAEM;UACRvE,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAA2B;UAGrBD,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA4E;UAAvBD,EAAA,CAAAuD,UAAA,mBAAAiB,0DAAA;YAAA,OAASlB,GAAA,CAAAd,UAAA,EAAY;UAAA,EAAC;UACzExC,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;UA/F0CH,EAAA,CAAAO,SAAA,IAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAA8C,GAAA,CAAAnC,cAAA,CAAAE,SAAA,CAAsC;UAEnFrB,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,UAAA,SAAAiE,GAAA,CAAAC,OAAA,IAAAD,GAAA,CAAAE,OAAA,CAAgC;UAWgB3E,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAA8C,GAAA,CAAAnC,cAAA,CAAAG,QAAA,CAAqC;UAErFtB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,SAAAoE,GAAA,CAAAF,OAAA,IAAAE,GAAA,CAAAD,OAAA,CAAsC;UAcU3E,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,YAAA8C,GAAA,CAAAnC,cAAA,CAAAI,KAAA,CAAkC;UAElFvB,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,SAAAqE,GAAA,CAAAH,OAAA,IAAAG,GAAA,CAAAF,OAAA,CAAoC;UAWc3E,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAA8C,GAAA,CAAAnC,cAAA,CAAAK,SAAA,CAAsC;UAExFxB,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAiE,OAAA,IAAAjE,GAAA,CAAAkE,OAAA,CAA4C;UAeU3E,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAA8C,GAAA,CAAAnC,cAAA,CAAAO,QAAA,CAAqC;UAE3F1B,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAQ,UAAA,SAAAsE,GAAA,CAAAJ,OAAA,IAAAI,GAAA,CAAAH,OAAA,CAA0C;UAcL3E,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAA8C,GAAA,CAAAnC,cAAA,CAAAQ,QAAA,CAAqC;UAClD3B,EAAA,CAAAO,SAAA,GAAS;UAATP,EAAA,CAAAQ,UAAA,YAAA8C,GAAA,CAAA1B,MAAA,CAAS;UAKjC5B,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,SAAAuE,IAAA,CAAAL,OAAA,IAAAK,IAAA,CAAAJ,OAAA,CAAsC;UASE3E,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,aAAAwE,GAAA,CAAAN,OAAA,CAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}