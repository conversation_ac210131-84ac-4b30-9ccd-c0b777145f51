{"ast": null, "code": "/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.16.1\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\nvar timeoutDuration = function () {\n  var longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}();\nfunction microtaskDebounce(fn) {\n  var called = false;\n  return function () {\n    if (called) {\n      return;\n    }\n    called = true;\n    window.Promise.resolve().then(function () {\n      called = false;\n      fn();\n    });\n  };\n}\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\nvar supportsMicroTasks = isBrowser && window.Promise;\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var window = element.ownerDocument.defaultView;\n  var css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n    overflow = _getStyleComputedProp.overflow,\n    overflowX = _getStyleComputedProp.overflowX,\n    overflowY = _getStyleComputedProp.overflowY;\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nfunction getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\nvar isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nvar isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nfunction isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n  var noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  var offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n  var nodeName = offsetParent && offsetParent.nodeName;\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n  return offsetParent;\n}\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = element.ownerDocument.documentElement;\n    var scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n  return parseFloat(styles['border' + sideA + 'Width']) + parseFloat(styles['border' + sideB + 'Width']);\n}\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE(10) ? parseInt(html['offset' + axis]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')]) : 0);\n}\nfunction getWindowSizes(document) {\n  var body = document.body;\n  var html = document.documentElement;\n  var computedStyle = isIE(10) && getComputedStyle(html);\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n};\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } else {\n      rect = element.getBoundingClientRect();\n    }\n  } catch (e) {}\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  var width = sizes.width || element.clientWidth || result.width;\n  var height = sizes.height || element.clientHeight || result.height;\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n  return getClientRect(result);\n}\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var fixedPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var isIE10 = isIE(10);\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = parseFloat(styles.borderTopWidth);\n  var borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = parseFloat(styles.marginTop);\n    var marginLeft = parseFloat(styles.marginLeft);\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n  if (isIE10 && !fixedPosition ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n  return offsets;\n}\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var excludeScroll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var html = element.ownerDocument.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n  var scrollTop = !excludeScroll ? getScroll(html) : 0;\n  var scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  var parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  var el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  var fixedPosition = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n\n  // NOTE: 1 DOM access here\n\n  var boundaries = {\n    top: 0,\n    left: 0\n  };\n  var offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(popper.ownerDocument),\n        height = _getWindowSizes.height,\n        width = _getWindowSizes.width;\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  var isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0;\n  boundaries.top += isPaddingNumber ? padding : padding.top || 0;\n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0;\n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0;\n  return boundaries;\n}\nfunction getArea(_ref) {\n  var width = _ref.width,\n    height = _ref.height;\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n      height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n  var variation = placement.split('-')[1];\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var fixedPosition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n  var commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var window = element.ownerDocument.defaultView;\n  var styles = window.getComputedStyle(element);\n  var x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  var y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = {\n    left: 'right',\n    right: 'left',\n    bottom: 'top',\n    top: 'bottom'\n  };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier['function']) {\n      // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n      data = fn(data, modifier);\n    }\n  });\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference, this.options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n  data.offsets.popper.position = this.options.positionFixed ? 'fixed' : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n      enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n  for (var i = 0; i < prefixes.length; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\n/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nfunction getWindow(element) {\n  var ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, {\n    passive: true\n  });\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, {\n    passive: true\n  });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, {\n    position: options.positionFixed ? 'fixed' : 'absolute'\n  });\n  return options;\n}\n\n/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nfunction getRoundedOffsets(data, shouldRound) {\n  var _data$offsets = data.offsets,\n    popper = _data$offsets.popper,\n    reference = _data$offsets.reference;\n  var round = Math.round,\n    floor = Math.floor;\n  var noRound = function noRound(v) {\n    return v;\n  };\n  var referenceWidth = round(reference.width);\n  var popperWidth = round(popper.width);\n  var isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  var isVariation = data.placement.indexOf('-') !== -1;\n  var sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  var bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n  var horizontalToInteger = !shouldRound ? noRound : isVertical || isVariation || sameWidthParity ? round : floor;\n  var verticalToInteger = !shouldRound ? noRound : round;\n  return {\n    left: horizontalToInteger(bothOddWidth && !isVariation && shouldRound ? popper.left - 1 : popper.left),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right)\n  };\n}\nvar isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n    y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n  var offsets = getRoundedOffsets(data, window.devicePixelRatio < 2 || !isFirefox);\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n    top = void 0;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  var _data$offsets$arrow;\n\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n    popper = _data$offsets.popper,\n    reference = _data$offsets.reference;\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var css = getStyleComputedProperty(data.instance.popper);\n  var popperMarginSide = parseFloat(css['margin' + sideCapitalized]);\n  var popperBorderSide = parseFloat(css['border' + sideCapitalized + 'Width']);\n  var sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ''), _data$offsets$arrow);\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement, data.positionFixed);\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n  var flipOrder = [];\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    var flippedVariationByRef = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    // flips variation if popper content overflows boundaries\n    var flippedVariationByContent = !!options.flipVariationsByContent && (isVertical && variation === 'start' && overflowsRight || isVertical && variation === 'end' && overflowsLeft || !isVertical && variation === 'start' && overflowsBottom || !isVertical && variation === 'end' && overflowsTop);\n    var flippedVariation = flippedVariationByRef || flippedVariationByContent;\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n    popper = _data$offsets.popper,\n    reference = _data$offsets.reference;\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n    _data$offsets = data.offsets,\n    popper = _data$offsets.popper,\n    reference = _data$offsets.reference;\n  var basePlacement = placement.split('-')[0];\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  var transformProp = getSupportedPropertyName('transform');\n  var popperStyles = data.instance.popper.style; // assignment to help minification\n  var top = popperStyles.top,\n    left = popperStyles.left,\n    transform = popperStyles[transformProp];\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement, data.positionFixed);\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n  options.boundaries = boundaries;\n  var order = options.priority;\n  var popper = data.offsets.popper;\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n  data.offsets.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n      reference = _data$offsets.reference,\n      popper = _data$offsets.popper;\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n    popper = _data$offsets.popper,\n    reference = _data$offsets.reference;\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false\n  },\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedules an update. It will run on the next UI update available.\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n  }]);\n\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\nexport default Popper;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document", "navigator", "timeoutDuration", "longerTimeoutBrowsers", "i", "length", "userAgent", "indexOf", "microtaskDebounce", "fn", "called", "Promise", "resolve", "then", "taskDebounce", "scheduled", "setTimeout", "supportsMicroTasks", "debounce", "isFunction", "functionToCheck", "getType", "toString", "call", "getStyleComputedProperty", "element", "property", "nodeType", "ownerDocument", "defaultView", "css", "getComputedStyle", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "body", "_getStyleComputedProp", "overflow", "overflowX", "overflowY", "test", "getReferenceNode", "reference", "referenceNode", "isIE11", "MSInputMethodContext", "documentMode", "isIE10", "isIE", "version", "getOffsetParent", "documentElement", "noOffsetParent", "offsetParent", "nextElement<PERSON><PERSON>ling", "isOffsetContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "element1root", "getScroll", "side", "arguments", "undefined", "upperSide", "html", "scrollingElement", "includeScroll", "rect", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getBordersSize", "styles", "axis", "sideA", "sideB", "parseFloat", "getSize", "computedStyle", "Math", "max", "parseInt", "getWindowSizes", "height", "width", "classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "createClass", "defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "protoProps", "staticProps", "prototype", "obj", "value", "_extends", "assign", "source", "hasOwnProperty", "getClientRect", "offsets", "getBoundingClientRect", "e", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getOffsetRectRelativeToArbitraryNode", "children", "parent", "fixedPosition", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "excludeScroll", "relativeOffset", "innerWidth", "innerHeight", "offset", "isFixed", "getFixedPositionOffsetParent", "parentElement", "el", "getBoundaries", "popper", "padding", "boundariesElement", "boundaries", "boundariesNode", "_getWindowSizes", "isPaddingNumber", "getArea", "_ref", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "keys", "map", "area", "sort", "a", "b", "filtered<PERSON><PERSON>s", "filter", "_ref2", "computedPlacement", "variation", "split", "getReferenceOffsets", "state", "commonOffsetParent", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "find", "arr", "check", "Array", "findIndex", "prop", "cur", "match", "runModifiers", "modifiers", "data", "ends", "modifiersToRun", "slice", "for<PERSON>ach", "console", "warn", "enabled", "update", "isDestroyed", "arrowStyles", "attributes", "flipped", "options", "positionFixed", "flip", "originalPlacement", "position", "isCreated", "onCreate", "onUpdate", "isModifierEnabled", "modifierName", "some", "name", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "prefix", "to<PERSON><PERSON><PERSON>", "style", "destroy", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "disableEventListeners", "removeOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "getWindow", "attachToScrollParents", "event", "callback", "scrollParents", "isBody", "addEventListener", "passive", "push", "setupEventListeners", "updateBound", "scrollElement", "eventsEnabled", "enableEventListeners", "scheduleUpdate", "removeEventListeners", "removeEventListener", "cancelAnimationFrame", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "setAttributes", "setAttribute", "applyStyle", "arrowElement", "applyStyleOnLoad", "modifierOptions", "getRoundedOffsets", "shouldRound", "_data$offsets", "round", "floor", "noRound", "v", "referenceWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isVertical", "isVariation", "sameWidthParity", "bothOddWidth", "horizontalToInteger", "verticalToInteger", "isFirefox", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "devicePixelRatio", "prefixedProperty", "invertTop", "invertLeft", "arrow", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "_requesting", "requested", "_data$offsets$arrow", "querySelector", "len", "sideCapitalized", "toLowerCase", "altSide", "opSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "min", "getOppositeVariation", "placements", "validPlacements", "clockwise", "counter", "index", "concat", "reverse", "BEHAVIORS", "FLIP", "CLOCKWISE", "COUNTERCLOCKWISE", "placementOpposite", "flipOrder", "behavior", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariationByRef", "flipVariations", "flippedVariationByContent", "flipVariationsByContent", "flippedVariation", "keepTogether", "toValue", "str", "size", "parseOffset", "basePlacement", "useHeight", "fragments", "frag", "trim", "divider", "search", "splitRegex", "ops", "op", "mergeWithPrevious", "reduce", "index2", "preventOverflow", "transformProp", "popperStyles", "transform", "priority", "primary", "escapeWithReference", "secondary", "shift", "shiftvariation", "shiftOffsets", "hide", "bound", "inner", "subtractLength", "onLoad", "De<PERSON>ults", "<PERSON><PERSON>", "_this", "requestAnimationFrame", "bind", "j<PERSON>y", "update$$1", "destroy$$1", "enableEventListeners$$1", "disableEventListeners$$1", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Documents/Final/frontend/node_modules/popper.js/dist/esm/popper.js"], "sourcesContent": ["/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.16.1\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n\nvar timeoutDuration = function () {\n  var longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}();\n\nfunction microtaskDebounce(fn) {\n  var called = false;\n  return function () {\n    if (called) {\n      return;\n    }\n    called = true;\n    window.Promise.resolve().then(function () {\n      called = false;\n      fn();\n    });\n  };\n}\n\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nvar supportsMicroTasks = isBrowser && window.Promise;\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var window = element.ownerDocument.defaultView;\n  var css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n      overflow = _getStyleComputedProp.overflow,\n      overflowX = _getStyleComputedProp.overflowX,\n      overflowY = _getStyleComputedProp.overflowY;\n\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nfunction getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n\nvar isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nvar isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nfunction isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  var noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  var offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  var nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = element.ownerDocument.documentElement;\n    var scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return parseFloat(styles['border' + sideA + 'Width']) + parseFloat(styles['border' + sideB + 'Width']);\n}\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE(10) ? parseInt(html['offset' + axis]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')]) : 0);\n}\n\nfunction getWindowSizes(document) {\n  var body = document.body;\n  var html = document.documentElement;\n  var computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n};\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } else {\n      rect = element.getBoundingClientRect();\n    }\n  } catch (e) {}\n\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  var width = sizes.width || element.clientWidth || result.width;\n  var height = sizes.height || element.clientHeight || result.height;\n\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var fixedPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var isIE10 = isIE(10);\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = parseFloat(styles.borderTopWidth);\n  var borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = parseFloat(styles.marginTop);\n    var marginLeft = parseFloat(styles.marginLeft);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (isIE10 && !fixedPosition ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var excludeScroll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var html = element.ownerDocument.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  var scrollTop = !excludeScroll ? getScroll(html) : 0;\n  var scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  var parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  var el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  var fixedPosition = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n\n  // NOTE: 1 DOM access here\n\n  var boundaries = { top: 0, left: 0 };\n  var offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(popper.ownerDocument),\n          height = _getWindowSizes.height,\n          width = _getWindowSizes.width;\n\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  var isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0;\n  boundaries.top += isPaddingNumber ? padding : padding.top || 0;\n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0;\n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0;\n\n  return boundaries;\n}\n\nfunction getArea(_ref) {\n  var width = _ref.width,\n      height = _ref.height;\n\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n        height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n\n  var variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var fixedPosition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n\n  var commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var window = element.ownerDocument.defaultView;\n  var styles = window.getComputedStyle(element);\n  var x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  var y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier['function']) {\n      // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference, this.options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n\n  data.offsets.popper.position = this.options.positionFixed ? 'fixed' : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n        enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (var i = 0; i < prefixes.length; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\n/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nfunction getWindow(element) {\n  var ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n\n/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nfunction getRoundedOffsets(data, shouldRound) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n  var round = Math.round,\n      floor = Math.floor;\n\n  var noRound = function noRound(v) {\n    return v;\n  };\n\n  var referenceWidth = round(reference.width);\n  var popperWidth = round(popper.width);\n\n  var isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  var isVariation = data.placement.indexOf('-') !== -1;\n  var sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  var bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n\n  var horizontalToInteger = !shouldRound ? noRound : isVertical || isVariation || sameWidthParity ? round : floor;\n  var verticalToInteger = !shouldRound ? noRound : round;\n\n  return {\n    left: horizontalToInteger(bothOddWidth && !isVariation && shouldRound ? popper.left - 1 : popper.left),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right)\n  };\n}\n\nvar isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n      y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n\n  var offsets = getRoundedOffsets(data, window.devicePixelRatio < 2 || !isFirefox);\n\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n      top = void 0;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  var _data$offsets$arrow;\n\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var css = getStyleComputedProperty(data.instance.popper);\n  var popperMarginSide = parseFloat(css['margin' + sideCapitalized]);\n  var popperBorderSide = parseFloat(css['border' + sideCapitalized + 'Width']);\n  var sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ''), _data$offsets$arrow);\n\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement, data.positionFixed);\n\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n\n  var flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    var flippedVariationByRef = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    // flips variation if popper content overflows boundaries\n    var flippedVariationByContent = !!options.flipVariationsByContent && (isVertical && variation === 'start' && overflowsRight || isVertical && variation === 'end' && overflowsLeft || !isVertical && variation === 'start' && overflowsBottom || !isVertical && variation === 'end' && overflowsTop);\n\n    var flippedVariation = flippedVariationByRef || flippedVariationByContent;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n      _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var basePlacement = placement.split('-')[0];\n\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  var transformProp = getSupportedPropertyName('transform');\n  var popperStyles = data.instance.popper.style; // assignment to help minification\n  var top = popperStyles.top,\n      left = popperStyles.left,\n      transform = popperStyles[transformProp];\n\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement, data.positionFixed);\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  var order = options.priority;\n  var popper = data.offsets.popper;\n\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n        reference = _data$offsets.reference,\n        popper = _data$offsets.popper;\n\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedules an update. It will run on the next UI update available.\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\n\nexport default Popper;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,OAAOC,SAAS,KAAK,WAAW;AAEpH,IAAIC,eAAe,GAAG,YAAY;EAChC,IAAIC,qBAAqB,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC;EAC1D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,qBAAqB,CAACE,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACxD,IAAIN,SAAS,IAAIG,SAAS,CAACK,SAAS,CAACC,OAAO,CAACJ,qBAAqB,CAACC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MAC3E,OAAO,CAAC;IACV;EACF;EACA,OAAO,CAAC;AACV,CAAC,CAAC,CAAC;AAEH,SAASI,iBAAiBA,CAACC,EAAE,EAAE;EAC7B,IAAIC,MAAM,GAAG,KAAK;EAClB,OAAO,YAAY;IACjB,IAAIA,MAAM,EAAE;MACV;IACF;IACAA,MAAM,GAAG,IAAI;IACbX,MAAM,CAACY,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;MACxCH,MAAM,GAAG,KAAK;MACdD,EAAE,CAAC,CAAC;IACN,CAAC,CAAC;EACJ,CAAC;AACH;AAEA,SAASK,YAAYA,CAACL,EAAE,EAAE;EACxB,IAAIM,SAAS,GAAG,KAAK;EACrB,OAAO,YAAY;IACjB,IAAI,CAACA,SAAS,EAAE;MACdA,SAAS,GAAG,IAAI;MAChBC,UAAU,CAAC,YAAY;QACrBD,SAAS,GAAG,KAAK;QACjBN,EAAE,CAAC,CAAC;MACN,CAAC,EAAEP,eAAe,CAAC;IACrB;EACF,CAAC;AACH;AAEA,IAAIe,kBAAkB,GAAGnB,SAAS,IAAIC,MAAM,CAACY,OAAO;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIO,QAAQ,GAAGD,kBAAkB,GAAGT,iBAAiB,GAAGM,YAAY;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,UAAUA,CAACC,eAAe,EAAE;EACnC,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,OAAOD,eAAe,IAAIC,OAAO,CAACC,QAAQ,CAACC,IAAI,CAACH,eAAe,CAAC,KAAK,mBAAmB;AAC1F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,wBAAwBA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EACnD,IAAID,OAAO,CAACE,QAAQ,KAAK,CAAC,EAAE;IAC1B,OAAO,EAAE;EACX;EACA;EACA,IAAI5B,MAAM,GAAG0B,OAAO,CAACG,aAAa,CAACC,WAAW;EAC9C,IAAIC,GAAG,GAAG/B,MAAM,CAACgC,gBAAgB,CAACN,OAAO,EAAE,IAAI,CAAC;EAChD,OAAOC,QAAQ,GAAGI,GAAG,CAACJ,QAAQ,CAAC,GAAGI,GAAG;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACP,OAAO,EAAE;EAC9B,IAAIA,OAAO,CAACQ,QAAQ,KAAK,MAAM,EAAE;IAC/B,OAAOR,OAAO;EAChB;EACA,OAAOA,OAAO,CAACS,UAAU,IAAIT,OAAO,CAACU,IAAI;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACX,OAAO,EAAE;EAChC;EACA,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOzB,QAAQ,CAACqC,IAAI;EACtB;EAEA,QAAQZ,OAAO,CAACQ,QAAQ;IACtB,KAAK,MAAM;IACX,KAAK,MAAM;MACT,OAAOR,OAAO,CAACG,aAAa,CAACS,IAAI;IACnC,KAAK,WAAW;MACd,OAAOZ,OAAO,CAACY,IAAI;EACvB;;EAEA;;EAEA,IAAIC,qBAAqB,GAAGd,wBAAwB,CAACC,OAAO,CAAC;IACzDc,QAAQ,GAAGD,qBAAqB,CAACC,QAAQ;IACzCC,SAAS,GAAGF,qBAAqB,CAACE,SAAS;IAC3CC,SAAS,GAAGH,qBAAqB,CAACG,SAAS;EAE/C,IAAI,uBAAuB,CAACC,IAAI,CAACH,QAAQ,GAAGE,SAAS,GAAGD,SAAS,CAAC,EAAE;IAClE,OAAOf,OAAO;EAChB;EAEA,OAAOW,eAAe,CAACJ,aAAa,CAACP,OAAO,CAAC,CAAC;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,gBAAgBA,CAACC,SAAS,EAAE;EACnC,OAAOA,SAAS,IAAIA,SAAS,CAACC,aAAa,GAAGD,SAAS,CAACC,aAAa,GAAGD,SAAS;AACnF;AAEA,IAAIE,MAAM,GAAGhD,SAAS,IAAI,CAAC,EAAEC,MAAM,CAACgD,oBAAoB,IAAI/C,QAAQ,CAACgD,YAAY,CAAC;AAClF,IAAIC,MAAM,GAAGnD,SAAS,IAAI,SAAS,CAAC4C,IAAI,CAACzC,SAAS,CAACK,SAAS,CAAC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4C,IAAIA,CAACC,OAAO,EAAE;EACrB,IAAIA,OAAO,KAAK,EAAE,EAAE;IAClB,OAAOL,MAAM;EACf;EACA,IAAIK,OAAO,KAAK,EAAE,EAAE;IAClB,OAAOF,MAAM;EACf;EACA,OAAOH,MAAM,IAAIG,MAAM;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAAC3B,OAAO,EAAE;EAChC,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOzB,QAAQ,CAACqD,eAAe;EACjC;EAEA,IAAIC,cAAc,GAAGJ,IAAI,CAAC,EAAE,CAAC,GAAGlD,QAAQ,CAACqC,IAAI,GAAG,IAAI;;EAEpD;EACA,IAAIkB,YAAY,GAAG9B,OAAO,CAAC8B,YAAY,IAAI,IAAI;EAC/C;EACA,OAAOA,YAAY,KAAKD,cAAc,IAAI7B,OAAO,CAAC+B,kBAAkB,EAAE;IACpED,YAAY,GAAG,CAAC9B,OAAO,GAAGA,OAAO,CAAC+B,kBAAkB,EAAED,YAAY;EACpE;EAEA,IAAItB,QAAQ,GAAGsB,YAAY,IAAIA,YAAY,CAACtB,QAAQ;EAEpD,IAAI,CAACA,QAAQ,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,MAAM,EAAE;IAC3D,OAAOR,OAAO,GAAGA,OAAO,CAACG,aAAa,CAACyB,eAAe,GAAGrD,QAAQ,CAACqD,eAAe;EACnF;;EAEA;EACA;EACA,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC9C,OAAO,CAACgD,YAAY,CAACtB,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAIT,wBAAwB,CAAC+B,YAAY,EAAE,UAAU,CAAC,KAAK,QAAQ,EAAE;IAClI,OAAOH,eAAe,CAACG,YAAY,CAAC;EACtC;EAEA,OAAOA,YAAY;AACrB;AAEA,SAASE,iBAAiBA,CAAChC,OAAO,EAAE;EAClC,IAAIQ,QAAQ,GAAGR,OAAO,CAACQ,QAAQ;EAE/B,IAAIA,QAAQ,KAAK,MAAM,EAAE;IACvB,OAAO,KAAK;EACd;EACA,OAAOA,QAAQ,KAAK,MAAM,IAAImB,eAAe,CAAC3B,OAAO,CAACiC,iBAAiB,CAAC,KAAKjC,OAAO;AACtF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkC,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAIA,IAAI,CAAC1B,UAAU,KAAK,IAAI,EAAE;IAC5B,OAAOyB,OAAO,CAACC,IAAI,CAAC1B,UAAU,CAAC;EACjC;EAEA,OAAO0B,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAClD;EACA,IAAI,CAACD,QAAQ,IAAI,CAACA,QAAQ,CAACnC,QAAQ,IAAI,CAACoC,QAAQ,IAAI,CAACA,QAAQ,CAACpC,QAAQ,EAAE;IACtE,OAAO3B,QAAQ,CAACqD,eAAe;EACjC;;EAEA;EACA,IAAIW,KAAK,GAAGF,QAAQ,CAACG,uBAAuB,CAACF,QAAQ,CAAC,GAAGG,IAAI,CAACC,2BAA2B;EACzF,IAAIC,KAAK,GAAGJ,KAAK,GAAGF,QAAQ,GAAGC,QAAQ;EACvC,IAAIM,GAAG,GAAGL,KAAK,GAAGD,QAAQ,GAAGD,QAAQ;;EAErC;EACA,IAAIQ,KAAK,GAAGtE,QAAQ,CAACuE,WAAW,CAAC,CAAC;EAClCD,KAAK,CAACE,QAAQ,CAACJ,KAAK,EAAE,CAAC,CAAC;EACxBE,KAAK,CAACG,MAAM,CAACJ,GAAG,EAAE,CAAC,CAAC;EACpB,IAAIK,uBAAuB,GAAGJ,KAAK,CAACI,uBAAuB;;EAE3D;;EAEA,IAAIZ,QAAQ,KAAKY,uBAAuB,IAAIX,QAAQ,KAAKW,uBAAuB,IAAIN,KAAK,CAACO,QAAQ,CAACN,GAAG,CAAC,EAAE;IACvG,IAAIZ,iBAAiB,CAACiB,uBAAuB,CAAC,EAAE;MAC9C,OAAOA,uBAAuB;IAChC;IAEA,OAAOtB,eAAe,CAACsB,uBAAuB,CAAC;EACjD;;EAEA;EACA,IAAIE,YAAY,GAAGjB,OAAO,CAACG,QAAQ,CAAC;EACpC,IAAIc,YAAY,CAACzC,IAAI,EAAE;IACrB,OAAO0B,sBAAsB,CAACe,YAAY,CAACzC,IAAI,EAAE4B,QAAQ,CAAC;EAC5D,CAAC,MAAM;IACL,OAAOF,sBAAsB,CAACC,QAAQ,EAAEH,OAAO,CAACI,QAAQ,CAAC,CAAC5B,IAAI,CAAC;EACjE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0C,SAASA,CAACpD,OAAO,EAAE;EAC1B,IAAIqD,IAAI,GAAGC,SAAS,CAAC1E,MAAM,GAAG,CAAC,IAAI0E,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAEpF,IAAIE,SAAS,GAAGH,IAAI,KAAK,KAAK,GAAG,WAAW,GAAG,YAAY;EAC3D,IAAI7C,QAAQ,GAAGR,OAAO,CAACQ,QAAQ;EAE/B,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,MAAM,EAAE;IAC9C,IAAIiD,IAAI,GAAGzD,OAAO,CAACG,aAAa,CAACyB,eAAe;IAChD,IAAI8B,gBAAgB,GAAG1D,OAAO,CAACG,aAAa,CAACuD,gBAAgB,IAAID,IAAI;IACrE,OAAOC,gBAAgB,CAACF,SAAS,CAAC;EACpC;EAEA,OAAOxD,OAAO,CAACwD,SAAS,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,aAAaA,CAACC,IAAI,EAAE5D,OAAO,EAAE;EACpC,IAAI6D,QAAQ,GAAGP,SAAS,CAAC1E,MAAM,GAAG,CAAC,IAAI0E,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAExF,IAAIQ,SAAS,GAAGV,SAAS,CAACpD,OAAO,EAAE,KAAK,CAAC;EACzC,IAAI+D,UAAU,GAAGX,SAAS,CAACpD,OAAO,EAAE,MAAM,CAAC;EAC3C,IAAIgE,QAAQ,GAAGH,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;EAChCD,IAAI,CAACK,GAAG,IAAIH,SAAS,GAAGE,QAAQ;EAChCJ,IAAI,CAACM,MAAM,IAAIJ,SAAS,GAAGE,QAAQ;EACnCJ,IAAI,CAACO,IAAI,IAAIJ,UAAU,GAAGC,QAAQ;EAClCJ,IAAI,CAACQ,KAAK,IAAIL,UAAU,GAAGC,QAAQ;EACnC,OAAOJ,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASS,cAAcA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACpC,IAAIC,KAAK,GAAGD,IAAI,KAAK,GAAG,GAAG,MAAM,GAAG,KAAK;EACzC,IAAIE,KAAK,GAAGD,KAAK,KAAK,MAAM,GAAG,OAAO,GAAG,QAAQ;EAEjD,OAAOE,UAAU,CAACJ,MAAM,CAAC,QAAQ,GAAGE,KAAK,GAAG,OAAO,CAAC,CAAC,GAAGE,UAAU,CAACJ,MAAM,CAAC,QAAQ,GAAGG,KAAK,GAAG,OAAO,CAAC,CAAC;AACxG;AAEA,SAASE,OAAOA,CAACJ,IAAI,EAAE3D,IAAI,EAAE6C,IAAI,EAAEmB,aAAa,EAAE;EAChD,OAAOC,IAAI,CAACC,GAAG,CAAClE,IAAI,CAAC,QAAQ,GAAG2D,IAAI,CAAC,EAAE3D,IAAI,CAAC,QAAQ,GAAG2D,IAAI,CAAC,EAAEd,IAAI,CAAC,QAAQ,GAAGc,IAAI,CAAC,EAAEd,IAAI,CAAC,QAAQ,GAAGc,IAAI,CAAC,EAAEd,IAAI,CAAC,QAAQ,GAAGc,IAAI,CAAC,EAAE9C,IAAI,CAAC,EAAE,CAAC,GAAGsD,QAAQ,CAACtB,IAAI,CAAC,QAAQ,GAAGc,IAAI,CAAC,CAAC,GAAGQ,QAAQ,CAACH,aAAa,CAAC,QAAQ,IAAIL,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAGQ,QAAQ,CAACH,aAAa,CAAC,QAAQ,IAAIL,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9U;AAEA,SAASS,cAAcA,CAACzG,QAAQ,EAAE;EAChC,IAAIqC,IAAI,GAAGrC,QAAQ,CAACqC,IAAI;EACxB,IAAI6C,IAAI,GAAGlF,QAAQ,CAACqD,eAAe;EACnC,IAAIgD,aAAa,GAAGnD,IAAI,CAAC,EAAE,CAAC,IAAInB,gBAAgB,CAACmD,IAAI,CAAC;EAEtD,OAAO;IACLwB,MAAM,EAAEN,OAAO,CAAC,QAAQ,EAAE/D,IAAI,EAAE6C,IAAI,EAAEmB,aAAa,CAAC;IACpDM,KAAK,EAAEP,OAAO,CAAC,OAAO,EAAE/D,IAAI,EAAE6C,IAAI,EAAEmB,aAAa;EACnD,CAAC;AACH;AAEA,IAAIO,cAAc,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,WAAW,EAAE;EACpD,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAC1D;AACF,CAAC;AAED,IAAIC,WAAW,GAAG,YAAY;EAC5B,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IACvC,KAAK,IAAI/G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+G,KAAK,CAAC9G,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAIgH,UAAU,GAAGD,KAAK,CAAC/G,CAAC,CAAC;MACzBgH,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MACtDD,UAAU,CAACE,YAAY,GAAG,IAAI;MAC9B,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MACrDC,MAAM,CAACC,cAAc,CAACP,MAAM,EAAEE,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;IAC3D;EACF;EAEA,OAAO,UAAUN,WAAW,EAAEa,UAAU,EAAEC,WAAW,EAAE;IACrD,IAAID,UAAU,EAAEV,gBAAgB,CAACH,WAAW,CAACe,SAAS,EAAEF,UAAU,CAAC;IACnE,IAAIC,WAAW,EAAEX,gBAAgB,CAACH,WAAW,EAAEc,WAAW,CAAC;IAC3D,OAAOd,WAAW;EACpB,CAAC;AACH,CAAC,CAAC,CAAC;AAMH,IAAIW,cAAc,GAAG,SAAAA,CAAUK,GAAG,EAAEJ,GAAG,EAAEK,KAAK,EAAE;EAC9C,IAAIL,GAAG,IAAII,GAAG,EAAE;IACdN,MAAM,CAACC,cAAc,CAACK,GAAG,EAAEJ,GAAG,EAAE;MAC9BK,KAAK,EAAEA,KAAK;MACZV,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLO,GAAG,CAACJ,GAAG,CAAC,GAAGK,KAAK;EAClB;EAEA,OAAOD,GAAG;AACZ,CAAC;AAED,IAAIE,QAAQ,GAAGR,MAAM,CAACS,MAAM,IAAI,UAAUf,MAAM,EAAE;EAChD,KAAK,IAAI9G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2E,SAAS,CAAC1E,MAAM,EAAED,CAAC,EAAE,EAAE;IACzC,IAAI8H,MAAM,GAAGnD,SAAS,CAAC3E,CAAC,CAAC;IAEzB,KAAK,IAAIsH,GAAG,IAAIQ,MAAM,EAAE;MACtB,IAAIV,MAAM,CAACK,SAAS,CAACM,cAAc,CAAC5G,IAAI,CAAC2G,MAAM,EAAER,GAAG,CAAC,EAAE;QACrDR,MAAM,CAACQ,GAAG,CAAC,GAAGQ,MAAM,CAACR,GAAG,CAAC;MAC3B;IACF;EACF;EAEA,OAAOR,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,aAAaA,CAACC,OAAO,EAAE;EAC9B,OAAOL,QAAQ,CAAC,CAAC,CAAC,EAAEK,OAAO,EAAE;IAC3BxC,KAAK,EAAEwC,OAAO,CAACzC,IAAI,GAAGyC,OAAO,CAAC1B,KAAK;IACnChB,MAAM,EAAE0C,OAAO,CAAC3C,GAAG,GAAG2C,OAAO,CAAC3B;EAChC,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4B,qBAAqBA,CAAC7G,OAAO,EAAE;EACtC,IAAI4D,IAAI,GAAG,CAAC,CAAC;;EAEb;EACA;EACA;EACA,IAAI;IACF,IAAInC,IAAI,CAAC,EAAE,CAAC,EAAE;MACZmC,IAAI,GAAG5D,OAAO,CAAC6G,qBAAqB,CAAC,CAAC;MACtC,IAAI/C,SAAS,GAAGV,SAAS,CAACpD,OAAO,EAAE,KAAK,CAAC;MACzC,IAAI+D,UAAU,GAAGX,SAAS,CAACpD,OAAO,EAAE,MAAM,CAAC;MAC3C4D,IAAI,CAACK,GAAG,IAAIH,SAAS;MACrBF,IAAI,CAACO,IAAI,IAAIJ,UAAU;MACvBH,IAAI,CAACM,MAAM,IAAIJ,SAAS;MACxBF,IAAI,CAACQ,KAAK,IAAIL,UAAU;IAC1B,CAAC,MAAM;MACLH,IAAI,GAAG5D,OAAO,CAAC6G,qBAAqB,CAAC,CAAC;IACxC;EACF,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;EAEb,IAAIC,MAAM,GAAG;IACX5C,IAAI,EAAEP,IAAI,CAACO,IAAI;IACfF,GAAG,EAAEL,IAAI,CAACK,GAAG;IACbiB,KAAK,EAAEtB,IAAI,CAACQ,KAAK,GAAGR,IAAI,CAACO,IAAI;IAC7Bc,MAAM,EAAErB,IAAI,CAACM,MAAM,GAAGN,IAAI,CAACK;EAC7B,CAAC;;EAED;EACA,IAAI+C,KAAK,GAAGhH,OAAO,CAACQ,QAAQ,KAAK,MAAM,GAAGwE,cAAc,CAAChF,OAAO,CAACG,aAAa,CAAC,GAAG,CAAC,CAAC;EACpF,IAAI+E,KAAK,GAAG8B,KAAK,CAAC9B,KAAK,IAAIlF,OAAO,CAACiH,WAAW,IAAIF,MAAM,CAAC7B,KAAK;EAC9D,IAAID,MAAM,GAAG+B,KAAK,CAAC/B,MAAM,IAAIjF,OAAO,CAACkH,YAAY,IAAIH,MAAM,CAAC9B,MAAM;EAElE,IAAIkC,cAAc,GAAGnH,OAAO,CAACoH,WAAW,GAAGlC,KAAK;EAChD,IAAImC,aAAa,GAAGrH,OAAO,CAACsH,YAAY,GAAGrC,MAAM;;EAEjD;EACA;EACA,IAAIkC,cAAc,IAAIE,aAAa,EAAE;IACnC,IAAI/C,MAAM,GAAGvE,wBAAwB,CAACC,OAAO,CAAC;IAC9CmH,cAAc,IAAI9C,cAAc,CAACC,MAAM,EAAE,GAAG,CAAC;IAC7C+C,aAAa,IAAIhD,cAAc,CAACC,MAAM,EAAE,GAAG,CAAC;IAE5CyC,MAAM,CAAC7B,KAAK,IAAIiC,cAAc;IAC9BJ,MAAM,CAAC9B,MAAM,IAAIoC,aAAa;EAChC;EAEA,OAAOV,aAAa,CAACI,MAAM,CAAC;AAC9B;AAEA,SAASQ,oCAAoCA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EAC9D,IAAIC,aAAa,GAAGpE,SAAS,CAAC1E,MAAM,GAAG,CAAC,IAAI0E,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAE7F,IAAI9B,MAAM,GAAGC,IAAI,CAAC,EAAE,CAAC;EACrB,IAAIkG,MAAM,GAAGF,MAAM,CAACjH,QAAQ,KAAK,MAAM;EACvC,IAAIoH,YAAY,GAAGf,qBAAqB,CAACW,QAAQ,CAAC;EAClD,IAAIK,UAAU,GAAGhB,qBAAqB,CAACY,MAAM,CAAC;EAC9C,IAAIK,YAAY,GAAGnH,eAAe,CAAC6G,QAAQ,CAAC;EAE5C,IAAIlD,MAAM,GAAGvE,wBAAwB,CAAC0H,MAAM,CAAC;EAC7C,IAAIM,cAAc,GAAGrD,UAAU,CAACJ,MAAM,CAACyD,cAAc,CAAC;EACtD,IAAIC,eAAe,GAAGtD,UAAU,CAACJ,MAAM,CAAC0D,eAAe,CAAC;;EAExD;EACA,IAAIN,aAAa,IAAIC,MAAM,EAAE;IAC3BE,UAAU,CAAC5D,GAAG,GAAGY,IAAI,CAACC,GAAG,CAAC+C,UAAU,CAAC5D,GAAG,EAAE,CAAC,CAAC;IAC5C4D,UAAU,CAAC1D,IAAI,GAAGU,IAAI,CAACC,GAAG,CAAC+C,UAAU,CAAC1D,IAAI,EAAE,CAAC,CAAC;EAChD;EACA,IAAIyC,OAAO,GAAGD,aAAa,CAAC;IAC1B1C,GAAG,EAAE2D,YAAY,CAAC3D,GAAG,GAAG4D,UAAU,CAAC5D,GAAG,GAAG8D,cAAc;IACvD5D,IAAI,EAAEyD,YAAY,CAACzD,IAAI,GAAG0D,UAAU,CAAC1D,IAAI,GAAG6D,eAAe;IAC3D9C,KAAK,EAAE0C,YAAY,CAAC1C,KAAK;IACzBD,MAAM,EAAE2C,YAAY,CAAC3C;EACvB,CAAC,CAAC;EACF2B,OAAO,CAACqB,SAAS,GAAG,CAAC;EACrBrB,OAAO,CAACsB,UAAU,GAAG,CAAC;;EAEtB;EACA;EACA;EACA;EACA,IAAI,CAAC1G,MAAM,IAAImG,MAAM,EAAE;IACrB,IAAIM,SAAS,GAAGvD,UAAU,CAACJ,MAAM,CAAC2D,SAAS,CAAC;IAC5C,IAAIC,UAAU,GAAGxD,UAAU,CAACJ,MAAM,CAAC4D,UAAU,CAAC;IAE9CtB,OAAO,CAAC3C,GAAG,IAAI8D,cAAc,GAAGE,SAAS;IACzCrB,OAAO,CAAC1C,MAAM,IAAI6D,cAAc,GAAGE,SAAS;IAC5CrB,OAAO,CAACzC,IAAI,IAAI6D,eAAe,GAAGE,UAAU;IAC5CtB,OAAO,CAACxC,KAAK,IAAI4D,eAAe,GAAGE,UAAU;;IAE7C;IACAtB,OAAO,CAACqB,SAAS,GAAGA,SAAS;IAC7BrB,OAAO,CAACsB,UAAU,GAAGA,UAAU;EACjC;EAEA,IAAI1G,MAAM,IAAI,CAACkG,aAAa,GAAGD,MAAM,CAACvE,QAAQ,CAAC4E,YAAY,CAAC,GAAGL,MAAM,KAAKK,YAAY,IAAIA,YAAY,CAACtH,QAAQ,KAAK,MAAM,EAAE;IAC1HoG,OAAO,GAAGjD,aAAa,CAACiD,OAAO,EAAEa,MAAM,CAAC;EAC1C;EAEA,OAAOb,OAAO;AAChB;AAEA,SAASuB,6CAA6CA,CAACnI,OAAO,EAAE;EAC9D,IAAIoI,aAAa,GAAG9E,SAAS,CAAC1E,MAAM,GAAG,CAAC,IAAI0E,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAE7F,IAAIG,IAAI,GAAGzD,OAAO,CAACG,aAAa,CAACyB,eAAe;EAChD,IAAIyG,cAAc,GAAGd,oCAAoC,CAACvH,OAAO,EAAEyD,IAAI,CAAC;EACxE,IAAIyB,KAAK,GAAGL,IAAI,CAACC,GAAG,CAACrB,IAAI,CAACwD,WAAW,EAAE3I,MAAM,CAACgK,UAAU,IAAI,CAAC,CAAC;EAC9D,IAAIrD,MAAM,GAAGJ,IAAI,CAACC,GAAG,CAACrB,IAAI,CAACyD,YAAY,EAAE5I,MAAM,CAACiK,WAAW,IAAI,CAAC,CAAC;EAEjE,IAAIzE,SAAS,GAAG,CAACsE,aAAa,GAAGhF,SAAS,CAACK,IAAI,CAAC,GAAG,CAAC;EACpD,IAAIM,UAAU,GAAG,CAACqE,aAAa,GAAGhF,SAAS,CAACK,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC;EAE7D,IAAI+E,MAAM,GAAG;IACXvE,GAAG,EAAEH,SAAS,GAAGuE,cAAc,CAACpE,GAAG,GAAGoE,cAAc,CAACJ,SAAS;IAC9D9D,IAAI,EAAEJ,UAAU,GAAGsE,cAAc,CAAClE,IAAI,GAAGkE,cAAc,CAACH,UAAU;IAClEhD,KAAK,EAAEA,KAAK;IACZD,MAAM,EAAEA;EACV,CAAC;EAED,OAAO0B,aAAa,CAAC6B,MAAM,CAAC;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACzI,OAAO,EAAE;EACxB,IAAIQ,QAAQ,GAAGR,OAAO,CAACQ,QAAQ;EAC/B,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,MAAM,EAAE;IAC9C,OAAO,KAAK;EACd;EACA,IAAIT,wBAAwB,CAACC,OAAO,EAAE,UAAU,CAAC,KAAK,OAAO,EAAE;IAC7D,OAAO,IAAI;EACb;EACA,IAAIS,UAAU,GAAGF,aAAa,CAACP,OAAO,CAAC;EACvC,IAAI,CAACS,UAAU,EAAE;IACf,OAAO,KAAK;EACd;EACA,OAAOgI,OAAO,CAAChI,UAAU,CAAC;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASiI,4BAA4BA,CAAC1I,OAAO,EAAE;EAC7C;EACA,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAAC2I,aAAa,IAAIlH,IAAI,CAAC,CAAC,EAAE;IAChD,OAAOlD,QAAQ,CAACqD,eAAe;EACjC;EACA,IAAIgH,EAAE,GAAG5I,OAAO,CAAC2I,aAAa;EAC9B,OAAOC,EAAE,IAAI7I,wBAAwB,CAAC6I,EAAE,EAAE,WAAW,CAAC,KAAK,MAAM,EAAE;IACjEA,EAAE,GAAGA,EAAE,CAACD,aAAa;EACvB;EACA,OAAOC,EAAE,IAAIrK,QAAQ,CAACqD,eAAe;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiH,aAAaA,CAACC,MAAM,EAAE3H,SAAS,EAAE4H,OAAO,EAAEC,iBAAiB,EAAE;EACpE,IAAItB,aAAa,GAAGpE,SAAS,CAAC1E,MAAM,GAAG,CAAC,IAAI0E,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;;EAE7F;;EAEA,IAAI2F,UAAU,GAAG;IAAEhF,GAAG,EAAE,CAAC;IAAEE,IAAI,EAAE;EAAE,CAAC;EACpC,IAAIrC,YAAY,GAAG4F,aAAa,GAAGgB,4BAA4B,CAACI,MAAM,CAAC,GAAG1G,sBAAsB,CAAC0G,MAAM,EAAE5H,gBAAgB,CAACC,SAAS,CAAC,CAAC;;EAErI;EACA,IAAI6H,iBAAiB,KAAK,UAAU,EAAE;IACpCC,UAAU,GAAGd,6CAA6C,CAACrG,YAAY,EAAE4F,aAAa,CAAC;EACzF,CAAC,MAAM;IACL;IACA,IAAIwB,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAIF,iBAAiB,KAAK,cAAc,EAAE;MACxCE,cAAc,GAAGvI,eAAe,CAACJ,aAAa,CAACY,SAAS,CAAC,CAAC;MAC1D,IAAI+H,cAAc,CAAC1I,QAAQ,KAAK,MAAM,EAAE;QACtC0I,cAAc,GAAGJ,MAAM,CAAC3I,aAAa,CAACyB,eAAe;MACvD;IACF,CAAC,MAAM,IAAIoH,iBAAiB,KAAK,QAAQ,EAAE;MACzCE,cAAc,GAAGJ,MAAM,CAAC3I,aAAa,CAACyB,eAAe;IACvD,CAAC,MAAM;MACLsH,cAAc,GAAGF,iBAAiB;IACpC;IAEA,IAAIpC,OAAO,GAAGW,oCAAoC,CAAC2B,cAAc,EAAEpH,YAAY,EAAE4F,aAAa,CAAC;;IAE/F;IACA,IAAIwB,cAAc,CAAC1I,QAAQ,KAAK,MAAM,IAAI,CAACiI,OAAO,CAAC3G,YAAY,CAAC,EAAE;MAChE,IAAIqH,eAAe,GAAGnE,cAAc,CAAC8D,MAAM,CAAC3I,aAAa,CAAC;QACtD8E,MAAM,GAAGkE,eAAe,CAAClE,MAAM;QAC/BC,KAAK,GAAGiE,eAAe,CAACjE,KAAK;MAEjC+D,UAAU,CAAChF,GAAG,IAAI2C,OAAO,CAAC3C,GAAG,GAAG2C,OAAO,CAACqB,SAAS;MACjDgB,UAAU,CAAC/E,MAAM,GAAGe,MAAM,GAAG2B,OAAO,CAAC3C,GAAG;MACxCgF,UAAU,CAAC9E,IAAI,IAAIyC,OAAO,CAACzC,IAAI,GAAGyC,OAAO,CAACsB,UAAU;MACpDe,UAAU,CAAC7E,KAAK,GAAGc,KAAK,GAAG0B,OAAO,CAACzC,IAAI;IACzC,CAAC,MAAM;MACL;MACA8E,UAAU,GAAGrC,OAAO;IACtB;EACF;;EAEA;EACAmC,OAAO,GAAGA,OAAO,IAAI,CAAC;EACtB,IAAIK,eAAe,GAAG,OAAOL,OAAO,KAAK,QAAQ;EACjDE,UAAU,CAAC9E,IAAI,IAAIiF,eAAe,GAAGL,OAAO,GAAGA,OAAO,CAAC5E,IAAI,IAAI,CAAC;EAChE8E,UAAU,CAAChF,GAAG,IAAImF,eAAe,GAAGL,OAAO,GAAGA,OAAO,CAAC9E,GAAG,IAAI,CAAC;EAC9DgF,UAAU,CAAC7E,KAAK,IAAIgF,eAAe,GAAGL,OAAO,GAAGA,OAAO,CAAC3E,KAAK,IAAI,CAAC;EAClE6E,UAAU,CAAC/E,MAAM,IAAIkF,eAAe,GAAGL,OAAO,GAAGA,OAAO,CAAC7E,MAAM,IAAI,CAAC;EAEpE,OAAO+E,UAAU;AACnB;AAEA,SAASI,OAAOA,CAACC,IAAI,EAAE;EACrB,IAAIpE,KAAK,GAAGoE,IAAI,CAACpE,KAAK;IAClBD,MAAM,GAAGqE,IAAI,CAACrE,MAAM;EAExB,OAAOC,KAAK,GAAGD,MAAM;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsE,oBAAoBA,CAACC,SAAS,EAAEC,OAAO,EAAEX,MAAM,EAAE3H,SAAS,EAAE6H,iBAAiB,EAAE;EACtF,IAAID,OAAO,GAAGzF,SAAS,CAAC1E,MAAM,GAAG,CAAC,IAAI0E,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAEnF,IAAIkG,SAAS,CAAC1K,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;IACpC,OAAO0K,SAAS;EAClB;EAEA,IAAIP,UAAU,GAAGJ,aAAa,CAACC,MAAM,EAAE3H,SAAS,EAAE4H,OAAO,EAAEC,iBAAiB,CAAC;EAE7E,IAAIU,KAAK,GAAG;IACVzF,GAAG,EAAE;MACHiB,KAAK,EAAE+D,UAAU,CAAC/D,KAAK;MACvBD,MAAM,EAAEwE,OAAO,CAACxF,GAAG,GAAGgF,UAAU,CAAChF;IACnC,CAAC;IACDG,KAAK,EAAE;MACLc,KAAK,EAAE+D,UAAU,CAAC7E,KAAK,GAAGqF,OAAO,CAACrF,KAAK;MACvCa,MAAM,EAAEgE,UAAU,CAAChE;IACrB,CAAC;IACDf,MAAM,EAAE;MACNgB,KAAK,EAAE+D,UAAU,CAAC/D,KAAK;MACvBD,MAAM,EAAEgE,UAAU,CAAC/E,MAAM,GAAGuF,OAAO,CAACvF;IACtC,CAAC;IACDC,IAAI,EAAE;MACJe,KAAK,EAAEuE,OAAO,CAACtF,IAAI,GAAG8E,UAAU,CAAC9E,IAAI;MACrCc,MAAM,EAAEgE,UAAU,CAAChE;IACrB;EACF,CAAC;EAED,IAAI0E,WAAW,GAAG5D,MAAM,CAAC6D,IAAI,CAACF,KAAK,CAAC,CAACG,GAAG,CAAC,UAAU5D,GAAG,EAAE;IACtD,OAAOM,QAAQ,CAAC;MACdN,GAAG,EAAEA;IACP,CAAC,EAAEyD,KAAK,CAACzD,GAAG,CAAC,EAAE;MACb6D,IAAI,EAAET,OAAO,CAACK,KAAK,CAACzD,GAAG,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC8D,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACtB,OAAOA,CAAC,CAACH,IAAI,GAAGE,CAAC,CAACF,IAAI;EACxB,CAAC,CAAC;EAEF,IAAII,aAAa,GAAGP,WAAW,CAACQ,MAAM,CAAC,UAAUC,KAAK,EAAE;IACtD,IAAIlF,KAAK,GAAGkF,KAAK,CAAClF,KAAK;MACnBD,MAAM,GAAGmF,KAAK,CAACnF,MAAM;IACzB,OAAOC,KAAK,IAAI4D,MAAM,CAAC7B,WAAW,IAAIhC,MAAM,IAAI6D,MAAM,CAAC5B,YAAY;EACrE,CAAC,CAAC;EAEF,IAAImD,iBAAiB,GAAGH,aAAa,CAACtL,MAAM,GAAG,CAAC,GAAGsL,aAAa,CAAC,CAAC,CAAC,CAACjE,GAAG,GAAG0D,WAAW,CAAC,CAAC,CAAC,CAAC1D,GAAG;EAE5F,IAAIqE,SAAS,GAAGd,SAAS,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAEvC,OAAOF,iBAAiB,IAAIC,SAAS,GAAG,GAAG,GAAGA,SAAS,GAAG,EAAE,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,mBAAmBA,CAACC,KAAK,EAAE3B,MAAM,EAAE3H,SAAS,EAAE;EACrD,IAAIuG,aAAa,GAAGpE,SAAS,CAAC1E,MAAM,GAAG,CAAC,IAAI0E,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAE5F,IAAIoH,kBAAkB,GAAGhD,aAAa,GAAGgB,4BAA4B,CAACI,MAAM,CAAC,GAAG1G,sBAAsB,CAAC0G,MAAM,EAAE5H,gBAAgB,CAACC,SAAS,CAAC,CAAC;EAC3I,OAAOoG,oCAAoC,CAACpG,SAAS,EAAEuJ,kBAAkB,EAAEhD,aAAa,CAAC;AAC3F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiD,aAAaA,CAAC3K,OAAO,EAAE;EAC9B,IAAI1B,MAAM,GAAG0B,OAAO,CAACG,aAAa,CAACC,WAAW;EAC9C,IAAIkE,MAAM,GAAGhG,MAAM,CAACgC,gBAAgB,CAACN,OAAO,CAAC;EAC7C,IAAI4K,CAAC,GAAGlG,UAAU,CAACJ,MAAM,CAAC2D,SAAS,IAAI,CAAC,CAAC,GAAGvD,UAAU,CAACJ,MAAM,CAACuG,YAAY,IAAI,CAAC,CAAC;EAChF,IAAIC,CAAC,GAAGpG,UAAU,CAACJ,MAAM,CAAC4D,UAAU,IAAI,CAAC,CAAC,GAAGxD,UAAU,CAACJ,MAAM,CAACyG,WAAW,IAAI,CAAC,CAAC;EAChF,IAAIhE,MAAM,GAAG;IACX7B,KAAK,EAAElF,OAAO,CAACoH,WAAW,GAAG0D,CAAC;IAC9B7F,MAAM,EAAEjF,OAAO,CAACsH,YAAY,GAAGsD;EACjC,CAAC;EACD,OAAO7D,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiE,oBAAoBA,CAACxB,SAAS,EAAE;EACvC,IAAIyB,IAAI,GAAG;IAAE9G,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE,MAAM;IAAEF,MAAM,EAAE,KAAK;IAAED,GAAG,EAAE;EAAS,CAAC;EACzE,OAAOuF,SAAS,CAAC0B,OAAO,CAAC,wBAAwB,EAAE,UAAUC,OAAO,EAAE;IACpE,OAAOF,IAAI,CAACE,OAAO,CAAC;EACtB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACtC,MAAM,EAAEuC,gBAAgB,EAAE7B,SAAS,EAAE;EAC7DA,SAAS,GAAGA,SAAS,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEnC;EACA,IAAIe,UAAU,GAAGX,aAAa,CAAC7B,MAAM,CAAC;;EAEtC;EACA,IAAIyC,aAAa,GAAG;IAClBrG,KAAK,EAAEoG,UAAU,CAACpG,KAAK;IACvBD,MAAM,EAAEqG,UAAU,CAACrG;EACrB,CAAC;;EAED;EACA,IAAIuG,OAAO,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC1M,OAAO,CAAC0K,SAAS,CAAC,KAAK,CAAC,CAAC;EACzD,IAAIiC,QAAQ,GAAGD,OAAO,GAAG,KAAK,GAAG,MAAM;EACvC,IAAIE,aAAa,GAAGF,OAAO,GAAG,MAAM,GAAG,KAAK;EAC5C,IAAIG,WAAW,GAAGH,OAAO,GAAG,QAAQ,GAAG,OAAO;EAC9C,IAAII,oBAAoB,GAAG,CAACJ,OAAO,GAAG,QAAQ,GAAG,OAAO;EAExDD,aAAa,CAACE,QAAQ,CAAC,GAAGJ,gBAAgB,CAACI,QAAQ,CAAC,GAAGJ,gBAAgB,CAACM,WAAW,CAAC,GAAG,CAAC,GAAGL,UAAU,CAACK,WAAW,CAAC,GAAG,CAAC;EACtH,IAAInC,SAAS,KAAKkC,aAAa,EAAE;IAC/BH,aAAa,CAACG,aAAa,CAAC,GAAGL,gBAAgB,CAACK,aAAa,CAAC,GAAGJ,UAAU,CAACM,oBAAoB,CAAC;EACnG,CAAC,MAAM;IACLL,aAAa,CAACG,aAAa,CAAC,GAAGL,gBAAgB,CAACL,oBAAoB,CAACU,aAAa,CAAC,CAAC;EACtF;EAEA,OAAOH,aAAa;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,IAAIA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACxB;EACA,IAAIC,KAAK,CAAC5F,SAAS,CAACyF,IAAI,EAAE;IACxB,OAAOC,GAAG,CAACD,IAAI,CAACE,KAAK,CAAC;EACxB;;EAEA;EACA,OAAOD,GAAG,CAAC3B,MAAM,CAAC4B,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACH,GAAG,EAAEI,IAAI,EAAE5F,KAAK,EAAE;EACnC;EACA,IAAI0F,KAAK,CAAC5F,SAAS,CAAC6F,SAAS,EAAE;IAC7B,OAAOH,GAAG,CAACG,SAAS,CAAC,UAAUE,GAAG,EAAE;MAClC,OAAOA,GAAG,CAACD,IAAI,CAAC,KAAK5F,KAAK;IAC5B,CAAC,CAAC;EACJ;;EAEA;EACA,IAAI8F,KAAK,GAAGP,IAAI,CAACC,GAAG,EAAE,UAAUzF,GAAG,EAAE;IACnC,OAAOA,GAAG,CAAC6F,IAAI,CAAC,KAAK5F,KAAK;EAC5B,CAAC,CAAC;EACF,OAAOwF,GAAG,CAAChN,OAAO,CAACsN,KAAK,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,SAAS,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC3C,IAAIC,cAAc,GAAGD,IAAI,KAAKjJ,SAAS,GAAG+I,SAAS,GAAGA,SAAS,CAACI,KAAK,CAAC,CAAC,EAAET,SAAS,CAACK,SAAS,EAAE,MAAM,EAAEE,IAAI,CAAC,CAAC;EAE5GC,cAAc,CAACE,OAAO,CAAC,UAAU3I,QAAQ,EAAE;IACzC,IAAIA,QAAQ,CAAC,UAAU,CAAC,EAAE;MACxB;MACA4I,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;IACvE;IACA,IAAI7N,EAAE,GAAGgF,QAAQ,CAAC,UAAU,CAAC,IAAIA,QAAQ,CAAChF,EAAE,CAAC,CAAC;IAC9C,IAAIgF,QAAQ,CAAC8I,OAAO,IAAIpN,UAAU,CAACV,EAAE,CAAC,EAAE;MACtC;MACA;MACA;MACAuN,IAAI,CAAC3F,OAAO,CAACkC,MAAM,GAAGnC,aAAa,CAAC4F,IAAI,CAAC3F,OAAO,CAACkC,MAAM,CAAC;MACxDyD,IAAI,CAAC3F,OAAO,CAACzF,SAAS,GAAGwF,aAAa,CAAC4F,IAAI,CAAC3F,OAAO,CAACzF,SAAS,CAAC;MAE9DoL,IAAI,GAAGvN,EAAE,CAACuN,IAAI,EAAEvI,QAAQ,CAAC;IAC3B;EACF,CAAC,CAAC;EAEF,OAAOuI,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,MAAMA,CAAA,EAAG;EAChB;EACA,IAAI,IAAI,CAACtC,KAAK,CAACuC,WAAW,EAAE;IAC1B;EACF;EAEA,IAAIT,IAAI,GAAG;IACTnH,QAAQ,EAAE,IAAI;IACdd,MAAM,EAAE,CAAC,CAAC;IACV2I,WAAW,EAAE,CAAC,CAAC;IACfC,UAAU,EAAE,CAAC,CAAC;IACdC,OAAO,EAAE,KAAK;IACdvG,OAAO,EAAE,CAAC;EACZ,CAAC;;EAED;EACA2F,IAAI,CAAC3F,OAAO,CAACzF,SAAS,GAAGqJ,mBAAmB,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC3B,MAAM,EAAE,IAAI,CAAC3H,SAAS,EAAE,IAAI,CAACiM,OAAO,CAACC,aAAa,CAAC;;EAEjH;EACA;EACA;EACAd,IAAI,CAAC/C,SAAS,GAAGD,oBAAoB,CAAC,IAAI,CAAC6D,OAAO,CAAC5D,SAAS,EAAE+C,IAAI,CAAC3F,OAAO,CAACzF,SAAS,EAAE,IAAI,CAAC2H,MAAM,EAAE,IAAI,CAAC3H,SAAS,EAAE,IAAI,CAACiM,OAAO,CAACd,SAAS,CAACgB,IAAI,CAACtE,iBAAiB,EAAE,IAAI,CAACoE,OAAO,CAACd,SAAS,CAACgB,IAAI,CAACvE,OAAO,CAAC;;EAEtM;EACAwD,IAAI,CAACgB,iBAAiB,GAAGhB,IAAI,CAAC/C,SAAS;EAEvC+C,IAAI,CAACc,aAAa,GAAG,IAAI,CAACD,OAAO,CAACC,aAAa;;EAE/C;EACAd,IAAI,CAAC3F,OAAO,CAACkC,MAAM,GAAGsC,gBAAgB,CAAC,IAAI,CAACtC,MAAM,EAAEyD,IAAI,CAAC3F,OAAO,CAACzF,SAAS,EAAEoL,IAAI,CAAC/C,SAAS,CAAC;EAE3F+C,IAAI,CAAC3F,OAAO,CAACkC,MAAM,CAAC0E,QAAQ,GAAG,IAAI,CAACJ,OAAO,CAACC,aAAa,GAAG,OAAO,GAAG,UAAU;;EAEhF;EACAd,IAAI,GAAGF,YAAY,CAAC,IAAI,CAACC,SAAS,EAAEC,IAAI,CAAC;;EAEzC;EACA;EACA,IAAI,CAAC,IAAI,CAAC9B,KAAK,CAACgD,SAAS,EAAE;IACzB,IAAI,CAAChD,KAAK,CAACgD,SAAS,GAAG,IAAI;IAC3B,IAAI,CAACL,OAAO,CAACM,QAAQ,CAACnB,IAAI,CAAC;EAC7B,CAAC,MAAM;IACL,IAAI,CAACa,OAAO,CAACO,QAAQ,CAACpB,IAAI,CAAC;EAC7B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,iBAAiBA,CAACtB,SAAS,EAAEuB,YAAY,EAAE;EAClD,OAAOvB,SAAS,CAACwB,IAAI,CAAC,UAAUxE,IAAI,EAAE;IACpC,IAAIyE,IAAI,GAAGzE,IAAI,CAACyE,IAAI;MAChBjB,OAAO,GAAGxD,IAAI,CAACwD,OAAO;IAC1B,OAAOA,OAAO,IAAIiB,IAAI,KAAKF,YAAY;EACzC,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,wBAAwBA,CAAC/N,QAAQ,EAAE;EAC1C,IAAIgO,QAAQ,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC;EAClD,IAAIC,SAAS,GAAGjO,QAAQ,CAACkO,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGnO,QAAQ,CAACyM,KAAK,CAAC,CAAC,CAAC;EAEpE,KAAK,IAAI/N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsP,QAAQ,CAACrP,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,IAAI0P,MAAM,GAAGJ,QAAQ,CAACtP,CAAC,CAAC;IACxB,IAAI2P,OAAO,GAAGD,MAAM,GAAG,EAAE,GAAGA,MAAM,GAAGH,SAAS,GAAGjO,QAAQ;IACzD,IAAI,OAAO1B,QAAQ,CAACqC,IAAI,CAAC2N,KAAK,CAACD,OAAO,CAAC,KAAK,WAAW,EAAE;MACvD,OAAOA,OAAO;IAChB;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASE,OAAOA,CAAA,EAAG;EACjB,IAAI,CAAC/D,KAAK,CAACuC,WAAW,GAAG,IAAI;;EAE7B;EACA,IAAIY,iBAAiB,CAAC,IAAI,CAACtB,SAAS,EAAE,YAAY,CAAC,EAAE;IACnD,IAAI,CAACxD,MAAM,CAAC2F,eAAe,CAAC,aAAa,CAAC;IAC1C,IAAI,CAAC3F,MAAM,CAACyF,KAAK,CAACf,QAAQ,GAAG,EAAE;IAC/B,IAAI,CAAC1E,MAAM,CAACyF,KAAK,CAACtK,GAAG,GAAG,EAAE;IAC1B,IAAI,CAAC6E,MAAM,CAACyF,KAAK,CAACpK,IAAI,GAAG,EAAE;IAC3B,IAAI,CAAC2E,MAAM,CAACyF,KAAK,CAACnK,KAAK,GAAG,EAAE;IAC5B,IAAI,CAAC0E,MAAM,CAACyF,KAAK,CAACrK,MAAM,GAAG,EAAE;IAC7B,IAAI,CAAC4E,MAAM,CAACyF,KAAK,CAACG,UAAU,GAAG,EAAE;IACjC,IAAI,CAAC5F,MAAM,CAACyF,KAAK,CAACP,wBAAwB,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE;EAC/D;EAEA,IAAI,CAACW,qBAAqB,CAAC,CAAC;;EAE5B;EACA;EACA,IAAI,IAAI,CAACvB,OAAO,CAACwB,eAAe,EAAE;IAChC,IAAI,CAAC9F,MAAM,CAACrI,UAAU,CAACoO,WAAW,CAAC,IAAI,CAAC/F,MAAM,CAAC;EACjD;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASgG,SAASA,CAAC9O,OAAO,EAAE;EAC1B,IAAIG,aAAa,GAAGH,OAAO,CAACG,aAAa;EACzC,OAAOA,aAAa,GAAGA,aAAa,CAACC,WAAW,GAAG9B,MAAM;AAC3D;AAEA,SAASyQ,qBAAqBA,CAACjH,YAAY,EAAEkH,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAE;EAC3E,IAAIC,MAAM,GAAGrH,YAAY,CAACtH,QAAQ,KAAK,MAAM;EAC7C,IAAIiF,MAAM,GAAG0J,MAAM,GAAGrH,YAAY,CAAC3H,aAAa,CAACC,WAAW,GAAG0H,YAAY;EAC3ErC,MAAM,CAAC2J,gBAAgB,CAACJ,KAAK,EAAEC,QAAQ,EAAE;IAAEI,OAAO,EAAE;EAAK,CAAC,CAAC;EAE3D,IAAI,CAACF,MAAM,EAAE;IACXJ,qBAAqB,CAACpO,eAAe,CAAC8E,MAAM,CAAChF,UAAU,CAAC,EAAEuO,KAAK,EAAEC,QAAQ,EAAEC,aAAa,CAAC;EAC3F;EACAA,aAAa,CAACI,IAAI,CAAC7J,MAAM,CAAC;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8J,mBAAmBA,CAACpO,SAAS,EAAEiM,OAAO,EAAE3C,KAAK,EAAE+E,WAAW,EAAE;EACnE;EACA/E,KAAK,CAAC+E,WAAW,GAAGA,WAAW;EAC/BV,SAAS,CAAC3N,SAAS,CAAC,CAACiO,gBAAgB,CAAC,QAAQ,EAAE3E,KAAK,CAAC+E,WAAW,EAAE;IAAEH,OAAO,EAAE;EAAK,CAAC,CAAC;;EAErF;EACA,IAAII,aAAa,GAAG9O,eAAe,CAACQ,SAAS,CAAC;EAC9C4N,qBAAqB,CAACU,aAAa,EAAE,QAAQ,EAAEhF,KAAK,CAAC+E,WAAW,EAAE/E,KAAK,CAACyE,aAAa,CAAC;EACtFzE,KAAK,CAACgF,aAAa,GAAGA,aAAa;EACnChF,KAAK,CAACiF,aAAa,GAAG,IAAI;EAE1B,OAAOjF,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkF,oBAAoBA,CAAA,EAAG;EAC9B,IAAI,CAAC,IAAI,CAAClF,KAAK,CAACiF,aAAa,EAAE;IAC7B,IAAI,CAACjF,KAAK,GAAG8E,mBAAmB,CAAC,IAAI,CAACpO,SAAS,EAAE,IAAI,CAACiM,OAAO,EAAE,IAAI,CAAC3C,KAAK,EAAE,IAAI,CAACmF,cAAc,CAAC;EACjG;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAC1O,SAAS,EAAEsJ,KAAK,EAAE;EAC9C;EACAqE,SAAS,CAAC3N,SAAS,CAAC,CAAC2O,mBAAmB,CAAC,QAAQ,EAAErF,KAAK,CAAC+E,WAAW,CAAC;;EAErE;EACA/E,KAAK,CAACyE,aAAa,CAACvC,OAAO,CAAC,UAAUlH,MAAM,EAAE;IAC5CA,MAAM,CAACqK,mBAAmB,CAAC,QAAQ,EAAErF,KAAK,CAAC+E,WAAW,CAAC;EACzD,CAAC,CAAC;;EAEF;EACA/E,KAAK,CAAC+E,WAAW,GAAG,IAAI;EACxB/E,KAAK,CAACyE,aAAa,GAAG,EAAE;EACxBzE,KAAK,CAACgF,aAAa,GAAG,IAAI;EAC1BhF,KAAK,CAACiF,aAAa,GAAG,KAAK;EAC3B,OAAOjF,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkE,qBAAqBA,CAAA,EAAG;EAC/B,IAAI,IAAI,CAAClE,KAAK,CAACiF,aAAa,EAAE;IAC5BK,oBAAoB,CAAC,IAAI,CAACH,cAAc,CAAC;IACzC,IAAI,CAACnF,KAAK,GAAGoF,oBAAoB,CAAC,IAAI,CAAC1O,SAAS,EAAE,IAAI,CAACsJ,KAAK,CAAC;EAC/D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuF,SAASA,CAACC,CAAC,EAAE;EACpB,OAAOA,CAAC,KAAK,EAAE,IAAI,CAACC,KAAK,CAACxL,UAAU,CAACuL,CAAC,CAAC,CAAC,IAAIE,QAAQ,CAACF,CAAC,CAAC;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,SAASA,CAACpQ,OAAO,EAAEsE,MAAM,EAAE;EAClCyB,MAAM,CAAC6D,IAAI,CAACtF,MAAM,CAAC,CAACqI,OAAO,CAAC,UAAUT,IAAI,EAAE;IAC1C,IAAImE,IAAI,GAAG,EAAE;IACb;IACA,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAACvR,OAAO,CAACoN,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI8D,SAAS,CAAC1L,MAAM,CAAC4H,IAAI,CAAC,CAAC,EAAE;MACzGmE,IAAI,GAAG,IAAI;IACb;IACArQ,OAAO,CAACuO,KAAK,CAACrC,IAAI,CAAC,GAAG5H,MAAM,CAAC4H,IAAI,CAAC,GAAGmE,IAAI;EAC3C,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACtQ,OAAO,EAAEkN,UAAU,EAAE;EAC1CnH,MAAM,CAAC6D,IAAI,CAACsD,UAAU,CAAC,CAACP,OAAO,CAAC,UAAUT,IAAI,EAAE;IAC9C,IAAI5F,KAAK,GAAG4G,UAAU,CAAChB,IAAI,CAAC;IAC5B,IAAI5F,KAAK,KAAK,KAAK,EAAE;MACnBtG,OAAO,CAACuQ,YAAY,CAACrE,IAAI,EAAEgB,UAAU,CAAChB,IAAI,CAAC,CAAC;IAC9C,CAAC,MAAM;MACLlM,OAAO,CAACyO,eAAe,CAACvC,IAAI,CAAC;IAC/B;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsE,UAAUA,CAACjE,IAAI,EAAE;EACxB;EACA;EACA;EACA;EACA6D,SAAS,CAAC7D,IAAI,CAACnH,QAAQ,CAAC0D,MAAM,EAAEyD,IAAI,CAACjI,MAAM,CAAC;;EAE5C;EACA;EACAgM,aAAa,CAAC/D,IAAI,CAACnH,QAAQ,CAAC0D,MAAM,EAAEyD,IAAI,CAACW,UAAU,CAAC;;EAEpD;EACA,IAAIX,IAAI,CAACkE,YAAY,IAAI1K,MAAM,CAAC6D,IAAI,CAAC2C,IAAI,CAACU,WAAW,CAAC,CAACrO,MAAM,EAAE;IAC7DwR,SAAS,CAAC7D,IAAI,CAACkE,YAAY,EAAElE,IAAI,CAACU,WAAW,CAAC;EAChD;EAEA,OAAOV,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmE,gBAAgBA,CAACvP,SAAS,EAAE2H,MAAM,EAAEsE,OAAO,EAAEuD,eAAe,EAAElG,KAAK,EAAE;EAC5E;EACA,IAAIY,gBAAgB,GAAGb,mBAAmB,CAACC,KAAK,EAAE3B,MAAM,EAAE3H,SAAS,EAAEiM,OAAO,CAACC,aAAa,CAAC;;EAE3F;EACA;EACA;EACA,IAAI7D,SAAS,GAAGD,oBAAoB,CAAC6D,OAAO,CAAC5D,SAAS,EAAE6B,gBAAgB,EAAEvC,MAAM,EAAE3H,SAAS,EAAEiM,OAAO,CAACd,SAAS,CAACgB,IAAI,CAACtE,iBAAiB,EAAEoE,OAAO,CAACd,SAAS,CAACgB,IAAI,CAACvE,OAAO,CAAC;EAEtKD,MAAM,CAACyH,YAAY,CAAC,aAAa,EAAE/G,SAAS,CAAC;;EAE7C;EACA;EACA4G,SAAS,CAACtH,MAAM,EAAE;IAAE0E,QAAQ,EAAEJ,OAAO,CAACC,aAAa,GAAG,OAAO,GAAG;EAAW,CAAC,CAAC;EAE7E,OAAOD,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwD,iBAAiBA,CAACrE,IAAI,EAAEsE,WAAW,EAAE;EAC5C,IAAIC,aAAa,GAAGvE,IAAI,CAAC3F,OAAO;IAC5BkC,MAAM,GAAGgI,aAAa,CAAChI,MAAM;IAC7B3H,SAAS,GAAG2P,aAAa,CAAC3P,SAAS;EACvC,IAAI4P,KAAK,GAAGlM,IAAI,CAACkM,KAAK;IAClBC,KAAK,GAAGnM,IAAI,CAACmM,KAAK;EAEtB,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,CAAC,EAAE;IAChC,OAAOA,CAAC;EACV,CAAC;EAED,IAAIC,cAAc,GAAGJ,KAAK,CAAC5P,SAAS,CAAC+D,KAAK,CAAC;EAC3C,IAAIkM,WAAW,GAAGL,KAAK,CAACjI,MAAM,CAAC5D,KAAK,CAAC;EAErC,IAAImM,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAACvS,OAAO,CAACyN,IAAI,CAAC/C,SAAS,CAAC,KAAK,CAAC,CAAC;EACjE,IAAI8H,WAAW,GAAG/E,IAAI,CAAC/C,SAAS,CAAC1K,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;EACpD,IAAIyS,eAAe,GAAGJ,cAAc,GAAG,CAAC,KAAKC,WAAW,GAAG,CAAC;EAC5D,IAAII,YAAY,GAAGL,cAAc,GAAG,CAAC,KAAK,CAAC,IAAIC,WAAW,GAAG,CAAC,KAAK,CAAC;EAEpE,IAAIK,mBAAmB,GAAG,CAACZ,WAAW,GAAGI,OAAO,GAAGI,UAAU,IAAIC,WAAW,IAAIC,eAAe,GAAGR,KAAK,GAAGC,KAAK;EAC/G,IAAIU,iBAAiB,GAAG,CAACb,WAAW,GAAGI,OAAO,GAAGF,KAAK;EAEtD,OAAO;IACL5M,IAAI,EAAEsN,mBAAmB,CAACD,YAAY,IAAI,CAACF,WAAW,IAAIT,WAAW,GAAG/H,MAAM,CAAC3E,IAAI,GAAG,CAAC,GAAG2E,MAAM,CAAC3E,IAAI,CAAC;IACtGF,GAAG,EAAEyN,iBAAiB,CAAC5I,MAAM,CAAC7E,GAAG,CAAC;IAClCC,MAAM,EAAEwN,iBAAiB,CAAC5I,MAAM,CAAC5E,MAAM,CAAC;IACxCE,KAAK,EAAEqN,mBAAmB,CAAC3I,MAAM,CAAC1E,KAAK;EACzC,CAAC;AACH;AAEA,IAAIuN,SAAS,GAAGtT,SAAS,IAAI,UAAU,CAAC4C,IAAI,CAACzC,SAAS,CAACK,SAAS,CAAC;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+S,YAAYA,CAACrF,IAAI,EAAEa,OAAO,EAAE;EACnC,IAAIxC,CAAC,GAAGwC,OAAO,CAACxC,CAAC;IACbE,CAAC,GAAGsC,OAAO,CAACtC,CAAC;EACjB,IAAIhC,MAAM,GAAGyD,IAAI,CAAC3F,OAAO,CAACkC,MAAM;;EAEhC;;EAEA,IAAI+I,2BAA2B,GAAGhG,IAAI,CAACU,IAAI,CAACnH,QAAQ,CAACkH,SAAS,EAAE,UAAUtI,QAAQ,EAAE;IAClF,OAAOA,QAAQ,CAAC+J,IAAI,KAAK,YAAY;EACvC,CAAC,CAAC,CAAC+D,eAAe;EAClB,IAAID,2BAA2B,KAAKtO,SAAS,EAAE;IAC7CqJ,OAAO,CAACC,IAAI,CAAC,+HAA+H,CAAC;EAC/I;EACA,IAAIiF,eAAe,GAAGD,2BAA2B,KAAKtO,SAAS,GAAGsO,2BAA2B,GAAGzE,OAAO,CAAC0E,eAAe;EAEvH,IAAIhQ,YAAY,GAAGH,eAAe,CAAC4K,IAAI,CAACnH,QAAQ,CAAC0D,MAAM,CAAC;EACxD,IAAIiJ,gBAAgB,GAAGlL,qBAAqB,CAAC/E,YAAY,CAAC;;EAE1D;EACA,IAAIwC,MAAM,GAAG;IACXkJ,QAAQ,EAAE1E,MAAM,CAAC0E;EACnB,CAAC;EAED,IAAI5G,OAAO,GAAGgK,iBAAiB,CAACrE,IAAI,EAAEjO,MAAM,CAAC0T,gBAAgB,GAAG,CAAC,IAAI,CAACL,SAAS,CAAC;EAEhF,IAAInN,KAAK,GAAGoG,CAAC,KAAK,QAAQ,GAAG,KAAK,GAAG,QAAQ;EAC7C,IAAInG,KAAK,GAAGqG,CAAC,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;;EAE5C;EACA;EACA;EACA,IAAImH,gBAAgB,GAAGjE,wBAAwB,CAAC,WAAW,CAAC;;EAE5D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI7J,IAAI,GAAG,KAAK,CAAC;IACbF,GAAG,GAAG,KAAK,CAAC;EAChB,IAAIO,KAAK,KAAK,QAAQ,EAAE;IACtB;IACA;IACA,IAAI1C,YAAY,CAACtB,QAAQ,KAAK,MAAM,EAAE;MACpCyD,GAAG,GAAG,CAACnC,YAAY,CAACoF,YAAY,GAAGN,OAAO,CAAC1C,MAAM;IACnD,CAAC,MAAM;MACLD,GAAG,GAAG,CAAC8N,gBAAgB,CAAC9M,MAAM,GAAG2B,OAAO,CAAC1C,MAAM;IACjD;EACF,CAAC,MAAM;IACLD,GAAG,GAAG2C,OAAO,CAAC3C,GAAG;EACnB;EACA,IAAIQ,KAAK,KAAK,OAAO,EAAE;IACrB,IAAI3C,YAAY,CAACtB,QAAQ,KAAK,MAAM,EAAE;MACpC2D,IAAI,GAAG,CAACrC,YAAY,CAACmF,WAAW,GAAGL,OAAO,CAACxC,KAAK;IAClD,CAAC,MAAM;MACLD,IAAI,GAAG,CAAC4N,gBAAgB,CAAC7M,KAAK,GAAG0B,OAAO,CAACxC,KAAK;IAChD;EACF,CAAC,MAAM;IACLD,IAAI,GAAGyC,OAAO,CAACzC,IAAI;EACrB;EACA,IAAI2N,eAAe,IAAIG,gBAAgB,EAAE;IACvC3N,MAAM,CAAC2N,gBAAgB,CAAC,GAAG,cAAc,GAAG9N,IAAI,GAAG,MAAM,GAAGF,GAAG,GAAG,QAAQ;IAC1EK,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;IACjBF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC;IACjBH,MAAM,CAACoK,UAAU,GAAG,WAAW;EACjC,CAAC,MAAM;IACL;IACA,IAAIwD,SAAS,GAAG1N,KAAK,KAAK,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;IAC3C,IAAI2N,UAAU,GAAG1N,KAAK,KAAK,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC;IAC3CH,MAAM,CAACE,KAAK,CAAC,GAAGP,GAAG,GAAGiO,SAAS;IAC/B5N,MAAM,CAACG,KAAK,CAAC,GAAGN,IAAI,GAAGgO,UAAU;IACjC7N,MAAM,CAACoK,UAAU,GAAGlK,KAAK,GAAG,IAAI,GAAGC,KAAK;EAC1C;;EAEA;EACA,IAAIyI,UAAU,GAAG;IACf,aAAa,EAAEX,IAAI,CAAC/C;EACtB,CAAC;;EAED;EACA+C,IAAI,CAACW,UAAU,GAAG3G,QAAQ,CAAC,CAAC,CAAC,EAAE2G,UAAU,EAAEX,IAAI,CAACW,UAAU,CAAC;EAC3DX,IAAI,CAACjI,MAAM,GAAGiC,QAAQ,CAAC,CAAC,CAAC,EAAEjC,MAAM,EAAEiI,IAAI,CAACjI,MAAM,CAAC;EAC/CiI,IAAI,CAACU,WAAW,GAAG1G,QAAQ,CAAC,CAAC,CAAC,EAAEgG,IAAI,CAAC3F,OAAO,CAACwL,KAAK,EAAE7F,IAAI,CAACU,WAAW,CAAC;EAErE,OAAOV,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8F,kBAAkBA,CAAC/F,SAAS,EAAEgG,cAAc,EAAEC,aAAa,EAAE;EACpE,IAAIC,UAAU,GAAG3G,IAAI,CAACS,SAAS,EAAE,UAAUhD,IAAI,EAAE;IAC/C,IAAIyE,IAAI,GAAGzE,IAAI,CAACyE,IAAI;IACpB,OAAOA,IAAI,KAAKuE,cAAc;EAChC,CAAC,CAAC;EAEF,IAAIG,UAAU,GAAG,CAAC,CAACD,UAAU,IAAIlG,SAAS,CAACwB,IAAI,CAAC,UAAU9J,QAAQ,EAAE;IAClE,OAAOA,QAAQ,CAAC+J,IAAI,KAAKwE,aAAa,IAAIvO,QAAQ,CAAC8I,OAAO,IAAI9I,QAAQ,CAACzB,KAAK,GAAGiQ,UAAU,CAACjQ,KAAK;EACjG,CAAC,CAAC;EAEF,IAAI,CAACkQ,UAAU,EAAE;IACf,IAAIC,WAAW,GAAG,GAAG,GAAGJ,cAAc,GAAG,GAAG;IAC5C,IAAIK,SAAS,GAAG,GAAG,GAAGJ,aAAa,GAAG,GAAG;IACzC3F,OAAO,CAACC,IAAI,CAAC8F,SAAS,GAAG,2BAA2B,GAAGD,WAAW,GAAG,2DAA2D,GAAGA,WAAW,GAAG,GAAG,CAAC;EACvJ;EACA,OAAOD,UAAU;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,KAAKA,CAAC7F,IAAI,EAAEa,OAAO,EAAE;EAC5B,IAAIwF,mBAAmB;;EAEvB;EACA,IAAI,CAACP,kBAAkB,CAAC9F,IAAI,CAACnH,QAAQ,CAACkH,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,EAAE;IACzE,OAAOC,IAAI;EACb;EAEA,IAAIkE,YAAY,GAAGrD,OAAO,CAACpN,OAAO;;EAElC;EACA,IAAI,OAAOyQ,YAAY,KAAK,QAAQ,EAAE;IACpCA,YAAY,GAAGlE,IAAI,CAACnH,QAAQ,CAAC0D,MAAM,CAAC+J,aAAa,CAACpC,YAAY,CAAC;;IAE/D;IACA,IAAI,CAACA,YAAY,EAAE;MACjB,OAAOlE,IAAI;IACb;EACF,CAAC,MAAM;IACL;IACA;IACA,IAAI,CAACA,IAAI,CAACnH,QAAQ,CAAC0D,MAAM,CAAC5F,QAAQ,CAACuN,YAAY,CAAC,EAAE;MAChD7D,OAAO,CAACC,IAAI,CAAC,+DAA+D,CAAC;MAC7E,OAAON,IAAI;IACb;EACF;EAEA,IAAI/C,SAAS,GAAG+C,IAAI,CAAC/C,SAAS,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5C,IAAIuG,aAAa,GAAGvE,IAAI,CAAC3F,OAAO;IAC5BkC,MAAM,GAAGgI,aAAa,CAAChI,MAAM;IAC7B3H,SAAS,GAAG2P,aAAa,CAAC3P,SAAS;EAEvC,IAAIkQ,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAACvS,OAAO,CAAC0K,SAAS,CAAC,KAAK,CAAC,CAAC;EAE5D,IAAIsJ,GAAG,GAAGzB,UAAU,GAAG,QAAQ,GAAG,OAAO;EACzC,IAAI0B,eAAe,GAAG1B,UAAU,GAAG,KAAK,GAAG,MAAM;EACjD,IAAIhO,IAAI,GAAG0P,eAAe,CAACC,WAAW,CAAC,CAAC;EACxC,IAAIC,OAAO,GAAG5B,UAAU,GAAG,MAAM,GAAG,KAAK;EACzC,IAAI6B,MAAM,GAAG7B,UAAU,GAAG,QAAQ,GAAG,OAAO;EAC5C,IAAI8B,gBAAgB,GAAGxI,aAAa,CAAC8F,YAAY,CAAC,CAACqC,GAAG,CAAC;;EAEvD;EACA;EACA;EACA;;EAEA;EACA,IAAI3R,SAAS,CAAC+R,MAAM,CAAC,GAAGC,gBAAgB,GAAGrK,MAAM,CAACzF,IAAI,CAAC,EAAE;IACvDkJ,IAAI,CAAC3F,OAAO,CAACkC,MAAM,CAACzF,IAAI,CAAC,IAAIyF,MAAM,CAACzF,IAAI,CAAC,IAAIlC,SAAS,CAAC+R,MAAM,CAAC,GAAGC,gBAAgB,CAAC;EACpF;EACA;EACA,IAAIhS,SAAS,CAACkC,IAAI,CAAC,GAAG8P,gBAAgB,GAAGrK,MAAM,CAACoK,MAAM,CAAC,EAAE;IACvD3G,IAAI,CAAC3F,OAAO,CAACkC,MAAM,CAACzF,IAAI,CAAC,IAAIlC,SAAS,CAACkC,IAAI,CAAC,GAAG8P,gBAAgB,GAAGrK,MAAM,CAACoK,MAAM,CAAC;EAClF;EACA3G,IAAI,CAAC3F,OAAO,CAACkC,MAAM,GAAGnC,aAAa,CAAC4F,IAAI,CAAC3F,OAAO,CAACkC,MAAM,CAAC;;EAExD;EACA,IAAIsK,MAAM,GAAGjS,SAAS,CAACkC,IAAI,CAAC,GAAGlC,SAAS,CAAC2R,GAAG,CAAC,GAAG,CAAC,GAAGK,gBAAgB,GAAG,CAAC;;EAExE;EACA;EACA,IAAI9S,GAAG,GAAGN,wBAAwB,CAACwM,IAAI,CAACnH,QAAQ,CAAC0D,MAAM,CAAC;EACxD,IAAIuK,gBAAgB,GAAG3O,UAAU,CAACrE,GAAG,CAAC,QAAQ,GAAG0S,eAAe,CAAC,CAAC;EAClE,IAAIO,gBAAgB,GAAG5O,UAAU,CAACrE,GAAG,CAAC,QAAQ,GAAG0S,eAAe,GAAG,OAAO,CAAC,CAAC;EAC5E,IAAIQ,SAAS,GAAGH,MAAM,GAAG7G,IAAI,CAAC3F,OAAO,CAACkC,MAAM,CAACzF,IAAI,CAAC,GAAGgQ,gBAAgB,GAAGC,gBAAgB;;EAExF;EACAC,SAAS,GAAG1O,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC2O,GAAG,CAAC1K,MAAM,CAACgK,GAAG,CAAC,GAAGK,gBAAgB,EAAEI,SAAS,CAAC,EAAE,CAAC,CAAC;EAE5EhH,IAAI,CAACkE,YAAY,GAAGA,YAAY;EAChClE,IAAI,CAAC3F,OAAO,CAACwL,KAAK,IAAIQ,mBAAmB,GAAG,CAAC,CAAC,EAAE5M,cAAc,CAAC4M,mBAAmB,EAAEvP,IAAI,EAAEwB,IAAI,CAACkM,KAAK,CAACwC,SAAS,CAAC,CAAC,EAAEvN,cAAc,CAAC4M,mBAAmB,EAAEK,OAAO,EAAE,EAAE,CAAC,EAAEL,mBAAmB,CAAC;EAExL,OAAOrG,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkH,oBAAoBA,CAACnJ,SAAS,EAAE;EACvC,IAAIA,SAAS,KAAK,KAAK,EAAE;IACvB,OAAO,OAAO;EAChB,CAAC,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;IAChC,OAAO,KAAK;EACd;EACA,OAAOA,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIoJ,UAAU,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,CAAC;;AAEjM;AACA,IAAIC,eAAe,GAAGD,UAAU,CAAChH,KAAK,CAAC,CAAC,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkH,SAASA,CAACpK,SAAS,EAAE;EAC5B,IAAIqK,OAAO,GAAGvQ,SAAS,CAAC1E,MAAM,GAAG,CAAC,IAAI0E,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAEvF,IAAIwQ,KAAK,GAAGH,eAAe,CAAC7U,OAAO,CAAC0K,SAAS,CAAC;EAC9C,IAAIsC,GAAG,GAAG6H,eAAe,CAACjH,KAAK,CAACoH,KAAK,GAAG,CAAC,CAAC,CAACC,MAAM,CAACJ,eAAe,CAACjH,KAAK,CAAC,CAAC,EAAEoH,KAAK,CAAC,CAAC;EAClF,OAAOD,OAAO,GAAG/H,GAAG,CAACkI,OAAO,CAAC,CAAC,GAAGlI,GAAG;AACtC;AAEA,IAAImI,SAAS,GAAG;EACdC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,WAAW;EACtBC,gBAAgB,EAAE;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS9G,IAAIA,CAACf,IAAI,EAAEa,OAAO,EAAE;EAC3B;EACA,IAAIQ,iBAAiB,CAACrB,IAAI,CAACnH,QAAQ,CAACkH,SAAS,EAAE,OAAO,CAAC,EAAE;IACvD,OAAOC,IAAI;EACb;EAEA,IAAIA,IAAI,CAACY,OAAO,IAAIZ,IAAI,CAAC/C,SAAS,KAAK+C,IAAI,CAACgB,iBAAiB,EAAE;IAC7D;IACA,OAAOhB,IAAI;EACb;EAEA,IAAItD,UAAU,GAAGJ,aAAa,CAAC0D,IAAI,CAACnH,QAAQ,CAAC0D,MAAM,EAAEyD,IAAI,CAACnH,QAAQ,CAACjE,SAAS,EAAEiM,OAAO,CAACrE,OAAO,EAAEqE,OAAO,CAACpE,iBAAiB,EAAEuD,IAAI,CAACc,aAAa,CAAC;EAE7I,IAAI7D,SAAS,GAAG+C,IAAI,CAAC/C,SAAS,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5C,IAAI8J,iBAAiB,GAAGrJ,oBAAoB,CAACxB,SAAS,CAAC;EACvD,IAAIc,SAAS,GAAGiC,IAAI,CAAC/C,SAAS,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;EAElD,IAAI+J,SAAS,GAAG,EAAE;EAElB,QAAQlH,OAAO,CAACmH,QAAQ;IACtB,KAAKN,SAAS,CAACC,IAAI;MACjBI,SAAS,GAAG,CAAC9K,SAAS,EAAE6K,iBAAiB,CAAC;MAC1C;IACF,KAAKJ,SAAS,CAACE,SAAS;MACtBG,SAAS,GAAGV,SAAS,CAACpK,SAAS,CAAC;MAChC;IACF,KAAKyK,SAAS,CAACG,gBAAgB;MAC7BE,SAAS,GAAGV,SAAS,CAACpK,SAAS,EAAE,IAAI,CAAC;MACtC;IACF;MACE8K,SAAS,GAAGlH,OAAO,CAACmH,QAAQ;EAChC;EAEAD,SAAS,CAAC3H,OAAO,CAAC,UAAU6H,IAAI,EAAEV,KAAK,EAAE;IACvC,IAAItK,SAAS,KAAKgL,IAAI,IAAIF,SAAS,CAAC1V,MAAM,KAAKkV,KAAK,GAAG,CAAC,EAAE;MACxD,OAAOvH,IAAI;IACb;IAEA/C,SAAS,GAAG+C,IAAI,CAAC/C,SAAS,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxC8J,iBAAiB,GAAGrJ,oBAAoB,CAACxB,SAAS,CAAC;IAEnD,IAAI+B,aAAa,GAAGgB,IAAI,CAAC3F,OAAO,CAACkC,MAAM;IACvC,IAAI2L,UAAU,GAAGlI,IAAI,CAAC3F,OAAO,CAACzF,SAAS;;IAEvC;IACA,IAAI6P,KAAK,GAAGnM,IAAI,CAACmM,KAAK;IACtB,IAAI0D,WAAW,GAAGlL,SAAS,KAAK,MAAM,IAAIwH,KAAK,CAACzF,aAAa,CAACnH,KAAK,CAAC,GAAG4M,KAAK,CAACyD,UAAU,CAACtQ,IAAI,CAAC,IAAIqF,SAAS,KAAK,OAAO,IAAIwH,KAAK,CAACzF,aAAa,CAACpH,IAAI,CAAC,GAAG6M,KAAK,CAACyD,UAAU,CAACrQ,KAAK,CAAC,IAAIoF,SAAS,KAAK,KAAK,IAAIwH,KAAK,CAACzF,aAAa,CAACrH,MAAM,CAAC,GAAG8M,KAAK,CAACyD,UAAU,CAACxQ,GAAG,CAAC,IAAIuF,SAAS,KAAK,QAAQ,IAAIwH,KAAK,CAACzF,aAAa,CAACtH,GAAG,CAAC,GAAG+M,KAAK,CAACyD,UAAU,CAACvQ,MAAM,CAAC;IAE5U,IAAIyQ,aAAa,GAAG3D,KAAK,CAACzF,aAAa,CAACpH,IAAI,CAAC,GAAG6M,KAAK,CAAC/H,UAAU,CAAC9E,IAAI,CAAC;IACtE,IAAIyQ,cAAc,GAAG5D,KAAK,CAACzF,aAAa,CAACnH,KAAK,CAAC,GAAG4M,KAAK,CAAC/H,UAAU,CAAC7E,KAAK,CAAC;IACzE,IAAIyQ,YAAY,GAAG7D,KAAK,CAACzF,aAAa,CAACtH,GAAG,CAAC,GAAG+M,KAAK,CAAC/H,UAAU,CAAChF,GAAG,CAAC;IACnE,IAAI6Q,eAAe,GAAG9D,KAAK,CAACzF,aAAa,CAACrH,MAAM,CAAC,GAAG8M,KAAK,CAAC/H,UAAU,CAAC/E,MAAM,CAAC;IAE5E,IAAI6Q,mBAAmB,GAAGvL,SAAS,KAAK,MAAM,IAAImL,aAAa,IAAInL,SAAS,KAAK,OAAO,IAAIoL,cAAc,IAAIpL,SAAS,KAAK,KAAK,IAAIqL,YAAY,IAAIrL,SAAS,KAAK,QAAQ,IAAIsL,eAAe;;IAE9L;IACA,IAAIzD,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAACvS,OAAO,CAAC0K,SAAS,CAAC,KAAK,CAAC,CAAC;;IAE5D;IACA,IAAIwL,qBAAqB,GAAG,CAAC,CAAC5H,OAAO,CAAC6H,cAAc,KAAK5D,UAAU,IAAI/G,SAAS,KAAK,OAAO,IAAIqK,aAAa,IAAItD,UAAU,IAAI/G,SAAS,KAAK,KAAK,IAAIsK,cAAc,IAAI,CAACvD,UAAU,IAAI/G,SAAS,KAAK,OAAO,IAAIuK,YAAY,IAAI,CAACxD,UAAU,IAAI/G,SAAS,KAAK,KAAK,IAAIwK,eAAe,CAAC;;IAEtR;IACA,IAAII,yBAAyB,GAAG,CAAC,CAAC9H,OAAO,CAAC+H,uBAAuB,KAAK9D,UAAU,IAAI/G,SAAS,KAAK,OAAO,IAAIsK,cAAc,IAAIvD,UAAU,IAAI/G,SAAS,KAAK,KAAK,IAAIqK,aAAa,IAAI,CAACtD,UAAU,IAAI/G,SAAS,KAAK,OAAO,IAAIwK,eAAe,IAAI,CAACzD,UAAU,IAAI/G,SAAS,KAAK,KAAK,IAAIuK,YAAY,CAAC;IAEnS,IAAIO,gBAAgB,GAAGJ,qBAAqB,IAAIE,yBAAyB;IAEzE,IAAIR,WAAW,IAAIK,mBAAmB,IAAIK,gBAAgB,EAAE;MAC1D;MACA7I,IAAI,CAACY,OAAO,GAAG,IAAI;MAEnB,IAAIuH,WAAW,IAAIK,mBAAmB,EAAE;QACtCvL,SAAS,GAAG8K,SAAS,CAACR,KAAK,GAAG,CAAC,CAAC;MAClC;MAEA,IAAIsB,gBAAgB,EAAE;QACpB9K,SAAS,GAAGmJ,oBAAoB,CAACnJ,SAAS,CAAC;MAC7C;MAEAiC,IAAI,CAAC/C,SAAS,GAAGA,SAAS,IAAIc,SAAS,GAAG,GAAG,GAAGA,SAAS,GAAG,EAAE,CAAC;;MAE/D;MACA;MACAiC,IAAI,CAAC3F,OAAO,CAACkC,MAAM,GAAGvC,QAAQ,CAAC,CAAC,CAAC,EAAEgG,IAAI,CAAC3F,OAAO,CAACkC,MAAM,EAAEsC,gBAAgB,CAACmB,IAAI,CAACnH,QAAQ,CAAC0D,MAAM,EAAEyD,IAAI,CAAC3F,OAAO,CAACzF,SAAS,EAAEoL,IAAI,CAAC/C,SAAS,CAAC,CAAC;MAEvI+C,IAAI,GAAGF,YAAY,CAACE,IAAI,CAACnH,QAAQ,CAACkH,SAAS,EAAEC,IAAI,EAAE,MAAM,CAAC;IAC5D;EACF,CAAC,CAAC;EACF,OAAOA,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8I,YAAYA,CAAC9I,IAAI,EAAE;EAC1B,IAAIuE,aAAa,GAAGvE,IAAI,CAAC3F,OAAO;IAC5BkC,MAAM,GAAGgI,aAAa,CAAChI,MAAM;IAC7B3H,SAAS,GAAG2P,aAAa,CAAC3P,SAAS;EAEvC,IAAIqI,SAAS,GAAG+C,IAAI,CAAC/C,SAAS,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5C,IAAIyG,KAAK,GAAGnM,IAAI,CAACmM,KAAK;EACtB,IAAIK,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAACvS,OAAO,CAAC0K,SAAS,CAAC,KAAK,CAAC,CAAC;EAC5D,IAAInG,IAAI,GAAGgO,UAAU,GAAG,OAAO,GAAG,QAAQ;EAC1C,IAAI6B,MAAM,GAAG7B,UAAU,GAAG,MAAM,GAAG,KAAK;EACxC,IAAI1F,WAAW,GAAG0F,UAAU,GAAG,OAAO,GAAG,QAAQ;EAEjD,IAAIvI,MAAM,CAACzF,IAAI,CAAC,GAAG2N,KAAK,CAAC7P,SAAS,CAAC+R,MAAM,CAAC,CAAC,EAAE;IAC3C3G,IAAI,CAAC3F,OAAO,CAACkC,MAAM,CAACoK,MAAM,CAAC,GAAGlC,KAAK,CAAC7P,SAAS,CAAC+R,MAAM,CAAC,CAAC,GAAGpK,MAAM,CAAC6C,WAAW,CAAC;EAC9E;EACA,IAAI7C,MAAM,CAACoK,MAAM,CAAC,GAAGlC,KAAK,CAAC7P,SAAS,CAACkC,IAAI,CAAC,CAAC,EAAE;IAC3CkJ,IAAI,CAAC3F,OAAO,CAACkC,MAAM,CAACoK,MAAM,CAAC,GAAGlC,KAAK,CAAC7P,SAAS,CAACkC,IAAI,CAAC,CAAC;EACtD;EAEA,OAAOkJ,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+I,OAAOA,CAACC,GAAG,EAAE5J,WAAW,EAAEJ,aAAa,EAAEF,gBAAgB,EAAE;EAClE;EACA,IAAId,KAAK,GAAGgL,GAAG,CAACnJ,KAAK,CAAC,2BAA2B,CAAC;EAClD,IAAI9F,KAAK,GAAG,CAACiE,KAAK,CAAC,CAAC,CAAC;EACrB,IAAI8F,IAAI,GAAG9F,KAAK,CAAC,CAAC,CAAC;;EAEnB;EACA,IAAI,CAACjE,KAAK,EAAE;IACV,OAAOiP,GAAG;EACZ;EAEA,IAAIlF,IAAI,CAACvR,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IAC3B,IAAIkB,OAAO,GAAG,KAAK,CAAC;IACpB,QAAQqQ,IAAI;MACV,KAAK,IAAI;QACPrQ,OAAO,GAAGuL,aAAa;QACvB;MACF,KAAK,GAAG;MACR,KAAK,IAAI;MACT;QACEvL,OAAO,GAAGqL,gBAAgB;IAC9B;IAEA,IAAIzH,IAAI,GAAG+C,aAAa,CAAC3G,OAAO,CAAC;IACjC,OAAO4D,IAAI,CAAC+H,WAAW,CAAC,GAAG,GAAG,GAAGrF,KAAK;EACxC,CAAC,MAAM,IAAI+J,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;IACzC;IACA,IAAImF,IAAI,GAAG,KAAK,CAAC;IACjB,IAAInF,IAAI,KAAK,IAAI,EAAE;MACjBmF,IAAI,GAAG3Q,IAAI,CAACC,GAAG,CAACvG,QAAQ,CAACqD,eAAe,CAACsF,YAAY,EAAE5I,MAAM,CAACiK,WAAW,IAAI,CAAC,CAAC;IACjF,CAAC,MAAM;MACLiN,IAAI,GAAG3Q,IAAI,CAACC,GAAG,CAACvG,QAAQ,CAACqD,eAAe,CAACqF,WAAW,EAAE3I,MAAM,CAACgK,UAAU,IAAI,CAAC,CAAC;IAC/E;IACA,OAAOkN,IAAI,GAAG,GAAG,GAAGlP,KAAK;EAC3B,CAAC,MAAM;IACL;IACA;IACA,OAAOA,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmP,WAAWA,CAACjN,MAAM,EAAE+C,aAAa,EAAEF,gBAAgB,EAAEqK,aAAa,EAAE;EAC3E,IAAI9O,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEpB;EACA;EACA;EACA,IAAI+O,SAAS,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC7W,OAAO,CAAC4W,aAAa,CAAC,KAAK,CAAC,CAAC;;EAE/D;EACA;EACA,IAAIE,SAAS,GAAGpN,MAAM,CAAC+B,KAAK,CAAC,SAAS,CAAC,CAACV,GAAG,CAAC,UAAUgM,IAAI,EAAE;IAC1D,OAAOA,IAAI,CAACC,IAAI,CAAC,CAAC;EACpB,CAAC,CAAC;;EAEF;EACA;EACA,IAAIC,OAAO,GAAGH,SAAS,CAAC9W,OAAO,CAAC+M,IAAI,CAAC+J,SAAS,EAAE,UAAUC,IAAI,EAAE;IAC9D,OAAOA,IAAI,CAACG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EACnC,CAAC,CAAC,CAAC;EAEH,IAAIJ,SAAS,CAACG,OAAO,CAAC,IAAIH,SAAS,CAACG,OAAO,CAAC,CAACjX,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAChE8N,OAAO,CAACC,IAAI,CAAC,8EAA8E,CAAC;EAC9F;;EAEA;EACA;EACA,IAAIoJ,UAAU,GAAG,aAAa;EAC9B,IAAIC,GAAG,GAAGH,OAAO,KAAK,CAAC,CAAC,GAAG,CAACH,SAAS,CAAClJ,KAAK,CAAC,CAAC,EAAEqJ,OAAO,CAAC,CAAChC,MAAM,CAAC,CAAC6B,SAAS,CAACG,OAAO,CAAC,CAACxL,KAAK,CAAC0L,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACL,SAAS,CAACG,OAAO,CAAC,CAACxL,KAAK,CAAC0L,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAClC,MAAM,CAAC6B,SAAS,CAAClJ,KAAK,CAACqJ,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAACH,SAAS,CAAC;;EAExM;EACAM,GAAG,GAAGA,GAAG,CAACrM,GAAG,CAAC,UAAUsM,EAAE,EAAErC,KAAK,EAAE;IACjC;IACA,IAAInI,WAAW,GAAG,CAACmI,KAAK,KAAK,CAAC,GAAG,CAAC6B,SAAS,GAAGA,SAAS,IAAI,QAAQ,GAAG,OAAO;IAC7E,IAAIS,iBAAiB,GAAG,KAAK;IAC7B,OAAOD;IACP;IACA;IAAA,CACCE,MAAM,CAAC,UAAUrM,CAAC,EAAEC,CAAC,EAAE;MACtB,IAAID,CAAC,CAACA,CAAC,CAACpL,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAACE,OAAO,CAACmL,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QAC1DD,CAAC,CAACA,CAAC,CAACpL,MAAM,GAAG,CAAC,CAAC,GAAGqL,CAAC;QACnBmM,iBAAiB,GAAG,IAAI;QACxB,OAAOpM,CAAC;MACV,CAAC,MAAM,IAAIoM,iBAAiB,EAAE;QAC5BpM,CAAC,CAACA,CAAC,CAACpL,MAAM,GAAG,CAAC,CAAC,IAAIqL,CAAC;QACpBmM,iBAAiB,GAAG,KAAK;QACzB,OAAOpM,CAAC;MACV,CAAC,MAAM;QACL,OAAOA,CAAC,CAAC+J,MAAM,CAAC9J,CAAC,CAAC;MACpB;IACF,CAAC,EAAE,EAAE;IACL;IAAA,CACCJ,GAAG,CAAC,UAAU0L,GAAG,EAAE;MAClB,OAAOD,OAAO,CAACC,GAAG,EAAE5J,WAAW,EAAEJ,aAAa,EAAEF,gBAAgB,CAAC;IACnE,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA6K,GAAG,CAACvJ,OAAO,CAAC,UAAUwJ,EAAE,EAAErC,KAAK,EAAE;IAC/BqC,EAAE,CAACxJ,OAAO,CAAC,UAAUkJ,IAAI,EAAES,MAAM,EAAE;MACjC,IAAItG,SAAS,CAAC6F,IAAI,CAAC,EAAE;QACnBjP,OAAO,CAACkN,KAAK,CAAC,IAAI+B,IAAI,IAAIM,EAAE,CAACG,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAC5D;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO1P,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4B,MAAMA,CAAC+D,IAAI,EAAEjD,IAAI,EAAE;EAC1B,IAAId,MAAM,GAAGc,IAAI,CAACd,MAAM;EACxB,IAAIgB,SAAS,GAAG+C,IAAI,CAAC/C,SAAS;IAC1BsH,aAAa,GAAGvE,IAAI,CAAC3F,OAAO;IAC5BkC,MAAM,GAAGgI,aAAa,CAAChI,MAAM;IAC7B3H,SAAS,GAAG2P,aAAa,CAAC3P,SAAS;EAEvC,IAAIuU,aAAa,GAAGlM,SAAS,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAE3C,IAAI3D,OAAO,GAAG,KAAK,CAAC;EACpB,IAAIoJ,SAAS,CAAC,CAACxH,MAAM,CAAC,EAAE;IACtB5B,OAAO,GAAG,CAAC,CAAC4B,MAAM,EAAE,CAAC,CAAC;EACxB,CAAC,MAAM;IACL5B,OAAO,GAAG6O,WAAW,CAACjN,MAAM,EAAEM,MAAM,EAAE3H,SAAS,EAAEuU,aAAa,CAAC;EACjE;EAEA,IAAIA,aAAa,KAAK,MAAM,EAAE;IAC5B5M,MAAM,CAAC7E,GAAG,IAAI2C,OAAO,CAAC,CAAC,CAAC;IACxBkC,MAAM,CAAC3E,IAAI,IAAIyC,OAAO,CAAC,CAAC,CAAC;EAC3B,CAAC,MAAM,IAAI8O,aAAa,KAAK,OAAO,EAAE;IACpC5M,MAAM,CAAC7E,GAAG,IAAI2C,OAAO,CAAC,CAAC,CAAC;IACxBkC,MAAM,CAAC3E,IAAI,IAAIyC,OAAO,CAAC,CAAC,CAAC;EAC3B,CAAC,MAAM,IAAI8O,aAAa,KAAK,KAAK,EAAE;IAClC5M,MAAM,CAAC3E,IAAI,IAAIyC,OAAO,CAAC,CAAC,CAAC;IACzBkC,MAAM,CAAC7E,GAAG,IAAI2C,OAAO,CAAC,CAAC,CAAC;EAC1B,CAAC,MAAM,IAAI8O,aAAa,KAAK,QAAQ,EAAE;IACrC5M,MAAM,CAAC3E,IAAI,IAAIyC,OAAO,CAAC,CAAC,CAAC;IACzBkC,MAAM,CAAC7E,GAAG,IAAI2C,OAAO,CAAC,CAAC,CAAC;EAC1B;EAEA2F,IAAI,CAACzD,MAAM,GAAGA,MAAM;EACpB,OAAOyD,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgK,eAAeA,CAAChK,IAAI,EAAEa,OAAO,EAAE;EACtC,IAAIpE,iBAAiB,GAAGoE,OAAO,CAACpE,iBAAiB,IAAIrH,eAAe,CAAC4K,IAAI,CAACnH,QAAQ,CAAC0D,MAAM,CAAC;;EAE1F;EACA;EACA;EACA,IAAIyD,IAAI,CAACnH,QAAQ,CAACjE,SAAS,KAAK6H,iBAAiB,EAAE;IACjDA,iBAAiB,GAAGrH,eAAe,CAACqH,iBAAiB,CAAC;EACxD;;EAEA;EACA;EACA;EACA,IAAIwN,aAAa,GAAGxI,wBAAwB,CAAC,WAAW,CAAC;EACzD,IAAIyI,YAAY,GAAGlK,IAAI,CAACnH,QAAQ,CAAC0D,MAAM,CAACyF,KAAK,CAAC,CAAC;EAC/C,IAAItK,GAAG,GAAGwS,YAAY,CAACxS,GAAG;IACtBE,IAAI,GAAGsS,YAAY,CAACtS,IAAI;IACxBuS,SAAS,GAAGD,YAAY,CAACD,aAAa,CAAC;EAE3CC,YAAY,CAACxS,GAAG,GAAG,EAAE;EACrBwS,YAAY,CAACtS,IAAI,GAAG,EAAE;EACtBsS,YAAY,CAACD,aAAa,CAAC,GAAG,EAAE;EAEhC,IAAIvN,UAAU,GAAGJ,aAAa,CAAC0D,IAAI,CAACnH,QAAQ,CAAC0D,MAAM,EAAEyD,IAAI,CAACnH,QAAQ,CAACjE,SAAS,EAAEiM,OAAO,CAACrE,OAAO,EAAEC,iBAAiB,EAAEuD,IAAI,CAACc,aAAa,CAAC;;EAErI;EACA;EACAoJ,YAAY,CAACxS,GAAG,GAAGA,GAAG;EACtBwS,YAAY,CAACtS,IAAI,GAAGA,IAAI;EACxBsS,YAAY,CAACD,aAAa,CAAC,GAAGE,SAAS;EAEvCtJ,OAAO,CAACnE,UAAU,GAAGA,UAAU;EAE/B,IAAI1G,KAAK,GAAG6K,OAAO,CAACuJ,QAAQ;EAC5B,IAAI7N,MAAM,GAAGyD,IAAI,CAAC3F,OAAO,CAACkC,MAAM;EAEhC,IAAIiD,KAAK,GAAG;IACV6K,OAAO,EAAE,SAASA,OAAOA,CAACpN,SAAS,EAAE;MACnC,IAAIlD,KAAK,GAAGwC,MAAM,CAACU,SAAS,CAAC;MAC7B,IAAIV,MAAM,CAACU,SAAS,CAAC,GAAGP,UAAU,CAACO,SAAS,CAAC,IAAI,CAAC4D,OAAO,CAACyJ,mBAAmB,EAAE;QAC7EvQ,KAAK,GAAGzB,IAAI,CAACC,GAAG,CAACgE,MAAM,CAACU,SAAS,CAAC,EAAEP,UAAU,CAACO,SAAS,CAAC,CAAC;MAC5D;MACA,OAAOxD,cAAc,CAAC,CAAC,CAAC,EAAEwD,SAAS,EAAElD,KAAK,CAAC;IAC7C,CAAC;IACDwQ,SAAS,EAAE,SAASA,SAASA,CAACtN,SAAS,EAAE;MACvC,IAAIiC,QAAQ,GAAGjC,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK;MACrD,IAAIlD,KAAK,GAAGwC,MAAM,CAAC2C,QAAQ,CAAC;MAC5B,IAAI3C,MAAM,CAACU,SAAS,CAAC,GAAGP,UAAU,CAACO,SAAS,CAAC,IAAI,CAAC4D,OAAO,CAACyJ,mBAAmB,EAAE;QAC7EvQ,KAAK,GAAGzB,IAAI,CAAC2O,GAAG,CAAC1K,MAAM,CAAC2C,QAAQ,CAAC,EAAExC,UAAU,CAACO,SAAS,CAAC,IAAIA,SAAS,KAAK,OAAO,GAAGV,MAAM,CAAC5D,KAAK,GAAG4D,MAAM,CAAC7D,MAAM,CAAC,CAAC;MACpH;MACA,OAAOe,cAAc,CAAC,CAAC,CAAC,EAAEyF,QAAQ,EAAEnF,KAAK,CAAC;IAC5C;EACF,CAAC;EAED/D,KAAK,CAACoK,OAAO,CAAC,UAAUnD,SAAS,EAAE;IACjC,IAAInG,IAAI,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAACvE,OAAO,CAAC0K,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,GAAG,WAAW;IAC9EV,MAAM,GAAGvC,QAAQ,CAAC,CAAC,CAAC,EAAEuC,MAAM,EAAEiD,KAAK,CAAC1I,IAAI,CAAC,CAACmG,SAAS,CAAC,CAAC;EACvD,CAAC,CAAC;EAEF+C,IAAI,CAAC3F,OAAO,CAACkC,MAAM,GAAGA,MAAM;EAE5B,OAAOyD,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwK,KAAKA,CAACxK,IAAI,EAAE;EACnB,IAAI/C,SAAS,GAAG+C,IAAI,CAAC/C,SAAS;EAC9B,IAAIkM,aAAa,GAAGlM,SAAS,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3C,IAAIyM,cAAc,GAAGxN,SAAS,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;EAE5C;EACA,IAAIyM,cAAc,EAAE;IAClB,IAAIlG,aAAa,GAAGvE,IAAI,CAAC3F,OAAO;MAC5BzF,SAAS,GAAG2P,aAAa,CAAC3P,SAAS;MACnC2H,MAAM,GAAGgI,aAAa,CAAChI,MAAM;IAEjC,IAAIuI,UAAU,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAACvS,OAAO,CAAC4W,aAAa,CAAC,KAAK,CAAC,CAAC;IAChE,IAAIrS,IAAI,GAAGgO,UAAU,GAAG,MAAM,GAAG,KAAK;IACtC,IAAI1F,WAAW,GAAG0F,UAAU,GAAG,OAAO,GAAG,QAAQ;IAEjD,IAAI4F,YAAY,GAAG;MACjBtU,KAAK,EAAEqD,cAAc,CAAC,CAAC,CAAC,EAAE3C,IAAI,EAAElC,SAAS,CAACkC,IAAI,CAAC,CAAC;MAChDT,GAAG,EAAEoD,cAAc,CAAC,CAAC,CAAC,EAAE3C,IAAI,EAAElC,SAAS,CAACkC,IAAI,CAAC,GAAGlC,SAAS,CAACwK,WAAW,CAAC,GAAG7C,MAAM,CAAC6C,WAAW,CAAC;IAC9F,CAAC;IAEDY,IAAI,CAAC3F,OAAO,CAACkC,MAAM,GAAGvC,QAAQ,CAAC,CAAC,CAAC,EAAEuC,MAAM,EAAEmO,YAAY,CAACD,cAAc,CAAC,CAAC;EAC1E;EAEA,OAAOzK,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2K,IAAIA,CAAC3K,IAAI,EAAE;EAClB,IAAI,CAAC8F,kBAAkB,CAAC9F,IAAI,CAACnH,QAAQ,CAACkH,SAAS,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE;IAC3E,OAAOC,IAAI;EACb;EAEA,IAAI9C,OAAO,GAAG8C,IAAI,CAAC3F,OAAO,CAACzF,SAAS;EACpC,IAAIgW,KAAK,GAAGtL,IAAI,CAACU,IAAI,CAACnH,QAAQ,CAACkH,SAAS,EAAE,UAAUtI,QAAQ,EAAE;IAC5D,OAAOA,QAAQ,CAAC+J,IAAI,KAAK,iBAAiB;EAC5C,CAAC,CAAC,CAAC9E,UAAU;EAEb,IAAIQ,OAAO,CAACvF,MAAM,GAAGiT,KAAK,CAAClT,GAAG,IAAIwF,OAAO,CAACtF,IAAI,GAAGgT,KAAK,CAAC/S,KAAK,IAAIqF,OAAO,CAACxF,GAAG,GAAGkT,KAAK,CAACjT,MAAM,IAAIuF,OAAO,CAACrF,KAAK,GAAG+S,KAAK,CAAChT,IAAI,EAAE;IACxH;IACA,IAAIoI,IAAI,CAAC2K,IAAI,KAAK,IAAI,EAAE;MACtB,OAAO3K,IAAI;IACb;IAEAA,IAAI,CAAC2K,IAAI,GAAG,IAAI;IAChB3K,IAAI,CAACW,UAAU,CAAC,qBAAqB,CAAC,GAAG,EAAE;EAC7C,CAAC,MAAM;IACL;IACA,IAAIX,IAAI,CAAC2K,IAAI,KAAK,KAAK,EAAE;MACvB,OAAO3K,IAAI;IACb;IAEAA,IAAI,CAAC2K,IAAI,GAAG,KAAK;IACjB3K,IAAI,CAACW,UAAU,CAAC,qBAAqB,CAAC,GAAG,KAAK;EAChD;EAEA,OAAOX,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6K,KAAKA,CAAC7K,IAAI,EAAE;EACnB,IAAI/C,SAAS,GAAG+C,IAAI,CAAC/C,SAAS;EAC9B,IAAIkM,aAAa,GAAGlM,SAAS,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3C,IAAIuG,aAAa,GAAGvE,IAAI,CAAC3F,OAAO;IAC5BkC,MAAM,GAAGgI,aAAa,CAAChI,MAAM;IAC7B3H,SAAS,GAAG2P,aAAa,CAAC3P,SAAS;EAEvC,IAAIqK,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC1M,OAAO,CAAC4W,aAAa,CAAC,KAAK,CAAC,CAAC;EAE7D,IAAI2B,cAAc,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAACvY,OAAO,CAAC4W,aAAa,CAAC,KAAK,CAAC,CAAC;EAElE5M,MAAM,CAAC0C,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC,GAAGrK,SAAS,CAACuU,aAAa,CAAC,IAAI2B,cAAc,GAAGvO,MAAM,CAAC0C,OAAO,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;EAEzHe,IAAI,CAAC/C,SAAS,GAAGwB,oBAAoB,CAACxB,SAAS,CAAC;EAChD+C,IAAI,CAAC3F,OAAO,CAACkC,MAAM,GAAGnC,aAAa,CAACmC,MAAM,CAAC;EAE3C,OAAOyD,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAID,SAAS,GAAG;EACd;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEyK,KAAK,EAAE;IACL;IACAxU,KAAK,EAAE,GAAG;IACV;IACAuK,OAAO,EAAE,IAAI;IACb;IACA9N,EAAE,EAAE+X;EACN,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEvO,MAAM,EAAE;IACN;IACAjG,KAAK,EAAE,GAAG;IACV;IACAuK,OAAO,EAAE,IAAI;IACb;IACA9N,EAAE,EAAEwJ,MAAM;IACV;AACJ;AACA;IACIA,MAAM,EAAE;EACV,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE+N,eAAe,EAAE;IACf;IACAhU,KAAK,EAAE,GAAG;IACV;IACAuK,OAAO,EAAE,IAAI;IACb;IACA9N,EAAE,EAAEuX,eAAe;IACnB;AACJ;AACA;AACA;AACA;IACII,QAAQ,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC;IAC5C;AACJ;AACA;AACA;AACA;AACA;IACI5N,OAAO,EAAE,CAAC;IACV;AACJ;AACA;AACA;AACA;IACIC,iBAAiB,EAAE;EACrB,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEqM,YAAY,EAAE;IACZ;IACA9S,KAAK,EAAE,GAAG;IACV;IACAuK,OAAO,EAAE,IAAI;IACb;IACA9N,EAAE,EAAEqW;EACN,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEjD,KAAK,EAAE;IACL;IACA7P,KAAK,EAAE,GAAG;IACV;IACAuK,OAAO,EAAE,IAAI;IACb;IACA9N,EAAE,EAAEoT,KAAK;IACT;IACApS,OAAO,EAAE;EACX,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEsN,IAAI,EAAE;IACJ;IACA/K,KAAK,EAAE,GAAG;IACV;IACAuK,OAAO,EAAE,IAAI;IACb;IACA9N,EAAE,EAAEsO,IAAI;IACR;AACJ;AACA;AACA;AACA;AACA;IACIiH,QAAQ,EAAE,MAAM;IAChB;AACJ;AACA;AACA;IACIxL,OAAO,EAAE,CAAC;IACV;AACJ;AACA;AACA;AACA;AACA;IACIC,iBAAiB,EAAE,UAAU;IAC7B;AACJ;AACA;AACA;AACA;AACA;AACA;IACIiM,cAAc,EAAE,KAAK;IACrB;AACJ;AACA;AACA;AACA;AACA;AACA;IACIE,uBAAuB,EAAE;EAC3B,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEiC,KAAK,EAAE;IACL;IACA7U,KAAK,EAAE,GAAG;IACV;IACAuK,OAAO,EAAE,KAAK;IACd;IACA9N,EAAE,EAAEoY;EACN,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEF,IAAI,EAAE;IACJ;IACA3U,KAAK,EAAE,GAAG;IACV;IACAuK,OAAO,EAAE,IAAI;IACb;IACA9N,EAAE,EAAEkY;EACN,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtF,YAAY,EAAE;IACZ;IACArP,KAAK,EAAE,GAAG;IACV;IACAuK,OAAO,EAAE,IAAI;IACb;IACA9N,EAAE,EAAE4S,YAAY;IAChB;AACJ;AACA;AACA;AACA;IACIE,eAAe,EAAE,IAAI;IACrB;AACJ;AACA;AACA;AACA;IACIlH,CAAC,EAAE,QAAQ;IACX;AACJ;AACA;AACA;AACA;IACIE,CAAC,EAAE;EACL,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE0F,UAAU,EAAE;IACV;IACAjO,KAAK,EAAE,GAAG;IACV;IACAuK,OAAO,EAAE,IAAI;IACb;IACA9N,EAAE,EAAEwR,UAAU;IACd;IACA8G,MAAM,EAAE5G,gBAAgB;IACxB;AACJ;AACA;AACA;AACA;AACA;IACIoB,eAAe,EAAEvO;EACnB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIgU,QAAQ,GAAG;EACb;AACF;AACA;AACA;EACE/N,SAAS,EAAE,QAAQ;EAEnB;AACF;AACA;AACA;EACE6D,aAAa,EAAE,KAAK;EAEpB;AACF;AACA;AACA;EACEqC,aAAa,EAAE,IAAI;EAEnB;AACF;AACA;AACA;AACA;EACEd,eAAe,EAAE,KAAK;EAEtB;AACF;AACA;AACA;AACA;AACA;EACElB,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG,CAAC,CAAC;EAEhC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG,CAAC,CAAC;EAEhC;AACF;AACA;AACA;AACA;EACErB,SAAS,EAAEA;AACb,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAIkL,MAAM,GAAG,YAAY;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,MAAMA,CAACrW,SAAS,EAAE2H,MAAM,EAAE;IACjC,IAAI2O,KAAK,GAAG,IAAI;IAEhB,IAAIrK,OAAO,GAAG9J,SAAS,CAAC1E,MAAM,GAAG,CAAC,IAAI0E,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF6B,cAAc,CAAC,IAAI,EAAEqS,MAAM,CAAC;IAE5B,IAAI,CAAC5H,cAAc,GAAG,YAAY;MAChC,OAAO8H,qBAAqB,CAACD,KAAK,CAAC1K,MAAM,CAAC;IAC5C,CAAC;;IAED;IACA,IAAI,CAACA,MAAM,GAAGtN,QAAQ,CAAC,IAAI,CAACsN,MAAM,CAAC4K,IAAI,CAAC,IAAI,CAAC,CAAC;;IAE9C;IACA,IAAI,CAACvK,OAAO,GAAG7G,QAAQ,CAAC,CAAC,CAAC,EAAEiR,MAAM,CAACD,QAAQ,EAAEnK,OAAO,CAAC;;IAErD;IACA,IAAI,CAAC3C,KAAK,GAAG;MACXuC,WAAW,EAAE,KAAK;MAClBS,SAAS,EAAE,KAAK;MAChByB,aAAa,EAAE;IACjB,CAAC;;IAED;IACA,IAAI,CAAC/N,SAAS,GAAGA,SAAS,IAAIA,SAAS,CAACyW,MAAM,GAAGzW,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS;IACzE,IAAI,CAAC2H,MAAM,GAAGA,MAAM,IAAIA,MAAM,CAAC8O,MAAM,GAAG9O,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM;;IAE1D;IACA,IAAI,CAACsE,OAAO,CAACd,SAAS,GAAG,CAAC,CAAC;IAC3BvG,MAAM,CAAC6D,IAAI,CAACrD,QAAQ,CAAC,CAAC,CAAC,EAAEiR,MAAM,CAACD,QAAQ,CAACjL,SAAS,EAAEc,OAAO,CAACd,SAAS,CAAC,CAAC,CAACK,OAAO,CAAC,UAAUoB,IAAI,EAAE;MAC9F0J,KAAK,CAACrK,OAAO,CAACd,SAAS,CAACyB,IAAI,CAAC,GAAGxH,QAAQ,CAAC,CAAC,CAAC,EAAEiR,MAAM,CAACD,QAAQ,CAACjL,SAAS,CAACyB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAEX,OAAO,CAACd,SAAS,GAAGc,OAAO,CAACd,SAAS,CAACyB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACvI,CAAC,CAAC;;IAEF;IACA,IAAI,CAACzB,SAAS,GAAGvG,MAAM,CAAC6D,IAAI,CAAC,IAAI,CAACwD,OAAO,CAACd,SAAS,CAAC,CAACzC,GAAG,CAAC,UAAUkE,IAAI,EAAE;MACvE,OAAOxH,QAAQ,CAAC;QACdwH,IAAI,EAAEA;MACR,CAAC,EAAE0J,KAAK,CAACrK,OAAO,CAACd,SAAS,CAACyB,IAAI,CAAC,CAAC;IACnC,CAAC;IACD;IAAA,CACChE,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACpB,OAAOD,CAAC,CAACzH,KAAK,GAAG0H,CAAC,CAAC1H,KAAK;IAC1B,CAAC,CAAC;;IAEF;IACA;IACA;IACA;IACA,IAAI,CAAC+J,SAAS,CAACK,OAAO,CAAC,UAAUgE,eAAe,EAAE;MAChD,IAAIA,eAAe,CAAC7D,OAAO,IAAIpN,UAAU,CAACiR,eAAe,CAAC2G,MAAM,CAAC,EAAE;QACjE3G,eAAe,CAAC2G,MAAM,CAACG,KAAK,CAACtW,SAAS,EAAEsW,KAAK,CAAC3O,MAAM,EAAE2O,KAAK,CAACrK,OAAO,EAAEuD,eAAe,EAAE8G,KAAK,CAAChN,KAAK,CAAC;MACpG;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACsC,MAAM,CAAC,CAAC;IAEb,IAAI2C,aAAa,GAAG,IAAI,CAACtC,OAAO,CAACsC,aAAa;IAC9C,IAAIA,aAAa,EAAE;MACjB;MACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC7B;IAEA,IAAI,CAAClF,KAAK,CAACiF,aAAa,GAAGA,aAAa;EAC1C;;EAEA;EACA;;EAGAnK,WAAW,CAACiS,MAAM,EAAE,CAAC;IACnBvR,GAAG,EAAE,QAAQ;IACbK,KAAK,EAAE,SAASuR,SAASA,CAAA,EAAG;MAC1B,OAAO9K,MAAM,CAACjN,IAAI,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE;IACDmG,GAAG,EAAE,SAAS;IACdK,KAAK,EAAE,SAASwR,UAAUA,CAAA,EAAG;MAC3B,OAAOtJ,OAAO,CAAC1O,IAAI,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC,EAAE;IACDmG,GAAG,EAAE,sBAAsB;IAC3BK,KAAK,EAAE,SAASyR,uBAAuBA,CAAA,EAAG;MACxC,OAAOpI,oBAAoB,CAAC7P,IAAI,CAAC,IAAI,CAAC;IACxC;EACF,CAAC,EAAE;IACDmG,GAAG,EAAE,uBAAuB;IAC5BK,KAAK,EAAE,SAAS0R,wBAAwBA,CAAA,EAAG;MACzC,OAAOrJ,qBAAqB,CAAC7O,IAAI,CAAC,IAAI,CAAC;IACzC;;IAEA;AACJ;AACA;AACA;AACA;;IAGI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEE,CAAC,CAAC,CAAC;;EACH,OAAO0X,MAAM;AACf,CAAC,CAAC,CAAC;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGAA,MAAM,CAACS,KAAK,GAAG,CAAC,OAAO3Z,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG4Z,MAAM,EAAEC,WAAW;AAC5EX,MAAM,CAAC9D,UAAU,GAAGA,UAAU;AAC9B8D,MAAM,CAACD,QAAQ,GAAGA,QAAQ;AAE1B,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}