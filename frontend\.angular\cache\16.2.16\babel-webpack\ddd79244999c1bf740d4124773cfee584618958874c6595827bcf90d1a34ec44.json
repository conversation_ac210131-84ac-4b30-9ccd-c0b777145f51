{"ast": null, "code": "import * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js'; // Import Chart.js and register components\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = [\"myChart\"];\nconst _c1 = function (a1) {\n  return [\"/edit-responsable\", a1];\n};\nfunction UsersComponent_tr_82_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\")(16, \"a\", 45)(17, \"i\", 46);\n    i0.ɵɵtext(18, \"\\uE254\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"a\", 47);\n    i0.ɵɵlistener(\"click\", function UsersComponent_tr_82_Template_a_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const responsable_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(responsable_r1.id ? ctx_r2.deleteResponsable(responsable_r1.id) : null);\n    });\n    i0.ɵɵelementStart(20, \"i\", 48);\n    i0.ɵɵtext(21, \"\\uE872\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const responsable_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.nom);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.prenom);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.telephone);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.role);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(responsable_r1.groupe == null ? null : responsable_r1.groupe.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(8, _c1, responsable_r1.id));\n  }\n}\nconst _c2 = function () {\n  return [\"/add-responsable\"];\n};\nconst _c3 = \"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, .25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] { border-top: 1px solid #e5e5e5; }\\n.border-bottom[_ngcontent-%COMP%] { border-bottom: 1px solid #e5e5e5; }\\n\\n\\n.btn[_ngcontent-%COMP%] {\\npadding: 8px 16px;\\nfont-size: 14px;\\nborder: none;\\ncursor: pointer;\\nborder-radius: 4px;\\nfont-weight: bold;\\ntransition: all 0.3s ease;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\nbackground-color: #ff4d4d; \\n\\ncolor: white;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%]:hover {\\nbackground-color: #ff1a1a;\\n}\\n\\n.modify-btn[_ngcontent-%COMP%] {\\nbackground-color: #4d94ff; \\n\\ncolor: white;\\n}\\n\\n.modify-btn[_ngcontent-%COMP%]:hover {\\nbackground-color: #0066cc;\\n}\\n\\n\\n\\n\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n  color: #007bff;  \\n\\n}\\n\\n\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;  \\n\\n}\\n\\n\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;  \\n\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n  color: #c82333;  \\n\\n}\\n\\n.modal[_ngcontent-%COMP%]   .modal-dialog[_ngcontent-%COMP%] {\\nmax-width: 400px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%], .modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%], .modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\\npadding: 20px 30px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\nborder-radius: 3px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\\nbackground: #ecf0f1;\\nborder-radius: 0 0 3px 3px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n    display: inline-block;\\n}\\n.modal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\nborder-radius: 2px;\\nbox-shadow: none;\\nborder-color: #dddddd;\\n}\\n.modal[_ngcontent-%COMP%]   textarea.form-control[_ngcontent-%COMP%] {\\nresize: vertical;\\n}\\n.modal[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\nborder-radius: 2px;\\nmin-width: 100px;\\n}\\t\\n.modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\nfont-weight: normal;\\n}\\t\\n\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745, #218838); \\n\\n  color: #fff;\\n  font-weight: bold;\\n  padding: 12px 30px;\\n  border-radius: 50px; \\n\\n  text-transform: uppercase; \\n\\n  transition: all 0.3s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #218838, #28a745); \\n\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); \\n\\n}\\n\\n\\n\\n.btn-custom[_ngcontent-%COMP%]   .material-icons[_ngcontent-%COMP%] {\\n  margin-right: 8px; \\n\\n  font-size: 24px; \\n\\n  transition: color 0.3s ease;\\n}\\n\\n\\n\\n.btn-custom[_ngcontent-%COMP%]:hover   .material-icons[_ngcontent-%COMP%] {\\n  color: #fff; \\n\\n}\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9kYXNoYm9hcmQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWiwyQkFBMkI7QUFDN0I7O0FBRUE7O0VBRUU7O0FBRUY7RUFDRSxlQUFlO0VBQ2YsTUFBTTtFQUNOLFNBQVM7RUFDVCxPQUFPO0VBQ1AsWUFBWSxFQUFFLHNCQUFzQjtFQUNwQyxVQUFVO0VBQ1YsNENBQTRDO0FBQzlDOztBQUVBO0VBRUUsZ0JBQWdCO0VBQ2hCLFNBQVMsRUFBRSxxQkFBcUI7RUFDaEMsMEJBQTBCO0VBQzFCLGtCQUFrQjtFQUNsQixrQkFBa0I7RUFDbEIsZ0JBQWdCLEVBQUUsNkRBQTZEO0FBQ2pGOztBQUVBO0VBQ0UsZ0JBQWdCO0VBQ2hCLFdBQVc7QUFDYjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixXQUFXO0FBQ2I7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBOztFQUVFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIseUJBQXlCO0FBQzNCOztBQUVBOztFQUVFOztBQUVGO0VBQ0UsbUJBQW1CO0VBQ25CLHNCQUFzQjtFQUN0QixlQUFlO0VBQ2Ysb0NBQW9DO0VBQ3BDLDZDQUE2QztBQUMvQzs7QUFFQTtFQUNFLG9CQUFvQjtFQUNwQixlQUFlO0VBQ2YsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsV0FBVztFQUNYLHlDQUF5QztFQUN6QyxxQ0FBcUM7QUFDdkM7O0FBRUE7RUFDRSx5QkFBeUI7RUFDekIsOENBQThDO0FBQ2hEOztBQUVBOztFQUVFOztBQUVGLGNBQWMsNkJBQTZCLEVBQUU7QUFDN0MsaUJBQWlCLGdDQUFnQyxFQUFFO0FBQ25ELHNCQUFzQjtBQUN0QjtBQUNBLGlCQUFpQjtBQUNqQixlQUFlO0FBQ2YsWUFBWTtBQUNaLGVBQWU7QUFDZixrQkFBa0I7QUFDbEIsaUJBQWlCO0FBQ2pCLHlCQUF5QjtBQUN6Qjs7QUFFQTtBQUNBLHlCQUF5QixFQUFFLFFBQVE7QUFDbkMsWUFBWTtBQUNaOztBQUVBO0FBQ0EseUJBQXlCO0FBQ3pCOztBQUVBO0FBQ0EseUJBQXlCLEVBQUUsU0FBUztBQUNwQyxZQUFZO0FBQ1o7O0FBRUE7QUFDQSx5QkFBeUI7QUFDekI7O0FBRUEsMEVBQTBFO0FBQzFFLGtDQUFrQztBQUNsQztFQUNFLGNBQWMsR0FBRyxTQUFTO0FBQzVCOztBQUVBLG9DQUFvQztBQUNwQztFQUNFLGNBQWMsR0FBRyxRQUFRO0FBQzNCOztBQUVBLDZDQUE2QztBQUM3QztFQUNFLGNBQWMsR0FBRyxnQkFBZ0I7QUFDbkM7O0FBRUE7RUFDRSxjQUFjLEdBQUcsZUFBZTtBQUNsQzs7QUFFQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQiwwQkFBMEI7QUFDMUI7QUFDQTtJQUNJLHFCQUFxQjtBQUN6QjtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCLGdCQUFnQjtBQUNoQixxQkFBcUI7QUFDckI7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0Esd0JBQXdCO0FBQ3hCO0VBQ0UscURBQXFELEVBQUUsbUJBQW1CO0VBQzFFLFdBQVc7RUFDWCxpQkFBaUI7RUFDakIsa0JBQWtCO0VBQ2xCLG1CQUFtQixFQUFFLG9CQUFvQjtFQUN6Qyx5QkFBeUIsRUFBRSxtQkFBbUI7RUFDOUMseUJBQXlCO0VBQ3pCLG9CQUFvQjtFQUNwQixtQkFBbUI7RUFDbkIsdUJBQXVCO0FBQ3pCOztBQUVBLHdCQUF3QjtBQUN4QjtFQUNFLHFEQUFxRCxFQUFFLCtCQUErQjtFQUN0Rix5Q0FBeUMsRUFBRSwyQkFBMkI7QUFDeEU7O0FBRUEsaUJBQWlCO0FBQ2pCO0VBQ0UsaUJBQWlCLEVBQUUsZ0NBQWdDO0VBQ25ELGVBQWUsRUFBRSx5QkFBeUI7RUFDMUMsMkJBQTJCO0FBQzdCOztBQUVBLHNCQUFzQjtBQUN0QjtFQUNFLFdBQVcsRUFBRSw2QkFBNkI7QUFDNUMiLCJzb3VyY2VzQ29udGVudCI6WyJib2R5IHtcclxuICBmb250LXNpemU6IC44NzVyZW07XHJcbn1cclxuXHJcbi5mZWF0aGVyIHtcclxuICB3aWR0aDogMTZweDtcclxuICBoZWlnaHQ6IDE2cHg7XHJcbiAgdmVydGljYWwtYWxpZ246IHRleHQtYm90dG9tO1xyXG59XHJcblxyXG4vKlxyXG4gKiBTaWRlYmFyXHJcbiAqL1xyXG5cclxuLnNpZGViYXIge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgYm90dG9tOiAwO1xyXG4gIGxlZnQ6IDA7XHJcbiAgei1pbmRleDogMTAwOyAvKiBCZWhpbmQgdGhlIG5hdmJhciAqL1xyXG4gIHBhZGRpbmc6IDA7XHJcbiAgYm94LXNoYWRvdzogaW5zZXQgLTFweCAwIDAgcmdiYSgwLCAwLCAwLCAuMSk7XHJcbn1cclxuXHJcbi5zaWRlYmFyLXN0aWNreSB7XHJcbiAgcG9zaXRpb246IC13ZWJraXQtc3RpY2t5O1xyXG4gIHBvc2l0aW9uOiBzdGlja3k7XHJcbiAgdG9wOiA0OHB4OyAvKiBIZWlnaHQgb2YgbmF2YmFyICovXHJcbiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNDhweCk7XHJcbiAgcGFkZGluZy10b3A6IC41cmVtO1xyXG4gIG92ZXJmbG93LXg6IGhpZGRlbjtcclxuICBvdmVyZmxvdy15OiBhdXRvOyAvKiBTY3JvbGxhYmxlIGNvbnRlbnRzIGlmIHZpZXdwb3J0IGlzIHNob3J0ZXIgdGhhbiBjb250ZW50LiAqL1xyXG59XHJcblxyXG4uc2lkZWJhciAubmF2LWxpbmsge1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgY29sb3I6ICMzMzM7XHJcbn1cclxuXHJcbi5zaWRlYmFyIC5uYXYtbGluayAuZmVhdGhlciB7XHJcbiAgbWFyZ2luLXJpZ2h0OiA0cHg7XHJcbiAgY29sb3I6ICM5OTk7XHJcbn1cclxuXHJcbi5zaWRlYmFyIC5uYXYtbGluay5hY3RpdmUge1xyXG4gIGNvbG9yOiAjMDA3YmZmO1xyXG59XHJcblxyXG4uc2lkZWJhciAubmF2LWxpbms6aG92ZXIgLmZlYXRoZXIsXHJcbi5zaWRlYmFyIC5uYXYtbGluay5hY3RpdmUgLmZlYXRoZXIge1xyXG4gIGNvbG9yOiBpbmhlcml0O1xyXG59XHJcblxyXG4uc2lkZWJhci1oZWFkaW5nIHtcclxuICBmb250LXNpemU6IC43NXJlbTtcclxuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xyXG59XHJcblxyXG4vKlxyXG4gKiBOYXZiYXJcclxuICovXHJcblxyXG4ubmF2YmFyLWJyYW5kIHtcclxuICBwYWRkaW5nLXRvcDogLjc1cmVtO1xyXG4gIHBhZGRpbmctYm90dG9tOiAuNzVyZW07XHJcbiAgZm9udC1zaXplOiAxcmVtO1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgLjI1KTtcclxuICBib3gtc2hhZG93OiBpbnNldCAtMXB4IDAgMCByZ2JhKDAsIDAsIDAsIC4yNSk7XHJcbn1cclxuXHJcbi5uYXZiYXIgLmZvcm0tY29udHJvbCB7XHJcbiAgcGFkZGluZzogLjc1cmVtIDFyZW07XHJcbiAgYm9yZGVyLXdpZHRoOiAwO1xyXG4gIGJvcmRlci1yYWRpdXM6IDA7XHJcbn1cclxuXHJcbi5mb3JtLWNvbnRyb2wtZGFyayB7XHJcbiAgY29sb3I6ICNmZmY7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAuMSk7XHJcbiAgYm9yZGVyLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIC4xKTtcclxufVxyXG5cclxuLmZvcm0tY29udHJvbC1kYXJrOmZvY3VzIHtcclxuICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xyXG4gIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKDI1NSwgMjU1LCAyNTUsIC4yNSk7XHJcbn1cclxuXHJcbi8qXHJcbiAqIFV0aWxpdGllc1xyXG4gKi9cclxuXHJcbi5ib3JkZXItdG9wIHsgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlNWU1ZTU7IH1cclxuLmJvcmRlci1ib3R0b20geyBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U1ZTVlNTsgfVxyXG4vKiBCYXNlIGJ1dHRvbiBzdHlsZSAqL1xyXG4uYnRuIHtcclxucGFkZGluZzogOHB4IDE2cHg7XHJcbmZvbnQtc2l6ZTogMTRweDtcclxuYm9yZGVyOiBub25lO1xyXG5jdXJzb3I6IHBvaW50ZXI7XHJcbmJvcmRlci1yYWRpdXM6IDRweDtcclxuZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbnRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbn1cclxuXHJcbi5kZWxldGUtYnRuIHtcclxuYmFja2dyb3VuZC1jb2xvcjogI2ZmNGQ0ZDsgLyogUmVkICovXHJcbmNvbG9yOiB3aGl0ZTtcclxufVxyXG5cclxuLmRlbGV0ZS1idG46aG92ZXIge1xyXG5iYWNrZ3JvdW5kLWNvbG9yOiAjZmYxYTFhO1xyXG59XHJcblxyXG4ubW9kaWZ5LWJ0biB7XHJcbmJhY2tncm91bmQtY29sb3I6ICM0ZDk0ZmY7IC8qIEJsdWUgKi9cclxuY29sb3I6IHdoaXRlO1xyXG59XHJcblxyXG4ubW9kaWZ5LWJ0bjpob3ZlciB7XHJcbmJhY2tncm91bmQtY29sb3I6ICMwMDY2Y2M7XHJcbn1cclxuXHJcbi8qIE9wdGlvbmFsOiBZb3UgY2FuIGFkZCBtb3JlIGJ1dHRvbiBzdHlsZXMgbGlrZSAndmlldycsICdhcmNoaXZlJywgZXRjLiAqL1xyXG4vKiBDaGFuZ2UgY29sb3Igb2YgdGhlIGVkaXQgaWNvbiAqL1xyXG4uZWRpdC1pY29uIHtcclxuICBjb2xvcjogIzAwN2JmZjsgIC8qIEJsdWUgKi9cclxufVxyXG5cclxuLyogQ2hhbmdlIGNvbG9yIG9mIHRoZSBkZWxldGUgaWNvbiAqL1xyXG4uZGVsZXRlLWljb24ge1xyXG4gIGNvbG9yOiAjZGMzNTQ1OyAgLyogUmVkICovXHJcbn1cclxuXHJcbi8qIE9wdGlvbmFsOiBBZGQgaG92ZXIgZWZmZWN0IHRvIGJvdGggaWNvbnMgKi9cclxuLmVkaXQtaWNvbjpob3ZlciB7XHJcbiAgY29sb3I6ICMwMDU2YjM7ICAvKiBEYXJrZXIgYmx1ZSAqL1xyXG59XHJcblxyXG4uZGVsZXRlLWljb246aG92ZXIge1xyXG4gIGNvbG9yOiAjYzgyMzMzOyAgLyogRGFya2VyIHJlZCAqL1xyXG59XHJcblxyXG4ubW9kYWwgLm1vZGFsLWRpYWxvZyB7XHJcbm1heC13aWR0aDogNDAwcHg7XHJcbn1cclxuLm1vZGFsIC5tb2RhbC1oZWFkZXIsIC5tb2RhbCAubW9kYWwtYm9keSwgLm1vZGFsIC5tb2RhbC1mb290ZXIge1xyXG5wYWRkaW5nOiAyMHB4IDMwcHg7XHJcbn1cclxuLm1vZGFsIC5tb2RhbC1jb250ZW50IHtcclxuYm9yZGVyLXJhZGl1czogM3B4O1xyXG59XHJcbi5tb2RhbCAubW9kYWwtZm9vdGVyIHtcclxuYmFja2dyb3VuZDogI2VjZjBmMTtcclxuYm9yZGVyLXJhZGl1czogMCAwIDNweCAzcHg7XHJcbn1cclxuLm1vZGFsIC5tb2RhbC10aXRsZSB7XHJcbiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XHJcbn1cclxuLm1vZGFsIC5mb3JtLWNvbnRyb2wge1xyXG5ib3JkZXItcmFkaXVzOiAycHg7XHJcbmJveC1zaGFkb3c6IG5vbmU7XHJcbmJvcmRlci1jb2xvcjogI2RkZGRkZDtcclxufVxyXG4ubW9kYWwgdGV4dGFyZWEuZm9ybS1jb250cm9sIHtcclxucmVzaXplOiB2ZXJ0aWNhbDtcclxufVxyXG4ubW9kYWwgLmJ0biB7XHJcbmJvcmRlci1yYWRpdXM6IDJweDtcclxubWluLXdpZHRoOiAxMDBweDtcclxufVx0XHJcbi5tb2RhbCBmb3JtIGxhYmVsIHtcclxuZm9udC13ZWlnaHQ6IG5vcm1hbDtcclxufVx0XHJcbi8qIEN1c3RvbSBidXR0b24gc3R5bGUgKi9cclxuLmJ0bi1jdXN0b20ge1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyOGE3NDUsICMyMTg4MzgpOyAvKiBHcmVlbiBncmFkaWVudCAqL1xyXG4gIGNvbG9yOiAjZmZmO1xyXG4gIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gIHBhZGRpbmc6IDEycHggMzBweDtcclxuICBib3JkZXItcmFkaXVzOiA1MHB4OyAvKiBSb3VuZGVkIGNvcm5lcnMgKi9cclxuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlOyAvKiBVcHBlcmNhc2UgdGV4dCAqL1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgZGlzcGxheTogaW5saW5lLWZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxufVxyXG5cclxuLyogQnV0dG9uIGhvdmVyIGVmZmVjdCAqL1xyXG4uYnRuLWN1c3RvbTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzIxODgzOCwgIzI4YTc0NSk7IC8qIFJldmVyc2VkIGdyYWRpZW50IG9uIGhvdmVyICovXHJcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7IC8qIFN1YnRsZSBzaGFkb3cgb24gaG92ZXIgKi9cclxufVxyXG5cclxuLyogSWNvbiBzdHlsaW5nICovXHJcbi5idG4tY3VzdG9tIC5tYXRlcmlhbC1pY29ucyB7XHJcbiAgbWFyZ2luLXJpZ2h0OiA4cHg7IC8qIFNwYWNlIGJldHdlZW4gaWNvbiBhbmQgdGV4dCAqL1xyXG4gIGZvbnQtc2l6ZTogMjRweDsgLyogU2xpZ2h0bHkgYmlnZ2VyIGljb24gKi9cclxuICB0cmFuc2l0aW9uOiBjb2xvciAwLjNzIGVhc2U7XHJcbn1cclxuXHJcbi8qIEljb24gaG92ZXIgZWZmZWN0ICovXHJcbi5idG4tY3VzdG9tOmhvdmVyIC5tYXRlcmlhbC1pY29ucyB7XHJcbiAgY29sb3I6ICNmZmY7IC8qIEtlZXAgaWNvbiB3aGl0ZSBvbiBob3ZlciAqL1xyXG59XHJcblxyXG5cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\";\nexport class UsersComponent {\n  constructor(responsableService, cdr, router) {\n    this.responsableService = responsableService;\n    this.cdr = cdr;\n    this.router = router;\n    this.responsables = [];\n  }\n  ngOnInit() {\n    this.loadResponsables();\n  }\n  loadResponsables() {\n    this.responsableService.getAllUsers().subscribe(data => {\n      this.responsables = data;\n    }, error => {\n      console.error('Error fetching users:', error);\n    });\n  }\n  deleteResponsable(id) {\n    if (id === undefined) {\n      console.error(\"Error: Responsable ID is undefined\");\n      return;\n    }\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: response => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: error => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  updateResponsable(id, updatedResponsable) {\n    if (id && updatedResponsable) {\n      // Ensure that the id and updatedResponsable are valid\n      this.responsableService.updateResponsable(id, updatedResponsable).subscribe(() => {\n        this.loadResponsables();\n      }, error => {\n        console.error('Error updating responsable:', error);\n      });\n    }\n  }\n  trackById(index, item) {\n    return item.id ?? 0; // Return 0 if id is undefined\n  }\n\n  ngAfterViewInit() {\n    feather.replace();\n    Chart.register(...registerables); // Register chart.js components for version 4+\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement;\n      // Ensure the chart has data and a configuration\n      const chart = new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n          datasets: [{\n            data: [15339, 21345, 18483, 24003, 23489, 24092, 12034],\n            tension: 0.4,\n            backgroundColor: 'rgba(0,123,255,0.2)',\n            borderColor: '#007bff',\n            borderWidth: 4,\n            pointBackgroundColor: '#007bff'\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            y: {\n              beginAtZero: true\n            }\n          }\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function UsersComponent_Factory(t) {\n      return new (t || UsersComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UsersComponent,\n      selectors: [[\"app-respon-dashboard\"]],\n      viewQuery: function UsersComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 83,\n      vars: 3,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"form-control-dark\", \"w-100\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-light\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\"], [1, \"text-center\", \"mt-5\", \"mb-4\"], [1, \"mb-3\"], [1, \"btn\", \"btn-custom\", 3, \"routerLink\"], [\"data-toggle\", \"tooltip\", \"title\", \"Ajouter un responsable\", 1, \"material-icons\", 2, \"color\", \"#fff\", \"font-size\", \"20px\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\"], [4, \"ngFor\", \"ngForOf\"], [\"data-toggle\", \"modal\", 1, \"edit\", 3, \"routerLink\"], [\"data-toggle\", \"tooltip\", \"title\", \"Modifier\", 1, \"material-icons\", \"edit-icon\"], [\"href\", \"#\", \"data-toggle\", \"modal\", 1, \"delete\", 3, \"click\"], [\"data-toggle\", \"tooltip\", \"title\", \"Supprimer\", 1, \"material-icons\", \"delete-icon\"]],\n      template: function UsersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"meta\", 6)(8, \"meta\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10)(12, \"link\", 11)(13, \"link\", 12);\n          i0.ɵɵelementStart(14, \"title\");\n          i0.ɵɵtext(15, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"body\")(17, \"nav\", 13)(18, \"a\", 14);\n          i0.ɵɵtext(19, \"GTI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"input\", 15);\n          i0.ɵɵelementStart(21, \"ul\", 16)(22, \"li\", 17)(23, \"a\", 18);\n          i0.ɵɵlistener(\"click\", function UsersComponent_Template_a_click_23_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(24, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 19)(26, \"div\", 20)(27, \"nav\", 21)(28, \"div\", 22)(29, \"ul\", 23)(30, \"li\", 24)(31, \"a\", 25);\n          i0.ɵɵelement(32, \"span\", 26);\n          i0.ɵɵtext(33, \" Dashboard \");\n          i0.ɵɵelementStart(34, \"span\", 27);\n          i0.ɵɵtext(35, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"li\", 24)(37, \"a\", 28);\n          i0.ɵɵelement(38, \"span\", 29);\n          i0.ɵɵtext(39, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"li\", 24)(41, \"a\", 30);\n          i0.ɵɵelement(42, \"span\", 31);\n          i0.ɵɵtext(43, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(44, \"main\", 32)(45, \"div\", 33)(46, \"h1\", 34);\n          i0.ɵɵtext(47, \"Gestion des utilisateurs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 35)(49, \"div\", 36)(50, \"button\", 37);\n          i0.ɵɵtext(51, \"Import\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"button\", 37);\n          i0.ɵɵtext(53, \"Export\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(54, \"h2\", 38);\n          i0.ɵɵtext(55, \"Liste des utilisateurs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\", 39)(57, \"a\", 40)(58, \"i\", 41);\n          i0.ɵɵtext(59, \"\\uE145\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" Ajouter un responsable \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"div\", 42)(62, \"table\", 43)(63, \"thead\")(64, \"tr\")(65, \"th\");\n          i0.ɵɵtext(66, \"id\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\");\n          i0.ɵɵtext(68, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"th\");\n          i0.ɵɵtext(70, \"Prenom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"th\");\n          i0.ɵɵtext(72, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"th\");\n          i0.ɵɵtext(74, \"Telephone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"th\");\n          i0.ɵɵtext(76, \"Role\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"th\");\n          i0.ɵɵtext(78, \"Group\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"th\");\n          i0.ɵɵtext(80, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(81, \"tbody\");\n          i0.ɵɵtemplate(82, UsersComponent_tr_82_Template, 22, 10, \"tr\", 44);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(57);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c2));\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngForOf\", ctx.responsables);\n        }\n      },\n      dependencies: [i3.NgForOf, i2.RouterLink],\n      styles: [_c3, _c3, \".btn-custom[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #62d17c, #218838); \\n\\n  color: #fff;\\n  font-weight: bold;\\n  padding: 12px 30px;\\n  border-radius: 50px; \\n\\n  text-transform: uppercase; \\n\\n  transition: all 0.3s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #218838, #28a745); \\n\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); \\n\\n}\\n\\n\\n\\n.btn-custom[_ngcontent-%COMP%]   .material-icons[_ngcontent-%COMP%] {\\n  margin-right: 8px; \\n\\n  font-size: 24px; \\n\\n  transition: color 0.3s ease;\\n}\\n\\n\\n\\n.btn-custom[_ngcontent-%COMP%]:hover   .material-icons[_ngcontent-%COMP%] {\\n  color: #fff; \\n\\n}\\n\\n\\n\\n      .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n        background-color: #4d7eb1;\\n        color: white;\\n        font-weight: bold;\\n      }\\n      \\n      .table-striped[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-of-type(odd) {\\n        background-color: #f8f9fa;\\n      }\\n      \\n      .table-striped[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n        background-color: #e2e6ea;\\n        cursor: pointer;\\n      }\\n\\n      .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n        vertical-align: middle;\\n        text-align: center;\\n        font-size: 1.1rem;\\n      }\\n\\n      .table[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n        padding: 0.5rem 1rem;\\n        font-size: 1.1rem;\\n      }\\n\\n   \\n\\n.edit-icon[_ngcontent-%COMP%] {\\n  color: #007bff;  \\n\\n}\\n\\n\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;  \\n\\n}\\n\\n\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;  \\n\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n  color: #c82333;  \\n\\n}\\n\\n      .material-icons[_ngcontent-%COMP%] {\\n        font-size: 24px;\\n      }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "Chart", "registerables", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "UsersComponent_tr_82_Template_a_click_19_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "responsable_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "id", "deleteResponsable", "ɵɵadvance", "ɵɵtextInterpolate", "nom", "prenom", "email", "telephone", "role", "groupe", "nomGroupe", "ɵɵproperty", "ɵɵpureFunction1", "_c1", "UsersComponent", "constructor", "responsableService", "cdr", "router", "responsables", "ngOnInit", "loadResponsables", "getAllUsers", "subscribe", "data", "error", "console", "undefined", "confirm", "next", "response", "log", "filter", "responsable", "logout", "localStorage", "removeItem", "getItem", "navigate", "updateResponsable", "updatedResponsable", "trackById", "index", "item", "ngAfterViewInit", "replace", "register", "myChartRef", "ctx", "nativeElement", "chart", "type", "labels", "datasets", "tension", "backgroundColor", "borderColor", "borderWidth", "pointBackgroundColor", "options", "responsive", "maintainAspectRatio", "scales", "y", "beginAtZero", "ɵɵdirectiveInject", "i1", "ResponsableService", "ChangeDetectorRef", "i2", "Router", "selectors", "viewQuery", "UsersComponent_Query", "rf", "ɵɵelement", "UsersComponent_Template_a_click_23_listener", "ɵɵtemplate", "UsersComponent_tr_82_Template", "ɵɵpureFunction0", "_c2"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\users\\users.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\users\\users.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, AfterViewInit, ElementRef, ChangeDetectorRef } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport * as feather from 'feather-icons';\nimport { Chart, registerables } from 'chart.js'; // Import Chart.js and register components\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './users.component.html',\n  styleUrls: ['../../dashboard.css']\n})\nexport class UsersComponent implements AfterViewInit, OnInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  responsables: User[] = []; \n\n  constructor(\n    private responsableService: ResponsableService,\n    private cdr: ChangeDetectorRef, \n    private router: Router \n  ) {}\n\n  ngOnInit(): void {\n    this.loadResponsables();\n  }\n\n  loadResponsables() {\n    this.responsableService.getAllUsers().subscribe(\n      (data) => {\n        this.responsables = data;\n      },\n      (error) => {\n        console.error('Error fetching users:', error);\n      }\n    );\n  }\n  \n  deleteResponsable(id?: number) {\n    if (id === undefined) {\n      console.error(\"Error: Responsable ID is undefined\");\n      return;\n    }\n    if (confirm('Are you sure you want to delete this Responsable?')) {\n      this.responsableService.deleteResponsable(id).subscribe({\n        next: (response) => {\n          console.log('Delete response:', response);\n          this.responsables = this.responsables.filter(responsable => responsable.id !== id);\n        },\n        error: (error) => {\n          console.error('Error deleting responsable:', error);\n        }\n      });\n    }\n  }\n  \n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n\n  updateResponsable(id: number, updatedResponsable: User): void {\n    if (id && updatedResponsable) {  // Ensure that the id and updatedResponsable are valid\n      this.responsableService.updateResponsable(id, updatedResponsable).subscribe(\n        () => {\n          this.loadResponsables(); \n        },\n        (error) => {\n          console.error('Error updating responsable:', error);\n        }\n      );\n    }\n  }\n  trackById(index: number, item: User): number | undefined {\n    return item.id ?? 0;  // Return 0 if id is undefined\n  }\n  \n\n  ngAfterViewInit() {\n    feather.replace();\n    Chart.register(...registerables);  // Register chart.js components for version 4+\n\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement as HTMLCanvasElement;\n\n      // Ensure the chart has data and a configuration\n      const chart = new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n          datasets: [{\n            data: [15339, 21345, 18483, 24003, 23489, 24092, 12034],\n            tension: 0.4, // Replaces lineTension\n            backgroundColor: 'rgba(0,123,255,0.2)', // Light blue background\n            borderColor: '#007bff',\n            borderWidth: 4,\n            pointBackgroundColor: '#007bff',\n          }]\n        },\n        options: {\n          responsive: true, // Make the chart responsive\n          maintainAspectRatio: false, // Allow the chart to resize with container\n          scales: {\n            y: {\n              beginAtZero: true\n            }\n          }\n        }\n      });\n    }\n  }\n}", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <script src=\"https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js\"></script>\n    <script src=\"https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n    <link href=\"../../dashboard.css\" rel=\"stylesheet\">\n\n    <!-- Custom Styling for Table -->\n    <style>\n\n.btn-custom {\n  background: linear-gradient(135deg, #62d17c, #218838); /* Green gradient */\n  color: #fff;\n  font-weight: bold;\n  padding: 12px 30px;\n  border-radius: 50px; /* Rounded corners */\n  text-transform: uppercase; /* Uppercase text */\n  transition: all 0.3s ease;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* Button hover effect */\n.btn-custom:hover {\n  background: linear-gradient(135deg, #218838, #28a745); /* Reversed gradient on hover */\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Subtle shadow on hover */\n}\n\n/* Icon styling */\n.btn-custom .material-icons {\n  margin-right: 8px; /* Space between icon and text */\n  font-size: 24px; /* Slightly bigger icon */\n  transition: color 0.3s ease;\n}\n\n/* Icon hover effect */\n.btn-custom:hover .material-icons {\n  color: #fff; /* Keep icon white on hover */\n}\n\n\n\n      .table thead th {\n        background-color: #4d7eb1;\n        color: white;\n        font-weight: bold;\n      }\n      \n      .table-striped tbody tr:nth-of-type(odd) {\n        background-color: #f8f9fa;\n      }\n      \n      .table-striped tbody tr:hover {\n        background-color: #e2e6ea;\n        cursor: pointer;\n      }\n\n      .table th, .table td {\n        vertical-align: middle;\n        text-align: center;\n        font-size: 1.1rem;\n      }\n\n      .table .btn {\n        padding: 0.5rem 1rem;\n        font-size: 1.1rem;\n      }\n\n   /* Change color of the edit icon */\n.edit-icon {\n  color: #007bff;  /* Blue */\n}\n\n/* Change color of the delete icon */\n.delete-icon {\n  color: #dc3545;  /* Red */\n}\n\n/* Optional: Add hover effect to both icons */\n.edit-icon:hover {\n  color: #0056b3;  /* Darker blue */\n}\n\n.delete-icon:hover {\n  color: #c82333;  /* Darker red */\n}\n\n      .material-icons {\n        font-size: 24px;\n      }\n    </style>\n\n    <title>Dashboard Template for Bootstrap</title>\n  </head>\n\n  <body>\n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"adminDash\">GTI</a>\n      <input class=\"form-control form-control-dark w-100\" type=\"text\" placeholder=\"Search\" aria-label=\"Search\">\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block bg-light sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"users\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Gestion des utilisateurs</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n                <button class=\"btn btn-sm btn-outline-secondary\">Import</button>\n                <button class=\"btn btn-sm btn-outline-secondary\">Export</button>\n              </div>\n            </div>\n          </div>\n        \n\n          <h2 class=\"text-center mt-5 mb-4\">Liste des utilisateurs</h2>\n          <div class=\"mb-3\">\n            <a [routerLink]=\"['/add-responsable']\" class=\"btn btn-custom\">\n              <i class=\"material-icons\" style=\"color: #fff; font-size: 20px;\" data-toggle=\"tooltip\" title=\"Ajouter un responsable\">&#xE145;</i>\n              Ajouter un responsable\n            </a>\n          </div>\n          \n          <div class=\"table-responsive\">\n            <table class=\"table table-striped\">\n              <thead>\n                <tr>\n                  <th>id</th>\n                  <th>Nom</th>\n                  <th>Prenom</th>\n                  <th>Email</th>\n                  <th>Telephone</th>\n                  <th>Role</th>\n                  <th>Group</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let responsable of responsables\">\n                  <td>{{ responsable.id }}</td>\n                  <td>{{ responsable.nom }}</td>\n                  <td>{{ responsable.prenom }}</td>\n                  <td>{{ responsable.email }}</td>\n                  <td>{{ responsable.telephone }}</td>\n                  <td>{{ responsable.role }}</td>\n                  <td>{{ responsable.groupe?.nomGroupe }}</td>\n\n                  <td>\n                    <a [routerLink]=\"['/edit-responsable', responsable.id]\" class=\"edit\" data-toggle=\"modal\">\n                      <i class=\"material-icons edit-icon\" data-toggle=\"tooltip\" title=\"Modifier\">&#xE254;</i>\n                    </a>\n                    <a href=\"#\" class=\"delete\" (click)=\"responsable.id ? deleteResponsable(responsable.id) : null\" data-toggle=\"modal\">\n                      <i class=\"material-icons delete-icon\" data-toggle=\"tooltip\" title=\"Supprimer\">&#xE872;</i>\n                    </a>\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\"></script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>feather.replace()</script>\n\n  </body>\n</html>\n"], "mappings": "AAGA,OAAO,KAAKA,OAAO,MAAM,eAAe;AACxC,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU,CAAC,CAAC;;;;;;;;;;;;ICmLjCC,EAAA,CAAAC,cAAA,SAA6C;IACvCD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5CH,EAAA,CAAAC,cAAA,UAAI;IAE2ED,EAAA,CAAAE,MAAA,cAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEzFH,EAAA,CAAAC,cAAA,aAAmH;IAAxFD,EAAA,CAAAI,UAAA,mBAAAC,kDAAA;MAAA,MAAAC,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,cAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAJ,cAAA,CAAAK,EAAA,GAAiBH,MAAA,CAAAI,iBAAA,CAAAN,cAAA,CAAAK,EAAA,CAAiC,GAAG,IAAI;IAAA,EAAC;IAC5Fd,EAAA,CAAAC,cAAA,aAA8E;IAAAD,EAAA,CAAAE,MAAA,cAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAb1FH,EAAA,CAAAgB,SAAA,GAAoB;IAApBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAK,EAAA,CAAoB;IACpBd,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAS,GAAA,CAAqB;IACrBlB,EAAA,CAAAgB,SAAA,GAAwB;IAAxBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAU,MAAA,CAAwB;IACxBnB,EAAA,CAAAgB,SAAA,GAAuB;IAAvBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAW,KAAA,CAAuB;IACvBpB,EAAA,CAAAgB,SAAA,GAA2B;IAA3BhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAY,SAAA,CAA2B;IAC3BrB,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAa,IAAA,CAAsB;IACtBtB,EAAA,CAAAgB,SAAA,GAAmC;IAAnChB,EAAA,CAAAiB,iBAAA,CAAAR,cAAA,CAAAc,MAAA,kBAAAd,cAAA,CAAAc,MAAA,CAAAC,SAAA,CAAmC;IAGlCxB,EAAA,CAAAgB,SAAA,GAAoD;IAApDhB,EAAA,CAAAyB,UAAA,eAAAzB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAAlB,cAAA,CAAAK,EAAA,EAAoD;;;;;;;ADrL3E,OAAM,MAAOc,cAAc;EAIzBC,YACUC,kBAAsC,EACtCC,GAAsB,EACtBC,MAAc;IAFd,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,YAAY,GAAW,EAAE;EAMtB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACL,kBAAkB,CAACM,WAAW,EAAE,CAACC,SAAS,CAC5CC,IAAI,IAAI;MACP,IAAI,CAACL,YAAY,GAAGK,IAAI;IAC1B,CAAC,EACAC,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,CACF;EACH;EAEAxB,iBAAiBA,CAACD,EAAW;IAC3B,IAAIA,EAAE,KAAK2B,SAAS,EAAE;MACpBD,OAAO,CAACD,KAAK,CAAC,oCAAoC,CAAC;MACnD;;IAEF,IAAIG,OAAO,CAAC,mDAAmD,CAAC,EAAE;MAChE,IAAI,CAACZ,kBAAkB,CAACf,iBAAiB,CAACD,EAAE,CAAC,CAACuB,SAAS,CAAC;QACtDM,IAAI,EAAGC,QAAQ,IAAI;UACjBJ,OAAO,CAACK,GAAG,CAAC,kBAAkB,EAAED,QAAQ,CAAC;UACzC,IAAI,CAACX,YAAY,GAAG,IAAI,CAACA,YAAY,CAACa,MAAM,CAACC,WAAW,IAAIA,WAAW,CAACjC,EAAE,KAAKA,EAAE,CAAC;QACpF,CAAC;QACDyB,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;OACD,CAAC;;EAEN;EAGAS,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCV,OAAO,CAACK,GAAG,CAACI,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,iBAAiBA,CAACvC,EAAU,EAAEwC,kBAAwB;IACpD,IAAIxC,EAAE,IAAIwC,kBAAkB,EAAE;MAAG;MAC/B,IAAI,CAACxB,kBAAkB,CAACuB,iBAAiB,CAACvC,EAAE,EAAEwC,kBAAkB,CAAC,CAACjB,SAAS,CACzE,MAAK;QACH,IAAI,CAACF,gBAAgB,EAAE;MACzB,CAAC,EACAI,KAAK,IAAI;QACRC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD,CAAC,CACF;;EAEL;EACAgB,SAASA,CAACC,KAAa,EAAEC,IAAU;IACjC,OAAOA,IAAI,CAAC3C,EAAE,IAAI,CAAC,CAAC,CAAE;EACxB;;EAGA4C,eAAeA,CAAA;IACb7D,OAAO,CAAC8D,OAAO,EAAE;IACjB7D,KAAK,CAAC8D,QAAQ,CAAC,GAAG7D,aAAa,CAAC,CAAC,CAAE;IAEnC,IAAI,IAAI,CAAC8D,UAAU,EAAE;MACnB,MAAMC,GAAG,GAAG,IAAI,CAACD,UAAU,CAACE,aAAkC;MAE9D;MACA,MAAMC,KAAK,GAAG,IAAIlE,KAAK,CAACgE,GAAG,EAAE;QAC3BG,IAAI,EAAE,MAAM;QACZ3B,IAAI,EAAE;UACJ4B,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;UACtFC,QAAQ,EAAE,CAAC;YACT7B,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YACvD8B,OAAO,EAAE,GAAG;YACZC,eAAe,EAAE,qBAAqB;YACtCC,WAAW,EAAE,SAAS;YACtBC,WAAW,EAAE,CAAC;YACdC,oBAAoB,EAAE;WACvB;SACF;QACDC,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE,KAAK;UAC1BC,MAAM,EAAE;YACNC,CAAC,EAAE;cACDC,WAAW,EAAE;;;;OAIpB,CAAC;;EAEN;;;uBArGWlD,cAAc,EAAA5B,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAjF,EAAA,CAAA+E,iBAAA,CAAA/E,EAAA,CAAAkF,iBAAA,GAAAlF,EAAA,CAAA+E,iBAAA,CAAAI,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdxD,cAAc;MAAAyD,SAAA;MAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAA1B,GAAA;QAAA,IAAA0B,EAAA;;;;;;;;;;;;;UCX3BxF,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAyF,SAAA,cAAsB;UAwGtBzF,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAE,MAAA,wCAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGjDH,EAAA,CAAAC,cAAA,YAAM;UAE8DD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAyF,SAAA,iBAAyG;UACzGzF,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAI,UAAA,mBAAAsF,4CAAA;YAAA,OAAS5B,GAAA,CAAAd,MAAA,EAAQ;UAAA,EAAC;UAAChD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKzDH,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAAyF,SAAA,gBAAiC;UACjCzF,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGpDH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAyF,SAAA,gBAAiC;UACjCzF,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAyF,SAAA,gBAAkC;UAClCzF,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMZH,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAC,cAAA,eAAsC;UAEeD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChEH,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMtEH,EAAA,CAAAC,cAAA,cAAkC;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7DH,EAAA,CAAAC,cAAA,eAAkB;UAEuGD,EAAA,CAAAE,MAAA,cAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjIH,EAAA,CAAAE,MAAA,gCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACXH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACZH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACfH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACbH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpBH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA2F,UAAA,KAAAC,6BAAA,mBAiBK;UACP5F,EAAA,CAAAG,YAAA,EAAQ;;;UAvCPH,EAAA,CAAAgB,SAAA,IAAmC;UAAnChB,EAAA,CAAAyB,UAAA,eAAAzB,EAAA,CAAA6F,eAAA,IAAAC,GAAA,EAAmC;UAqBN9F,EAAA,CAAAgB,SAAA,IAAe;UAAfhB,EAAA,CAAAyB,UAAA,YAAAqC,GAAA,CAAA7B,YAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}