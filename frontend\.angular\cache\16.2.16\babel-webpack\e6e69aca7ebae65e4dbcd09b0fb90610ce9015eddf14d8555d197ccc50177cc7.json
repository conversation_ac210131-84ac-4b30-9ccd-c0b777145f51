{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./authentication.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(next, state) {\n    const token = this.authService.getToken();\n    console.log(\"Token retrieved from storage:\", token); // Debugging\n    if (token) {\n      const {\n        role,\n        groupe\n      } = this.authService.getUserDetailsFromToken(token);\n      console.log(\"User role from token:\", role, \"Group:\", groupe); // Debugging\n      const currentPath = state.url.split('?')[0]; // Clean up query params\n      console.log(\"Current path (trimmed):\", currentPath);\n      if (currentPath === '/adminDash' && role !== 'ROLE_ADMIN') {\n        console.warn(\"Access denied for non-admin users.\");\n        this.router.navigate(['/not-authorized']);\n        return false;\n      } // Debugging\n      const allowedGroups = {\n        '/transactions': 'TRANSACTION',\n        '/actions': 'ACTION',\n        '/actionnaires': 'ACTIONNAIRE',\n        '/reports': 'NOTIFICATION',\n        '/port': 'PORTEFEUILLE',\n        '/ResDash': groupe // Always allow responsible users to access their dashboard\n      };\n\n      const expectedGroup = allowedGroups[currentPath];\n      console.log(\"Expected group:\", expectedGroup); // Debugging\n      // Check if the current group matches the expected group for the path\n      if (expectedGroup && groupe !== expectedGroup) {\n        console.warn(\"Access denied for group:\", groupe);\n        this.router.navigate(['/not-authorized']);\n        return false;\n      }\n      // Allow access if everything matches\n      return true;\n    }\n    // If no token, redirect to login\n    this.router.navigate(['/login']);\n    return false;\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthenticationService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "next", "state", "token", "getToken", "console", "log", "role", "groupe", "getUserDetailsFromToken", "currentPath", "url", "split", "warn", "navigate", "allowedGroups", "expectedGroup", "i0", "ɵɵinject", "i1", "AuthenticationService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { AuthenticationService } from './authentication.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {\n    const token = this.authService.getToken();\n    console.log(\"Token retrieved from storage:\", token);  // Debugging\n\n    if (token) {\n      const { role, groupe } = this.authService.getUserDetailsFromToken(token);\n      console.log(\"User role from token:\", role, \"Group:\", groupe);  // Debugging\n\n      const currentPath = state.url.split('?')[0]; // Clean up query params\n      console.log(\"Current path (trimmed):\", currentPath);\n       \n      if (currentPath === '/adminDash' && role !== 'ROLE_ADMIN') {\n        console.warn(\"Access denied for non-admin users.\");\n        this.router.navigate(['/not-authorized']);\n        return false;\n      } // Debugging\n\n      const allowedGroups: Record<string, string> = {\n        '/transactions': 'TRANSACTION',\n        '/actions': 'ACTION',\n        '/actionnaires': 'ACTIONNAIRE',\n        '/reports': 'NOTIFICATION',\n        '/port': 'PORTEFEUILLE',\n        '/ResDash': groupe // Always allow responsible users to access their dashboard\n      };\n\n      const expectedGroup = allowedGroups[currentPath];\n      console.log(\"Expected group:\", expectedGroup);  // Debugging\n\n      // Check if the current group matches the expected group for the path\n      if (expectedGroup && groupe !== expectedGroup) {\n        console.warn(\"Access denied for group:\", groupe);\n        this.router.navigate(['/not-authorized']);\n        return false;\n      }\n\n      // Allow access if everything matches\n      return true;\n    }\n\n    // If no token, redirect to login\n    this.router.navigate(['/login']);\n    return false;\n  }\n}\n"], "mappings": ";;;AAOA,OAAM,MAAOA,SAAS;EAEpBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;EAAW;EAEjFC,WAAWA,CAACC,IAA4B,EAAEC,KAA0B;IAClE,MAAMC,KAAK,GAAG,IAAI,CAACL,WAAW,CAACM,QAAQ,EAAE;IACzCC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,KAAK,CAAC,CAAC,CAAE;IAEtD,IAAIA,KAAK,EAAE;MACT,MAAM;QAAEI,IAAI;QAAEC;MAAM,CAAE,GAAG,IAAI,CAACV,WAAW,CAACW,uBAAuB,CAACN,KAAK,CAAC;MACxEE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,IAAI,EAAE,QAAQ,EAAEC,MAAM,CAAC,CAAC,CAAE;MAE/D,MAAME,WAAW,GAAGR,KAAK,CAACS,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7CP,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEI,WAAW,CAAC;MAEnD,IAAIA,WAAW,KAAK,YAAY,IAAIH,IAAI,KAAK,YAAY,EAAE;QACzDF,OAAO,CAACQ,IAAI,CAAC,oCAAoC,CAAC;QAClD,IAAI,CAACd,MAAM,CAACe,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QACzC,OAAO,KAAK;OACb,CAAC;MAEF,MAAMC,aAAa,GAA2B;QAC5C,eAAe,EAAE,aAAa;QAC9B,UAAU,EAAE,QAAQ;QACpB,eAAe,EAAE,aAAa;QAC9B,UAAU,EAAE,cAAc;QAC1B,OAAO,EAAE,cAAc;QACvB,UAAU,EAAEP,MAAM,CAAC;OACpB;;MAED,MAAMQ,aAAa,GAAGD,aAAa,CAACL,WAAW,CAAC;MAChDL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEU,aAAa,CAAC,CAAC,CAAE;MAEhD;MACA,IAAIA,aAAa,IAAIR,MAAM,KAAKQ,aAAa,EAAE;QAC7CX,OAAO,CAACQ,IAAI,CAAC,0BAA0B,EAAEL,MAAM,CAAC;QAChD,IAAI,CAACT,MAAM,CAACe,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QACzC,OAAO,KAAK;;MAGd;MACA,OAAO,IAAI;;IAGb;IACA,IAAI,CAACf,MAAM,CAACe,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAChC,OAAO,KAAK;EACd;;;uBA/CWlB,SAAS,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAT1B,SAAS;MAAA2B,OAAA,EAAT3B,SAAS,CAAA4B,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}