{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { JwtInterceptor } from './auth/jwt.interceptor';\nimport { AppRoutingModule } from './app-routing.module';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component'; // Import the AppRoutingModule\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, LoginComponent, SignupComponent, ResponDashboardComponent, NotAuthorizedComponent, TransactionsComponent, ActionsComponent, ActionnairesComponent, ReportsComponent, PortefeuillesComponent],\n  imports: [BrowserModule, FormsModule, HttpClientModule, AppRoutingModule // Include it here\n  ],\n\n  providers: [{\n    provide: HTTP_INTERCEPTORS,\n    useClass: JwtInterceptor,\n    multi: true\n  }],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "FormsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "AppComponent", "LoginComponent", "SignupComponent", "JwtInterceptor", "AppRoutingModule", "ResponDashboardComponent", "NotAuthorizedComponent", "TransactionsComponent", "ActionsComponent", "ActionnairesComponent", "ReportsComponent", "PortefeuillesComponent", "AppModule", "__decorate", "declarations", "imports", "providers", "provide", "useClass", "multi", "bootstrap"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { JwtInterceptor } from './auth/jwt.interceptor'; \nimport { AppRoutingModule } from './app-routing.module';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';  // Import the AppRoutingModule\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    SignupComponent,\n    ResponDashboardComponent,\n    NotAuthorizedComponent,\n    TransactionsComponent,\n    ActionsComponent,\n    ActionnairesComponent,\n    ReportsComponent,\n    PortefeuillesComponent\n  ],\n  imports: [\n    BrowserModule,\n    FormsModule,\n    HttpClientModule,\n    AppRoutingModule  // Include it here\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: JwtInterceptor,\n      multi: true\n    }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,sBAAsB,QAAQ,yCAAyC,CAAC,CAAE;AA8B5E,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EA5BrBlB,QAAQ,CAAC;EACRmB,YAAY,EAAE,CACZd,YAAY,EACZC,cAAc,EACdC,eAAe,EACfG,wBAAwB,EACxBC,sBAAsB,EACtBC,qBAAqB,EACrBC,gBAAgB,EAChBC,qBAAqB,EACrBC,gBAAgB,EAChBC,sBAAsB,CACvB;EACDI,OAAO,EAAE,CACPnB,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChBM,gBAAgB,CAAE;EAAA,CACnB;;EACDY,SAAS,EAAE,CACT;IACEC,OAAO,EAAElB,iBAAiB;IAC1BmB,QAAQ,EAAEf,cAAc;IACxBgB,KAAK,EAAE;GACR,CACF;EACDC,SAAS,EAAE,CAACpB,YAAY;CACzB,CAAC,C,EACWY,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}