{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/groupe.service\";\nimport * as i2 from \"../services/ressource.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = function (a1) {\n  return [\"/edit-groupe\", a1];\n};\nfunction GroupsComponent_tr_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"a\", 58)(7, \"i\", 59);\n    i0.ɵɵtext(8, \"\\uE254\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"a\", 60);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_tr_74_Template_a_click_9_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const groupe_r2 = restoredCtx.$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(groupe_r2.idGroupe ? ctx_r3.deleteGroupe(groupe_r2.idGroupe, $event) : null);\n    });\n    i0.ɵɵelementStart(10, \"i\", 61);\n    i0.ɵɵtext(11, \"\\uE872\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const groupe_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r2.idGroupe || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r2.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, groupe_r2.idGroupe));\n  }\n}\nfunction GroupsComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 62)(2, \"input\", 63);\n    i0.ɵɵlistener(\"change\", function GroupsComponent_div_90_Template_input_change_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const ressource_r5 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onCheckboxChange($event, ressource_r5.idRessource));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 64);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementStart(5, \"small\", 65);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ressource_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r1.selectedRessources.includes(ressource_r5.idRessource));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ressource_r5.nomRessource, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ressource_r5.link_path, \")\");\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, 0.25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n    border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n    border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n    font-size: 14px;\\n    border: none;\\n    cursor: pointer;\\n    border-radius: 4px;\\n    font-weight: bold;\\n    transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n    font-family: 'Poppins', sans-serif;\\n    margin: 0;\\n    padding: 0;\\n    min-height: 100vh;\\n    background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n    background-size: 400% 400%;\\n    animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n    color: #fff;\\n}\\n\\n\\n.modal-content.border-danger[_ngcontent-%COMP%] {\\n    border: 2px solid #dc3545;\\n    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);\\n  }\\n  \\n  .modal-header.bg-danger[_ngcontent-%COMP%] {\\n    background-color: #dc3545 !important;\\n    color: white;\\n  }\\n  \\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n    0% {\\n        background-position: 0% 50%;\\n    }\\n\\n    50% {\\n        background-position: 100% 50%;\\n    }\\n\\n    100% {\\n        background-position: 0% 50%;\\n    }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 2rem;\\n    margin-top: 20px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 1.5rem;\\n    -webkit-backdrop-filter: blur(12px);\\n            backdrop-filter: blur(12px);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n    color: #fff;\\n    border-radius: 15px;\\n    overflow: hidden;\\n    border-collapse: separate;\\n    border-spacing: 0 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.15);\\n    color: #ffffff;\\n    font-weight: 600;\\n    text-align: center;\\n    border: none;\\n    padding: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.1);\\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\\n    text-align: center;\\n    border-radius: 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.01);\\n    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    vertical-align: middle;\\n    padding: 0.9rem;\\n    font-size: 0.95rem;\\n}\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n    background-color: #28a745;\\n    color: white;\\n    border: none;\\n    padding: 12px 25px;\\n    border-radius: 30px;\\n    text-transform: uppercase;\\n    font-weight: bold;\\n    transition: background 0.3s, transform 0.2s;\\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n    background-color: #218838;\\n    transform: translateY(-2px);\\n}\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n    color: #206ee1;\\n    cursor: pointer;\\n    font-size: 20px;\\n    margin: 0 10px;\\n    transition: color 0.3s;\\n}\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n    color: #d22d2d;\\n    cursor: pointer;\\n    font-size: 20px;\\n    transition: color 0.3s;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n    background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n    color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n    text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n    background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n    background-color: #fff;\\n    padding: 1.5rem;\\n    color: #333;\\n    font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n    background-color: #f1f1f1;\\n    padding: 1rem;\\n    border-top: 1px solid #ddd;\\n    display: flex;\\n    justify-content: center; \\n\\n    gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem;\\n    font-size: 1rem;\\n    border-radius: 0.3rem;\\n    transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    color: #fff;\\n    border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n    transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n    background-color: #007bff;\\n    color: #fff;\\n    border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n    transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column; \\n\\n    align-items: flex-start; \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n    margin-top: 10px;\\n    background-color: rgba(255, 255, 255, 0.1); \\n\\n    border: 1px solid rgba(255, 255, 255, 0.3); \\n\\n    border-radius: 30px; \\n\\n    color: #fff; \\n\\n    padding: 10px 20px; \\n\\n    font-size: 1rem; \\n\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); \\n\\n    width: 100%; \\n\\n    max-width: 400px; \\n\\n    transition: all 0.3s ease; \\n\\n}\\n\\n.search-bar[_ngcontent-%COMP%]:focus {\\n    outline: none; \\n\\n    border-color: #007bff; \\n\\n    box-shadow: 0 0 8px rgba(0, 123, 255, 0.6); \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%]::placeholder {\\n    color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n    background: rgba(20, 33, 59, 0.9); \\n\\n    color: #fff;\\n    min-height: 100vh;\\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #ccc;\\n    transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, 0.1);\\n    border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n    background-color: #000 !important;\\n    color: #fff;\\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n    position: relative;\\n    padding: 0.5rem 1rem;\\n    transition: color 0.3s ease;\\n    font-weight: 500;\\n    letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n    content: '';\\n    position: absolute;\\n    left: 0;\\n    bottom: 0;\\n    height: 2px;\\n    width: 0;\\n    background: #ff4c60;\\n    transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n    width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n    height: 30px;\\n    width: 30px;\\n    object-fit: cover;\\n    border-radius: 50%; \\n\\n    margin-right: 8px;\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class GroupsComponent {\n  constructor(groupeService, ressourceService, router) {\n    this.groupeService = groupeService;\n    this.ressourceService = ressourceService;\n    this.router = router;\n    this.groupes = [];\n    this.filteredGroupes = [];\n    this.newGroupe = {\n      nomGroupe: '',\n      ressources: []\n    };\n    this.editGroupe = null;\n    this.searchQuery = '';\n    this.ressources = [];\n    this.selectedRessources = [];\n  }\n  ngOnInit() {\n    this.loadGroupes();\n    this.loadRessources();\n  }\n  ngAfterViewInit() {\n    feather.replace();\n  }\n  loadGroupes() {\n    this.groupeService.getAllGroupes().subscribe(data => {\n      this.groupes = data;\n      this.filteredGroupes = data;\n    }, err => {\n      console.error('Error loading groups', err);\n    });\n  }\n  loadRessources() {\n    this.ressourceService.getAllRessources().subscribe(data => {\n      this.ressources = data;\n    }, err => {\n      console.error('Error loading ressources', err);\n    });\n  }\n  filterGroups() {\n    if (!this.searchQuery) {\n      this.filteredGroupes = this.groupes;\n    } else {\n      this.filteredGroupes = this.groupes.filter(groupe => groupe.nomGroupe.toLowerCase().includes(this.searchQuery.toLowerCase()));\n    }\n  }\n  addGroupe() {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.newGroupe.ressources = this.selectedRessources;\n      this.groupeService.addGroupe(this.newGroupe).subscribe(() => {\n        this.newGroupe = {\n          nomGroupe: '',\n          ressources: []\n        };\n        this.selectedRessources = [];\n        this.loadGroupes();\n      }, err => {\n        console.error('Error adding group', err);\n      });\n    }\n  }\n  setEditGroupe(groupe) {\n    this.editGroupe = {\n      ...groupe\n    };\n  }\n  updateGroupe() {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(() => {\n        this.editGroupe = null;\n        this.loadGroupes();\n      }, err => {\n        console.error('Error updating group', err);\n      });\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n  deleteGroupe(id, event) {\n    event.preventDefault();\n    this.groupeService.deleteGroupe(id).subscribe({\n      next: () => {\n        console.log('Group deleted successfully');\n        this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n        this.filterGroups();\n      },\n      error: err => {\n        if (err.error && err.error.message && err.error.message.includes('users')) {\n          alert('❌ You cannot delete this group because it is linked to users.');\n        } else {\n          console.error('Error deleting group', err);\n        }\n      }\n    });\n  }\n  onCheckboxChange(event, ressourceId) {\n    if (event.target.checked) {\n      if (!this.selectedRessources.includes(ressourceId)) {\n        this.selectedRessources.push(ressourceId);\n      }\n    } else {\n      this.selectedRessources = this.selectedRessources.filter(id => id !== ressourceId);\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token'));\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function GroupsComponent_Factory(t) {\n      return new (t || GroupsComponent)(i0.ɵɵdirectiveInject(i1.GroupeService), i0.ɵɵdirectiveInject(i2.RessourceService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupsComponent,\n      selectors: [[\"app-groups\"]],\n      decls: 96,\n      vars: 4,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"text-center\", \"mt-4\", \"mb-4\"], [1, \"d-flex\", \"justify-content-end\", \"mb-2\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#addGroupeModal\", 1, \"btn\", \"btn-custom\"], [1, \"material-icons\", \"align-middle\", 2, \"font-size\", \"20px\"], [1, \"table-responsive\"], [1, \"mb-3\", \"d-flex\", \"justify-content-between\"], [\"type\", \"text\", \"placeholder\", \"Rechercher\", \"aria-label\", \"Search\", 1, \"form-control\", \"w-50\", \"search-bar\", 3, \"ngModel\", \"ngModelChange\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"addGroupeModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"addGroupeModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [3, \"ngSubmit\"], [1, \"modal-header\"], [\"id\", \"addGroupeModalLabel\", 1, \"modal-title\"], [1, \"modal-body\"], [1, \"form-group\"], [\"for\", \"nomGroupe\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [1, \"edit\", 3, \"routerLink\"], [\"data-toggle\", \"tooltip\", \"title\", \"Modifier\", 1, \"material-icons\", \"edit-icon\"], [1, \"delete\", 3, \"click\"], [\"data-toggle\", \"tooltip\", \"title\", \"Supprimer\", 1, \"material-icons\", \"delete-icon\"], [1, \"form-check\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"checked\", \"change\"], [1, \"form-check-label\"], [1, \"text-muted\"]],\n      template: function GroupsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"link\", 6)(8, \"link\", 7)(9, \"link\", 8)(10, \"link\", 9);\n          i0.ɵɵelementStart(11, \"title\");\n          i0.ɵɵtext(12, \"Gestion des Groupes\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"body\")(14, \"nav\", 10)(15, \"a\", 11);\n          i0.ɵɵelement(16, \"img\", 12);\n          i0.ɵɵtext(17, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"ul\", 13)(19, \"li\", 14)(20, \"a\", 15);\n          i0.ɵɵlistener(\"click\", function GroupsComponent_Template_a_click_20_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(21, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"nav\", 18)(25, \"div\", 19)(26, \"ul\", 20)(27, \"li\", 21)(28, \"a\", 22);\n          i0.ɵɵelement(29, \"span\", 23);\n          i0.ɵɵtext(30, \" Dashboard \");\n          i0.ɵɵelementStart(31, \"span\", 24);\n          i0.ɵɵtext(32, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"li\", 21)(34, \"a\", 25);\n          i0.ɵɵelement(35, \"span\", 26);\n          i0.ɵɵtext(36, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"li\", 21)(38, \"a\", 27);\n          i0.ɵɵelement(39, \"span\", 28);\n          i0.ɵɵtext(40, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\", 21)(42, \"a\", 29);\n          i0.ɵɵelement(43, \"span\", 30);\n          i0.ɵɵtext(44, \" Gestion des transactions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"li\", 21)(46, \"a\", 31);\n          i0.ɵɵelement(47, \"span\", 32);\n          i0.ɵɵtext(48, \" Gestion des actions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"li\", 21)(50, \"a\", 33);\n          i0.ɵɵelement(51, \"span\", 34);\n          i0.ɵɵtext(52, \" Gestion des actionnaires \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(53, \"main\", 35)(54, \"h2\", 36);\n          i0.ɵɵtext(55, \"Gestion des groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\", 37)(57, \"button\", 38)(58, \"i\", 39);\n          i0.ɵɵtext(59, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" Ajouter un groupe \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"div\", 40)(62, \"div\", 41)(63, \"input\", 42);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_63_listener($event) {\n            return ctx.searchQuery = $event;\n          })(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_63_listener() {\n            return ctx.filterGroups();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"table\", 43)(65, \"thead\")(66, \"tr\")(67, \"th\");\n          i0.ɵɵtext(68, \"R\\u00E9ference\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"th\");\n          i0.ɵɵtext(70, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"th\");\n          i0.ɵɵtext(72, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"tbody\");\n          i0.ɵɵtemplate(74, GroupsComponent_tr_74_Template, 12, 5, \"tr\", 44);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(75, \"div\", 45)(76, \"div\", 46)(77, \"div\", 47)(78, \"form\", 48);\n          i0.ɵɵlistener(\"ngSubmit\", function GroupsComponent_Template_form_ngSubmit_78_listener() {\n            return ctx.addGroupe();\n          });\n          i0.ɵɵelementStart(79, \"div\", 49)(80, \"h5\", 50);\n          i0.ɵɵtext(81, \"Ajouter un groupe\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 51)(83, \"div\", 52)(84, \"label\", 53);\n          i0.ɵɵtext(85, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"input\", 54);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_86_listener($event) {\n            return ctx.newGroupe.nomGroupe = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 52)(88, \"label\");\n          i0.ɵɵtext(89, \"Ressources associ\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(90, GroupsComponent_div_90_Template, 7, 3, \"div\", 44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"div\", 55)(92, \"button\", 56);\n          i0.ɵɵtext(93, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"button\", 57);\n          i0.ɵɵtext(95, \"Ajouter\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(63);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredGroupes);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngModel\", ctx.newGroupe.nomGroupe);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.ressources);\n        }\n      },\n      dependencies: [i4.NgForOf, i3.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GroupsComponent_tr_74_Template_a_click_9_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r4", "groupe_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "idGroupe", "deleteGroupe", "ɵɵadvance", "ɵɵtextInterpolate", "nomGroupe", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "GroupsComponent_div_90_Template_input_change_2_listener", "_r7", "ressource_r5", "ctx_r6", "onCheckboxChange", "idRessource", "ctx_r1", "selectedRessources", "includes", "ɵɵtextInterpolate1", "nomRessource", "link_path", "GroupsComponent", "constructor", "groupeService", "ressourceService", "router", "groupes", "filteredGroupes", "newGroupe", "ressources", "editGroupe", "searchQuery", "ngOnInit", "loadGroupes", "loadRessources", "ngAfterViewInit", "replace", "getAllGroupes", "subscribe", "data", "err", "console", "error", "getAllRessources", "filterGroups", "filter", "groupe", "toLowerCase", "addGroupe", "trim", "setEditGroupe", "updateGroupe", "id", "event", "preventDefault", "next", "log", "message", "alert", "ressourceId", "target", "checked", "push", "logout", "localStorage", "removeItem", "getItem", "navigate", "ɵɵdirectiveInject", "i1", "GroupeService", "i2", "RessourceService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "GroupsComponent_Template", "rf", "ctx", "ɵɵelement", "GroupsComponent_Template_a_click_20_listener", "GroupsComponent_Template_input_ngModelChange_63_listener", "ɵɵtemplate", "GroupsComponent_tr_74_Template", "GroupsComponent_Template_form_ngSubmit_78_listener", "GroupsComponent_Template_input_ngModelChange_86_listener", "GroupsComponent_div_90_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\groups\\groups.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\groups\\groups.component.html"], "sourcesContent": ["import { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Component, OnInit, AfterViewInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\nimport { RessourceService } from '../services/ressource.service';\nimport { Ressource } from '../model/ressource.model';\n\n@Component({\n  selector: 'app-groups',\n  templateUrl: './groups.component.html',\n  styleUrls: ['./groups.component.css']\n})\nexport class GroupsComponent implements OnInit, AfterViewInit {\n  groupes: Groupe[] = [];\n  filteredGroupes: Groupe[] = [];\n  newGroupe: Groupe = { nomGroupe: '', ressources: [] };\n  editGroupe: Groupe | null = null;\n  searchQuery: string = '';\n\n  ressources: Ressource[] = [];\n  selectedRessources: number[] = [];\n\n  constructor(\n    private groupeService: GroupeService,\n    private ressourceService: RessourceService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadGroupes();\n    this.loadRessources();\n  }\n\n  ngAfterViewInit(): void {\n    feather.replace();\n  }\n\n  loadGroupes(): void {\n    this.groupeService.getAllGroupes().subscribe(\n      (data) => {\n        this.groupes = data;\n        this.filteredGroupes = data;\n      },\n      (err) => {\n        console.error('Error loading groups', err);\n      }\n    );\n  }\n\n  loadRessources(): void {\n    this.ressourceService.getAllRessources().subscribe(\n      (data) => {\n        this.ressources = data;\n      },\n      (err) => {\n        console.error('Error loading ressources', err);\n      }\n    );\n  }\n\n  filterGroups(): void {\n    if (!this.searchQuery) {\n      this.filteredGroupes = this.groupes;\n    } else {\n      this.filteredGroupes = this.groupes.filter(groupe =>\n        groupe.nomGroupe.toLowerCase().includes(this.searchQuery.toLowerCase())\n      );\n    }\n  }\n\n  addGroupe(): void {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.newGroupe.ressources = this.selectedRessources;\n\n      this.groupeService.addGroupe(this.newGroupe).subscribe(\n        () => {\n          this.newGroupe = { nomGroupe: '', ressources: [] };\n          this.selectedRessources = [];\n          this.loadGroupes();\n        },\n        (err) => {\n          console.error('Error adding group', err);\n        }\n      );\n    }\n  }\n\n  setEditGroupe(groupe: Groupe): void {\n    this.editGroupe = { ...groupe };\n  }\n\n  updateGroupe(): void {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(\n        () => {\n          this.editGroupe = null;\n          this.loadGroupes();\n        },\n        (err) => {\n          console.error('Error updating group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n\n  deleteGroupe(id: number, event: Event): void {\n    event.preventDefault();\n  \n    this.groupeService.deleteGroupe(id).subscribe({\n      next: () => {\n        console.log('Group deleted successfully');\n        this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n        this.filterGroups();\n      },\n      error: (err) => {\n        if (err.error && err.error.message && err.error.message.includes('users')) {\n          alert('❌ You cannot delete this group because it is linked to users.');\n        } else {\n          console.error('Error deleting group', err);\n        }\n      }\n    });\n  }\n  \n\n  onCheckboxChange(event: any, ressourceId: number): void {\n    if (event.target.checked) {\n      if (!this.selectedRessources.includes(ressourceId)) {\n        this.selectedRessources.push(ressourceId);\n      }\n    } else {\n      this.selectedRessources = this.selectedRessources.filter(id => id !== ressourceId);\n    }\n  }\n\n  logout(): void {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token'));\n    this.router.navigate(['/login']);\n  }\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n    <link href=\"./groups.component.css\" rel=\"stylesheet\">\n    <title>Gestion des Groupes</title>\n  </head>\n\n  <body>\n    <!-- Navbar -->\n    <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\" href=\"adminDash\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n    \n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\"  href=\"/transactions\">\n                  <span data-feather=\"dollar-sign\"></span>\n                  Gestion des transactions\n                </a>\n                <li class=\"nav-item\">\n                  <a class=\"nav-link\" href=\"/actions\">\n                    <span data-feather=\"trending-up\"></span>\n                   Gestion des actions\n                  </a>\n                  <li class=\"nav-item\">\n                    <a class=\"nav-link\" href=\"/actionnaires\">\n                      <span data-feather=\"users\"></span>\n                      Gestion des actionnaires\n                    </a>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <h2 class=\"text-center mt-4 mb-4\">Gestion des groupes</h2>\n\n       \n\n          <div class=\"d-flex justify-content-end mb-2\">\n            <button type=\"button\" class=\"btn btn-custom\" data-toggle=\"modal\" data-target=\"#addGroupeModal\">\n              <i class=\"material-icons align-middle\" style=\"font-size: 20px;\">add</i>\n              Ajouter un groupe\n            </button>\n          </div>\n          \n          \n\n          <!-- Table -->\n          <div class=\"table-responsive\">\n             <!-- Search Bar and Add Button -->\n             <div class=\"mb-3 d-flex justify-content-between\">\n              <input class=\"form-control w-50 search-bar\" \n                     type=\"text\" \n                     placeholder=\"Rechercher\" \n                     aria-label=\"Search\" \n                     [(ngModel)]=\"searchQuery\" \n                     (ngModelChange)=\"filterGroups()\">\n            </div>\n            <table class=\"table table-hover\">\n              <thead>\n                <tr>\n                  <th>Réference</th>\n                  <th>Nom</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let groupe of filteredGroupes\">\n                  <td>{{ groupe.idGroupe || 'N/A' }}</td>\n                  <td>{{ groupe.nomGroupe }}</td>\n                  <td>\n                    <a [routerLink]=\"['/edit-groupe', groupe.idGroupe]\" class=\"edit\">\n                      <i class=\"material-icons edit-icon\" data-toggle=\"tooltip\" title=\"Modifier\">&#xE254;</i>\n                    </a>\n                    <a class=\"delete\" (click)=\"groupe.idGroupe ? deleteGroupe(groupe.idGroupe, $event) : null\">\n                      <i class=\"material-icons delete-icon\" data-toggle=\"tooltip\" title=\"Supprimer\">&#xE872;</i>\n                    </a>\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <!-- Modal: Ajouter un groupe -->\n    <div class=\"modal fade\" id=\"addGroupeModal\" tabindex=\"-1\" aria-labelledby=\"addGroupeModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog\">\n        <div class=\"modal-content\">\n          <form (ngSubmit)=\"addGroupe()\">\n            <div class=\"modal-header\">\n              <h5 class=\"modal-title\" id=\"addGroupeModalLabel\">Ajouter un groupe</h5>\n            </div>\n            <div class=\"modal-body\">\n              <div class=\"form-group\">\n                <label for=\"nomGroupe\">Nom du groupe</label>\n                <input type=\"text\" id=\"nomGroupe\" [(ngModel)]=\"newGroupe.nomGroupe\" name=\"nomGroupe\" class=\"form-control\" required>\n              </div>\n\n              <!-- Ressource Checkboxes -->\n              <div class=\"form-group\">\n              <label>Ressources associées</label>\n               <div *ngFor=\"let ressource of ressources\">\n                   <div class=\"form-check\">\n                   <input type=\"checkbox\"\n                  class=\"form-check-input\"\n                   [checked]=\"selectedRessources.includes(ressource.idRessource)\"\n                     (change)=\"onCheckboxChange($event, ressource.idRessource)\">\n                   <label class=\"form-check-label\">\n              {{ ressource.nomRessource }} <small class=\"text-muted\">({{ ressource.link_path }})</small>\n                 </label>\n                  </div>\n                    </div>\n                    </div>\n\n\n            </div>\n            <div class=\"modal-footer\">\n              <button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\">Annuler</button>\n              <button type=\"submit\" class=\"btn btn-primary\">Ajouter</button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n\n\n\n\n    <!-- Scripts -->\n    <script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/@popperjs/core@2.10.2/dist/umd/popper.min.js\"></script>\n    <script src=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/js/bootstrap.min.js\"></script>\n  </body>\n</html>\n"], "mappings": "AAIA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;;;;;;;;;;ICuGxBC,EAAA,CAAAC,cAAA,SAA2C;IACrCD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAE2ED,EAAA,CAAAE,MAAA,aAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEzFH,EAAA,CAAAC,cAAA,YAA2F;IAAzED,EAAA,CAAAI,UAAA,mBAAAC,kDAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAJ,SAAA,CAAAK,QAAA,GAAkBH,MAAA,CAAAI,YAAA,CAAAN,SAAA,CAAAK,QAAA,EAAAT,MAAA,CAAqC,GAAG,IAAI;IAAA,EAAC;IACxFN,EAAA,CAAAC,cAAA,aAA8E;IAAAD,EAAA,CAAAE,MAAA,cAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAP1FH,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAkB,iBAAA,CAAAR,SAAA,CAAAK,QAAA,UAA8B;IAC9Bf,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,iBAAA,CAAAR,SAAA,CAAAS,SAAA,CAAsB;IAErBnB,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAoB,UAAA,eAAApB,EAAA,CAAAqB,eAAA,IAAAC,GAAA,EAAAZ,SAAA,CAAAK,QAAA,EAAgD;;;;;;IAgCxDf,EAAA,CAAAC,cAAA,UAA0C;IAKpCD,EAAA,CAAAI,UAAA,oBAAAmB,wDAAAjB,MAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAgB,GAAA;MAAA,MAAAC,YAAA,GAAAlB,WAAA,CAAAI,SAAA;MAAA,MAAAe,MAAA,GAAA1B,EAAA,CAAAa,aAAA;MAAA,OAAUb,EAAA,CAAAc,WAAA,CAAAY,MAAA,CAAAC,gBAAA,CAAArB,MAAA,EAAAmB,YAAA,CAAAG,WAAA,CAA+C;IAAA,EAAC;IAH5D5B,EAAA,CAAAG,YAAA,EAG6D;IAC7DH,EAAA,CAAAC,cAAA,gBAAgC;IACrCD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAHrFH,EAAA,CAAAiB,SAAA,GAA8D;IAA9DjB,EAAA,CAAAoB,UAAA,YAAAS,MAAA,CAAAC,kBAAA,CAAAC,QAAA,CAAAN,YAAA,CAAAG,WAAA,EAA8D;IAGnE5B,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAAgC,kBAAA,MAAAP,YAAA,CAAAQ,YAAA,MAA6B;IAA0BjC,EAAA,CAAAiB,SAAA,GAA2B;IAA3BjB,EAAA,CAAAgC,kBAAA,MAAAP,YAAA,CAAAS,SAAA,MAA2B;;;;ADzIhG,OAAM,MAAOC,eAAe;EAU1BC,YACUC,aAA4B,EAC5BC,gBAAkC,EAClCC,MAAc;IAFd,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,MAAM,GAANA,MAAM;IAZhB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,SAAS,GAAW;MAAEvB,SAAS,EAAE,EAAE;MAAEwB,UAAU,EAAE;IAAE,CAAE;IACrD,KAAAC,UAAU,GAAkB,IAAI;IAChC,KAAAC,WAAW,GAAW,EAAE;IAExB,KAAAF,UAAU,GAAgB,EAAE;IAC5B,KAAAb,kBAAkB,GAAa,EAAE;EAM9B;EAEHgB,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,eAAeA,CAAA;IACblD,OAAO,CAACmD,OAAO,EAAE;EACnB;EAEAH,WAAWA,CAAA;IACT,IAAI,CAACV,aAAa,CAACc,aAAa,EAAE,CAACC,SAAS,CACzCC,IAAI,IAAI;MACP,IAAI,CAACb,OAAO,GAAGa,IAAI;MACnB,IAAI,CAACZ,eAAe,GAAGY,IAAI;IAC7B,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;IAC5C,CAAC,CACF;EACH;EAEAN,cAAcA,CAAA;IACZ,IAAI,CAACV,gBAAgB,CAACmB,gBAAgB,EAAE,CAACL,SAAS,CAC/CC,IAAI,IAAI;MACP,IAAI,CAACV,UAAU,GAAGU,IAAI;IACxB,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEF,GAAG,CAAC;IAChD,CAAC,CACF;EACH;EAEAI,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACb,WAAW,EAAE;MACrB,IAAI,CAACJ,eAAe,GAAG,IAAI,CAACD,OAAO;KACpC,MAAM;MACL,IAAI,CAACC,eAAe,GAAG,IAAI,CAACD,OAAO,CAACmB,MAAM,CAACC,MAAM,IAC/CA,MAAM,CAACzC,SAAS,CAAC0C,WAAW,EAAE,CAAC9B,QAAQ,CAAC,IAAI,CAACc,WAAW,CAACgB,WAAW,EAAE,CAAC,CACxE;;EAEL;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACpB,SAAS,CAACvB,SAAS,CAAC4C,IAAI,EAAE,EAAE;MACnC,IAAI,CAACrB,SAAS,CAACC,UAAU,GAAG,IAAI,CAACb,kBAAkB;MAEnD,IAAI,CAACO,aAAa,CAACyB,SAAS,CAAC,IAAI,CAACpB,SAAS,CAAC,CAACU,SAAS,CACpD,MAAK;QACH,IAAI,CAACV,SAAS,GAAG;UAAEvB,SAAS,EAAE,EAAE;UAAEwB,UAAU,EAAE;QAAE,CAAE;QAClD,IAAI,CAACb,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACiB,WAAW,EAAE;MACpB,CAAC,EACAO,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;MAC1C,CAAC,CACF;;EAEL;EAEAU,aAAaA,CAACJ,MAAc;IAC1B,IAAI,CAAChB,UAAU,GAAG;MAAE,GAAGgB;IAAM,CAAE;EACjC;EAEAK,YAAYA,CAAA;IACV,IAAI,IAAI,CAACrB,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC7B,QAAQ,EAAE;MAC/C,IAAI,CAACsB,aAAa,CAAC4B,YAAY,CAAC,IAAI,CAACrB,UAAU,CAAC7B,QAAQ,EAAE,IAAI,CAAC6B,UAAU,CAAC,CAACQ,SAAS,CAClF,MAAK;QACH,IAAI,CAACR,UAAU,GAAG,IAAI;QACtB,IAAI,CAACG,WAAW,EAAE;MACpB,CAAC,EACAO,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;;EAEtD;EAEAxC,YAAYA,CAACkD,EAAU,EAAEC,KAAY;IACnCA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI,CAAC/B,aAAa,CAACrB,YAAY,CAACkD,EAAE,CAAC,CAACd,SAAS,CAAC;MAC5CiB,IAAI,EAAEA,CAAA,KAAK;QACTd,OAAO,CAACe,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAAC9B,OAAO,GAAG,IAAI,CAACA,OAAO,CAACmB,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC7C,QAAQ,KAAKmD,EAAE,CAAC;QACpE,IAAI,CAACR,YAAY,EAAE;MACrB,CAAC;MACDF,KAAK,EAAGF,GAAG,IAAI;QACb,IAAIA,GAAG,CAACE,KAAK,IAAIF,GAAG,CAACE,KAAK,CAACe,OAAO,IAAIjB,GAAG,CAACE,KAAK,CAACe,OAAO,CAACxC,QAAQ,CAAC,OAAO,CAAC,EAAE;UACzEyC,KAAK,CAAC,+DAA+D,CAAC;SACvE,MAAM;UACLjB,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;;MAE9C;KACD,CAAC;EACJ;EAGA3B,gBAAgBA,CAACwC,KAAU,EAAEM,WAAmB;IAC9C,IAAIN,KAAK,CAACO,MAAM,CAACC,OAAO,EAAE;MACxB,IAAI,CAAC,IAAI,CAAC7C,kBAAkB,CAACC,QAAQ,CAAC0C,WAAW,CAAC,EAAE;QAClD,IAAI,CAAC3C,kBAAkB,CAAC8C,IAAI,CAACH,WAAW,CAAC;;KAE5C,MAAM;MACL,IAAI,CAAC3C,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC6B,MAAM,CAACO,EAAE,IAAIA,EAAE,KAAKO,WAAW,CAAC;;EAEtF;EAEAI,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCxB,OAAO,CAACe,GAAG,CAACQ,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACzC,MAAM,CAAC0C,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAnIW9C,eAAe,EAAAnC,EAAA,CAAAkF,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAApF,EAAA,CAAAkF,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAtF,EAAA,CAAAkF,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfrD,eAAe;MAAAsD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ5B/F,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAiG,SAAA,cAAsB;UAWtBjG,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGpCH,EAAA,CAAAC,cAAA,YAAM;UAIAD,EAAA,CAAAiG,SAAA,eAAwE;UACxEjG,EAAA,CAAAE,MAAA,aACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAI,UAAA,mBAAA8F,6CAAA;YAAA,OAASF,GAAA,CAAAnB,MAAA,EAAQ;UAAA,EAAC;UAAC7E,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKzDH,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAAiG,SAAA,gBAAiC;UACjCjG,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGpDH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAiG,SAAA,gBAAiC;UACjCjG,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAiG,SAAA,gBAAiC;UACjCjG,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAiG,SAAA,gBAAwC;UACxCjG,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAiG,SAAA,gBAAwC;UACzCjG,EAAA,CAAAE,MAAA,6BACD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAiG,SAAA,gBAAkC;UAClCjG,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKhBH,EAAA,CAAAC,cAAA,gBAAkE;UAC9BD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAI1DH,EAAA,CAAAC,cAAA,eAA6C;UAEuBD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMXH,EAAA,CAAAC,cAAA,eAA8B;UAOnBD,EAAA,CAAAI,UAAA,2BAAA+F,yDAAA7F,MAAA;YAAA,OAAA0F,GAAA,CAAAnD,WAAA,GAAAvC,MAAA;UAAA,EAAyB,2BAAA6F,yDAAA;YAAA,OACRH,GAAA,CAAAtC,YAAA,EAAc;UAAA,EADN;UAJhC1D,EAAA,CAAAG,YAAA,EAKwC;UAE1CH,EAAA,CAAAC,cAAA,iBAAiC;UAGvBD,EAAA,CAAAE,MAAA,sBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACZH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpBH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAoG,UAAA,KAAAC,8BAAA,kBAWK;UACPrG,EAAA,CAAAG,YAAA,EAAQ;UAQlBH,EAAA,CAAAC,cAAA,eAAmH;UAGvGD,EAAA,CAAAI,UAAA,sBAAAkG,mDAAA;YAAA,OAAYN,GAAA,CAAAlC,SAAA,EAAW;UAAA,EAAC;UAC5B9D,EAAA,CAAAC,cAAA,eAA0B;UACyBD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEzEH,EAAA,CAAAC,cAAA,eAAwB;UAEGD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,iBAAmH;UAAjFD,EAAA,CAAAI,UAAA,2BAAAmG,yDAAAjG,MAAA;YAAA,OAAA0F,GAAA,CAAAtD,SAAA,CAAAvB,SAAA,GAAAb,MAAA;UAAA,EAAiC;UAAnEN,EAAA,CAAAG,YAAA,EAAmH;UAIrHH,EAAA,CAAAC,cAAA,eAAwB;UACjBD,EAAA,CAAAE,MAAA,iCAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAoG,UAAA,KAAAI,+BAAA,kBAUW;UACNxG,EAAA,CAAAG,YAAA,EAAM;UAIdH,EAAA,CAAAC,cAAA,eAA0B;UAC6CD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrFH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UAjEvDH,EAAA,CAAAiB,SAAA,IAAyB;UAAzBjB,EAAA,CAAAoB,UAAA,YAAA4E,GAAA,CAAAnD,WAAA,CAAyB;UAYP7C,EAAA,CAAAiB,SAAA,IAAkB;UAAlBjB,EAAA,CAAAoB,UAAA,YAAA4E,GAAA,CAAAvD,eAAA,CAAkB;UA8BPzC,EAAA,CAAAiB,SAAA,IAAiC;UAAjCjB,EAAA,CAAAoB,UAAA,YAAA4E,GAAA,CAAAtD,SAAA,CAAAvB,SAAA,CAAiC;UAMzCnB,EAAA,CAAAiB,SAAA,GAAa;UAAbjB,EAAA,CAAAoB,UAAA,YAAA4E,GAAA,CAAArD,UAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}