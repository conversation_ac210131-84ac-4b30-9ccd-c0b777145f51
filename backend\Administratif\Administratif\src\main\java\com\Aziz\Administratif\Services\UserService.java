package com.Aziz.Administratif.Services;

import com.Aziz.Administratif.Entity.Groupe;
import com.Aziz.Administratif.Entity.Habilitation;
import com.Aziz.Administratif.Entity.Ressource;
import com.Aziz.Administratif.Entity.User;
import com.Aziz.Administratif.Enum.Role;
import com.Aziz.Administratif.Security.auth.PasswordGenerator;
import com.Aziz.Administratif.Security.auth.PasswordResetRequest;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.security.crypto.password.PasswordEncoder; // ✅ Add this

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class UserService {
    private final com.Aziz.Administratif.Repositories.UserRepository UserRepository;
    private final com.Aziz.Administratif.Repositories.GroupeRepository GroupeRepository;
    private final com.Aziz.Administratif.Repositories.HabilitationRepository HabilitationRepository;
    private final com.Aziz.Administratif.Repositories.RessourceRepository RessourceRepository;
    private final EmailService emailService;
    private final PasswordEncoder passwordEncoder;
    private final com.Aziz.Administratif.Repositories.passwordResetRepository passwordResetRepository;



    @Autowired
    private com.Aziz.Administratif.ClientRelation.ClientClient ClientClient;

    public Optional<User> findById(Long userId) {
        return UserRepository.findById(userId);
    }


    @Transactional
    public void saveUser(User user, Long idGroupe, Long idRessource) {

        // Find the groupe
        Groupe groupe = GroupeRepository.findById(idGroupe)
                .orElseThrow(() -> new RuntimeException("Groupe not found"));

        // Find the ressource
        Ressource ressource = RessourceRepository.findById(idRessource)
                .orElseThrow(() -> new RuntimeException("Ressource not found"));

        // Try to find the habilitation for the groupe and ressource
        Optional<Habilitation> habOpt = HabilitationRepository
                .findByIdGroupeAndRessource_IdRessource(idGroupe, idRessource);

        Habilitation habilitation;

        if (habOpt.isEmpty()) {
            // If no habilitation exists, create a new one
            habilitation = new Habilitation();
            habilitation.setRessource(ressource);
            habilitation = HabilitationRepository.save(habilitation); // Save the new habilitation
        } else {
            habilitation = habOpt.get(); // If found, use the existing one
        }

        // Set other fields0
        user.setGroupe(groupe);
        user.setHabilitation(habilitation);
        user.setPermissionsFromHabilitation(habilitation);  // Setting CRUD permissions from habilitation

        // Save the User with all updated details
        UserRepository.save(user);
    }


    public List<User> findAllUsers(){
        return UserRepository.findAll();
    }

    public List<User> findAllByRole(Role role) {
        return UserRepository.findAllByRole(role);

    }

    public void deleteUser(Long userId) {
        UserRepository.deleteById(userId);
    }



    public User updateUser(Long userId, User updatedUser) {
        return UserRepository.findById(userId).map(existingUser -> {
            // Update basic user details

            existingUser.setNom(updatedUser.getNom());
            existingUser.setPrenom(updatedUser.getPrenom());
            existingUser.setEmail(updatedUser.getEmail());
            existingUser.setTelephone(updatedUser.getTelephone());
            existingUser.setFlag(updatedUser.getFlag());
            existingUser.setMatricule(updatedUser.getMatricule());

            // Only update password if provided
            if (updatedUser.getPassword() != null && !updatedUser.getPassword().isEmpty()) {
                existingUser.setPassword(passwordEncoder.encode(updatedUser.getPassword()));
            }
            // Update permissions
            existingUser.setCanCreate(Boolean.TRUE.equals(updatedUser.getCanCreate()));
            existingUser.setCanRead(Boolean.TRUE.equals(updatedUser.getCanRead()));
            existingUser.setCanUpdate(Boolean.TRUE.equals(updatedUser.getCanUpdate()));
            existingUser.setCanDelete(Boolean.TRUE.equals(updatedUser.getCanDelete()));

            // Update flag (default to false if null)
            existingUser.setFlag(Boolean.TRUE.equals(updatedUser.getFlag()));

            // Update role if provided
            if (updatedUser.getRole() != null) {
                existingUser.setRole(updatedUser.getRole());
            }

            // Update groupe if provided
            if (updatedUser.getGroupe() != null && updatedUser.getGroupe().getIdGroupe() != null) {
                Groupe groupe = GroupeRepository.findById(updatedUser.getGroupe().getIdGroupe())
                        .orElseThrow(() -> new RuntimeException("Groupe not found"));
                existingUser.setGroupe(groupe);
            }

            // Update habilitation if ressource is provided
            if (updatedUser.getHabilitation() != null && updatedUser.getHabilitation().getRessource() != null) {
                Long ressourceId = updatedUser.getHabilitation().getRessource().getIdRessource();
                Long groupeId = updatedUser.getGroupe().getIdGroupe();

                Habilitation habilitation = HabilitationRepository
                        .findByIdGroupeAndRessource_IdRessource(groupeId, ressourceId)
                        .orElseGet(() -> {
                            Habilitation newHab = new Habilitation();
                            newHab.setIdGroupe(groupeId);
                            newHab.setRessource(updatedUser.getHabilitation().getRessource());
                            return HabilitationRepository.save(newHab);
                        });

                existingUser.setHabilitation(habilitation);
            }

            // Update modification date and save
            existingUser.setDateModification(LocalDateTime.now());
            existingUser.setFlag(true);

            User savedUser = UserRepository.save(existingUser);

        /*
        // OPTIONAL: Generate new token if needed
        Map<String, Object> extraClaims = new HashMap<>();
        extraClaims.put("role", savedUser.getRole().name());
        String newToken = jwtService.generateToken(extraClaims, savedUser);
        // You could return this token in a response object if needed
        */

            return savedUser;

        }).orElseThrow(() -> new RuntimeException("User not found"));
    }


    private void updateBasicUserDetails(User existingUser, User updatedUser) {
        existingUser.setNom(updatedUser.getNom());
        existingUser.setPrenom(updatedUser.getPrenom());
        existingUser.setEmail(updatedUser.getEmail());
        existingUser.setTelephone(updatedUser.getTelephone());
        existingUser.setCanCreate(updatedUser.getCanCreate());
        existingUser.setCanRead(updatedUser.getCanRead());
        existingUser.setCanUpdate(updatedUser.getCanUpdate());
        existingUser.setCanDelete(updatedUser.getCanDelete());
    }

    private void updateUserRoleAndGroup(User existingUser, User updatedUser) {
        if (updatedUser.getRole() != null) {
            existingUser.setRole(updatedUser.getRole());
        }

        if (updatedUser.getGroupe() != null && updatedUser.getGroupe().getIdGroupe() != null) {
            Groupe groupe = GroupeRepository.findById(updatedUser.getGroupe().getIdGroupe())
                    .orElseThrow(() -> new RuntimeException("Groupe not found"));
            existingUser.setGroupe(groupe);
        }
    }

    private void updateHabilitationAndResource(User existingUser, User updatedUser) {
        if (updatedUser.getHabilitation() != null) {
            Habilitation updatedHabilitation = updatedUser.getHabilitation();

            // Update Groupe and Ressource in Habilitation if necessary
            if (updatedHabilitation.getIdGroupe() != null) {
                Groupe groupe = GroupeRepository.findById(updatedHabilitation.getIdGroupe())
                        .orElseThrow(() -> new RuntimeException("Groupe not found"));

                Ressource ressource = RessourceRepository.findById(updatedHabilitation.getRessource().getIdRessource())
                        .orElseThrow(() -> new RuntimeException("Ressource not found"));

                Habilitation habilitation = existingUser.getHabilitation();
                habilitation.setIdGroupe(groupe.getIdGroupe());
                habilitation.setRessource(ressource);

                // Save updated habilitation
                HabilitationRepository.save(habilitation);

                // Update the user habilitation
                existingUser.setHabilitation(habilitation);
            }
        }
    }

//    //relation avec actionnaire
//    public FullUserResponse findUserswithActionnaire(Long userId) {
//        var user=UserRepository.findById(userId)
//                .orElse(
//                        User.builder()
//                                .nom("Not_Found")
//                                .prenom("Not_Found")
//                                .email("Not_Found")
//                                .build()
//                );
//        var actionnaire= ActionnaireClient.findActionnairesbyUser(userId);
//        return FullUserResponse.builder()
//                .nom(user.getNom())
//                .prenom(user.getPrenom())
//                .email(user.getEmail())
//                .telephone(user.getTelephone())
//                // .Actionnaire(actionnaire)
//                .build();
//    }

//    public void saveUser(User responsable) {
//    }

    public User toggleUserState(Long userId) {
        Optional<User> userOpt = UserRepository.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setState(!user.getState());  // Toggle the state
            return UserRepository.save(user);  // Save the updated user
        }
        return null;  // Return null if user not found
    }



    public ResponseEntity<String> ResetPasswordRequestToAdmin(Integer ForgetUserMatricule, String ForgetUserEmail) {
        //  User ForgetUser = UserRepository.findByMatriculeAndEmail(ForgetUserMatricule, ForgetUserEmail);

        PasswordResetRequest request1 = passwordResetRepository.findByEmail(ForgetUserEmail);

        User existingUser = UserRepository.findByMatricule(ForgetUserMatricule);
        Optional<User>  existingUserwithEmail = UserRepository.findByEmail(ForgetUserEmail);

        if ((existingUser == null)||(existingUserwithEmail.isEmpty())) {
            return ResponseEntity.status(404).body("User not found");
        }
        else {

            if (request1==null) {
                List<User> admins = UserRepository.findAllByRole(Role.valueOf("ADMIN"));
                User assignedAdmin = admins.get(new SecureRandom().nextInt(admins.size()));

                PasswordResetRequest request = new PasswordResetRequest();
                request.setMatricule(ForgetUserMatricule);
                request.setEmail(ForgetUserEmail);
                request.setRequestDate(LocalDateTime.now());
                request.setAssignedAdmin(assignedAdmin);
                passwordResetRepository.save(request);

                emailService.ForgetPasswordUserToAdmin(ForgetUserEmail, ForgetUserMatricule, assignedAdmin);
                return ResponseEntity.status(404).body("reset request created. Admin will contact you.");

            }
            else if (!request1.isCompleted()) {
                return ResponseEntity.status(403).body("your request of reset password is under process");
            }
            else {return ResponseEntity.status(403).body("your request was done, pls check your email");}





        }
    }


    public ResponseEntity<String> adminGeneratesPassword(Integer Matricule,String email) {
        PasswordResetRequest request = passwordResetRepository.findByEmail(email);
        List<PasswordResetRequest> reqs=passwordResetRepository.findAll();

        //.orElseThrow(() -> new RuntimeException("Request not found"));


        if (request != null) {
            User user = UserRepository.findByMatricule(request.getMatricule());
            if (user == null) {
                throw new RuntimeException("User not found");
            }


            final SecureRandom random = new SecureRandom();
            String rawPassword1 = PasswordGenerator.generateRandomPassword(8);
            String encodedPassword = passwordEncoder.encode(rawPassword1);

            user.setPassword(encodedPassword);
            user.setDateModification(LocalDateTime.now());
            request.setCompleted(true);
            user.setFlag(true);

            UserRepository.save(user);

            emailService.ForgetPasswordAdminToUser(user.getEmail(), rawPassword1, request.getAssignedAdmin());



        } else {
            return ResponseEntity.status(200).body("reset request created. Admin will contact you.");
        }


        return null;
    }


    @Scheduled(cron = "0 0 * * * *") // Every day at 9AM
    public void reassignResetRequests() {
        // System.out.println("Scheduled works: " + LocalDateTime.now());
        List<PasswordResetRequest> pending = passwordResetRepository.findByCompletedFalse();

        for (PasswordResetRequest req : pending) {
            if (req.getRequestDate().plusDays(2).isBefore(LocalDateTime.now())) {
                List<User> admins = UserRepository.findAllByRole(Role.valueOf("ADMIN"));
                admins.removeIf(a -> a.getId().equals(req.getAssignedAdmin().getId()));// si tu veux un autre Admin, optionnel

                if (!admins.isEmpty()) {
                    User newAdmin = admins.get(new SecureRandom().nextInt(admins.size()));
                    req.setAssignedAdmin(newAdmin);
                    req.setRequestDate(LocalDateTime.now());
                    passwordResetRepository.save(req);

                    emailService.ForgetPasswordUserToAdmin(req.getEmail(), req.getMatricule(), newAdmin);
                }
            }
        }


        List<PasswordResetRequest> completedRequests = passwordResetRepository.findByCompletedTrue();

        for (PasswordResetRequest request : completedRequests) {
            if (request.getRequestDate().plusDays(2).isBefore(LocalDateTime.now())) {
                passwordResetRepository.delete(request);
                System.out.println("Deleted: " + LocalDateTime.now());

            }
        }
        System.out.println("updated");
    }





    public User updateUserState(Long userId, User updatedUser) {
        return UserRepository.findById(userId).map(existingUser -> {
            // Update basic user details
            existingUser.setFlag(updatedUser.getState());
            // Only update password if provided
            if (updatedUser.getPassword() != null && !updatedUser.getPassword().isEmpty()) {
                existingUser.setPassword(passwordEncoder.encode(updatedUser.getPassword()));
            }
            // Update permissions
            existingUser.setCanCreate(Boolean.TRUE.equals(updatedUser.getCanCreate()));
            existingUser.setCanRead(Boolean.TRUE.equals(updatedUser.getCanRead()));
            existingUser.setCanUpdate(Boolean.TRUE.equals(updatedUser.getCanUpdate()));
            existingUser.setCanDelete(Boolean.TRUE.equals(updatedUser.getCanDelete()));

            // Update flag (default to false if null)
            existingUser.setFlag(Boolean.TRUE.equals(updatedUser.getFlag()));

            // Update role if provided
            if (updatedUser.getRole() != null) {
                existingUser.setRole(updatedUser.getRole());
            }

            if (updatedUser.getState() != null) {
                existingUser.setState(updatedUser.getState());
            }

            // Update groupe if provided
            if (updatedUser.getGroupe() != null && updatedUser.getGroupe().getIdGroupe() != null) {
                Groupe groupe = GroupeRepository.findById(updatedUser.getGroupe().getIdGroupe())
                        .orElseThrow(() -> new RuntimeException("Groupe not found"));
                existingUser.setGroupe(groupe);
            }

            // Update habilitation if ressource is provided
            if (updatedUser.getHabilitation() != null && updatedUser.getHabilitation().getRessource() != null) {
                Long ressourceId = updatedUser.getHabilitation().getRessource().getIdRessource();
                Long groupeId = updatedUser.getGroupe().getIdGroupe();

                Habilitation habilitation = HabilitationRepository
                        .findByIdGroupeAndRessource_IdRessource(groupeId, ressourceId)
                        .orElseGet(() -> {
                            Habilitation newHab = new Habilitation();
                            newHab.setIdGroupe(groupeId);
                            newHab.setRessource(updatedUser.getHabilitation().getRessource());
                            return HabilitationRepository.save(newHab);
                        });

                existingUser.setHabilitation(habilitation);
            }

            // Update modification date and save
            existingUser.setDateModification(LocalDateTime.now());
            existingUser.setFlag(true);

            User savedUser = UserRepository.save(existingUser);


            return savedUser;

        }).orElseThrow(() -> new RuntimeException("User not found"));
    }


//relation avec Client







}