package com.Aziz.Administratif.ClientRelation.Entity;

import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;



@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Transaction {


    @Id
    private Long idTransaction;
    private String type;


    private Integer qantite;
    private Double montatnt;
    private String observations;


    @CreationTimestamp
    private LocalDateTime DateCreation;


    private LocalDateTime DateTransaction;

    @ManyToOne
//    @JsonBackReference
    @JoinColumn(name = "idPortefeuille")
    private Portefeuille portefeuille;



    private Long userId;




}
