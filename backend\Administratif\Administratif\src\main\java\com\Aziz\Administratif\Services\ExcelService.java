package com.Aziz.Administratif.Services;

import com.Aziz.Administratif.ClientRelation.ClientClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class ExcelService {

    private final ClientClient clientClient;

    @Autowired
    public ExcelService(ClientClient clientClient) {
        this.clientClient = clientClient;
    }

    public String sendExcelFile(MultipartFile file, Long userId) {
        // Pass the userId along with the file to the ClientClient
        ResponseEntity<String> response = clientClient.uploadExcelFile(file, userId);
        return response.getBody();
    }
}