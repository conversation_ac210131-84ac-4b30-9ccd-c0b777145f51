{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./authentication.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(next, state) {\n    const token = this.authService.getToken();\n    console.log(\"Token retrieved from storage:\", token);\n    console.log(\"Token from localStorage:\", localStorage.getItem('token'));\n    // Debugging: log the token\n    if (token) {\n      const {\n        role,\n        groupe\n      } = this.authService.getUserDetailsFromToken(token);\n      console.log(\"User role from token:\", role, \"Group:\", groupe); // Debugging: log role and group\n      const currentPath = state.url.split('?')[0]; // Clean up query params\n      console.log(\"Current path (trimmed):\", currentPath); // Debugging: log current path\n      const allowedGroups = {\n        '/transactions': 'TRANSACTION',\n        '/actions': 'ACTION',\n        '/actionnaires': 'ACTIONNAIRE',\n        '/reports': 'NOTIFICATION',\n        '/port': 'PORTEFEUILLE',\n        '/ResDash': groupe // Always allow responsible users to access their dashboard\n      };\n\n      const expectedGroup = allowedGroups[currentPath];\n      console.log(\"Expected group:\", expectedGroup); // Debugging: log expected group for path\n      // Check if the current group matches the expected group for the path\n      if (expectedGroup && groupe !== expectedGroup) {\n        console.warn(\"Access denied for group:\", groupe);\n        this.router.navigate(['/not-authorized']);\n        return false;\n      }\n      // Allow access if everything matches\n      return true;\n    }\n    // If no token, redirect to login\n    this.router.navigate(['/login']);\n    return false;\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthenticationService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "next", "state", "token", "getToken", "console", "log", "localStorage", "getItem", "role", "groupe", "getUserDetailsFromToken", "currentPath", "url", "split", "allowedGroups", "expectedGroup", "warn", "navigate", "i0", "ɵɵinject", "i1", "AuthenticationService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { AuthenticationService } from './authentication.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {\n    const token = this.authService.getToken();\n    console.log(\"Token retrieved from storage:\", token);\n    console.log(\"Token from localStorage:\", localStorage.getItem('token'));\n// Debugging: log the token\n\n    if (token) {\n      const { role, groupe } = this.authService.getUserDetailsFromToken(token);\n      console.log(\"User role from token:\", role, \"Group:\", groupe); // Debugging: log role and group\n\n      const currentPath = state.url.split('?')[0]; // Clean up query params\n      console.log(\"Current path (trimmed):\", currentPath);  // Debugging: log current path\n\n      const allowedGroups: Record<string, string> = {\n        '/transactions': 'TRANSACTION',\n        '/actions': 'ACTION',\n        '/actionnaires': 'ACTIONNAIRE',\n        '/reports': 'NOTIFICATION',\n        '/port': 'PORTEFEUILLE',\n        '/ResDash': groupe  // Always allow responsible users to access their dashboard\n      };\n\n      const expectedGroup = allowedGroups[currentPath];\n      console.log(\"Expected group:\", expectedGroup);  // Debugging: log expected group for path\n\n      // Check if the current group matches the expected group for the path\n      if (expectedGroup && groupe !== expectedGroup) {\n        console.warn(\"Access denied for group:\", groupe);\n        this.router.navigate(['/not-authorized']);\n        return false;\n      }\n\n      // Allow access if everything matches\n      return true;\n    }\n\n    // If no token, redirect to login\n    this.router.navigate(['/login']);\n    return false;\n  }\n}\n"], "mappings": ";;;AAOA,OAAM,MAAOA,SAAS;EAEpBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;EAAW;EAEjFC,WAAWA,CAACC,IAA4B,EAAEC,KAA0B;IAClE,MAAMC,KAAK,GAAG,IAAI,CAACL,WAAW,CAACM,QAAQ,EAAE;IACzCC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,KAAK,CAAC;IACnDE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC1E;IAEI,IAAIL,KAAK,EAAE;MACT,MAAM;QAAEM,IAAI;QAAEC;MAAM,CAAE,GAAG,IAAI,CAACZ,WAAW,CAACa,uBAAuB,CAACR,KAAK,CAAC;MACxEE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEG,IAAI,EAAE,QAAQ,EAAEC,MAAM,CAAC,CAAC,CAAC;MAE9D,MAAME,WAAW,GAAGV,KAAK,CAACW,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7CT,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEM,WAAW,CAAC,CAAC,CAAE;MAEtD,MAAMG,aAAa,GAA2B;QAC5C,eAAe,EAAE,aAAa;QAC9B,UAAU,EAAE,QAAQ;QACpB,eAAe,EAAE,aAAa;QAC9B,UAAU,EAAE,cAAc;QAC1B,OAAO,EAAE,cAAc;QACvB,UAAU,EAAEL,MAAM,CAAE;OACrB;;MAED,MAAMM,aAAa,GAAGD,aAAa,CAACH,WAAW,CAAC;MAChDP,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEU,aAAa,CAAC,CAAC,CAAE;MAEhD;MACA,IAAIA,aAAa,IAAIN,MAAM,KAAKM,aAAa,EAAE;QAC7CX,OAAO,CAACY,IAAI,CAAC,0BAA0B,EAAEP,MAAM,CAAC;QAChD,IAAI,CAACX,MAAM,CAACmB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QACzC,OAAO,KAAK;;MAGd;MACA,OAAO,IAAI;;IAGb;IACA,IAAI,CAACnB,MAAM,CAACmB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAChC,OAAO,KAAK;EACd;;;uBA3CWtB,SAAS,EAAAuB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAT5B,SAAS;MAAA6B,OAAA,EAAT7B,SAAS,CAAA8B,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}