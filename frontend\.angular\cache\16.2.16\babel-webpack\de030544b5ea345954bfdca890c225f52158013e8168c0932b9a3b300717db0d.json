{"ast": null, "code": "import { Role } from '../model/role.enum'; // If you have Role enum like in edit\nimport { Groupe } from '../model/groupe.enum'; // If you have Groupe enum like in edit\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/forms\";\nexport class AddResponsableComponent {\n  constructor(responsableService, router) {\n    this.responsableService = responsableService;\n    this.router = router;\n    this.newResponsable = {\n      id: 0,\n      nom: '',\n      prenom: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE' // Role is fixed as RESPONSABLE\n      // Ensure other fields are set correctly\n    };\n\n    this.roles = Object.values(Role); // Dynamically get roles from Role enum (optional)\n    this.groupes = Object.values(Groupe); // Dynamically get groupes from Groupe enum (optional)\n  }\n  // Method to add the responsable when the form is submitted\n  addResponsable() {\n    // Check if all required fields are filled\n    if (this.isFormValid()) {\n      // Call service to add responsable\n      this.responsableService.addResponsable(this.newResponsable).subscribe(response => {\n        console.log('Responsable added successfully:', response);\n        this.router.navigate(['/users']); // Navigate to the users list or any desired page\n      }, error => {\n        console.error('Error adding responsable:', error);\n      });\n    } else {\n      console.error('Please fill in all required fields');\n    }\n  }\n  // Optional: Add validation for the form\n  isFormValid() {\n    return this.newResponsable.nom !== '' && this.newResponsable.prenom !== '' && this.newResponsable.email !== '' && this.newResponsable.telephone !== '';\n  }\n  static {\n    this.ɵfac = function AddResponsableComponent_Factory(t) {\n      return new (t || AddResponsableComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddResponsableComponent,\n      selectors: [[\"app-add-responsable\"]],\n      decls: 22,\n      vars: 4,\n      consts: [[1, \"container\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"nom\"], [\"type\", \"text\", \"id\", \"nom\", \"name\", \"nom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"prenom\"], [\"type\", \"text\", \"id\", \"prenom\", \"name\", \"prenom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"telephone\"], [\"type\", \"text\", \"id\", \"telephone\", \"name\", \"telephone\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\"]],\n      template: function AddResponsableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Add New Responsable\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"form\", 1);\n          i0.ɵɵlistener(\"ngSubmit\", function AddResponsableComponent_Template_form_ngSubmit_3_listener() {\n            return ctx.addResponsable();\n          });\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"input\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_7_listener($event) {\n            return ctx.newResponsable.nom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 2)(9, \"label\", 5);\n          i0.ɵɵtext(10, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"input\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.newResponsable.prenom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 2)(13, \"label\", 7);\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 8);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.newResponsable.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 2)(17, \"label\", 9);\n          i0.ɵɵtext(18, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"input\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_19_listener($event) {\n            return ctx.newResponsable.telephone = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"button\", 11);\n          i0.ɵɵtext(21, \"Add Responsable\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.nom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.prenom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.telephone);\n        }\n      },\n      dependencies: [i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Role", "Groupe", "AddResponsableComponent", "constructor", "responsableService", "router", "newResponsable", "id", "nom", "prenom", "email", "telephone", "role", "roles", "Object", "values", "groupes", "addResponsable", "isFormValid", "subscribe", "response", "console", "log", "navigate", "error", "i0", "ɵɵdirectiveInject", "i1", "ResponsableService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AddResponsableComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AddResponsableComponent_Template_form_ngSubmit_3_listener", "AddResponsableComponent_Template_input_ngModelChange_7_listener", "$event", "AddResponsableComponent_Template_input_ngModelChange_11_listener", "AddResponsableComponent_Template_input_ngModelChange_15_listener", "AddResponsableComponent_Template_input_ngModelChange_19_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport { Role } from '../model/role.enum';  // If you have Role enum like in edit\nimport { Groupe } from '../model/groupe.enum';  // If you have Groupe enum like in edit\n\n@Component({\n  selector: 'app-add-responsable',\n  templateUrl: './add-responsable.component.html',\n  styleUrls: ['./add-responsable.component.css']\n})\nexport class AddResponsableComponent {\n  newResponsable: User = {\n    id: 0,  // Let the backend handle the ID\n    nom: '',\n    prenom: '',\n    email: '',\n    telephone: '',\n    role: 'RESPONSABLE',  // Role is fixed as RESPONSABLE\n    // Ensure other fields are set correctly\n  };\n\n  roles = Object.values(Role);  // Dynamically get roles from Role enum (optional)\n  groupes = Object.values(Groupe);  // Dynamically get groupes from Groupe enum (optional)\n\n  constructor(\n    private responsableService: ResponsableService,\n    private router: Router\n  ) {}\n\n  // Method to add the responsable when the form is submitted\n  addResponsable(): void {\n    // Check if all required fields are filled\n    if (this.isFormValid()) {\n      // Call service to add responsable\n      this.responsableService.addResponsable(this.newResponsable).subscribe(\n        (response) => {\n          console.log('Responsable added successfully:', response);\n          this.router.navigate(['/users']);  // Navigate to the users list or any desired page\n        },\n        (error) => {\n          console.error('Error adding responsable:', error);\n        }\n      );\n    } else {\n      console.error('Please fill in all required fields');\n    }\n  }\n\n  // Optional: Add validation for the form\n  isFormValid(): boolean {\n    return this.newResponsable.nom !== '' && this.newResponsable.prenom !== '' && this.newResponsable.email !== '' && this.newResponsable.telephone !== '';\n  }\n}\n", "<div class=\"container\">\n    <h2>Add New Responsable</h2>\n    <form (ngSubmit)=\"addResponsable()\">\n      <div class=\"form-group\">\n        <label for=\"nom\">Nom</label>\n        <input type=\"text\" class=\"form-control\" id=\"nom\" [(ngModel)]=\"newResponsable.nom\" name=\"nom\" required />\n      </div>\n      <div class=\"form-group\">\n        <label for=\"prenom\">Prénom</label>\n        <input type=\"text\" class=\"form-control\" id=\"prenom\" [(ngModel)]=\"newResponsable.prenom\" name=\"prenom\" required />\n      </div>\n      <div class=\"form-group\">\n        <label for=\"email\">Email</label>\n        <input type=\"email\" class=\"form-control\" id=\"email\" [(ngModel)]=\"newResponsable.email\" name=\"email\" required />\n      </div>\n      <div class=\"form-group\">\n        <label for=\"telephone\">Téléphone</label>\n        <input type=\"text\" class=\"form-control\" id=\"telephone\" [(ngModel)]=\"newResponsable.telephone\" name=\"telephone\" required />\n      </div>\n      <button type=\"submit\" class=\"btn btn-success\">Add Responsable</button>\n    </form>\n  </div>\n  "], "mappings": "AAIA,SAASA,IAAI,QAAQ,oBAAoB,CAAC,CAAE;AAC5C,SAASC,MAAM,QAAQ,sBAAsB,CAAC,CAAE;;;;;AAOhD,OAAM,MAAOC,uBAAuB;EAclCC,YACUC,kBAAsC,EACtCC,MAAc;IADd,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IAfhB,KAAAC,cAAc,GAAS;MACrBC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa,CAAG;MACtB;KACD;;IAED,KAAAC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACf,IAAI,CAAC,CAAC,CAAE;IAC9B,KAAAgB,OAAO,GAAGF,MAAM,CAACC,MAAM,CAACd,MAAM,CAAC,CAAC,CAAE;EAK/B;EAEH;EACAgB,cAAcA,CAAA;IACZ;IACA,IAAI,IAAI,CAACC,WAAW,EAAE,EAAE;MACtB;MACA,IAAI,CAACd,kBAAkB,CAACa,cAAc,CAAC,IAAI,CAACX,cAAc,CAAC,CAACa,SAAS,CAClEC,QAAQ,IAAI;QACXC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,QAAQ,CAAC;QACxD,IAAI,CAACf,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAE;MACrC,CAAC,EACAC,KAAK,IAAI;QACRH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,CACF;KACF,MAAM;MACLH,OAAO,CAACG,KAAK,CAAC,oCAAoC,CAAC;;EAEvD;EAEA;EACAN,WAAWA,CAAA;IACT,OAAO,IAAI,CAACZ,cAAc,CAACE,GAAG,KAAK,EAAE,IAAI,IAAI,CAACF,cAAc,CAACG,MAAM,KAAK,EAAE,IAAI,IAAI,CAACH,cAAc,CAACI,KAAK,KAAK,EAAE,IAAI,IAAI,CAACJ,cAAc,CAACK,SAAS,KAAK,EAAE;EACxJ;;;uBAzCWT,uBAAuB,EAAAuB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB5B,uBAAuB;MAAA6B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZpCZ,EAAA,CAAAc,cAAA,aAAuB;UACfd,EAAA,CAAAe,MAAA,0BAAmB;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UAC5BhB,EAAA,CAAAc,cAAA,cAAoC;UAA9Bd,EAAA,CAAAiB,UAAA,sBAAAC,0DAAA;YAAA,OAAYL,GAAA,CAAArB,cAAA,EAAgB;UAAA,EAAC;UACjCQ,EAAA,CAAAc,cAAA,aAAwB;UACLd,EAAA,CAAAe,MAAA,UAAG;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UAC5BhB,EAAA,CAAAc,cAAA,eAAwG;UAAvDd,EAAA,CAAAiB,UAAA,2BAAAE,gEAAAC,MAAA;YAAA,OAAAP,GAAA,CAAAhC,cAAA,CAAAE,GAAA,GAAAqC,MAAA;UAAA,EAAgC;UAAjFpB,EAAA,CAAAgB,YAAA,EAAwG;UAE1GhB,EAAA,CAAAc,cAAA,aAAwB;UACFd,EAAA,CAAAe,MAAA,mBAAM;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UAClChB,EAAA,CAAAc,cAAA,gBAAiH;UAA7Dd,EAAA,CAAAiB,UAAA,2BAAAI,iEAAAD,MAAA;YAAA,OAAAP,GAAA,CAAAhC,cAAA,CAAAG,MAAA,GAAAoC,MAAA;UAAA,EAAmC;UAAvFpB,EAAA,CAAAgB,YAAA,EAAiH;UAEnHhB,EAAA,CAAAc,cAAA,cAAwB;UACHd,EAAA,CAAAe,MAAA,aAAK;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UAChChB,EAAA,CAAAc,cAAA,gBAA+G;UAA3Dd,EAAA,CAAAiB,UAAA,2BAAAK,iEAAAF,MAAA;YAAA,OAAAP,GAAA,CAAAhC,cAAA,CAAAI,KAAA,GAAAmC,MAAA;UAAA,EAAkC;UAAtFpB,EAAA,CAAAgB,YAAA,EAA+G;UAEjHhB,EAAA,CAAAc,cAAA,cAAwB;UACCd,EAAA,CAAAe,MAAA,2BAAS;UAAAf,EAAA,CAAAgB,YAAA,EAAQ;UACxChB,EAAA,CAAAc,cAAA,iBAA0H;UAAnEd,EAAA,CAAAiB,UAAA,2BAAAM,iEAAAH,MAAA;YAAA,OAAAP,GAAA,CAAAhC,cAAA,CAAAK,SAAA,GAAAkC,MAAA;UAAA,EAAsC;UAA7FpB,EAAA,CAAAgB,YAAA,EAA0H;UAE5HhB,EAAA,CAAAc,cAAA,kBAA8C;UAAAd,EAAA,CAAAe,MAAA,uBAAe;UAAAf,EAAA,CAAAgB,YAAA,EAAS;;;UAdnBhB,EAAA,CAAAwB,SAAA,GAAgC;UAAhCxB,EAAA,CAAAyB,UAAA,YAAAZ,GAAA,CAAAhC,cAAA,CAAAE,GAAA,CAAgC;UAI7BiB,EAAA,CAAAwB,SAAA,GAAmC;UAAnCxB,EAAA,CAAAyB,UAAA,YAAAZ,GAAA,CAAAhC,cAAA,CAAAG,MAAA,CAAmC;UAInCgB,EAAA,CAAAwB,SAAA,GAAkC;UAAlCxB,EAAA,CAAAyB,UAAA,YAAAZ,GAAA,CAAAhC,cAAA,CAAAI,KAAA,CAAkC;UAI/Be,EAAA,CAAAwB,SAAA,GAAsC;UAAtCxB,EAAA,CAAAyB,UAAA,YAAAZ,GAAA,CAAAhC,cAAA,CAAAK,SAAA,CAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}