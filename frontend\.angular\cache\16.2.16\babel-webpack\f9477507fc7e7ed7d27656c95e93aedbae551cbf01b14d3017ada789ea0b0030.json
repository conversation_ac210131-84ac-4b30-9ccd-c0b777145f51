{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/actionnaire.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = [\"myChart\"];\nfunction ActionnairesComponent_tr_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const a_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(a_r2.idActionnaire);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(a_r2.nomActionnaire);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(a_r2.prenomActionnaire);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(a_r2.emailActionnaire);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(a_r2.telephone);\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, 0.25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: 'Poppins', sans-serif;\\n  margin: 0;\\n  padding: 0;\\n  min-height: 100vh;\\n  background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n  background-size: 400% 400%;\\n  animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n  color: #fff;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n  0% {\\n      background-position: 0% 50%;\\n  }\\n\\n  50% {\\n      background-position: 100% 50%;\\n  }\\n\\n  100% {\\n      background-position: 0% 50%;\\n  }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  margin-top: 20px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 1.5rem;\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  color: #fff;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  border-collapse: separate;\\n  border-spacing: 0 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.15);\\n  color: #ffffff;\\n  font-weight: 600;\\n  text-align: center;\\n  border: none;\\n  padding: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  text-align: center;\\n  border-radius: 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.01);\\n  box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  vertical-align: middle;\\n  padding: 0.9rem;\\n  font-size: 0.95rem;\\n}\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n  border: none;\\n  padding: 12px 25px;\\n  border-radius: 30px;\\n  text-transform: uppercase;\\n  font-weight: bold;\\n  transition: background 0.3s, transform 0.2s;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n  background-color: #218838;\\n  transform: translateY(-2px);\\n}\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n  color: #206ee1;\\n  cursor: pointer;\\n  font-size: 20px;\\n  margin: 0 10px;\\n  transition: color 0.3s;\\n}\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffffff;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n  color: #d22d2d;\\n  cursor: pointer;\\n  font-size: 20px;\\n  transition: color 0.3s;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffffff;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 1.5rem;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n  background-color: #f1f1f1;\\n  padding: 1rem;\\n  border-top: 1px solid #ddd;\\n  display: flex;\\n  justify-content: center; \\n\\n  gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  font-size: 1rem;\\n  border-radius: 0.3rem;\\n  transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n  transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column; \\n\\n  align-items: flex-start; \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  background-color: rgba(255, 255, 255, 0.1); \\n\\n  border: 1px solid rgba(255, 255, 255, 0.3); \\n\\n  border-radius: 30px; \\n\\n  color: #fff; \\n\\n  padding: 10px 20px; \\n\\n  font-size: 1rem; \\n\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); \\n\\n  width: 100%; \\n\\n  max-width: 400px; \\n\\n  transition: all 0.3s ease; \\n\\n}\\n\\n.search-bar[_ngcontent-%COMP%]:focus {\\n  outline: none; \\n\\n  border-color: #007bff; \\n\\n  box-shadow: 0 0 8px rgba(0, 123, 255, 0.6); \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n  background: rgba(20, 33, 59, 0.9); \\n\\n  color: #fff;\\n  min-height: 100vh;\\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #ccc;\\n  transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n  background-color: #000 !important;\\n  color: #fff;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n  position: relative;\\n  padding: 0.5rem 1rem;\\n  transition: color 0.3s ease;\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  width: 0;\\n  background: #ff4c60;\\n  transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 30px;\\n  object-fit: cover;\\n  border-radius: 50%; \\n\\n  margin-right: 8px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWN0aW9ubmFpcmVzL2FjdGlvbm5haXJlcy5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWiwyQkFBMkI7QUFDN0I7O0FBRUE7O0NBRUM7O0FBRUQ7RUFDRSxlQUFlO0VBQ2YsTUFBTTtFQUNOLFNBQVM7RUFDVCxPQUFPO0VBQ1AsWUFBWSxFQUFFLHNCQUFzQjtFQUNwQyxVQUFVO0VBQ1YsNkNBQTZDO0FBQy9DOztBQUVBO0VBRUUsZ0JBQWdCO0VBQ2hCLFNBQVMsRUFBRSxxQkFBcUI7RUFDaEMsMEJBQTBCO0VBQzFCLGtCQUFrQjtFQUNsQixrQkFBa0I7RUFDbEIsZ0JBQWdCLEVBQUUsNkRBQTZEO0FBQ2pGOztBQUVBO0VBQ0UsZ0JBQWdCO0VBQ2hCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsV0FBVztBQUNiOztBQUVBO0VBQ0UsY0FBYztBQUNoQjs7QUFFQTs7RUFFRSxjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLHlCQUF5QjtBQUMzQjs7QUFFQTs7Q0FFQzs7QUFFRDtFQUNFLG1CQUFtQjtFQUNuQixzQkFBc0I7RUFDdEIsZUFBZTtFQUNmLHFDQUFxQztFQUNyQyw4Q0FBOEM7QUFDaEQ7O0FBRUE7RUFDRSxvQkFBb0I7RUFDcEIsZUFBZTtFQUNmLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLFdBQVc7RUFDWCx5Q0FBeUM7RUFDekMscUNBQXFDO0FBQ3ZDOztBQUVBO0VBQ0UseUJBQXlCO0VBQ3pCLDhDQUE4QztBQUNoRDs7QUFFQTs7Q0FFQzs7QUFFRDtFQUNFLDZCQUE2QjtBQUMvQjs7QUFFQTtFQUNFLGdDQUFnQztBQUNsQzs7QUFFQSxzQkFBc0I7QUFDdEI7RUFDRSxpQkFBaUI7RUFDakIsZUFBZTtFQUNmLFlBQVk7RUFDWixlQUFlO0VBQ2Ysa0JBQWtCO0VBQ2xCLGlCQUFpQjtFQUNqQix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxrQ0FBa0M7RUFDbEMsU0FBUztFQUNULFVBQVU7RUFDVixpQkFBaUI7RUFDakIsdUVBQXVFO0VBQ3ZFLDBCQUEwQjtFQUMxQix5Q0FBeUM7RUFDekMsV0FBVztBQUNiOztBQUVBO0VBQ0U7TUFDSSwyQkFBMkI7RUFDL0I7O0VBRUE7TUFDSSw2QkFBNkI7RUFDakM7O0VBRUE7TUFDSSwyQkFBMkI7RUFDL0I7QUFDRjs7QUFFQTtFQUNFLHFDQUFxQztFQUNyQyxtQkFBbUI7RUFDbkIsYUFBYTtFQUNiLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLHFDQUFxQztFQUNyQyxtQkFBbUI7RUFDbkIsZUFBZTtFQUNmLG1DQUEyQjtVQUEzQiwyQkFBMkI7RUFDM0IsMENBQTBDO0FBQzVDOztBQUVBO0VBQ0UsV0FBVztFQUNYLG1CQUFtQjtFQUNuQixnQkFBZ0I7RUFDaEIseUJBQXlCO0VBQ3pCLHNCQUFzQjtBQUN4Qjs7QUFFQTtFQUNFLDJDQUEyQztFQUMzQyxjQUFjO0VBQ2QsZ0JBQWdCO0VBQ2hCLGtCQUFrQjtFQUNsQixZQUFZO0VBQ1osYUFBYTtBQUNmOztBQUVBO0VBQ0UsMENBQTBDO0VBQzFDLHFEQUFxRDtFQUNyRCxrQkFBa0I7RUFDbEIsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0Usc0JBQXNCO0VBQ3RCLCtDQUErQztBQUNqRDs7QUFFQTtFQUNFLHNCQUFzQjtFQUN0QixlQUFlO0VBQ2Ysa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UseUJBQXlCO0VBQ3pCLFlBQVk7RUFDWixZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLG1CQUFtQjtFQUNuQix5QkFBeUI7RUFDekIsaUJBQWlCO0VBQ2pCLDJDQUEyQztFQUMzQyx5Q0FBeUM7QUFDM0M7O0FBRUE7RUFDRSx5QkFBeUI7RUFDekIsMkJBQTJCO0FBQzdCOztBQUVBO0VBQ0UsY0FBYztFQUNkLGVBQWU7RUFDZixlQUFlO0VBQ2YsY0FBYztFQUNkLHNCQUFzQjtBQUN4Qjs7QUFFQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxjQUFjO0VBQ2QsZUFBZTtFQUNmLGVBQWU7RUFDZixzQkFBc0I7QUFDeEI7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBO0VBQ0Usb0NBQW9DO0FBQ3RDOztBQUVBO0VBQ0UsV0FBVztBQUNiOztBQUVBO0VBQ0UscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0UseUJBQXlCO0FBQzNCOztBQUVBLGVBQWU7QUFDZjtFQUNFLHNCQUFzQjtFQUN0QixlQUFlO0VBQ2YsV0FBVztFQUNYLGVBQWU7QUFDakI7O0FBRUEsaUJBQWlCO0FBQ2pCO0VBQ0UseUJBQXlCO0VBQ3pCLGFBQWE7RUFDYiwwQkFBMEI7RUFDMUIsYUFBYTtFQUNiLHVCQUF1QixFQUFFLGdDQUFnQztFQUN6RCxTQUFTLEVBQUUsOEJBQThCO0FBQzNDOztBQUVBO0VBQ0Usb0JBQW9CO0VBQ3BCLGVBQWU7RUFDZixxQkFBcUI7RUFDckIsaURBQWlEO0FBQ25EOztBQUVBO0VBQ0UseUJBQXlCO0VBQ3pCLFdBQVc7RUFDWCxZQUFZO0FBQ2Q7O0FBRUE7RUFDRSx5QkFBeUI7RUFDekIsMkJBQTJCO0FBQzdCOztBQUVBO0VBQ0UseUJBQXlCO0VBQ3pCLFdBQVc7RUFDWCxZQUFZO0FBQ2Q7O0FBRUE7RUFDRSx5QkFBeUI7RUFDekIsMkJBQTJCO0FBQzdCOztBQUVBLCtDQUErQztBQUMvQztFQUNFLGFBQWE7RUFDYixzQkFBc0IsRUFBRSwyQkFBMkI7RUFDbkQsdUJBQXVCLEVBQUUsaUNBQWlDO0FBQzVEOztBQUVBLHNCQUFzQjtBQUN0QjtFQUNFLGdCQUFnQjtFQUNoQiwwQ0FBMEMsRUFBRSxpQ0FBaUM7RUFDN0UsMENBQTBDLEVBQUUsa0JBQWtCO0VBQzlELG1CQUFtQixFQUFFLG9CQUFvQjtFQUN6QyxXQUFXLEVBQUUsZUFBZTtFQUM1QixrQkFBa0IsRUFBRSw2QkFBNkI7RUFDakQsZUFBZSxFQUFFLHFDQUFxQztFQUN0RCx3Q0FBd0MsRUFBRSw0QkFBNEI7RUFDdEUsV0FBVyxFQUFFLHVCQUF1QjtFQUNwQyxnQkFBZ0IsRUFBRSw0REFBNEQ7RUFDOUUseUJBQXlCLEVBQUUsc0JBQXNCO0FBQ25EOztBQUVBO0VBQ0UsYUFBYSxFQUFFLDJCQUEyQjtFQUMxQyxxQkFBcUIsRUFBRSx3Q0FBd0M7RUFDL0QsMENBQTBDLEVBQUUscUJBQXFCO0FBQ25FOztBQUVBLDJCQUEyQjtBQUMzQjtFQUNFLCtCQUErQixFQUFFLDJCQUEyQjtBQUM5RDs7O0FBR0E7RUFDRSxpQ0FBaUMsRUFBRSwrQkFBK0I7RUFDbEUsV0FBVztFQUNYLGlCQUFpQjtFQUNqQixnREFBZ0Q7QUFDbEQ7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsaURBQWlEO0FBQ25EOztBQUVBOztFQUVFLFdBQVc7RUFDWCwwQ0FBMEM7RUFDMUMsa0JBQWtCO0FBQ3BCOzs7QUFHQTtFQUNFLGlDQUFpQztFQUNqQyxXQUFXO0VBQ1gsaURBQWlEO0FBQ25EOztBQUVBOztFQUVFLHNCQUFzQjtBQUN4Qjs7QUFFQTtFQUNFLDBCQUEwQjtBQUM1Qjs7OztBQUlBO0VBQ0Usc0JBQXNCO0VBQ3RCLGtCQUFrQjtFQUNsQixvQkFBb0I7RUFDcEIsMkJBQTJCO0VBQzNCLGdCQUFnQjtFQUNoQixxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLE9BQU87RUFDUCxTQUFTO0VBQ1QsV0FBVztFQUNYLFFBQVE7RUFDUixtQkFBbUI7RUFDbkIsMkJBQTJCO0FBQzdCOztBQUVBO0VBQ0UseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UsV0FBVztBQUNiOztBQUVBO0VBQ0UsWUFBWTtFQUNaLFdBQVc7RUFDWCxpQkFBaUI7RUFDakIsa0JBQWtCLEVBQUUsNkJBQTZCO0VBQ2pELGlCQUFpQjtBQUNuQiIsInNvdXJjZXNDb250ZW50IjpbImJvZHkge1xyXG4gIGZvbnQtc2l6ZTogLjg3NXJlbTtcclxufVxyXG5cclxuLmZlYXRoZXIge1xyXG4gIHdpZHRoOiAxNnB4O1xyXG4gIGhlaWdodDogMTZweDtcclxuICB2ZXJ0aWNhbC1hbGlnbjogdGV4dC1ib3R0b207XHJcbn1cclxuXHJcbi8qXHJcbiogU2lkZWJhclxyXG4qL1xyXG5cclxuLnNpZGViYXIge1xyXG4gIHBvc2l0aW9uOiBmaXhlZDtcclxuICB0b3A6IDA7XHJcbiAgYm90dG9tOiAwO1xyXG4gIGxlZnQ6IDA7XHJcbiAgei1pbmRleDogMTAwOyAvKiBCZWhpbmQgdGhlIG5hdmJhciAqL1xyXG4gIHBhZGRpbmc6IDA7XHJcbiAgYm94LXNoYWRvdzogaW5zZXQgLTFweCAwIDAgcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG59XHJcblxyXG4uc2lkZWJhci1zdGlja3kge1xyXG4gIHBvc2l0aW9uOiAtd2Via2l0LXN0aWNreTtcclxuICBwb3NpdGlvbjogc3RpY2t5O1xyXG4gIHRvcDogNDhweDsgLyogSGVpZ2h0IG9mIG5hdmJhciAqL1xyXG4gIGhlaWdodDogY2FsYygxMDB2aCAtIDQ4cHgpO1xyXG4gIHBhZGRpbmctdG9wOiAuNXJlbTtcclxuICBvdmVyZmxvdy14OiBoaWRkZW47XHJcbiAgb3ZlcmZsb3cteTogYXV0bzsgLyogU2Nyb2xsYWJsZSBjb250ZW50cyBpZiB2aWV3cG9ydCBpcyBzaG9ydGVyIHRoYW4gY29udGVudC4gKi9cclxufVxyXG5cclxuLnNpZGViYXIgLm5hdi1saW5rIHtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG4gIGNvbG9yOiAjMDAwMDAwO1xyXG59XHJcblxyXG4uc2lkZWJhciAubmF2LWxpbmsgLmZlYXRoZXIge1xyXG4gIG1hcmdpbi1yaWdodDogNHB4O1xyXG4gIGNvbG9yOiAjOTk5O1xyXG59XHJcblxyXG4uc2lkZWJhciAubmF2LWxpbmsuYWN0aXZlIHtcclxuICBjb2xvcjogIzAwN2JmZjtcclxufVxyXG5cclxuLnNpZGViYXIgLm5hdi1saW5rOmhvdmVyIC5mZWF0aGVyLFxyXG4uc2lkZWJhciAubmF2LWxpbmsuYWN0aXZlIC5mZWF0aGVyIHtcclxuICBjb2xvcjogaW5oZXJpdDtcclxufVxyXG5cclxuLnNpZGViYXItaGVhZGluZyB7XHJcbiAgZm9udC1zaXplOiAuNzVyZW07XHJcbiAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcclxufVxyXG5cclxuLypcclxuKiBOYXZiYXJcclxuKi9cclxuXHJcbi5uYXZiYXItYnJhbmQge1xyXG4gIHBhZGRpbmctdG9wOiAuNzVyZW07XHJcbiAgcGFkZGluZy1ib3R0b206IC43NXJlbTtcclxuICBmb250LXNpemU6IDFyZW07XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjI1KTtcclxuICBib3gtc2hhZG93OiBpbnNldCAtMXB4IDAgMCByZ2JhKDAsIDAsIDAsIDAuMjUpO1xyXG59XHJcblxyXG4ubmF2YmFyIC5mb3JtLWNvbnRyb2wge1xyXG4gIHBhZGRpbmc6IC43NXJlbSAxcmVtO1xyXG4gIGJvcmRlci13aWR0aDogMDtcclxuICBib3JkZXItcmFkaXVzOiAwO1xyXG59XHJcblxyXG4uZm9ybS1jb250cm9sLWRhcmsge1xyXG4gIGNvbG9yOiAjZmZmO1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgLjEpO1xyXG4gIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAuMSk7XHJcbn1cclxuXHJcbi5mb3JtLWNvbnRyb2wtZGFyazpmb2N1cyB7XHJcbiAgYm9yZGVyLWNvbG9yOiB0cmFuc3BhcmVudDtcclxuICBib3gtc2hhZG93OiAwIDAgMCAzcHggcmdiYSgyNTUsIDI1NSwgMjU1LCAuMjUpO1xyXG59XHJcblxyXG4vKlxyXG4qIFV0aWxpdGllc1xyXG4qL1xyXG5cclxuLmJvcmRlci10b3Age1xyXG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTVlNWU1O1xyXG59XHJcblxyXG4uYm9yZGVyLWJvdHRvbSB7XHJcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlNWU1ZTU7XHJcbn1cclxuXHJcbi8qIEJhc2UgYnV0dG9uIHN0eWxlICovXHJcbi5idG4ge1xyXG4gIHBhZGRpbmc6IDhweCAxNnB4O1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICBmb250LXdlaWdodDogYm9sZDtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG59XHJcblxyXG5ib2R5IHtcclxuICBmb250LWZhbWlseTogJ1BvcHBpbnMnLCBzYW5zLXNlcmlmO1xyXG4gIG1hcmdpbjogMDtcclxuICBwYWRkaW5nOiAwO1xyXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgtNDVkZWcsICMyNDM3NWEsICMxMDIwM2EsICMxNDIxM2IsICMwYzE3MjkpO1xyXG4gIGJhY2tncm91bmQtc2l6ZTogNDAwJSA0MDAlO1xyXG4gIGFuaW1hdGlvbjogZ3JhZGllbnRNb3ZlIDIwcyBlYXNlIGluZmluaXRlO1xyXG4gIGNvbG9yOiAjZmZmO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIGdyYWRpZW50TW92ZSB7XHJcbiAgMCUge1xyXG4gICAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAwJSA1MCU7XHJcbiAgfVxyXG5cclxuICA1MCUge1xyXG4gICAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAxMDAlIDUwJTtcclxuICB9XHJcblxyXG4gIDEwMCUge1xyXG4gICAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiAwJSA1MCU7XHJcbiAgfVxyXG59XHJcblxyXG4uY29udGFpbmVyLWZsdWlkIHtcclxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDYpO1xyXG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XHJcbiAgcGFkZGluZzogMnJlbTtcclxuICBtYXJnaW4tdG9wOiAyMHB4O1xyXG59XHJcblxyXG4udGFibGUtcmVzcG9uc2l2ZSB7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA2KTtcclxuICBib3JkZXItcmFkaXVzOiAxNXB4O1xyXG4gIHBhZGRpbmc6IDEuNXJlbTtcclxuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTJweCk7XHJcbiAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMjUpO1xyXG59XHJcblxyXG4udGFibGUge1xyXG4gIGNvbG9yOiAjZmZmO1xyXG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XHJcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICBib3JkZXItY29sbGFwc2U6IHNlcGFyYXRlO1xyXG4gIGJvcmRlci1zcGFjaW5nOiAwIDEycHg7XHJcbn1cclxuXHJcbi50YWJsZSB0aGVhZCB0aCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcclxuICBjb2xvcjogI2ZmZmZmZjtcclxuICBmb250LXdlaWdodDogNjAwO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgcGFkZGluZzogMXJlbTtcclxufVxyXG5cclxuLnRhYmxlIHRib2R5IHRyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XHJcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuMnMgZWFzZSwgYm94LXNoYWRvdyAwLjJzIGVhc2U7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbn1cclxuXHJcbi50YWJsZSB0Ym9keSB0cjpob3ZlciB7XHJcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjAxKTtcclxuICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcclxufVxyXG5cclxuLnRhYmxlIHRkIHtcclxuICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xyXG4gIHBhZGRpbmc6IDAuOXJlbTtcclxuICBmb250LXNpemU6IDAuOTVyZW07XHJcbn1cclxuXHJcbi5idG4tY3VzdG9tIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjhhNzQ1O1xyXG4gIGNvbG9yOiB3aGl0ZTtcclxuICBib3JkZXI6IG5vbmU7XHJcbiAgcGFkZGluZzogMTJweCAyNXB4O1xyXG4gIGJvcmRlci1yYWRpdXM6IDMwcHg7XHJcbiAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcclxuICBmb250LXdlaWdodDogYm9sZDtcclxuICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIDAuM3MsIHRyYW5zZm9ybSAwLjJzO1xyXG4gIGJveC1zaGFkb3c6IDAgNHB4IDE1cHggcmdiYSgwLCAwLCAwLCAwLjIpO1xyXG59XHJcblxyXG4uYnRuLWN1c3RvbTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzIxODgzODtcclxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbn1cclxuXHJcbi5lZGl0LWljb24ge1xyXG4gIGNvbG9yOiAjMjA2ZWUxO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxuICBmb250LXNpemU6IDIwcHg7XHJcbiAgbWFyZ2luOiAwIDEwcHg7XHJcbiAgdHJhbnNpdGlvbjogY29sb3IgMC4zcztcclxufVxyXG5cclxuLmVkaXQtaWNvbjpob3ZlciB7XHJcbiAgY29sb3I6ICNmZmZmZmY7XHJcbn1cclxuXHJcbi5kZWxldGUtaWNvbiB7XHJcbiAgY29sb3I6ICNkMjJkMmQ7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIGZvbnQtc2l6ZTogMjBweDtcclxuICB0cmFuc2l0aW9uOiBjb2xvciAwLjNzO1xyXG59XHJcblxyXG4uZGVsZXRlLWljb246aG92ZXIge1xyXG4gIGNvbG9yOiAjZmZmZmZmO1xyXG59XHJcblxyXG4ubmF2YmFyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMWMxYzFjICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbmgyIHtcclxuICBjb2xvcjogI2ZmZjtcclxufVxyXG5cclxuYS5yb3V0ZXItbGluayB7XHJcbiAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG59XHJcblxyXG4ubW9kYWwtaGVhZGVyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjUyNTI4O1xyXG59XHJcblxyXG4vKiBNb2RhbCBCb2R5ICovXHJcbi5tb2RhbC1ib2R5IHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gIHBhZGRpbmc6IDEuNXJlbTtcclxuICBjb2xvcjogIzMzMztcclxuICBmb250LXNpemU6IDFyZW07XHJcbn1cclxuXHJcbi8qIE1vZGFsIEZvb3RlciAqL1xyXG4ubW9kYWwtZm9vdGVyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjFmMWYxO1xyXG4gIHBhZGRpbmc6IDFyZW07XHJcbiAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNkZGQ7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsgLyogQ2VudGVyIGJ1dHRvbnMgaG9yaXpvbnRhbGx5ICovXHJcbiAgZ2FwOiAxcmVtOyAvKiBTcGFjZSBiZXR3ZWVuIHRoZSBidXR0b25zICovXHJcbn1cclxuXHJcbi5tb2RhbC1mb290ZXIgYnV0dG9uIHtcclxuICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcclxuICBmb250LXNpemU6IDFyZW07XHJcbiAgYm9yZGVyLXJhZGl1czogMC4zcmVtO1xyXG4gIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4zcywgdHJhbnNmb3JtIDAuMnM7XHJcbn1cclxuXHJcbi5tb2RhbC1mb290ZXIgLmJ0bi1zZWNvbmRhcnkge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICM2Yzc1N2Q7XHJcbiAgY29sb3I6ICNmZmY7XHJcbiAgYm9yZGVyOiBub25lO1xyXG59XHJcblxyXG4ubW9kYWwtZm9vdGVyIC5idG4tc2Vjb25kYXJ5OmhvdmVyIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjNWE2MjY4O1xyXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcclxufVxyXG5cclxuLm1vZGFsLWZvb3RlciAuYnRuLXByaW1hcnkge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICMwMDdiZmY7XHJcbiAgY29sb3I6ICNmZmY7XHJcbiAgYm9yZGVyOiBub25lO1xyXG59XHJcblxyXG4ubW9kYWwtZm9vdGVyIC5idG4tcHJpbWFyeTpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzAwNTZiMztcclxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbn1cclxuXHJcbi8qIEVuc3VyZSBzZWFyY2ggYmFyIGFwcGVhcnMgYmVsb3cgdGhlIGJ1dHRvbiAqL1xyXG4ubWItMyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOyAvKiBTdGFjayBpdGVtcyB2ZXJ0aWNhbGx5ICovXHJcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7IC8qIEFsaWduIHRoZSBpdGVtcyB0byB0aGUgc3RhcnQgKi9cclxufVxyXG5cclxuLyogU2VhcmNoIEJhciBTdHlsZXMgKi9cclxuLnNlYXJjaC1iYXIge1xyXG4gIG1hcmdpbi10b3A6IDEwcHg7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpOyAvKiBUcmFuc3BhcmVudCB3aGl0ZSBiYWNrZ3JvdW5kICovXHJcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpOyAvKiBTdWJ0bGUgYm9yZGVyICovXHJcbiAgYm9yZGVyLXJhZGl1czogMzBweDsgLyogUm91bmRlZCBjb3JuZXJzICovXHJcbiAgY29sb3I6ICNmZmY7IC8qIFdoaXRlIHRleHQgKi9cclxuICBwYWRkaW5nOiAxMHB4IDIwcHg7IC8qIFBhZGRpbmcgaW5zaWRlIHRoZSBpbnB1dCAqL1xyXG4gIGZvbnQtc2l6ZTogMXJlbTsgLyogRm9udCBzaXplIGZvciBiZXR0ZXIgcmVhZGFiaWxpdHkgKi9cclxuICBib3gtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLCAwLCAwLCAwLjMpOyAvKiBTbGlnaHQgc2hhZG93IGZvciBkZXB0aCAqL1xyXG4gIHdpZHRoOiAxMDAlOyAvKiBNYWtlIGl0IHJlc3BvbnNpdmUgKi9cclxuICBtYXgtd2lkdGg6IDQwMHB4OyAvKiBTZXQgYSBtYXggd2lkdGggZm9yIGJldHRlciBhcHBlYXJhbmNlIG9uIGxhcmdlciBzY3JlZW5zICovXHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsgLyogU21vb3RoIHRyYW5zaXRpb24gKi9cclxufVxyXG5cclxuLnNlYXJjaC1iYXI6Zm9jdXMge1xyXG4gIG91dGxpbmU6IG5vbmU7IC8qIFJlbW92ZSBkZWZhdWx0IG91dGxpbmUgKi9cclxuICBib3JkZXItY29sb3I6ICMwMDdiZmY7IC8qIEhpZ2hsaWdodCB3aXRoIGJsdWUgYm9yZGVyIG9uIGZvY3VzICovXHJcbiAgYm94LXNoYWRvdzogMCAwIDhweCByZ2JhKDAsIDEyMywgMjU1LCAwLjYpOyAvKiBCbHVlIGdsb3cgZWZmZWN0ICovXHJcbn1cclxuXHJcbi8qIFBsYWNlaG9sZGVyIHRleHQgY29sb3IgKi9cclxuLnNlYXJjaC1iYXI6OnBsYWNlaG9sZGVyIHtcclxuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpOyAvKiBMaWdodCBncmF5IHBsYWNlaG9sZGVyICovXHJcbn1cclxuXHJcblxyXG4uY3VzdG9tLXNpZGViYXIge1xyXG4gIGJhY2tncm91bmQ6IHJnYmEoMjAsIDMzLCA1OSwgMC45KTsgLyogZGFyaywgc2xpZ2h0bHkgdHJhbnNwYXJlbnQgKi9cclxuICBjb2xvcjogI2ZmZjtcclxuICBtaW4taGVpZ2h0OiAxMDB2aDtcclxuICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XHJcbn1cclxuXHJcbi5jdXN0b20tc2lkZWJhciAubmF2LWxpbmsge1xyXG4gIGNvbG9yOiAjY2NjO1xyXG4gIHRyYW5zaXRpb246IGNvbG9yIDAuM3MgZWFzZSwgYmFja2dyb3VuZCAwLjNzIGVhc2U7XHJcbn1cclxuXHJcbi5jdXN0b20tc2lkZWJhciAubmF2LWxpbms6aG92ZXIsXHJcbi5jdXN0b20tc2lkZWJhciAubmF2LWxpbmsuYWN0aXZlIHtcclxuICBjb2xvcjogI2ZmZjtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XHJcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG59XHJcblxyXG5cclxuLmN1c3RvbS10b3BiYXIge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICMwMDAgIWltcG9ydGFudDtcclxuICBjb2xvcjogI2ZmZjtcclxuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xyXG59XHJcblxyXG4uY3VzdG9tLXRvcGJhciAubmF2YmFyLWJyYW5kLFxyXG4uY3VzdG9tLXRvcGJhciAubmF2LWxpbmsge1xyXG4gIGNvbG9yOiAjZmZmICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5jdXN0b20tdG9wYmFyIC5uYXYtbGluazpob3ZlciB7XHJcbiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbn1cclxuXHJcblxyXG5cclxuLm5hdmJhciAubmF2LWxpbmsge1xyXG4gIGNvbG9yOiAjZmZmICFpbXBvcnRhbnQ7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xyXG4gIHRyYW5zaXRpb246IGNvbG9yIDAuM3MgZWFzZTtcclxuICBmb250LXdlaWdodDogNTAwO1xyXG4gIGxldHRlci1zcGFjaW5nOiAwLjVweDtcclxufVxyXG5cclxuLm5hdmJhciAubmF2LWxpbms6OmFmdGVyIHtcclxuICBjb250ZW50OiAnJztcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgbGVmdDogMDtcclxuICBib3R0b206IDA7XHJcbiAgaGVpZ2h0OiAycHg7XHJcbiAgd2lkdGg6IDA7XHJcbiAgYmFja2dyb3VuZDogI2ZmNGM2MDtcclxuICB0cmFuc2l0aW9uOiB3aWR0aCAwLjNzIGVhc2U7XHJcbn1cclxuXHJcbi5uYXZiYXIgLm5hdi1saW5rOmhvdmVyIHtcclxuICBjb2xvcjogI2ZmNGM2MCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4ubmF2YmFyIC5uYXYtbGluazpob3Zlcjo6YWZ0ZXIge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcblxyXG4ubG9nby1pbWcge1xyXG4gIGhlaWdodDogMzBweDtcclxuICB3aWR0aDogMzBweDtcclxuICBvYmplY3QtZml0OiBjb3ZlcjtcclxuICBib3JkZXItcmFkaXVzOiA1MCU7IC8qIE9wdGlvbmFsOiBtYWtlcyBpdCByb3VuZCAqL1xyXG4gIG1hcmdpbi1yaWdodDogOHB4O1xyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\";\nexport class ActionnairesComponent {\n  constructor(actionnaireService, router) {\n    this.actionnaireService = actionnaireService;\n    this.router = router;\n    this.actionnaires = [];\n  }\n  ngOnInit() {\n    this.fetchActionnaires();\n  }\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  fetchActionnaires() {\n    this.actionnaireService.getAllActionnaires().subscribe({\n      next: data => {\n        this.actionnaires = data;\n        console.log('Fetched actionnaires:', data);\n      },\n      error: err => {\n        console.error('Error fetching actionnaires:', err);\n      }\n    });\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // Should be null\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function ActionnairesComponent_Factory(t) {\n      return new (t || ActionnairesComponent)(i0.ɵɵdirectiveInject(i1.ActionnaireService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionnairesComponent,\n      selectors: [[\"app-actions\"]],\n      viewQuery: function ActionnairesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 85,\n      vars: 1,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"width\", \"900\", \"height\", \"380\", 2, \"background-color\", \"lightgrey\"], [\"myChart\", \"\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"thead-dark\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"myChart\", \"width\", \"900\", \"height\", \"380\", 1, \"my-4\"]],\n      template: function ActionnairesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"link\", 5)(7, \"link\", 5)(8, \"link\", 6)(9, \"link\", 7)(10, \"link\", 8);\n          i0.ɵɵelementStart(11, \"title\");\n          i0.ɵɵtext(12, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"link\", 9)(14, \"canvas\", 10, 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"body\")(17, \"nav\", 12)(18, \"a\", 13);\n          i0.ɵɵelement(19, \"img\", 14);\n          i0.ɵɵtext(20, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"ul\", 15)(22, \"li\", 16)(23, \"a\", 17);\n          i0.ɵɵlistener(\"click\", function ActionnairesComponent_Template_a_click_23_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(24, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 18)(26, \"div\", 19)(27, \"nav\", 20)(28, \"div\", 21)(29, \"ul\", 22)(30, \"li\", 23)(31, \"a\", 24);\n          i0.ɵɵelement(32, \"span\", 25);\n          i0.ɵɵtext(33, \" Dashboard \");\n          i0.ɵɵelementStart(34, \"span\", 26);\n          i0.ɵɵtext(35, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"li\", 23)(37, \"a\", 27);\n          i0.ɵɵelement(38, \"span\", 28);\n          i0.ɵɵtext(39, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"li\", 23)(41, \"a\", 29);\n          i0.ɵɵelement(42, \"span\", 30);\n          i0.ɵɵtext(43, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 23)(45, \"a\", 31);\n          i0.ɵɵelement(46, \"span\", 32);\n          i0.ɵɵtext(47, \" Gestion des transactions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"li\", 23)(49, \"a\", 33);\n          i0.ɵɵelement(50, \"span\", 34);\n          i0.ɵɵtext(51, \" Gestion des actions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"li\", 23)(53, \"a\", 35);\n          i0.ɵɵelement(54, \"span\", 36);\n          i0.ɵɵtext(55, \" Gestion des actionnaires \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(56, \"main\", 37)(57, \"div\", 38)(58, \"h1\", 39);\n          i0.ɵɵtext(59, \"Actionnaires\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 40)(61, \"div\", 41)(62, \"button\", 42);\n          i0.ɵɵtext(63, \"Import\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"button\", 42);\n          i0.ɵɵtext(65, \"Export\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(66, \"div\", 43)(67, \"h2\");\n          i0.ɵɵtext(68, \"Liste des Actionnaires\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"table\", 44)(70, \"thead\", 45)(71, \"tr\")(72, \"th\");\n          i0.ɵɵtext(73, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"th\");\n          i0.ɵɵtext(75, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"th\");\n          i0.ɵɵtext(77, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"th\");\n          i0.ɵɵtext(79, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"th\");\n          i0.ɵɵtext(81, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(82, \"tbody\");\n          i0.ɵɵtemplate(83, ActionnairesComponent_tr_83_Template, 11, 5, \"tr\", 46);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(84, \"canvas\", 47);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(83);\n          i0.ɵɵproperty(\"ngForOf\", ctx.actionnaires);\n        }\n      },\n      dependencies: [i3.NgForOf],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "a_r2", "idActionnaire", "nomActionnaire", "prenomActionnaire", "emailActionnaire", "telephone", "ActionnairesComponent", "constructor", "actionnaireService", "router", "actionnaires", "ngOnInit", "fetchActionnaires", "ngAfterViewInit", "replace", "getAllActionnaires", "subscribe", "next", "data", "console", "log", "error", "err", "logout", "localStorage", "removeItem", "getItem", "navigate", "ɵɵdirectiveInject", "i1", "ActionnaireService", "i2", "Router", "selectors", "viewQuery", "ActionnairesComponent_Query", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "ActionnairesComponent_Template_a_click_23_listener", "ɵɵtemplate", "ActionnairesComponent_tr_83_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\actionnaires\\actionnaires.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\actionnaires\\actionnaires.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, ElementRef, ViewChild } from '@angular/core';\nimport { ActionnaireService } from '../services/actionnaire.service';\nimport { Actionnaire } from '../model/actionnaire.model';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\n\n\n@Component({\n  selector: 'app-actions',\n  templateUrl: './actionnaires.component.html',\n  styleUrls: ['./actionnaires.component.css']\n})\nexport class ActionnairesComponent implements OnInit, AfterViewInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n\n  actionnaires: Actionnaire[] = [];\n\n  constructor(\n    private actionnaireService: ActionnaireService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.fetchActionnaires();\n  }\n\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  fetchActionnaires(): void {\n    this.actionnaireService.getAllActionnaires().subscribe({\n      next: (data) => {\n        this.actionnaires = data;\n        console.log('Fetched actionnaires:', data);\n      },\n      error: (err) => {\n        console.error('Error fetching actionnaires:', err);\n      }\n    });\n  }\n\n  logout(): void {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // Should be null\n    this.router.navigate(['/login']);\n  }\n}\n", "\n<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n\n    <!-- Custom styles for this template -->\n    <link href=\"./actionnaires.component.css\" rel=\"stylesheet\">\n    <canvas #myChart width=\"900\" height=\"380\" style=\"background-color: lightgrey;\"></canvas>\n\n  </head>\n\n  <body>\n    \n    <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\" href=\"adminDash\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            \n            <li class=\"nav-item\">\n              <a class=\"nav-link\"  href=\"/transactions\">\n                <span data-feather=\"dollar-sign\"></span>\n                Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/actions\">\n                <span data-feather=\"trending-up\"></span>\n               Gestion des actions\n              </a>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\">\n                  <span data-feather=\"users\"></span>\n                 Gestion des actionnaires\n                </a>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Actionnaires</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n                <button class=\"btn btn-sm btn-outline-secondary\">Import</button>\n                <button class=\"btn btn-sm btn-outline-secondary\">Export</button>\n              </div>\n\n              \n            </div>\n          </div>\n\n          <div class=\"table-responsive\">\n            <h2>Liste des Actionnaires</h2>\n          \n            <table class=\"table table-hover\">\n              <thead class=\"thead-dark\">\n                <tr>\n                  <th>ID</th>\n                  <th>Nom</th>\n                  <th>Prénom</th>\n                  <th>Email</th>\n                  <th>Téléphone</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let a of actionnaires\">\n                  <td>{{ a.idActionnaire }}</td>\n                  <td>{{ a.nomActionnaire }}</td>\n                  <td>{{ a.prenomActionnaire }}</td>\n                  <td>{{ a.emailActionnaire }}</td>\n                  <td>{{ a.telephone }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n          \n\n          <canvas class=\"my-4\" id=\"myChart\" width=\"900\" height=\"380\"></canvas>\n          \n        \n        </main>\n      </div>\n    </div>\n\n    <!-- Placed at the end of the document so the pages load faster -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>    \n\n    </script>\n  </body>\n</html>\n"], "mappings": "AAIA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;;;;;IC4GxBC,EAAA,CAAAC,cAAA,SAAmC;IAC7BD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAJtBH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,IAAA,CAAAC,aAAA,CAAqB;IACrBP,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAC,IAAA,CAAAE,cAAA,CAAsB;IACtBR,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAC,IAAA,CAAAG,iBAAA,CAAyB;IACzBT,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAAC,IAAA,CAAAI,gBAAA,CAAwB;IACxBV,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAC,IAAA,CAAAK,SAAA,CAAiB;;;;ADzGvC,OAAM,MAAOC,qBAAqB;EAKhCC,YACUC,kBAAsC,EACtCC,MAAc;IADd,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IAJhB,KAAAC,YAAY,GAAkB,EAAE;EAK7B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,eAAeA,CAAA;IACbpB,OAAO,CAACqB,OAAO,EAAE,CAAC,CAAC;EACrB;;EAEAF,iBAAiBA,CAAA;IACf,IAAI,CAACJ,kBAAkB,CAACO,kBAAkB,EAAE,CAACC,SAAS,CAAC;MACrDC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACR,YAAY,GAAGQ,IAAI;QACxBC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,IAAI,CAAC;MAC5C,CAAC;MACDG,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEC,GAAG,CAAC;MACpD;KACD,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCN,OAAO,CAACC,GAAG,CAACI,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBApCWrB,qBAAqB,EAAAZ,EAAA,CAAAkC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAApC,EAAA,CAAAkC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAArB1B,qBAAqB;MAAA2B,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCVlC1C,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAA4C,SAAA,cAAsB;UAUtB5C,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAE,MAAA,wCAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE/CH,EAAA,CAAA4C,SAAA,eAAmF;UAQrF5C,EAAA,CAAAG,YAAA,EAAO;UAEPH,EAAA,CAAAC,cAAA,YAAM;UAIAD,EAAA,CAAA4C,SAAA,eAAwE;UACxE5C,EAAA,CAAAE,MAAA,aACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAA6C,UAAA,mBAAAC,mDAAA;YAAA,OAASH,GAAA,CAAAd,MAAA,EAAQ;UAAA,EAAC;UAAC7B,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMzDH,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAA4C,SAAA,gBAAiC;UACjC5C,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGpDH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAA4C,SAAA,gBAAiC;UACjC5C,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAA4C,SAAA,gBAAiC;UACjC5C,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGRH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAA4C,SAAA,gBAAwC;UACxC5C,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAA4C,SAAA,gBAAwC;UACzC5C,EAAA,CAAAE,MAAA,6BACD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAA4C,SAAA,gBAAkC;UACnC5C,EAAA,CAAAE,MAAA,kCACD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKZH,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,eAAsC;UAEeD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChEH,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOtEH,EAAA,CAAAC,cAAA,eAA8B;UACxBD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE/BH,EAAA,CAAAC,cAAA,iBAAiC;UAGvBD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACXH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACZH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACfH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGtBH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA+C,UAAA,KAAAC,oCAAA,kBAMK;UACPhD,EAAA,CAAAG,YAAA,EAAQ;UAKZH,EAAA,CAAA4C,SAAA,kBAAoE;UAGtE5C,EAAA,CAAAG,YAAA,EAAO;;;UAfmBH,EAAA,CAAAI,SAAA,IAAe;UAAfJ,EAAA,CAAAiD,UAAA,YAAAN,GAAA,CAAA3B,YAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}