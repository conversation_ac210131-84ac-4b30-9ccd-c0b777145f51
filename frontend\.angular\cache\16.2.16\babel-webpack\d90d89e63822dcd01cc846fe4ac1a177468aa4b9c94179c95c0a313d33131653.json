{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthenticationService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth';\n  }\n  register(firstName, lastName, email, password) {\n    return this.http.post(`${this.apiUrl}/register`, {\n      firstName,\n      lastName,\n      email,\n      password\n    });\n  }\n  login(matricule, password) {\n    return this.http.post(`${this.apiUrl}/authenticate`, {\n      matricule,\n      password\n    });\n  }\n  storeToken(token) {\n    localStorage.setItem('jwt_token', token);\n  }\n  getToken() {\n    return localStorage.getItem('jwt_token');\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    this.router.navigate(['/login']);\n  }\n  isAuthenticated() {\n    return !!this.getToken();\n  }\n  getUserDetailsFromToken(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return {\n        role: payload?.role || '',\n        groupe: payload?.groupe || '',\n        prenom: payload?.prenom || '',\n        nom: payload?.nom || ''\n      };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return {\n        role: '',\n        groupe: '',\n        prenom: '',\n        nom: ''\n      };\n    }\n  }\n  static {\n    this.ɵfac = function AuthenticationService_Factory(t) {\n      return new (t || AuthenticationService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthenticationService,\n      factory: AuthenticationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AuthenticationService", "constructor", "http", "router", "apiUrl", "register", "firstName", "lastName", "email", "password", "post", "login", "matricule", "storeToken", "token", "localStorage", "setItem", "getToken", "getItem", "logout", "removeItem", "navigate", "isAuthenticated", "getUserDetailsFromToken", "payload", "JSON", "parse", "atob", "split", "role", "groupe", "prenom", "nom", "e", "console", "error", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\auth\\authentication.service.ts"], "sourcesContent": ["// authentication.service.ts\nimport { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { Observable } from 'rxjs';\n\ninterface AuthResponse {\n  token: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthenticationService {\n  private apiUrl = 'http://localhost:8000/api/v1/auth';\n\n  constructor(private http: HttpClient, private router: Router) {}\n\n  register(firstName: string, lastName: string, email: string, password: string): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/register`, { firstName, lastName, email, password });\n  }\n\n  login(matricule: number, password: string): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/authenticate`, { matricule, password });\n  }\n\n  storeToken(token: string) {\n    localStorage.setItem('jwt_token', token);\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('jwt_token');\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    this.router.navigate(['/login']);\n  }\n\n  isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n\n  getUserDetailsFromToken(token: string): { role: string, groupe: string, prenom: string, nom: string } {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      return {\n        role: payload?.role || '',\n        groupe: payload?.groupe || '',\n        prenom: payload?.prenom || '',\n        nom: payload?.nom || ''\n      };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return { role: '', groupe: '', prenom: '', nom: '' };\n    }\n  }\n}\n"], "mappings": ";;;AAaA,OAAM,MAAOA,qBAAqB;EAGhCC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAF5C,KAAAC,MAAM,GAAG,mCAAmC;EAEW;EAE/DC,QAAQA,CAACC,SAAiB,EAAEC,QAAgB,EAAEC,KAAa,EAAEC,QAAgB;IAC3E,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAe,GAAG,IAAI,CAACN,MAAM,WAAW,EAAE;MAAEE,SAAS;MAAEC,QAAQ;MAAEC,KAAK;MAAEC;IAAQ,CAAE,CAAC;EAC1G;EAEAE,KAAKA,CAACC,SAAiB,EAAEH,QAAgB;IACvC,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAe,GAAG,IAAI,CAACN,MAAM,eAAe,EAAE;MAAEQ,SAAS;MAAEH;IAAQ,CAAE,CAAC;EAC7F;EAEAI,UAAUA,CAACC,KAAa;IACtBC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEF,KAAK,CAAC;EAC1C;EAEAG,QAAQA,CAAA;IACN,OAAOF,YAAY,CAACG,OAAO,CAAC,WAAW,CAAC;EAC1C;EAEAC,MAAMA,CAAA;IACJJ,YAAY,CAACK,UAAU,CAAC,WAAW,CAAC;IACpCL,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;IAC/BL,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAACL,QAAQ,EAAE;EAC1B;EAEAM,uBAAuBA,CAACT,KAAa;IACnC,IAAI;MACF,MAAMU,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACb,KAAK,CAACc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,OAAO;QACLC,IAAI,EAAEL,OAAO,EAAEK,IAAI,IAAI,EAAE;QACzBC,MAAM,EAAEN,OAAO,EAAEM,MAAM,IAAI,EAAE;QAC7BC,MAAM,EAAEP,OAAO,EAAEO,MAAM,IAAI,EAAE;QAC7BC,GAAG,EAAER,OAAO,EAAEQ,GAAG,IAAI;OACtB;KACF,CAAC,OAAOC,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,CAAC,CAAC;MACzC,OAAO;QAAEJ,IAAI,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,GAAG,EAAE;MAAE,CAAE;;EAExD;;;uBA7CWhC,qBAAqB,EAAAoC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAArBzC,qBAAqB;MAAA0C,OAAA,EAArB1C,qBAAqB,CAAA2C,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}