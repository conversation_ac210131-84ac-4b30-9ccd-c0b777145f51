package com.Aziz.Administratif.ClientRelation.Entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.springframework.data.annotation.LastModifiedDate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Portefeuille {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idPortefeuille;
    private BigDecimal solde;
    private Integer quantite;

    @CreationTimestamp
    private LocalDateTime DateCreation;

    @LastModifiedDate
    private LocalDateTime DateModification;


    @ManyToOne
    @JoinColumn(name = "idAction")
    private Action action;

    @ManyToOne
    @JoinColumn(name = "idActionnaire")
//    @JsonBackReference
    private Actionnaire actionnaire;

    @OneToMany(mappedBy = "portefeuille")
    @JsonManagedReference
    @JsonIgnore
    private List<Transaction> transactions;

}
