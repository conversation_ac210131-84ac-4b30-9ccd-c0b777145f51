{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let AdminDashComponent = class AdminDashComponent {\n  constructor() {\n    this.responsables = [{\n      id: 1,\n      name: '<PERSON>',\n      email: '<EMAIL>'\n    }, {\n      id: 2,\n      name: '<PERSON>',\n      email: '<EMAIL>'\n    }, {\n      id: 3,\n      name: '<PERSON>',\n      email: '<EMAIL>'\n    }];\n  }\n  ngOnInit() {}\n  // Add Responsable\n  addResponsable() {\n    const newResponsable = {\n      id: this.responsables.length + 1,\n      name: 'New Responsable',\n      email: '<EMAIL>'\n    };\n    this.responsables.push(newResponsable);\n  }\n  // Edit Responsable\n  editResponsable(responsable) {\n    const updatedName = prompt('Edit Responsable Name:', responsable.name);\n    if (updatedName) {\n      responsable.name = updatedName;\n    }\n  }\n  // Delete Responsable\n  deleteResponsable(id) {\n    this.responsables = this.responsables.filter(res => res.id !== id);\n  }\n  // Add Actionnaire (similar to Responsable)\n  addActionnaire() {\n    alert('Actionnaire added! You can customize this function.');\n  }\n};\nAdminDashComponent = __decorate([Component({\n  selector: 'app-admin-dash',\n  templateUrl: './admin-dash.component.html',\n  styleUrls: ['./admin-dash.component.css']\n})], AdminDashComponent);", "map": {"version": 3, "names": ["Component", "AdminDashComponent", "constructor", "responsables", "id", "name", "email", "ngOnInit", "addResponsable", "newResponsable", "length", "push", "editResponsable", "responsable", "updatedName", "prompt", "deleteResponsable", "filter", "res", "addActionnaire", "alert", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\admin-dash\\admin-dash.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-admin-dash',\n  templateUrl: './admin-dash.component.html',\n  styleUrls: ['./admin-dash.component.css']\n})\nexport class AdminDashComponent implements OnInit {\n\n  responsables = [\n    { id: 1, name: '<PERSON>', email: '<EMAIL>' },\n    { id: 2, name: '<PERSON>', email: '<EMAIL>' },\n    { id: 3, name: '<PERSON>', email: '<EMAIL>' }\n  ];\n\n  constructor() {}\n\n  ngOnInit(): void {}\n\n  // Add Responsable\n  addResponsable() {\n    const newResponsable = { id: this.responsables.length + 1, name: 'New Responsable', email: '<EMAIL>' };\n    this.responsables.push(newResponsable);\n  }\n\n  // Edit Responsable\n  editResponsable(responsable: any) {\n    const updatedName = prompt('Edit Responsable Name:', responsable.name);\n    if (updatedName) {\n      responsable.name = updatedName;\n    }\n  }\n\n  // Delete Responsable\n  deleteResponsable(id: number) {\n    this.responsables = this.responsables.filter(res => res.id !== id);\n  }\n\n  // Add Actionnaire (similar to Responsable)\n  addActionnaire() {\n    alert('Actionnaire added! You can customize this function.');\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAO1C,WAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAQ7BC,YAAA;IANA,KAAAC,YAAY,GAAG,CACb;MAAEC,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACtD;MAAEF,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAkB,CAAE,EACxD;MAAEF,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAoB,CAAE,CAC7D;EAEc;EAEfC,QAAQA,CAAA,GAAU;EAElB;EACAC,cAAcA,CAAA;IACZ,MAAMC,cAAc,GAAG;MAAEL,EAAE,EAAE,IAAI,CAACD,YAAY,CAACO,MAAM,GAAG,CAAC;MAAEL,IAAI,EAAE,iBAAiB;MAAEC,KAAK,EAAE;IAAiB,CAAE;IAC9G,IAAI,CAACH,YAAY,CAACQ,IAAI,CAACF,cAAc,CAAC;EACxC;EAEA;EACAG,eAAeA,CAACC,WAAgB;IAC9B,MAAMC,WAAW,GAAGC,MAAM,CAAC,wBAAwB,EAAEF,WAAW,CAACR,IAAI,CAAC;IACtE,IAAIS,WAAW,EAAE;MACfD,WAAW,CAACR,IAAI,GAAGS,WAAW;;EAElC;EAEA;EACAE,iBAAiBA,CAACZ,EAAU;IAC1B,IAAI,CAACD,YAAY,GAAG,IAAI,CAACA,YAAY,CAACc,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACd,EAAE,KAAKA,EAAE,CAAC;EACpE;EAEA;EACAe,cAAcA,CAAA;IACZC,KAAK,CAAC,qDAAqD,CAAC;EAC9D;CACD;AAnCYnB,kBAAkB,GAAAoB,UAAA,EAL9BrB,SAAS,CAAC;EACTsB,QAAQ,EAAE,gBAAgB;EAC1BC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,CAAC,4BAA4B;CACzC,CAAC,C,EACWvB,kBAAkB,CAmC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}