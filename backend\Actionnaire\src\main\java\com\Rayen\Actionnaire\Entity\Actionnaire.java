package com.Rayen.Actionnaire.Entity;


import jakarta.persistence.*;

import java.util.Date;

@Entity
@Table(name="Actionnaire")
public class Actionnaire {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idActionnaire;
    private String NomActionnaire;
    private String PrenomActionnaire;
    private String EmailActionnaire;
    private Long Telephone;
    private Date DateCreation;
    private String UserCreation;
    private Date DateModification;
    private String UserModification;
    private Long userId;




    public Actionnaire() {
    }

    public Actionnaire(Long idActionnaire, String nomActionnaire, String prenomActionnaire, String emailActionnaire, Long telephone, Date dateCreation, String userCreation, Date dateModification, String userModification, Long userId) {
        this.idActionnaire = idActionnaire;
        NomActionnaire = nomActionnaire;
        PrenomActionnaire = prenomActionnaire;
        EmailActionnaire = emailActionnaire;
        Telephone = telephone;
        DateCreation = dateCreation;
        UserCreation = userCreation;
        DateModification = dateModification;
        UserModification = userModification;
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getIdActionnaire() {
        return idActionnaire;
    }

    public void setIdActionnaire(Long idActionnaire) {
        this.idActionnaire = idActionnaire;
    }

    public String getNomActionnaire() {
        return NomActionnaire;
    }

    public void setNomActionnaire(String nomActionnaire) {
        NomActionnaire = nomActionnaire;
    }

    public String getPrenomActionnaire() {
        return PrenomActionnaire;
    }

    public void setPrenomActionnaire(String prenomActionnaire) {
        PrenomActionnaire = prenomActionnaire;
    }

    public String getEmailActionnaire() {
        return EmailActionnaire;
    }

    public void setEmailActionnaire(String emailActionnaire) {
        EmailActionnaire = emailActionnaire;
    }

    public Long getTelephone() {
        return Telephone;
    }

    public void setTelephone(Long telephone) {
        Telephone = telephone;
    }

    public Date getDateCreation() {
        return DateCreation;
    }

    public void setDateCreation(Date dateCreation) {
        DateCreation = dateCreation;
    }

    public String getUserCreation() {
        return UserCreation;
    }

    public void setUserCreation(String userCreation) {
        UserCreation = userCreation;
    }

    public Date getDateModification() {
        return DateModification;
    }

    public void setDateModification(Date dateModification) {
        DateModification = dateModification;
    }

    public String getUserModification() {
        return UserModification;
    }

    public void setUserModification(String userModification) {
        UserModification = userModification;
    }
}
