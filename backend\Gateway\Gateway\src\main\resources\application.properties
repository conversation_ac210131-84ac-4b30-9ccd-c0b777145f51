spring.application.name=Gateway


server.port=8222

eureka.client.register-with-eureka=false

spring.cloud.gateway.discovery.locator.enabled=true

spring.config.import=optional:configserver:http://localhost:8888


spring.cloud.gateway.routes[0].id=Action
spring.cloud.gateway.routes[0].uri=http://localhost:8010
spring.cloud.gateway.routes[0].predicates[0]=Path=/GRA/Action/**

spring.cloud.gateway.routes[1].id=Action
spring.cloud.gateway.routes[1].uri=http://localhost:8020
spring.cloud.gateway.routes[1].predicates[1]=Path=/GRA/Actionnaire**

spring.cloud.gateway.routes[2].id=Notification
spring.cloud.gateway.routes[2].uri=http://localhost:8030
spring.cloud.gateway.routes[2].predicates[2]=Path=/GRA/Notification/**

spring.cloud.gateway.routes[3].id=Portefeuille
spring.cloud.gateway.routes[3].uri=http://localhost:8040
spring.cloud.gateway.routes[3].predicates[3]=Path=/GRA/Portefeuille/**

spring.cloud.gateway.routes[4].id=Transaction
spring.cloud.gateway.routes[4].uri=http://localhost:8050
spring.cloud.gateway.routes[4].predicates[4]=Path=/GRA/Transaction/**

spring.cloud.gateway.routes[5].id=User
spring.cloud.gateway.routes[5].uri=http://localhost:8060
spring.cloud.gateway.routes[5].predicates[5]=Path=/GRA/User/**