{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../responsable.service\";\nexport class ResponsableEditComponent {\n  constructor(route, responsableService, router) {\n    this.route = route;\n    this.responsableService = responsableService;\n    this.router = router;\n    this.responsable = {\n      id: 0,\n      nom: '',\n      prenom: '',\n      email: '',\n      telephone: '',\n      groupe: ''\n    };\n  }\n  ngOnInit() {\n    this.responsableId = +this.route.snapshot.paramMap.get('id');\n    this.responsableService.getResponsableById(this.responsableId).subscribe(data => {\n      this.responsable = data;\n    }, error => {\n      console.error('Error fetching responsable:', error.message);\n    });\n  }\n  updateResponsable() {\n    this.responsableService.updateResponsable(this.responsableId, this.responsable).subscribe(response => {\n      console.log('Responsable updated:', response);\n      this.router.navigate(['/ResDash']);\n    }, error => {\n      console.error('Error updating responsable:', error.message);\n    });\n  }\n  static {\n    this.ɵfac = function ResponsableEditComponent_Factory(t) {\n      return new (t || ResponsableEditComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ResponsableService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResponsableEditComponent,\n      selectors: [[\"app-responsable-edit\"]],\n      decls: 2,\n      vars: 0,\n      template: function ResponsableEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"responsable-edit works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ResponsableEditComponent", "constructor", "route", "responsableService", "router", "responsable", "id", "nom", "prenom", "email", "telephone", "groupe", "ngOnInit", "responsableId", "snapshot", "paramMap", "get", "getResponsableById", "subscribe", "data", "error", "console", "message", "updateResponsable", "response", "log", "navigate", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ResponsableService", "Router", "selectors", "decls", "vars", "template", "ResponsableEditComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\responsable-edit\\responsable-edit.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\responsable-edit\\responsable-edit.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport { HttpErrorResponse } from '@angular/common/http';  // Import HttpErrorResponse for error typing\n\n@Component({\n  selector: 'app-responsable-edit',\n  templateUrl: './responsable-edit.component.html',\n  styleUrls: ['./responsable-edit.component.css']\n})\nexport class ResponsableEditComponent implements OnInit {\n  responsableId!: number;\n  responsable: User = { id: 0, nom: '', prenom: '', email: '', telephone: '', groupe: '' };\n\n  constructor(\n    private route: ActivatedRoute,\n    private responsableService: ResponsableService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.responsableId = +this.route.snapshot.paramMap.get('id')!;\n\n    this.responsableService.getResponsableById(this.responsableId).subscribe(\n      (data: User) => {  // Explicitly typing 'data' as 'User'\n        this.responsable = data;\n      },\n      (error: HttpErrorResponse) => {  // Typing 'error' as 'HttpErrorResponse' to capture HTTP errors\n        console.error('Error fetching responsable:', error.message);\n      }\n    );\n  }\n\n  updateResponsable(): void {\n    this.responsableService.updateResponsable(this.responsableId, this.responsable).subscribe(\n      (response) => {\n        console.log('Responsable updated:', response);\n        this.router.navigate(['/ResDash']);\n      },\n      (error: HttpErrorResponse) => {  // Handling the error from the update request\n        console.error('Error updating responsable:', error.message);\n      }\n    );\n  }\n}\n", "<p>responsable-edit works!</p>\n"], "mappings": ";;;AAWA,OAAM,MAAOA,wBAAwB;EAInCC,YACUC,KAAqB,EACrBC,kBAAsC,EACtCC,MAAc;IAFd,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,WAAW,GAAS;MAAEC,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAE,CAAE;EAMrF;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG,CAAC,IAAI,CAACX,KAAK,CAACY,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAE;IAE7D,IAAI,CAACb,kBAAkB,CAACc,kBAAkB,CAAC,IAAI,CAACJ,aAAa,CAAC,CAACK,SAAS,CACrEC,IAAU,IAAI;MACb,IAAI,CAACd,WAAW,GAAGc,IAAI;IACzB,CAAC,EACAC,KAAwB,IAAI;MAC3BC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAACE,OAAO,CAAC;IAC7D,CAAC,CACF;EACH;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACpB,kBAAkB,CAACoB,iBAAiB,CAAC,IAAI,CAACV,aAAa,EAAE,IAAI,CAACR,WAAW,CAAC,CAACa,SAAS,CACtFM,QAAQ,IAAI;MACXH,OAAO,CAACI,GAAG,CAAC,sBAAsB,EAAED,QAAQ,CAAC;MAC7C,IAAI,CAACpB,MAAM,CAACsB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC,EACAN,KAAwB,IAAI;MAC3BC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAACE,OAAO,CAAC;IAC7D,CAAC,CACF;EACH;;;uBAjCWtB,wBAAwB,EAAA2B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAI,MAAA;IAAA;EAAA;;;YAAxBjC,wBAAwB;MAAAkC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXrCZ,EAAA,CAAAc,cAAA,QAAG;UAAAd,EAAA,CAAAe,MAAA,8BAAuB;UAAAf,EAAA,CAAAgB,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}