{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/groupe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction GroupsComponent_tr_82_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_tr_82_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const groupe_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.deleteGroupe(groupe_r2.idGroupe));\n    });\n    i0.ɵɵtext(1, \"Supprimer\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/edit-groupe\", a1];\n};\nfunction GroupsComponent_tr_82_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵtext(1, \"Modifier\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, groupe_r2.idGroupe));\n  }\n}\nfunction GroupsComponent_tr_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtemplate(6, GroupsComponent_tr_82_button_6_Template, 2, 0, \"button\", 53);\n    i0.ɵɵtemplate(7, GroupsComponent_tr_82_button_7_Template, 2, 3, \"button\", 54);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const groupe_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r2.idGroupe || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r2.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", groupe_r2.idGroupe);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", groupe_r2.idGroupe);\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n  }\\n  \\n  .feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);\\n  }\\n  \\n  .sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #333;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n  }\\n  \\n  .sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, .25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .border-top[_ngcontent-%COMP%] { border-top: 1px solid #e5e5e5; }\\n  .border-bottom[_ngcontent-%COMP%] { border-bottom: 1px solid #e5e5e5; }\\n  \\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  background-color: #ff4d4d; \\n\\n  color: white;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #ff1a1a;\\n}\\n\\n.modify-btn[_ngcontent-%COMP%] {\\n  background-color: #4d94ff; \\n\\n  color: white;\\n}\\n\\n.modify-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #0066cc;\\n}\\n\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class GroupsComponent {\n  constructor(groupeService, router) {\n    this.groupeService = groupeService;\n    this.router = router;\n    this.groupes = []; // List of groups\n    this.newGroupe = {\n      nomGroupe: ''\n    }; // New group model\n    this.editGroupe = null; // Group being edited\n  }\n\n  ngOnInit() {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n  // Load all groups\n  loadGroupes() {\n    this.groupeService.getAllGroupes().subscribe(data => {\n      this.groupes = data;\n    }, err => {\n      console.error('Error loading groups', err);\n    });\n  }\n  // Add a new group\n  addGroupe() {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(() => {\n        this.newGroupe = {\n          nomGroupe: ''\n        }; // Reset input\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error adding group', err);\n      });\n    }\n  }\n  // Set group to edit mode\n  setEditGroupe(groupe) {\n    this.editGroupe = {\n      ...groupe\n    }; // Clone object\n  }\n  // Update a group\n  updateGroupe() {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(() => {\n        this.editGroupe = null; // Reset edit mode\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error updating group', err);\n      });\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n  // Delete a group\n  deleteGroupe(id) {\n    if (id) {\n      this.groupeService.deleteGroupe(id).subscribe(() => {\n        this.loadGroupes(); // Refresh list after deletion\n      }, err => {\n        console.error('Error deleting group', err);\n      });\n    } else {\n      console.error('Invalid group ID');\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function GroupsComponent_Factory(t) {\n      return new (t || GroupsComponent)(i0.ɵɵdirectiveInject(i1.GroupeService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupsComponent,\n      selectors: [[\"app-groups\"]],\n      decls: 83,\n      vars: 2,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"width\", \"900\", \"height\", \"380\", 2, \"background-color\", \"white\", \"min-height\", \"380px\"], [\"myChart\", \"\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"form-control-dark\", \"w-100\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-light\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"dropdown-toggle\"], [\"data-feather\", \"calendar\"], [1, \"text-center\", \"mt-5\"], [1, \"row\", \"justify-content-center\"], [1, \"col-md-6\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"nomGroupe\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\"], [1, \"text-center\", \"mb-4\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-sm\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"btn btn-danger\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-warning\", 3, \"routerLink\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"btn\", \"btn-warning\", 3, \"routerLink\"]],\n      template: function GroupsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"meta\", 6)(8, \"meta\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"canvas\", 10, 11);\n          i0.ɵɵelementStart(13, \"title\");\n          i0.ɵɵtext(14, \"Gestion des Groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"link\", 12)(16, \"link\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"body\")(18, \"nav\", 14)(19, \"a\", 15);\n          i0.ɵɵtext(20, \"GTI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 16);\n          i0.ɵɵelementStart(22, \"ul\", 17)(23, \"li\", 18)(24, \"a\", 19);\n          i0.ɵɵlistener(\"click\", function GroupsComponent_Template_a_click_24_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(25, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21)(28, \"nav\", 22)(29, \"div\", 23)(30, \"ul\", 24)(31, \"li\", 25)(32, \"a\", 26);\n          i0.ɵɵelement(33, \"span\", 27);\n          i0.ɵɵtext(34, \" Dashboard \");\n          i0.ɵɵelementStart(35, \"span\", 28);\n          i0.ɵɵtext(36, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"li\", 25)(38, \"a\", 29);\n          i0.ɵɵelement(39, \"span\", 30);\n          i0.ɵɵtext(40, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\", 25)(42, \"a\", 31);\n          i0.ɵɵelement(43, \"span\", 32);\n          i0.ɵɵtext(44, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(45, \"main\", 33)(46, \"div\", 34)(47, \"h1\", 35);\n          i0.ɵɵtext(48, \"Gestion des groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 36)(50, \"div\", 37)(51, \"button\", 38);\n          i0.ɵɵtext(52, \"Import\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"button\", 38);\n          i0.ɵɵtext(54, \"Export\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"button\", 39);\n          i0.ɵɵelement(56, \"span\", 40);\n          i0.ɵɵtext(57, \" This week \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"h2\", 41);\n          i0.ɵɵtext(59, \"Ajouter un groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 42)(61, \"div\", 43)(62, \"form\", 44);\n          i0.ɵɵlistener(\"ngSubmit\", function GroupsComponent_Template_form_ngSubmit_62_listener() {\n            return ctx.addGroupe();\n          });\n          i0.ɵɵelementStart(63, \"div\", 45)(64, \"label\", 46);\n          i0.ɵɵtext(65, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"input\", 47);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_66_listener($event) {\n            return ctx.newGroupe.nomGroupe = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"button\", 48);\n          i0.ɵɵtext(68, \"Ajouter\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(69, \"h2\", 49);\n          i0.ɵɵtext(70, \"Liste des groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 50)(72, \"table\", 51)(73, \"thead\")(74, \"tr\")(75, \"th\");\n          i0.ɵɵtext(76, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"th\");\n          i0.ɵɵtext(78, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"th\");\n          i0.ɵɵtext(80, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(81, \"tbody\");\n          i0.ɵɵtemplate(82, GroupsComponent_tr_82_Template, 8, 4, \"tr\", 52);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(66);\n          i0.ɵɵproperty(\"ngModel\", ctx.newGroupe.nomGroupe);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groupes);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm, i2.RouterLink],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "GroupsComponent_tr_82_button_6_Template_button_click_0_listener", "ɵɵrestoreView", "_r7", "groupe_r2", "ɵɵnextContext", "$implicit", "ctx_r5", "ɵɵresetView", "deleteGroupe", "idGroupe", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ɵɵtemplate", "GroupsComponent_tr_82_button_6_Template", "GroupsComponent_tr_82_button_7_Template", "ɵɵadvance", "ɵɵtextInterpolate", "nomGroupe", "GroupsComponent", "constructor", "groupeService", "router", "groupes", "newGroupe", "editGroupe", "ngOnInit", "loadGroupes", "getAllGroupes", "subscribe", "data", "err", "console", "error", "addGroupe", "trim", "setEditGroupe", "groupe", "updateGroupe", "id", "logout", "localStorage", "removeItem", "log", "getItem", "navigate", "ɵɵdirectiveInject", "i1", "GroupeService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "GroupsComponent_Template", "rf", "ctx", "ɵɵelement", "GroupsComponent_Template_a_click_24_listener", "GroupsComponent_Template_form_ngSubmit_62_listener", "GroupsComponent_Template_input_ngModelChange_66_listener", "$event", "GroupsComponent_tr_82_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\groups\\groups.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\groups\\groups.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-groups',\n  templateUrl: './groups.component.html',\n  styleUrls: ['../../dashboard.css']})\nexport class GroupsComponent implements OnInit {\n  groupes: Groupe[] = []; // List of groups\n  newGroupe: Groupe = { nomGroupe: '' }; // New group model\n  editGroupe: Groupe | null = null; // Group being edited\n\n  constructor(private groupeService: GroupeService,\n    private router: Router) {}\n\n  ngOnInit(): void {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n\n  // Load all groups\n  loadGroupes(): void {\n    this.groupeService.getAllGroupes().subscribe(\n      (data) => {\n        this.groupes = data;\n      },\n      (err) => {\n        console.error('Error loading groups', err);\n      }\n    );\n  }\n\n  // Add a new group\n  addGroupe(): void {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(\n        () => {\n          this.newGroupe = { nomGroupe: '' }; // Reset input\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error adding group', err);\n        }\n      );\n    }\n  }\n\n  // Set group to edit mode\n  setEditGroupe(groupe: Groupe): void {\n    this.editGroupe = { ...groupe }; // Clone object\n  }\n\n  // Update a group\n  updateGroupe(): void {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(\n        () => {\n          this.editGroupe = null; // Reset edit mode\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error updating group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n\n  // Delete a group\n  deleteGroupe(id: number): void {\n    if (id) {\n      this.groupeService.deleteGroupe(id).subscribe(\n        () => {\n          this.loadGroupes(); // Refresh list after deletion\n        },\n        (err) => {\n          console.error('Error deleting group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group ID');\n    }\n  }\n\n  logout(): void {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <canvas #myChart width=\"900\" height=\"380\" style=\"background-color: white; min-height: 380px;\"></canvas>\n\n    <title>Gestion des Groupes</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" > \n\n    <!-- Custom styles for this template -->\n    <link href=\"../../dashboard.css\" rel=\"stylesheet\">\n  </head>\n\n  <body>\n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"adminDash\">GTI</a>\n      <input class=\"form-control form-control-dark w-100\" type=\"text\" placeholder=\"Search\" aria-label=\"Search\">\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block bg-light sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"users\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Gestion des groupes</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n                <button class=\"btn btn-sm btn-outline-secondary\">Import</button>\n                <button class=\"btn btn-sm btn-outline-secondary\">Export</button>\n              </div>\n              <button class=\"btn btn-sm btn-outline-secondary dropdown-toggle\">\n                <span data-feather=\"calendar\"></span>\n                This week\n              </button>\n            </div>\n          </div>\n          <h2 class=\"text-center mt-5\">Ajouter un groupe</h2>\n          <div class=\"row justify-content-center\">\n            <div class=\"col-md-6\">\n              <form (ngSubmit)=\"addGroupe()\">\n                <div class=\"form-group\">\n                  <label for=\"nomGroupe\">Nom du groupe</label>\n                  <input type=\"text\" id=\"nomGroupe\" [(ngModel)]=\"newGroupe.nomGroupe\" name=\"nomGroupe\" class=\"form-control\" required>\n                </div>\n                <button type=\"submit\" class=\"btn btn-primary w-100\">Ajouter</button>\n    \n\n\n              </form>\n            </div>\n            \n          </div>\n           \n\n          <h2 class=\"text-center mb-4\">Liste des groupes</h2>\n\n          <div class=\"table-responsive\">\n            <table class=\"table table-striped table-sm\">\n              <thead>\n                <tr>\n                  <th>ID</th>\n                  <th>Nom du groupe</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let groupe of groupes\">\n                  <td>{{ groupe.idGroupe || 'N/A' }}</td>\n                  <td>{{ groupe.nomGroupe }}</td>\n                  <td>\n                    <button class=\"btn btn-danger\" *ngIf=\"groupe.idGroupe\" (click)=\"deleteGroupe(groupe.idGroupe)\">Supprimer</button>\n                    <button class=\"btn btn-warning\" [routerLink]=\"['/edit-groupe', groupe.idGroupe]\" *ngIf=\"groupe.idGroupe\">Modifier</button>\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n\n\n        </main>\n      </div>\n    </div>\n\n    <!-- Bootstrap core JavaScript -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>feather.replace()</script>\n\n    <!-- Chart.js -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.min.js\"></script>\n  </body>\n</html>\n"], "mappings": ";;;;;;;;ICgHoBA,EAAA,CAAAC,cAAA,iBAA+F;IAAxCD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAN,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAU,WAAA,CAAAD,MAAA,CAAAE,YAAA,CAAAL,SAAA,CAAAM,QAAA,CAA6B;IAAA,EAAC;IAACZ,EAAA,CAAAa,MAAA,gBAAS;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;;;IACjHd,EAAA,CAAAC,cAAA,iBAAyG;IAAAD,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAA1Fd,EAAA,CAAAe,UAAA,eAAAf,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAX,SAAA,CAAAM,QAAA,EAAgD;;;;;IALpFZ,EAAA,CAAAC,cAAA,SAAmC;IAC7BD,EAAA,CAAAa,MAAA,GAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAsB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC/Bd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAkB,UAAA,IAAAC,uCAAA,qBAAiH;IACjHnB,EAAA,CAAAkB,UAAA,IAAAE,uCAAA,qBAA0H;IAC5HpB,EAAA,CAAAc,YAAA,EAAK;;;;IALDd,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAsB,iBAAA,CAAAhB,SAAA,CAAAM,QAAA,UAA8B;IAC9BZ,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAsB,iBAAA,CAAAhB,SAAA,CAAAiB,SAAA,CAAsB;IAEQvB,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAAe,UAAA,SAAAT,SAAA,CAAAM,QAAA,CAAqB;IAC6BZ,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAAe,UAAA,SAAAT,SAAA,CAAAM,QAAA,CAAqB;;;;ADxG3H,OAAM,MAAOY,eAAe;EAK1BC,YAAoBC,aAA4B,EACtCC,MAAc;IADJ,KAAAD,aAAa,GAAbA,aAAa;IACvB,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,OAAO,GAAa,EAAE,CAAC,CAAC;IACxB,KAAAC,SAAS,GAAW;MAAEN,SAAS,EAAE;IAAE,CAAE,CAAC,CAAC;IACvC,KAAAO,UAAU,GAAkB,IAAI,CAAC,CAAC;EAGP;;EAE3BC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;EACtB;EAEA;EACAA,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,aAAa,EAAE,CAACC,SAAS,CACzCC,IAAI,IAAI;MACP,IAAI,CAACP,OAAO,GAAGO,IAAI;IACrB,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;IAC5C,CAAC,CACF;EACH;EAEA;EACAG,SAASA,CAAA;IACP,IAAI,IAAI,CAACV,SAAS,CAACN,SAAS,CAACiB,IAAI,EAAE,EAAE;MACnC,IAAI,CAACd,aAAa,CAACa,SAAS,CAAC,IAAI,CAACV,SAAS,CAAC,CAACK,SAAS,CACpD,MAAK;QACH,IAAI,CAACL,SAAS,GAAG;UAAEN,SAAS,EAAE;QAAE,CAAE,CAAC,CAAC;QACpC,IAAI,CAACS,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAI,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;MAC1C,CAAC,CACF;;EAEL;EAEA;EACAK,aAAaA,CAACC,MAAc;IAC1B,IAAI,CAACZ,UAAU,GAAG;MAAE,GAAGY;IAAM,CAAE,CAAC,CAAC;EACnC;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACb,UAAU,IAAI,IAAI,CAACA,UAAU,CAAClB,QAAQ,EAAE;MAC/C,IAAI,CAACc,aAAa,CAACiB,YAAY,CAAC,IAAI,CAACb,UAAU,CAAClB,QAAQ,EAAE,IAAI,CAACkB,UAAU,CAAC,CAACI,SAAS,CAClF,MAAK;QACH,IAAI,CAACJ,UAAU,GAAG,IAAI,CAAC,CAAC;QACxB,IAAI,CAACE,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAI,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;;EAEtD;EAEA;EACA3B,YAAYA,CAACiC,EAAU;IACrB,IAAIA,EAAE,EAAE;MACN,IAAI,CAAClB,aAAa,CAACf,YAAY,CAACiC,EAAE,CAAC,CAACV,SAAS,CAC3C,MAAK;QACH,IAAI,CAACF,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAI,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,kBAAkB,CAAC;;EAErC;EAEAO,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCV,OAAO,CAACW,GAAG,CAACF,YAAY,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAnFW1B,eAAe,EAAAxB,EAAA,CAAAmD,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAArD,EAAA,CAAAmD,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAf/B,eAAe;MAAAgC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR5B9D,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAgE,SAAA,cAAsB;UAYtBhE,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAa,MAAA,2BAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAElCd,EAAA,CAAAgE,SAAA,gBAAmF;UAOrFhE,EAAA,CAAAc,YAAA,EAAO;UAEPd,EAAA,CAAAC,cAAA,YAAM;UAE8DD,EAAA,CAAAa,MAAA,WAAG;UAAAb,EAAA,CAAAc,YAAA,EAAI;UACvEd,EAAA,CAAAgE,SAAA,iBAAyG;UACzGhE,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAE,UAAA,mBAAA+D,6CAAA;YAAA,OAASF,GAAA,CAAAlB,MAAA,EAAQ;UAAA,EAAC;UAAC7C,EAAA,CAAAa,MAAA,gBAAQ;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAKzDd,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAAgE,SAAA,gBAAiC;UACjChE,EAAA,CAAAa,MAAA,mBAAU;UAAAb,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAGpDd,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAgE,SAAA,gBAAiC;UACjChE,EAAA,CAAAa,MAAA,kCACF;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAENd,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAgE,SAAA,gBAAkC;UAClChE,EAAA,CAAAa,MAAA,6BACF;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAMZd,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAa,MAAA,2BAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,eAAsC;UAEeD,EAAA,CAAAa,MAAA,cAAM;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAChEd,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAa,MAAA,cAAM;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAElEd,EAAA,CAAAC,cAAA,kBAAiE;UAC/DD,EAAA,CAAAgE,SAAA,gBAAqC;UACrChE,EAAA,CAAAa,MAAA,mBACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAGbd,EAAA,CAAAC,cAAA,cAA6B;UAAAD,EAAA,CAAAa,MAAA,yBAAiB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACnDd,EAAA,CAAAC,cAAA,eAAwC;UAE9BD,EAAA,CAAAE,UAAA,sBAAAgE,mDAAA;YAAA,OAAYH,GAAA,CAAAxB,SAAA,EAAW;UAAA,EAAC;UAC5BvC,EAAA,CAAAC,cAAA,eAAwB;UACCD,EAAA,CAAAa,MAAA,qBAAa;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAC5Cd,EAAA,CAAAC,cAAA,iBAAmH;UAAjFD,EAAA,CAAAE,UAAA,2BAAAiE,yDAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAlC,SAAA,CAAAN,SAAA,GAAA6C,MAAA;UAAA,EAAiC;UAAnEpE,EAAA,CAAAc,YAAA,EAAmH;UAErHd,EAAA,CAAAC,cAAA,kBAAoD;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAU1Ed,EAAA,CAAAC,cAAA,cAA6B;UAAAD,EAAA,CAAAa,MAAA,yBAAiB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAEnDd,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAa,MAAA,UAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACXd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,qBAAa;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACtBd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAGpBd,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAkB,UAAA,KAAAmD,8BAAA,iBAOK;UACPrE,EAAA,CAAAc,YAAA,EAAQ;;;UAhC8Bd,EAAA,CAAAqB,SAAA,IAAiC;UAAjCrB,EAAA,CAAAe,UAAA,YAAAgD,GAAA,CAAAlC,SAAA,CAAAN,SAAA,CAAiC;UAwB9CvB,EAAA,CAAAqB,SAAA,IAAU;UAAVrB,EAAA,CAAAe,UAAA,YAAAgD,GAAA,CAAAnC,OAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}