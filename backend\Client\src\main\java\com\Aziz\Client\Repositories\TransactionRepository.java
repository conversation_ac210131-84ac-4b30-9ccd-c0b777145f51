package com.Aziz.Client.Repositories;

import com.Aziz.Client.Entity.Transaction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TransactionRepository extends JpaRepository<Transaction, Long> {

    // Rechercher toutes les transactions par type (ex: <PERSON><PERSON>t, Vente)
    List<Transaction> findByTypeIgnoreCase(String type);

    // Rechercher les transactions par intervalle de montant
    List<Transaction> findByMontatntBetween(Double min, Double max);

    List<Transaction> findByPortefeuille_IdPortefeuille(Long idPortefeuille);

    List<Transaction> findByPortefeuille_Actionnaire_IdActionnaire(Long idActionnaire);

    List<Transaction> findByPortefeuille_Actionnaire_IdActionnaireAndTypeIgnoreCase(Long idActionnaire, String type);



    List<Transaction> findAllByUserId(Long userId);



}
