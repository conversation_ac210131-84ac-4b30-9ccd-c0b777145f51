package com.Aziz.Client.Controllers;

import com.Aziz.Client.Entity.Action;
import com.Aziz.Client.Entity.Actionnaire;
import com.Aziz.Client.Entity.Portefeuille;
import com.Aziz.Client.Entity.Transaction;
import com.Aziz.Client.Repositories.PortefeuilleRepository;
import com.Aziz.Client.Repositories.ActionRepository;
import com.Aziz.Client.Services.PortefeuilleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;


@RestController
@RequestMapping("GRA/Client/Portefeuille")
@RequiredArgsConstructor
public class PortefeuilleController {

    @Autowired
    private PortefeuilleRepository portefeuilleRepository;

    @Autowired
    private final PortefeuilleService portefeuilleService;

    @Autowired
    private ActionRepository ActionRepository;

    @CrossOrigin(origins = "http://localhost:4200")
    @PostMapping("/ExelInjecter_Potefeuille")
    public ResponseEntity<String> uploadFile() {

        Portefeuille portefeuille = new Portefeuille();
        portefeuille.setDateCreation(LocalDateTime.now());

// Créer et sauvegarder l'objet Action si nécessaire
        Action action = new Action();
        Actionnaire actionnaire=new Actionnaire();
        actionnaire.setIdActionnaire(1L);
        action.setIsinAction("TN0001234567");
        ActionRepository.save(action); // Sauvegarde d'abord l'Action

// Assigner l'Action au Portefeuille
        portefeuille.setAction(action);
        portefeuille.setActionnaire(actionnaire);

// Sauvegarder le Portefeuille
        portefeuilleRepository.save(portefeuille);

        return ResponseEntity.ok("File uploaded and data saved successfully.");
    }




    @GetMapping("/{idPortfeuille}")
    public ResponseEntity<Portefeuille> getPortefeuilleById(@PathVariable Long idPortfeuille) {
        return portefeuilleService.findById(idPortfeuille)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/actionnaire/{idActionnaire}")
    public ResponseEntity<List<Portefeuille>> getPortefeuillesByActionnaire(@PathVariable Long idActionnaire) {
        return ResponseEntity.ok(portefeuilleService.findByIdActionnaire(idActionnaire));
    }

    @GetMapping("/search")
    public ResponseEntity<Portefeuille> getByISINAndActionnaire(@RequestParam String isinAction, @RequestParam Long idActionnaire) {
        return portefeuilleService.findByISINAndIdActionnaire(isinAction, idActionnaire)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/create")
    public ResponseEntity<String> createPortefeuille(@RequestBody Portefeuille portefeuille) {

        try {

            portefeuilleService.savePortefeuille(portefeuille);
            return ResponseEntity.ok("Portefeuille créé avec succès");
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/All_Portefeuilles")
    public ResponseEntity<List<Portefeuille>> getAllPortefeuilles() {
        return ResponseEntity.ok(portefeuilleService.findAllPortefeuilles());
    }

    @DeleteMapping("/delete/{idPortefeuille}")
    public ResponseEntity<String> deletePortefeuille(@PathVariable Long idPortefeuille) {
        try {
            portefeuilleService.deletePortefeuille(idPortefeuille);
            return ResponseEntity.ok("Portefeuille supprimée");
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PostMapping("/deposit")
    public ResponseEntity<?> deposit(
            @RequestParam String isinAction,
            @RequestParam Long idActionnaire,
            @RequestParam Integer quantite,
            @RequestBody Actionnaire actionnaire,
            @RequestBody Action action,
            @RequestBody Transaction transaction) {

        try {
            Portefeuille result = portefeuilleService.deposit(isinAction, idActionnaire, quantite, actionnaire, action, transaction);
            return ResponseEntity.ok(result);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PostMapping("/withdraw")
    public ResponseEntity<?> withdraw(
            @RequestParam String isinAction,
            @RequestParam Long idActionnaire,
            @RequestParam Integer quantite,
            @RequestBody Actionnaire actionnaire,
            @RequestBody Action action) {

        try {
            Portefeuille result = portefeuilleService.withdraw(isinAction, idActionnaire, quantite, actionnaire, action);
            return ResponseEntity.ok(result);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }









}
