{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"../services/ressource.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction AddResponsableComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" Nom is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" Pr\\u00E9nom is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" A valid email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_58_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_58_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone must be a number and up to 8 digits.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtemplate(1, AddResponsableComponent_div_58_div_1_Template, 2, 0, \"div\", 65);\n    i0.ɵɵtemplate(2, AddResponsableComponent_div_58_div_2_Template, 2, 0, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r7 = i0.ɵɵreference(57);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r7.errors == null ? null : _r7.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r7.errors == null ? null : _r7.errors[\"pattern\"]);\n  }\n}\nfunction AddResponsableComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" Mot de passe is required (minimum 8 characters). \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" Role is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_option_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r18.idGroupe);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", group_r18.nomGroupe, \" \");\n  }\n}\nfunction AddResponsableComponent_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" Group is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class AddResponsableComponent {\n  constructor(responsableService, ressourceService,\n  // Inject Ressource service\n  router) {\n    this.responsableService = responsableService;\n    this.ressourceService = ressourceService;\n    this.router = router;\n    this.newResponsable = {\n      id: 0,\n      firstName: '',\n      lastName: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupeId: 0,\n      ressourceId: 0,\n      canCreate: false,\n      canRead: false,\n      canUpdate: false,\n      canDelete: false // Use ressourceId to link the selected resource\n    };\n\n    this.ressources = []; // Array to hold available resources\n    this.groups = []; // Array to hold available groups\n  }\n\n  ngOnInit() {\n    // Fetch the list of groups for the dropdown\n    this.responsableService.getGroups().subscribe(data => {\n      this.groups = data; // Store the groups to bind to the dropdown\n    });\n    // Fetch the list of ressources for the dropdown\n    this.ressourceService.getAllRessources().subscribe(data => {\n      this.ressources = data; // Store the ressources to bind to the dropdown\n    });\n  }\n  // Method to add the new responsable\n  addResponsable() {\n    if (!this.newResponsable.groupeId) {\n      alert('Please select a valid group');\n      return;\n    }\n    if (!this.newResponsable.ressourceId) {\n      alert('Please select a valid ressource');\n      return;\n    }\n    if (!this.newResponsable.password) {\n      alert('Please enter a password');\n      return;\n    }\n    // Log the object to verify\n    console.log('Adding Responsable:', this.newResponsable);\n    // Send the request to the backend\n    this.responsableService.addResponsable(this.newResponsable).subscribe(response => {\n      console.log('Responsable added successfully:', response);\n      // Reset the form after successful creation\n      this.newResponsable = {\n        firstName: '',\n        lastName: '',\n        email: '',\n        telephone: '',\n        role: 'RESPONSABLE',\n        password: '',\n        groupeId: 0,\n        ressourceId: 0 // Reset selected ressource\n      };\n\n      this.router.navigate(['/users']);\n    }, error => {\n      console.error('Error adding responsable:', error);\n    });\n  }\n  cancelEdit() {\n    // Reset form data (optional, if you want to clear the form fields on cancel)\n    this.newResponsable = {\n      id: 0,\n      firstName: '',\n      lastName: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupeId: 0,\n      ressourceId: 0\n    };\n    // Navigate to the users list or a different page (replace '/users' with your desired route)\n    this.router.navigateByUrl('/users');\n  }\n  static {\n    this.ɵfac = function AddResponsableComponent_Factory(t) {\n      return new (t || AddResponsableComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i2.RessourceService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddResponsableComponent,\n      selectors: [[\"app-add-responsable\"]],\n      decls: 123,\n      vars: 20,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"UTF-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1.0\"], [\"http-equiv\", \"X-UA-Compatible\", \"content\", \"ie=edge\"], [\"href\", \"https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css\", \"rel\", \"stylesheet\"], [\"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css\", \"rel\", \"stylesheet\"], [1, \"background-image\"], [1, \"container\", \"mt-5\"], [1, \"form-container\", \"mx-auto\", 2, \"max-width\", \"900px\"], [1, \"text-center\", \"mb-4\"], [1, \"form-group\", 3, \"ngSubmit\"], [\"responsableForm\", \"ngForm\"], [1, \"form-row\"], [1, \"col-md-6\"], [\"for\", \"nom\"], [1, \"input-group\"], [1, \"input-group-prepend\"], [1, \"input-group-text\"], [1, \"fa\", \"fa-user\"], [\"type\", \"text\", \"id\", \"nom\", \"name\", \"nom\", \"required\", \"\", \"placeholder\", \"Entrez le nom\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"nom\", \"ngModel\"], [\"class\", \"text-danger small\", 4, \"ngIf\"], [\"for\", \"prenom\"], [\"type\", \"text\", \"id\", \"prenom\", \"name\", \"prenom\", \"required\", \"\", \"placeholder\", \"Entrez le pr\\u00E9nom\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"prenom\", \"ngModel\"], [\"for\", \"email\"], [1, \"fa\", \"fa-envelope\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"Entrez l'email\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"email\", \"ngModel\"], [\"for\", \"telephone\"], [1, \"fa\", \"fa-phone\"], [\"type\", \"tel\", \"id\", \"telephone\", \"name\", \"telephone\", \"required\", \"\", \"pattern\", \"^[0-9]{1,8}$\", \"placeholder\", \"Entrez le t\\u00E9l\\u00E9phone\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"telephone\", \"ngModel\"], [\"for\", \"password\"], [1, \"fa\", \"fa-lock\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"minlength\", \"8\", \"placeholder\", \"Entrez un mot de passe\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"password\", \"ngModel\"], [\"for\", \"role\"], [1, \"fa\", \"fa-address-card\"], [\"id\", \"role\", \"name\", \"role\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"role\", \"ngModel\"], [\"value\", \"RESPONSABLE\"], [\"value\", \"ADMIN\"], [\"for\", \"groupe\"], [1, \"fa\", \"fa-users\"], [\"id\", \"groupe\", \"name\", \"groupeId\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"groupe\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-row\", \"mt-3\"], [1, \"col-md-12\"], [1, \"form-check\", \"form-check-inline\", \"ml-3\"], [\"type\", \"checkbox\", \"id\", \"canCreate\", \"name\", \"canCreate\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canCreate\", 1, \"form-check-label\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"checkbox\", \"id\", \"canRead\", \"name\", \"canRead\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canRead\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"canUpdate\", \"name\", \"canUpdate\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canUpdate\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"canDelete\", \"name\", \"canDelete\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canDelete\", 1, \"form-check-label\"], [1, \"form-row\", \"mt-4\"], [1, \"col-md-12\", \"text-center\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"ml-3\", 3, \"click\"], [1, \"text-danger\", \"small\"], [4, \"ngIf\"], [3, \"value\"]],\n      template: function AddResponsableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3);\n          i0.ɵɵelementStart(5, \"title\");\n          i0.ɵɵtext(6, \"Ajouter un Nouveau Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"link\", 4)(8, \"link\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"body\")(10, \"div\", 6)(11, \"div\", 7)(12, \"div\", 8)(13, \"h2\", 9);\n          i0.ɵɵtext(14, \"Ajouter un Nouveau Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"form\", 10, 11);\n          i0.ɵɵlistener(\"ngSubmit\", function AddResponsableComponent_Template_form_ngSubmit_15_listener() {\n            return ctx.addResponsable();\n          });\n          i0.ɵɵelementStart(17, \"div\", 12)(18, \"div\", 13)(19, \"label\", 14);\n          i0.ɵɵtext(20, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16)(23, \"span\", 17);\n          i0.ɵɵelement(24, \"i\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"input\", 19, 20);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_25_listener($event) {\n            return ctx.newResponsable.firstName = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(27, AddResponsableComponent_div_27_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 13)(29, \"label\", 22);\n          i0.ɵɵtext(30, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 15)(32, \"div\", 16)(33, \"span\", 17);\n          i0.ɵɵelement(34, \"i\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"input\", 23, 24);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_35_listener($event) {\n            return ctx.newResponsable.lastName = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(37, AddResponsableComponent_div_37_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 12)(39, \"div\", 13)(40, \"label\", 25);\n          i0.ɵɵtext(41, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 15)(43, \"div\", 16)(44, \"span\", 17);\n          i0.ɵɵelement(45, \"i\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"input\", 27, 28);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_46_listener($event) {\n            return ctx.newResponsable.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(48, AddResponsableComponent_div_48_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 13)(50, \"label\", 29);\n          i0.ɵɵtext(51, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\", 15)(53, \"div\", 16)(54, \"span\", 17);\n          i0.ɵɵelement(55, \"i\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"input\", 31, 32);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_56_listener($event) {\n            return ctx.newResponsable.telephone = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(58, AddResponsableComponent_div_58_Template, 3, 2, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"div\", 12)(60, \"div\", 13)(61, \"label\", 33);\n          i0.ɵɵtext(62, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 15)(64, \"div\", 16)(65, \"span\", 17);\n          i0.ɵɵelement(66, \"i\", 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"input\", 35, 36);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_67_listener($event) {\n            return ctx.newResponsable.password = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(69, AddResponsableComponent_div_69_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"div\", 12)(71, \"div\", 13)(72, \"label\", 37);\n          i0.ɵɵtext(73, \"Role\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 15)(75, \"div\", 16)(76, \"span\", 17);\n          i0.ɵɵelement(77, \"i\", 38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(78, \"select\", 39, 40);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_select_ngModelChange_78_listener($event) {\n            return ctx.newResponsable.role = $event;\n          });\n          i0.ɵɵelementStart(80, \"option\", 41);\n          i0.ɵɵtext(81, \"RESPONSABLE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"option\", 42);\n          i0.ɵɵtext(83, \"ADMIN\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(84, AddResponsableComponent_div_84_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"div\", 12)(86, \"div\", 13)(87, \"label\", 43);\n          i0.ɵɵtext(88, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"div\", 15)(90, \"div\", 16)(91, \"span\", 17);\n          i0.ɵɵelement(92, \"i\", 44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(93, \"select\", 45, 46);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_select_ngModelChange_93_listener($event) {\n            return ctx.newResponsable.groupeId = $event;\n          });\n          i0.ɵɵtemplate(95, AddResponsableComponent_option_95_Template, 2, 2, \"option\", 47);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(96, AddResponsableComponent_div_96_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(97, \"div\", 48)(98, \"div\", 49)(99, \"label\");\n          i0.ɵɵtext(100, \"Droits\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"div\", 50)(102, \"input\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_102_listener($event) {\n            return ctx.newResponsable.canCreate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"label\", 52);\n          i0.ɵɵtext(104, \"Cr\\u00E9er\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(105, \"div\", 53)(106, \"input\", 54);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_106_listener($event) {\n            return ctx.newResponsable.canRead = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"label\", 55);\n          i0.ɵɵtext(108, \"Lire\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(109, \"div\", 53)(110, \"input\", 56);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_110_listener($event) {\n            return ctx.newResponsable.canUpdate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"label\", 57);\n          i0.ɵɵtext(112, \"Mettre \\u00E0 jour\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"div\", 53)(114, \"input\", 58);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_114_listener($event) {\n            return ctx.newResponsable.canDelete = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"label\", 59);\n          i0.ɵɵtext(116, \"Supprimer\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(117, \"div\", 60)(118, \"div\", 61)(119, \"button\", 62);\n          i0.ɵɵtext(120, \" Ajouter \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(121, \"button\", 63);\n          i0.ɵɵlistener(\"click\", function AddResponsableComponent_Template_button_click_121_listener() {\n            return ctx.cancelEdit();\n          });\n          i0.ɵɵtext(122, \"Annuler\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(16);\n          const _r1 = i0.ɵɵreference(26);\n          const _r3 = i0.ɵɵreference(36);\n          const _r5 = i0.ɵɵreference(47);\n          const _r7 = i0.ɵɵreference(57);\n          const _r9 = i0.ɵɵreference(68);\n          const _r11 = i0.ɵɵreference(79);\n          const _r13 = i0.ɵɵreference(94);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.firstName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r1.invalid && _r1.touched);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.lastName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r3.invalid && _r3.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r5.invalid && _r5.touched);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.telephone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r7.invalid && _r7.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r9.invalid && _r9.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.role);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", _r11.invalid && _r11.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.groupeId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groups);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r13.invalid && _r13.touched);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.canCreate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.canRead);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.canUpdate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.canDelete);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", _r0.invalid);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.ɵNgNoValidate, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.CheckboxControlValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.MinLengthValidator, i5.PatternValidator, i5.EmailValidator, i5.NgModel, i5.NgForm],\n      styles: [\"\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n  background: linear-gradient(135deg, #131551, #2575fc); \\n\\n  color: #333; \\n\\n  height: 100vh;\\n  margin: 0;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n\\n\\n.form-container[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95); \\n\\n  padding: 40px;\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  max-width: 900px;\\n  width: 100%;\\n}\\n\\n\\n\\nh2[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 600;\\n  text-align: center;\\n  color: #333;\\n  margin-bottom: 30px;\\n}\\n\\n\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.input-group[_ngcontent-%COMP%] {\\n  border-radius: 10px; \\n\\n}\\n\\n.input-group-text[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  border-radius: 10px 0 0 10px; \\n\\n}\\n\\n.input-group-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  border: 1px solid #ddd;\\n  padding: 10px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff; \\n\\n  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);\\n}\\n\\n\\n\\nselect.form-control[_ngcontent-%COMP%] {\\n  background-color: #f1f1f1;\\n  border: 1px solid #ddd;\\n  padding: 10px;\\n  font-size: 1rem;\\n  border-radius: 10px;\\n  transition: all 0.3s ease; \\n\\n  appearance: none;\\n}\\n\\nselect.form-control[_ngcontent-%COMP%]:focus {\\n  background-color: #ffffff;\\n  border-color: #007bff;\\n  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);\\n}\\n\\n\\n\\nselect.form-control[_ngcontent-%COMP%]::-ms-expand {\\n  display: none;\\n}\\n\\n.input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 10px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n.form-group[_ngcontent-%COMP%]   select.form-control[_ngcontent-%COMP%] {\\n  padding-right: 35px; \\n\\n}\\n\\n\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #e74c3c;\\n}\\n\\n\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border: none;\\n  padding: 12px 30px;\\n  font-size: 1rem;\\n  border-radius: 25px;\\n  transition: background-color 0.3s;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border: none;\\n  padding: 12px 30px;\\n  font-size: 1rem;\\n  border-radius: 25px;\\n  transition: background-color 0.3s;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n}\\n\\n\\n\\n.form-row[_ngcontent-%COMP%]   .col-md-12[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.form-row[_ngcontent-%COMP%]   .col-md-12[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin: 0 10px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .form-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n\\n  h2[_ngcontent-%COMP%] {\\n    font-size: 1.6rem;\\n  }\\n\\n  .form-control[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n  }\\n\\n  .btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%] {\\n    padding: 10px 25px;\\n    font-size: 0.95rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "AddResponsableComponent_div_58_div_1_Template", "AddResponsableComponent_div_58_div_2_Template", "ɵɵadvance", "ɵɵproperty", "_r7", "errors", "group_r18", "idGroupe", "ɵɵtextInterpolate1", "nomGroupe", "AddResponsableComponent", "constructor", "responsableService", "ressourceService", "router", "newResponsable", "id", "firstName", "lastName", "email", "telephone", "role", "password", "groupeId", "ressourceId", "canCreate", "canRead", "canUpdate", "canDelete", "ressources", "groups", "ngOnInit", "getGroups", "subscribe", "data", "getAllRessources", "addResponsable", "alert", "console", "log", "response", "navigate", "error", "cancelEdit", "navigateByUrl", "ɵɵdirectiveInject", "i1", "ResponsableService", "i2", "RessourceService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "AddResponsableComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "AddResponsableComponent_Template_form_ngSubmit_15_listener", "AddResponsableComponent_Template_input_ngModelChange_25_listener", "$event", "AddResponsableComponent_div_27_Template", "AddResponsableComponent_Template_input_ngModelChange_35_listener", "AddResponsableComponent_div_37_Template", "AddResponsableComponent_Template_input_ngModelChange_46_listener", "AddResponsableComponent_div_48_Template", "AddResponsableComponent_Template_input_ngModelChange_56_listener", "AddResponsableComponent_div_58_Template", "AddResponsableComponent_Template_input_ngModelChange_67_listener", "AddResponsableComponent_div_69_Template", "AddResponsableComponent_Template_select_ngModelChange_78_listener", "AddResponsableComponent_div_84_Template", "AddResponsableComponent_Template_select_ngModelChange_93_listener", "AddResponsableComponent_option_95_Template", "AddResponsableComponent_div_96_Template", "AddResponsableComponent_Template_input_ngModelChange_102_listener", "AddResponsableComponent_Template_input_ngModelChange_106_listener", "AddResponsableComponent_Template_input_ngModelChange_110_listener", "AddResponsableComponent_Template_input_ngModelChange_114_listener", "AddResponsableComponent_Template_button_click_121_listener", "_r1", "invalid", "touched", "_r3", "_r5", "_r9", "_r11", "_r13", "_r0"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { Responsable } from '../model/respons.model';  // Import Responsable model\nimport { Groupe } from '../model/groupe.model';\nimport { Router } from '@angular/router';  // Import Router\nimport { Ressource } from '../model/ressource.model';     // Import the Ressource model\nimport { RessourceService } from '../services/ressource.service';  // Import Ressource service\n\n@Component({\n  selector: 'app-add-responsable',\n  templateUrl: './add-responsable.component.html',\n  styleUrls: ['./add-responsable.component.css']\n})\nexport class AddResponsableComponent implements OnInit {\n  newResponsable: Responsable = {  \n    id: 0, \n    firstName: '', \n    lastName: '', \n    email: '', \n    telephone: '', \n    role: 'RESPONSABLE',  \n    password: '',\n    groupeId: 0,\n    ressourceId: 0, \n    canCreate: false,\n    canRead: false,\n    canUpdate: false,\n    canDelete: false, // Use ressourceId to link the selected resource\n  };\n\n  ressources: Ressource[] = [];  // Array to hold available resources\n  groups: Groupe[] = [];  // Array to hold available groups\n  console: any;\n\n  constructor(\n    private responsableService: ResponsableService,\n    private ressourceService: RessourceService,  // Inject Ressource service\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Fetch the list of groups for the dropdown\n    this.responsableService.getGroups().subscribe((data) => {\n      this.groups = data;  // Store the groups to bind to the dropdown\n    });\n\n    // Fetch the list of ressources for the dropdown\n    this.ressourceService.getAllRessources().subscribe((data) => {\n      this.ressources = data;  // Store the ressources to bind to the dropdown\n    });\n  }\n\n  // Method to add the new responsable\n  addResponsable(): void {\n    if (!this.newResponsable.groupeId) {\n      alert('Please select a valid group');\n      return;\n    }\n\n    if (!this.newResponsable.ressourceId) {\n      alert('Please select a valid ressource');\n      return;\n    }\n\n    if (!this.newResponsable.password) {\n      alert('Please enter a password');\n      return;\n    }\n\n    // Log the object to verify\n    console.log('Adding Responsable:', this.newResponsable);\n\n    // Send the request to the backend\n    this.responsableService.addResponsable(this.newResponsable).subscribe(\n      (response) => {\n        console.log('Responsable added successfully:', response);\n\n        // Reset the form after successful creation\n        this.newResponsable = {\n          firstName: '',\n          lastName: '',\n          email: '',\n          telephone: '',\n          role: 'RESPONSABLE',\n          password: '',\n          groupeId: 0,  // Reset selected group\n          ressourceId: 0,  // Reset selected ressource\n        };\n        this.router.navigate(['/users']);\n      },\n      (error) => {\n        console.error('Error adding responsable:', error);\n      }\n    );\n  }\n\n  cancelEdit() {\n    // Reset form data (optional, if you want to clear the form fields on cancel)\n    this.newResponsable = {\n      id: 0,\n      firstName: '',\n      lastName: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupeId: 0,\n      ressourceId: 0,\n    };\n\n    // Navigate to the users list or a different page (replace '/users' with your desired route)\n    this.router.navigateByUrl('/users');\n  }\n}\n", "<!DOCTYPE html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\" />\n    <title>Ajouter un Nouveau Utilisateur</title>\n    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\" />\n    <link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css\" rel=\"stylesheet\" />\n  </head>\n\n  <body>\n    <div class=\"background-image\">\n      <div class=\"container mt-5\">\n        <div class=\"form-container mx-auto\" style=\"max-width: 900px;\">\n          <h2 class=\"text-center mb-4\">Ajouter un Nouveau Utilisateur</h2>\n          <form (ngSubmit)=\"addResponsable()\" #responsableForm=\"ngForm\" class=\"form-group\">\n            <!-- Nom Field -->\n            <div class=\"form-row\">\n              <div class=\"col-md-6\">\n                <label for=\"nom\">Nom</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-user\"></i></span>\n                  </div>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"nom\"\n                    [(ngModel)]=\"newResponsable.firstName\"\n                    name=\"nom\"\n                    required\n                    #nom=\"ngModel\"\n                    placeholder=\"Entrez le nom\"\n                  />\n                </div>\n                <div *ngIf=\"nom.invalid && nom.touched\" class=\"text-danger small\">\n                  Nom is required.\n                </div>\n              </div>\n\n              <!-- Prénom Field -->\n              <div class=\"col-md-6\">\n                <label for=\"prenom\">Prénom</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-user\"></i></span>\n                  </div>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"prenom\"\n                    [(ngModel)]=\"newResponsable.lastName\"\n                    name=\"prenom\"\n                    required\n                    #prenom=\"ngModel\"\n                    placeholder=\"Entrez le prénom\"\n                  />\n                </div>\n                <div *ngIf=\"prenom.invalid && prenom.touched\" class=\"text-danger small\">\n                  Prénom is required.\n                </div>\n              </div>\n            </div>\n\n            <!-- Email Field -->\n            <div class=\"form-row\">\n              <div class=\"col-md-6\">\n                <label for=\"email\">Email</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-envelope\"></i></span>\n                  </div>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"email\"\n                    [(ngModel)]=\"newResponsable.email\"\n                    name=\"email\"\n                    required\n                    email\n                    #email=\"ngModel\"\n                    placeholder=\"Entrez l'email\"\n                  />\n                </div>\n                <div *ngIf=\"email.invalid && email.touched\" class=\"text-danger small\">\n                  A valid email is required.\n                </div>\n              </div>\n\n              <!-- Téléphone Field -->\n              <div class=\"col-md-6\">\n                <label for=\"telephone\">Téléphone</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-phone\"></i></span>\n                  </div>\n                  <input\n                    type=\"tel\"\n                    class=\"form-control\"\n                    id=\"telephone\"\n                    [(ngModel)]=\"newResponsable.telephone\"\n                    name=\"telephone\"\n                    required\n                    #telephone=\"ngModel\"\n                    pattern=\"^[0-9]{1,8}$\"\n                    placeholder=\"Entrez le téléphone\"\n                  />\n                </div>\n                <div *ngIf=\"telephone.invalid && telephone.touched\" class=\"text-danger small\">\n                  <div *ngIf=\"telephone.errors?.['required']\">Téléphone is required.</div>\n                  <div *ngIf=\"telephone.errors?.['pattern']\">Téléphone must be a number and up to 8 digits.</div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Password Field -->\n            <div class=\"form-row\">\n              <div class=\"col-md-6\">\n                <label for=\"password\">Mot de passe</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-lock\"></i></span>\n                  </div>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"password\"\n                    [(ngModel)]=\"newResponsable.password\"\n                    name=\"password\"\n                    required\n                    minlength=\"8\"\n                    #password=\"ngModel\"\n                    placeholder=\"Entrez un mot de passe\"\n                  />\n                </div>\n                <div *ngIf=\"password.invalid && password.touched\" class=\"text-danger small\">\n                  Mot de passe is required (minimum 8 characters).\n                </div>\n              </div>\n            </div>\n\n            <!-- Role Dropdown -->\n            <div class=\"form-row\">\n              <div class=\"col-md-6\">\n                <label for=\"role\">Role</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-address-card\"></i></span>\n                  </div>\n                  <select\n                    id=\"role\"\n                    class=\"form-control\"\n                    [(ngModel)]=\"newResponsable.role\"\n                    name=\"role\"\n                    required\n                    #role=\"ngModel\"\n                  >\n                    <option value=\"RESPONSABLE\">RESPONSABLE</option>\n                    <option value=\"ADMIN\">ADMIN</option>\n                  </select>\n                </div>\n                <div *ngIf=\"role.invalid && role.touched\" class=\"text-danger small\">\n                  Role is required.\n                </div>\n              </div>\n            </div>\n\n            <!-- Group Dropdown -->\n            <div class=\"form-row\">\n              <div class=\"col-md-6\">\n                <label for=\"groupe\">Groupe</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-users\"></i></span>\n                  </div>\n                  <select\n                    id=\"groupe\"\n                    class=\"form-control\"\n                    [(ngModel)]=\"newResponsable.groupeId\"\n                    name=\"groupeId\"\n                    required\n                    #groupe=\"ngModel\"\n                  >\n                    <option *ngFor=\"let group of groups\" [value]=\"group.idGroupe\">\n                      {{ group.nomGroupe }}\n                    </option>\n                  </select>\n                </div>\n                <div *ngIf=\"groupe.invalid && groupe.touched\" class=\"text-danger small\">\n                  Group is required.\n                </div>\n              </div>\n            </div>\n\n\n            <!-- Droits Section -->\n<div class=\"form-row mt-3\">\n  <div class=\"col-md-12\">\n    <label>Droits</label>\n    <div class=\"form-check form-check-inline ml-3\">\n      <input\n        class=\"form-check-input\"\n        type=\"checkbox\"\n        id=\"canCreate\"\n        [(ngModel)]=\"newResponsable.canCreate\"\n        name=\"canCreate\"\n      />\n      <label class=\"form-check-label\" for=\"canCreate\">Créer</label>\n    </div>\n\n    <div class=\"form-check form-check-inline\">\n      <input\n        class=\"form-check-input\"\n        type=\"checkbox\"\n        id=\"canRead\"\n        [(ngModel)]=\"newResponsable.canRead\"\n        name=\"canRead\"\n      />\n      <label class=\"form-check-label\" for=\"canRead\">Lire</label>\n    </div>\n\n    <div class=\"form-check form-check-inline\">\n      <input\n        class=\"form-check-input\"\n        type=\"checkbox\"\n        id=\"canUpdate\"\n        [(ngModel)]=\"newResponsable.canUpdate\"\n        name=\"canUpdate\"\n      />\n      <label class=\"form-check-label\" for=\"canUpdate\">Mettre à jour</label>\n    </div>\n\n    <div class=\"form-check form-check-inline\">\n      <input\n        class=\"form-check-input\"\n        type=\"checkbox\"\n        id=\"canDelete\"\n        [(ngModel)]=\"newResponsable.canDelete\"\n        name=\"canDelete\"\n      />\n      <label class=\"form-check-label\" for=\"canDelete\">Supprimer</label>\n    </div>\n  </div>\n</div>\n\n\n            <!-- Action Buttons -->\n            <div class=\"form-row mt-4\">\n              <div class=\"col-md-12 text-center\">\n                <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"responsableForm.invalid\">\n                  Ajouter\n                </button>\n                <button type=\"button\" class=\"btn btn-secondary ml-3\" (click)=\"cancelEdit()\">Annuler</button>\n              </div>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n\n    <script src=\"https://code.jquery.com/jquery-3.5.1.slim.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/js/bootstrap.bundle.min.js\"></script>\n  </body>\n</html>\n"], "mappings": ";;;;;;;;ICoCgBA,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAqBNH,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAwBNH,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAuBJH,EAAA,CAAAC,cAAA,UAA4C;IAAAD,EAAA,CAAAE,MAAA,uCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACxEH,EAAA,CAAAC,cAAA,UAA2C;IAAAD,EAAA,CAAAE,MAAA,+DAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFjGH,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAI,UAAA,IAAAC,6CAAA,kBAAwE;IACxEL,EAAA,CAAAI,UAAA,IAAAE,6CAAA,kBAA+F;IACjGN,EAAA,CAAAG,YAAA,EAAM;;;;;IAFEH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAoC;IACpCV,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,YAAmC;;;;;IAyB3CV,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAwBNH,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBFH,EAAA,CAAAC,cAAA,iBAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF4BH,EAAA,CAAAQ,UAAA,UAAAG,SAAA,CAAAC,QAAA,CAAwB;IAC3DZ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAa,kBAAA,MAAAF,SAAA,CAAAG,SAAA,MACF;;;;;IAGJd,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;ADlLtB,OAAM,MAAOY,uBAAuB;EAqBlCC,YACUC,kBAAsC,EACtCC,gBAAkC;EAAG;EACrCC,MAAc;IAFd,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,MAAM,GAANA,MAAM;IAvBhB,KAAAC,cAAc,GAAgB;MAC5BC,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK,CAAE;KACnB;;IAED,KAAAC,UAAU,GAAgB,EAAE,CAAC,CAAE;IAC/B,KAAAC,MAAM,GAAa,EAAE,CAAC,CAAE;EAOrB;;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACnB,kBAAkB,CAACoB,SAAS,EAAE,CAACC,SAAS,CAAEC,IAAI,IAAI;MACrD,IAAI,CAACJ,MAAM,GAAGI,IAAI,CAAC,CAAE;IACvB,CAAC,CAAC;IAEF;IACA,IAAI,CAACrB,gBAAgB,CAACsB,gBAAgB,EAAE,CAACF,SAAS,CAAEC,IAAI,IAAI;MAC1D,IAAI,CAACL,UAAU,GAAGK,IAAI,CAAC,CAAE;IAC3B,CAAC,CAAC;EACJ;EAEA;EACAE,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACrB,cAAc,CAACQ,QAAQ,EAAE;MACjCc,KAAK,CAAC,6BAA6B,CAAC;MACpC;;IAGF,IAAI,CAAC,IAAI,CAACtB,cAAc,CAACS,WAAW,EAAE;MACpCa,KAAK,CAAC,iCAAiC,CAAC;MACxC;;IAGF,IAAI,CAAC,IAAI,CAACtB,cAAc,CAACO,QAAQ,EAAE;MACjCe,KAAK,CAAC,yBAAyB,CAAC;MAChC;;IAGF;IACAC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACxB,cAAc,CAAC;IAEvD;IACA,IAAI,CAACH,kBAAkB,CAACwB,cAAc,CAAC,IAAI,CAACrB,cAAc,CAAC,CAACkB,SAAS,CAClEO,QAAQ,IAAI;MACXF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAAC;MAExD;MACA,IAAI,CAACzB,cAAc,GAAG;QACpBE,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,CAAC;QACXC,WAAW,EAAE,CAAC,CAAG;OAClB;;MACD,IAAI,CAACV,MAAM,CAAC2B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC,EACAC,KAAK,IAAI;MACRJ,OAAO,CAACI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,CACF;EACH;EAEAC,UAAUA,CAAA;IACR;IACA,IAAI,CAAC5B,cAAc,GAAG;MACpBC,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE;KACd;IAED;IACA,IAAI,CAACV,MAAM,CAAC8B,aAAa,CAAC,QAAQ,CAAC;EACrC;;;uBAnGWlC,uBAAuB,EAAAf,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAApD,EAAA,CAAAkD,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAtD,EAAA,CAAAkD,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvBzC,uBAAuB;MAAA0C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZpC/D,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAiE,SAAA,cAAwB;UAGxBjE,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,qCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAAiE,SAAA,cAAwG;UAE1GjE,EAAA,CAAAG,YAAA,EAAO;UAEPH,EAAA,CAAAC,cAAA,WAAM;UAI+BD,EAAA,CAAAE,MAAA,sCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAC,cAAA,oBAAiF;UAA3ED,EAAA,CAAAkE,UAAA,sBAAAC,2DAAA;YAAA,OAAYH,GAAA,CAAAvB,cAAA,EAAgB;UAAA,EAAC;UAEjCzC,EAAA,CAAAC,cAAA,eAAsB;UAEDD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5BH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAiE,SAAA,aAA0B;UAAAjE,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,qBASE;UALAD,EAAA,CAAAkE,UAAA,2BAAAE,iEAAAC,MAAA;YAAA,OAAAL,GAAA,CAAA5C,cAAA,CAAAE,SAAA,GAAA+C,MAAA;UAAA,EAAsC;UAJxCrE,EAAA,CAAAG,YAAA,EASE;UAEJH,EAAA,CAAAI,UAAA,KAAAkE,uCAAA,kBAEM;UACRtE,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAsB;UACAD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAiE,SAAA,aAA0B;UAAAjE,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,qBASE;UALAD,EAAA,CAAAkE,UAAA,2BAAAK,iEAAAF,MAAA;YAAA,OAAAL,GAAA,CAAA5C,cAAA,CAAAG,QAAA,GAAA8C,MAAA;UAAA,EAAqC;UAJvCrE,EAAA,CAAAG,YAAA,EASE;UAEJH,EAAA,CAAAI,UAAA,KAAAoE,uCAAA,kBAEM;UACRxE,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAsB;UAECD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAiE,SAAA,aAA8B;UAAAjE,EAAA,CAAAG,YAAA,EAAO;UAEtEH,EAAA,CAAAC,cAAA,qBAUE;UANAD,EAAA,CAAAkE,UAAA,2BAAAO,iEAAAJ,MAAA;YAAA,OAAAL,GAAA,CAAA5C,cAAA,CAAAI,KAAA,GAAA6C,MAAA;UAAA,EAAkC;UAJpCrE,EAAA,CAAAG,YAAA,EAUE;UAEJH,EAAA,CAAAI,UAAA,KAAAsE,uCAAA,kBAEM;UACR1E,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAsB;UACGD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAiE,SAAA,aAA2B;UAAAjE,EAAA,CAAAG,YAAA,EAAO;UAEnEH,EAAA,CAAAC,cAAA,qBAUE;UANAD,EAAA,CAAAkE,UAAA,2BAAAS,iEAAAN,MAAA;YAAA,OAAAL,GAAA,CAAA5C,cAAA,CAAAK,SAAA,GAAA4C,MAAA;UAAA,EAAsC;UAJxCrE,EAAA,CAAAG,YAAA,EAUE;UAEJH,EAAA,CAAAI,UAAA,KAAAwE,uCAAA,kBAGM;UACR5E,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAsB;UAEID,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAiE,SAAA,aAA0B;UAAAjE,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,qBAUE;UANAD,EAAA,CAAAkE,UAAA,2BAAAW,iEAAAR,MAAA;YAAA,OAAAL,GAAA,CAAA5C,cAAA,CAAAO,QAAA,GAAA0C,MAAA;UAAA,EAAqC;UAJvCrE,EAAA,CAAAG,YAAA,EAUE;UAEJH,EAAA,CAAAI,UAAA,KAAA0E,uCAAA,kBAEM;UACR9E,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAsB;UAEAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAiE,SAAA,aAAkC;UAAAjE,EAAA,CAAAG,YAAA,EAAO;UAE1EH,EAAA,CAAAC,cAAA,sBAOC;UAJCD,EAAA,CAAAkE,UAAA,2BAAAa,kEAAAV,MAAA;YAAA,OAAAL,GAAA,CAAA5C,cAAA,CAAAM,IAAA,GAAA2C,MAAA;UAAA,EAAiC;UAKjCrE,EAAA,CAAAC,cAAA,kBAA4B;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChDH,EAAA,CAAAC,cAAA,kBAAsB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGxCH,EAAA,CAAAI,UAAA,KAAA4E,uCAAA,kBAEM;UACRhF,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAsB;UAEED,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAiE,SAAA,aAA2B;UAAAjE,EAAA,CAAAG,YAAA,EAAO;UAEnEH,EAAA,CAAAC,cAAA,sBAOC;UAJCD,EAAA,CAAAkE,UAAA,2BAAAe,kEAAAZ,MAAA;YAAA,OAAAL,GAAA,CAAA5C,cAAA,CAAAQ,QAAA,GAAAyC,MAAA;UAAA,EAAqC;UAKrCrE,EAAA,CAAAI,UAAA,KAAA8E,0CAAA,qBAES;UACXlF,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAI,UAAA,KAAA+E,uCAAA,kBAEM;UACRnF,EAAA,CAAAG,YAAA,EAAM;UAKpBH,EAAA,CAAAC,cAAA,eAA2B;UAEhBD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrBH,EAAA,CAAAC,cAAA,gBAA+C;UAK3CD,EAAA,CAAAkE,UAAA,2BAAAkB,kEAAAf,MAAA;YAAA,OAAAL,GAAA,CAAA5C,cAAA,CAAAU,SAAA,GAAAuC,MAAA;UAAA,EAAsC;UAJxCrE,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,mBAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAG/DH,EAAA,CAAAC,cAAA,gBAA0C;UAKtCD,EAAA,CAAAkE,UAAA,2BAAAmB,kEAAAhB,MAAA;YAAA,OAAAL,GAAA,CAAA5C,cAAA,CAAAW,OAAA,GAAAsC,MAAA;UAAA,EAAoC;UAJtCrE,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAG5DH,EAAA,CAAAC,cAAA,gBAA0C;UAKtCD,EAAA,CAAAkE,UAAA,2BAAAoB,kEAAAjB,MAAA;YAAA,OAAAL,GAAA,CAAA5C,cAAA,CAAAY,SAAA,GAAAqC,MAAA;UAAA,EAAsC;UAJxCrE,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,2BAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGvEH,EAAA,CAAAC,cAAA,gBAA0C;UAKtCD,EAAA,CAAAkE,UAAA,2BAAAqB,kEAAAlB,MAAA;YAAA,OAAAL,GAAA,CAAA5C,cAAA,CAAAa,SAAA,GAAAoC,MAAA;UAAA,EAAsC;UAJxCrE,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAO3DH,EAAA,CAAAC,cAAA,gBAA2B;UAGrBD,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAA4E;UAAvBD,EAAA,CAAAkE,UAAA,mBAAAsB,2DAAA;YAAA,OAASxB,GAAA,CAAAhB,UAAA,EAAY;UAAA,EAAC;UAAChD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;;UAhOxFH,EAAA,CAAAO,SAAA,IAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA5C,cAAA,CAAAE,SAAA,CAAsC;UAOpCtB,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,UAAA,SAAAiF,GAAA,CAAAC,OAAA,IAAAD,GAAA,CAAAE,OAAA,CAAgC;UAgBlC3F,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA5C,cAAA,CAAAG,QAAA,CAAqC;UAOnCvB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,SAAAoF,GAAA,CAAAF,OAAA,IAAAE,GAAA,CAAAD,OAAA,CAAsC;UAkBxC3F,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA5C,cAAA,CAAAI,KAAA,CAAkC;UAQhCxB,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,SAAAqF,GAAA,CAAAH,OAAA,IAAAG,GAAA,CAAAF,OAAA,CAAoC;UAgBtC3F,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA5C,cAAA,CAAAK,SAAA,CAAsC;UAQpCzB,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAiF,OAAA,IAAAjF,GAAA,CAAAkF,OAAA,CAA4C;UAmB9C3F,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA5C,cAAA,CAAAO,QAAA,CAAqC;UAQnC3B,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAQ,UAAA,SAAAsF,GAAA,CAAAJ,OAAA,IAAAI,GAAA,CAAAH,OAAA,CAA0C;UAiB5C3F,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA5C,cAAA,CAAAM,IAAA,CAAiC;UAS/B1B,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,SAAAuF,IAAA,CAAAL,OAAA,IAAAK,IAAA,CAAAJ,OAAA,CAAkC;UAiBpC3F,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA5C,cAAA,CAAAQ,QAAA,CAAqC;UAKX5B,EAAA,CAAAO,SAAA,GAAS;UAATP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA7B,MAAA,CAAS;UAKjCnC,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,SAAAwF,IAAA,CAAAN,OAAA,IAAAM,IAAA,CAAAL,OAAA,CAAsC;UAgBpD3F,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA5C,cAAA,CAAAU,SAAA,CAAsC;UAWtC9B,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA5C,cAAA,CAAAW,OAAA,CAAoC;UAWpC/B,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA5C,cAAA,CAAAY,SAAA,CAAsC;UAWtChC,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAwD,GAAA,CAAA5C,cAAA,CAAAa,SAAA,CAAsC;UAYgBjC,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,aAAAyF,GAAA,CAAAP,OAAA,CAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}