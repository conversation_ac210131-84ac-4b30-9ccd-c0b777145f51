{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, map, throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ResponsableService {\n  // Your API URL\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth/Users_Responsables';\n    this.baseUrl = 'http://localhost:8000/api/v1/auth';\n  }\n  // Helper function to get the token and set headers\n  getAuthHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.error('No token found!');\n      throw new Error('No token found');\n    }\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json',\n      'Accept': 'application/json'\n    });\n  }\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId) {\n    return this.http.get(`${this.apiUrl}/${responsableId}`, {\n      headers: this.getAuthHeaders() // Add auth headers\n    }).pipe(catchError(error => this.handleError(error)) // Handle any error\n    );\n  }\n\n  getAllUsers() {\n    return this.http.get(`${this.baseUrl}/Users`, {\n      headers: this.getAuthHeaders()\n    }).pipe(catchError(error => this.handleError(error)));\n  }\n  // Get all responsibles\n  getResponsables() {\n    return this.http.get(this.apiUrl, {\n      headers: this.getAuthHeaders() // Add auth headers\n    }).pipe(catchError(error => this.handleError(error)) // Handle any error\n    );\n  }\n  // Delete a responsable\n  deleteResponsable(id) {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, {\n      headers: this.getAuthHeaders(),\n      responseType: 'text'\n    }).pipe(catchError(error => this.handleError(error)) // Handle any error\n    );\n  }\n  // Update a responsable\n  updateResponsable(id, responsable) {\n    return this.http.put(`${this.apiUrl}/edit/${id}`, responsable, {\n      headers: this.getAuthHeaders(),\n      responseType: 'text' // Prevents Angular from trying to parse JSON\n    }).pipe(catchError(error => this.handleError(error)));\n  }\n  // Add a responsable\n  addResponsable(responsable) {\n    return this.http.post('http://localhost:8000/api/v1/auth/register', responsable, {\n      headers: this.getAuthHeaders() // Add auth headers\n    }).pipe(catchError(error => this.handleError(error)) // Handle any error\n    );\n  }\n  // Error handling method\n  handleError(error) {\n    console.error('An error occurred:', error);\n    // Optionally, show a user-friendly error message here\n    return throwError(() => new Error(error.message || 'Something went wrong'));\n  }\n  // Inside ResponsableService\n  register(responsable) {\n    return this.http.post('http://localhost:8000/api/v1/auth/register', responsable, {\n      headers: this.getAuthHeaders() // Add auth headers\n    }).pipe(catchError(error => this.handleError(error)) // Handle any error\n    );\n  }\n  // Add this method in ResponsableService\n  // ResponsableService\n  // ResponsableService\n  getGroups() {\n    return this.http.get('http://localhost:8000/api/v1/auth/Groupes/ALLGroupes', {\n      headers: this.getAuthHeaders()\n    }).pipe(map(response => {\n      // Map the response to the required format, extracting the idGroupe and nomGroupe\n      return response.map(group => ({\n        idGroupe: group.idGroupe,\n        nomGroupe: group.nomGroupe\n      }));\n    }), catchError(error => this.handleError(error)));\n  }\n  static {\n    this.ɵfac = function ResponsableService_Factory(t) {\n      return new (t || ResponsableService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ResponsableService,\n      factory: ResponsableService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "catchError", "map", "throwError", "ResponsableService", "constructor", "http", "apiUrl", "baseUrl", "getAuthHeaders", "token", "localStorage", "getItem", "console", "error", "Error", "getResponsableById", "responsableId", "get", "headers", "pipe", "handleError", "getAllUsers", "getResponsables", "deleteResponsable", "id", "delete", "responseType", "updateResponsable", "responsable", "put", "addResponsable", "post", "message", "register", "getGroups", "response", "group", "idGroupe", "nomGroupe", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\responsable.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { catchError, map, Observable, throwError } from 'rxjs';\nimport { User } from './model/user.model'; // Import the User model\nimport { Groupe } from './model/groupe.model';\nimport { Responsable } from './model/respons.model';\n\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ResponsableService {\n  private apiUrl = 'http://localhost:8000/api/v1/auth/Users_Responsables'; \n  private baseUrl = 'http://localhost:8000/api/v1/auth';\n// Your API URL\n\n  constructor(private http: HttpClient) {}\n\n  // Helper function to get the token and set headers\n  private getAuthHeaders(): HttpHeaders {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.error('No token found!');\n      throw new Error('No token found');\n    }\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n    });\n  }\n\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId: number): Observable<User> {\n    return this.http.get<User>(`${this.apiUrl}/${responsableId}`, {\n      headers: this.getAuthHeaders(), // Add auth headers\n    }).pipe(\n      catchError((error) => this.handleError(error)) // Handle any error\n    );\n  }\n  \ngetAllUsers(): Observable<User[]> {\n  return this.http.get<User[]>(`${this.baseUrl}/Users`, {\n    headers: this.getAuthHeaders(),\n  }).pipe(\n    catchError((error) => this.handleError(error))\n  );\n}\n\n  // Get all responsibles\n  getResponsables(): Observable<User[]> {\n    return this.http.get<User[]>(this.apiUrl, {\n      headers: this.getAuthHeaders(), // Add auth headers\n    }).pipe(\n      catchError((error) => this.handleError(error)) // Handle any error\n    );\n  }\n\n  // Delete a responsable\n  deleteResponsable(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, {\n      headers: this.getAuthHeaders(), // Add auth headers\n      responseType: 'text',\n    }).pipe(\n      catchError((error) => this.handleError(error)) // Handle any error\n    );\n  }\n\n  // Update a responsable\n  updateResponsable(id: number, responsable: User): Observable<any> {\n    return this.http.put(`${this.apiUrl}/edit/${id}`, responsable, {\n      headers: this.getAuthHeaders(),\n      responseType: 'text' as 'json'  // Prevents Angular from trying to parse JSON\n    }).pipe(\n      catchError((error) => this.handleError(error))\n    );\n  }\n  \n  // Add a responsable\n  addResponsable(responsable: Responsable): Observable<Responsable> {\n    return this.http.post<Responsable>('http://localhost:8000/api/v1/auth/register', responsable, {\n      headers: this.getAuthHeaders(), // Add auth headers\n    }).pipe(\n      catchError((error) => this.handleError(error)) // Handle any error\n    );\n  }\n  \n  // Error handling method\n  private handleError(error: any) {\n    console.error('An error occurred:', error);\n    // Optionally, show a user-friendly error message here\n    return throwError(() => new Error(error.message || 'Something went wrong'));\n  }\n  // Inside ResponsableService\nregister(responsable: User): Observable<any> {\n  return this.http.post<any>('http://localhost:8000/api/v1/auth/register', responsable, {\n    headers: this.getAuthHeaders(), // Add auth headers\n  }).pipe(\n    catchError((error) => this.handleError(error)) // Handle any error\n  );\n}\n\n// Add this method in ResponsableService\n// ResponsableService\n// ResponsableService\ngetGroups(): Observable<Groupe[]> {\n  return this.http.get<any>('http://localhost:8000/api/v1/auth/Groupes/ALLGroupes', {\n    headers: this.getAuthHeaders(),\n  }).pipe(\n    map(response => {\n      // Map the response to the required format, extracting the idGroupe and nomGroupe\n      return response.map((group: { idGroupe: any; nomGroupe: any; }) => ({\n        idGroupe: group.idGroupe,\n        nomGroupe: group.nomGroupe,\n      }));\n    }),\n    catchError((error) => this.handleError(error))\n  );\n}\n\n\n\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAEC,GAAG,EAAcC,UAAU,QAAQ,MAAM;;;AAS9D,OAAM,MAAOC,kBAAkB;EAG/B;EAEEC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJhB,KAAAC,MAAM,GAAG,sDAAsD;IAC/D,KAAAC,OAAO,GAAG,mCAAmC;EAGd;EAEvC;EACQC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVG,OAAO,CAACC,KAAK,CAAC,iBAAiB,CAAC;MAChC,MAAM,IAAIC,KAAK,CAAC,gBAAgB,CAAC;;IAEnC,OAAO,IAAIf,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUU,KAAK,EAAE;MAClC,cAAc,EAAE,kBAAkB;MAClC,QAAQ,EAAE;KACX,CAAC;EACJ;EAEA;EACAM,kBAAkBA,CAACC,aAAqB;IACtC,OAAO,IAAI,CAACX,IAAI,CAACY,GAAG,CAAO,GAAG,IAAI,CAACX,MAAM,IAAIU,aAAa,EAAE,EAAE;MAC5DE,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE,CAAE;KACjC,CAAC,CAACW,IAAI,CACLnB,UAAU,CAAEa,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC;KAChD;EACH;;EAEFQ,WAAWA,CAAA;IACT,OAAO,IAAI,CAAChB,IAAI,CAACY,GAAG,CAAS,GAAG,IAAI,CAACV,OAAO,QAAQ,EAAE;MACpDW,OAAO,EAAE,IAAI,CAACV,cAAc;KAC7B,CAAC,CAACW,IAAI,CACLnB,UAAU,CAAEa,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAC/C;EACH;EAEE;EACAS,eAAeA,CAAA;IACb,OAAO,IAAI,CAACjB,IAAI,CAACY,GAAG,CAAS,IAAI,CAACX,MAAM,EAAE;MACxCY,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE,CAAE;KACjC,CAAC,CAACW,IAAI,CACLnB,UAAU,CAAEa,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC;KAChD;EACH;EAEA;EACAU,iBAAiBA,CAACC,EAAU;IAC1B,OAAO,IAAI,CAACnB,IAAI,CAACoB,MAAM,CAAC,GAAG,IAAI,CAACnB,MAAM,WAAWkB,EAAE,EAAE,EAAE;MACrDN,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE;MAC9BkB,YAAY,EAAE;KACf,CAAC,CAACP,IAAI,CACLnB,UAAU,CAAEa,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC;KAChD;EACH;EAEA;EACAc,iBAAiBA,CAACH,EAAU,EAAEI,WAAiB;IAC7C,OAAO,IAAI,CAACvB,IAAI,CAACwB,GAAG,CAAC,GAAG,IAAI,CAACvB,MAAM,SAASkB,EAAE,EAAE,EAAEI,WAAW,EAAE;MAC7DV,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE;MAC9BkB,YAAY,EAAE,MAAgB,CAAE;KACjC,CAAC,CAACP,IAAI,CACLnB,UAAU,CAAEa,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAC/C;EACH;EAEA;EACAiB,cAAcA,CAACF,WAAwB;IACrC,OAAO,IAAI,CAACvB,IAAI,CAAC0B,IAAI,CAAc,4CAA4C,EAAEH,WAAW,EAAE;MAC5FV,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE,CAAE;KACjC,CAAC,CAACW,IAAI,CACLnB,UAAU,CAAEa,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC;KAChD;EACH;EAEA;EACQO,WAAWA,CAACP,KAAU;IAC5BD,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC1C;IACA,OAAOX,UAAU,CAAC,MAAM,IAAIY,KAAK,CAACD,KAAK,CAACmB,OAAO,IAAI,sBAAsB,CAAC,CAAC;EAC7E;EACA;EACFC,QAAQA,CAACL,WAAiB;IACxB,OAAO,IAAI,CAACvB,IAAI,CAAC0B,IAAI,CAAM,4CAA4C,EAAEH,WAAW,EAAE;MACpFV,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE,CAAE;KACjC,CAAC,CAACW,IAAI,CACLnB,UAAU,CAAEa,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC;KAChD;EACH;EAEA;EACA;EACA;EACAqB,SAASA,CAAA;IACP,OAAO,IAAI,CAAC7B,IAAI,CAACY,GAAG,CAAM,sDAAsD,EAAE;MAChFC,OAAO,EAAE,IAAI,CAACV,cAAc;KAC7B,CAAC,CAACW,IAAI,CACLlB,GAAG,CAACkC,QAAQ,IAAG;MACb;MACA,OAAOA,QAAQ,CAAClC,GAAG,CAAEmC,KAAyC,KAAM;QAClEC,QAAQ,EAAED,KAAK,CAACC,QAAQ;QACxBC,SAAS,EAAEF,KAAK,CAACE;OAClB,CAAC,CAAC;IACL,CAAC,CAAC,EACFtC,UAAU,CAAEa,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAC/C;EACH;;;uBA3GaV,kBAAkB,EAAAoC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBvC,kBAAkB;MAAAwC,OAAA,EAAlBxC,kBAAkB,CAAAyC,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}