{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class ReportsComponent {\n  static {\n    this.ɵfac = function ReportsComponent_Factory(t) {\n      return new (t || ReportsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReportsComponent,\n      selectors: [[\"app-reports\"]],\n      decls: 3,\n      vars: 0,\n      template: function ReportsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"reports works!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(2, \"router-outlet\");\n        }\n      },\n      dependencies: [i1.RouterOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ReportsComponent", "selectors", "decls", "vars", "template", "ReportsComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\reports\\reports.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\reports\\reports.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-reports',\n  templateUrl: './reports.component.html',\n  styleUrls: ['./reports.component.css']\n})\nexport class ReportsComponent {\n\n}\n", "<p>reports works!</p>\n<router-outlet></router-outlet>\n"], "mappings": ";;AAOA,OAAM,MAAOA,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACrBH,EAAA,CAAAI,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}