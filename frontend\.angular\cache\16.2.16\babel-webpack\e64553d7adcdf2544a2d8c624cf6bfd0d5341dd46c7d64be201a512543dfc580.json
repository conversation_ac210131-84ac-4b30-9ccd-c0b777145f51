{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let ResponDashboardComponent = class ResponDashboardComponent {};\nResponDashboardComponent = __decorate([Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './respon-dashboard.component.html',\n  styleUrls: ['./respon-dashboard.component.css']\n})], ResponDashboardComponent);", "map": {"version": 3, "names": ["Component", "ResponDashboardComponent", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\frontend\\src\\app\\respon-dashboard\\respon-dashboard.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './respon-dashboard.component.html',\n  styleUrls: ['./respon-dashboard.component.css']\n})\nexport class ResponDashboardComponent {\n\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAOlC,WAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB,GAEpC;AAFYA,wBAAwB,GAAAC,UAAA,EALpCF,SAAS,CAAC;EACTG,QAAQ,EAAE,sBAAsB;EAChCC,WAAW,EAAE,mCAAmC;EAChDC,SAAS,EAAE,CAAC,kCAAkC;CAC/C,CAAC,C,EACWJ,wBAAwB,CAEpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}