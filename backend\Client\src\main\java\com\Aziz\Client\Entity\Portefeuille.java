package com.Aziz.Client.Entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.springframework.data.annotation.LastModifiedDate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name="Portefeuille")
public class Portefeuille {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idPortefeuille;
    private BigDecimal solde;
    private Integer quantite;

    @CreationTimestamp
    private LocalDateTime DateCreation;

    @LastModifiedDate
    private LocalDateTime DateModification;


    @ManyToOne
    @JoinColumn(name = "idAction")
    private Action action;

    @ManyToOne
    @JoinColumn(name = "idActionnaire")
//    @JsonBackReference
    private Actionnaire actionnaire;

    @OneToMany(mappedBy = "portefeuille",cascade = CascadeType.ALL)
    @JsonManagedReference
    @JsonIgnore
    private List<Transaction> transactions;

}
