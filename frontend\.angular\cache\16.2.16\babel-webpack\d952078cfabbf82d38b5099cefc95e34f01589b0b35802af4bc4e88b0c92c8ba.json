{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ResponsableService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8060/api/v1/auth/Users_Responsables'; // Your API URL\n  }\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId) {\n    return this.http.get(`${this.apiUrl}/${responsableId}`); // Fetching a single user\n  }\n  // Get all responsibles\n  getResponsables() {\n    const headers = new HttpHeaders().set('Authorization', `Bearer ${localStorage.getItem('token')}`);\n    return this.http.get(this.apiUrl, {\n      headers\n    });\n  }\n  // Delete a responsable\n  deleteResponsable(id) {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`); // Deleting a user by ID\n  }\n  // Update a responsable\n  updateResponsable(id, responsable) {\n    const token = localStorage.getItem('token'); // Get the JWT token from localStorage\n    if (token) {\n      const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);\n      return this.http.put(`${this.apiUrl}/edit/${id}`, responsable, {\n        headers\n      });\n    }\n    return throwError(() => new Error('No token found'));\n  }\n  static {\n    this.ɵfac = function ResponsableService_Factory(t) {\n      return new (t || ResponsableService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ResponsableService,\n      factory: ResponsableService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "throwError", "ResponsableService", "constructor", "http", "apiUrl", "getResponsableById", "responsableId", "get", "getResponsables", "headers", "set", "localStorage", "getItem", "deleteResponsable", "id", "delete", "updateResponsable", "responsable", "token", "put", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\responsable.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { User } from './model/user.model'; // Import the User model\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ResponsableService {\n  private apiUrl = 'http://localhost:8060/api/v1/auth/Users_Responsables'; // Your API URL\n\n  constructor(private http: HttpClient) {}\n\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId: number): Observable<User> {\n    return this.http.get<User>(`${this.apiUrl}/${responsableId}`); // Fetching a single user\n  }\n\n  // Get all responsibles\n  getResponsables(): Observable<User[]> {\n    const headers = new HttpHeaders().set(\n      'Authorization',\n      `Bearer ${localStorage.getItem('token')}`\n    );\n\n    return this.http.get<User[]>(this.apiUrl, { headers });\n  }\n\n  // Delete a responsable\n  deleteResponsable(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`); // Deleting a user by ID\n  }\n\n  // Update a responsable\n  updateResponsable(id: number, responsable: User): Observable<User> {\n    const token = localStorage.getItem('token'); // Get the JWT token from localStorage\n    if (token) {\n      const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);\n      return this.http.put<User>(`${this.apiUrl}/edit/${id}`, responsable, { headers });\n    }\n    return throwError(() => new Error('No token found'));\n  }\n}  "], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAAqBC,UAAU,QAAQ,MAAM;;;AAM7C,OAAM,MAAOC,kBAAkB;EAG7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,sDAAsD,CAAC,CAAC;EAElC;EAEvC;EACAC,kBAAkBA,CAACC,aAAqB;IACtC,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAO,GAAG,IAAI,CAACH,MAAM,IAAIE,aAAa,EAAE,CAAC,CAAC,CAAC;EACjE;EAEA;EACAE,eAAeA,CAAA;IACb,MAAMC,OAAO,GAAG,IAAIV,WAAW,EAAE,CAACW,GAAG,CACnC,eAAe,EACf,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE,CAC1C;IAED,OAAO,IAAI,CAACT,IAAI,CAACI,GAAG,CAAS,IAAI,CAACH,MAAM,EAAE;MAAEK;IAAO,CAAE,CAAC;EACxD;EAEA;EACAI,iBAAiBA,CAACC,EAAU;IAC1B,OAAO,IAAI,CAACX,IAAI,CAACY,MAAM,CAAC,GAAG,IAAI,CAACX,MAAM,WAAWU,EAAE,EAAE,CAAC,CAAC,CAAC;EAC1D;EAEA;EACAE,iBAAiBA,CAACF,EAAU,EAAEG,WAAiB;IAC7C,MAAMC,KAAK,GAAGP,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7C,IAAIM,KAAK,EAAE;MACT,MAAMT,OAAO,GAAG,IAAIV,WAAW,EAAE,CAACW,GAAG,CAAC,eAAe,EAAE,UAAUQ,KAAK,EAAE,CAAC;MACzE,OAAO,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAO,GAAG,IAAI,CAACf,MAAM,SAASU,EAAE,EAAE,EAAEG,WAAW,EAAE;QAAER;MAAO,CAAE,CAAC;;IAEnF,OAAOT,UAAU,CAAC,MAAM,IAAIoB,KAAK,CAAC,gBAAgB,CAAC,CAAC;EACtD;;;uBAjCWnB,kBAAkB,EAAAoB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBvB,kBAAkB;MAAAwB,OAAA,EAAlBxB,kBAAkB,CAAAyB,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}