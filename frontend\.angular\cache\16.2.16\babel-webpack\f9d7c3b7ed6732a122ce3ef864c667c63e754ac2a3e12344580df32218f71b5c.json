{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/habilitation.service\";\nimport * as i3 from \"@angular/common\";\nfunction HabilitationComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Groupe:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.groupe.nomGroupe, \"\");\n  }\n}\nfunction HabilitationComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 7)(2, \"input\", 8);\n    i0.ɵɵlistener(\"change\", function HabilitationComponent_div_7_Template_input_change_2_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const ressource_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onCheckboxChange(ressource_r3.idRessource, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 9);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ressource_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"ressource-\" + ressource_r3.idRessource)(\"checked\", ctx_r1.selectedRessources.includes(ressource_r3.idRessource));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"ressource-\" + ressource_r3.idRessource);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ressource_r3.nomRessource, \" (\", ressource_r3.link_path, \") \");\n  }\n}\nfunction HabilitationComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1, \" Veuillez s\\u00E9lectionner au moins une ressource avant de soumettre. \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HabilitationComponent {\n  constructor(route, habilitationService, router) {\n    this.route = route;\n    this.habilitationService = habilitationService;\n    this.router = router;\n    this.ressources = [];\n    this.habilitation = {\n      id: 0,\n      idGroupe: 0,\n      ressources: [] // Initialize as an empty array since we are selecting multiple resources\n    };\n    // Track selected ressources as IDs\n    this.selectedRessources = [];\n  }\n  ngOnInit() {\n    const idParam = this.route.snapshot.paramMap.get('id');\n    if (idParam) {\n      this.groupeId = Number(idParam);\n      this.getGroupeDetails(this.groupeId);\n      this.getRessources();\n    }\n  }\n  getGroupeDetails(id) {\n    this.habilitationService.getGroupeById(id).subscribe({\n      next: group => {\n        this.groupe = group;\n        // Populate the selected ressources based on the groupe's existing ressources (IDs)\n        if (this.groupe?.ressources) {\n          this.selectedRessources = this.groupe.ressources;\n        }\n      },\n      error: err => {\n        console.error('Error fetching group:', err);\n      }\n    });\n  }\n  getRessources() {\n    this.habilitationService.getAllRessources().subscribe({\n      next: data => {\n        this.ressources = data;\n      },\n      error: err => {\n        console.error('Error fetching ressources:', err);\n      }\n    });\n  }\n  onCheckboxChange(ressourceId, event) {\n    if (event.target.checked) {\n      // Add the ressource ID to the selected list if checked\n      this.selectedRessources.push(ressourceId);\n    } else {\n      // Remove the ressource ID from the selected list if unchecked\n      this.selectedRessources = this.selectedRessources.filter(id => id !== ressourceId);\n    }\n    console.log('Selected Ressources:', this.selectedRessources);\n  }\n  submitHabilitation() {\n    // Ensure the selected resources are valid\n    if (this.selectedRessources.length === 0) {\n      console.warn('Please select at least one valid ressource before submitting.');\n      return; // Exit early if no valid ressources are selected\n    }\n    // Prepare the habilitation object with selected resources (IDs)\n    this.habilitation.idGroupe = this.groupeId;\n    this.habilitation.ressources = this.selectedRessources; // Use an array of resource IDs\n    this.habilitationService.submitHabilitation(this.habilitation).subscribe({\n      next: response => {\n        console.log('Habilitation saved successfully', response);\n        // Navigate to the /groups page after successful save\n        this.router.navigate(['/groups']);\n      },\n      error: err => {\n        console.error('Error saving habilitation:', err);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function HabilitationComponent_Factory(t) {\n      return new (t || HabilitationComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.HabilitationService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HabilitationComponent,\n      selectors: [[\"app-habilitation\"]],\n      decls: 11,\n      vars: 4,\n      consts: [[1, \"container\", \"mt-4\"], [4, \"ngIf\"], [1, \"mb-3\"], [1, \"form-label\"], [4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"class\", \"text-danger mt-2\", 4, \"ngIf\"], [1, \"form-check\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"id\", \"checked\", \"change\"], [1, \"form-check-label\", 3, \"for\"], [1, \"text-danger\", \"mt-2\"]],\n      template: function HabilitationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Cr\\u00E9er une Habilitation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, HabilitationComponent_div_3_Template, 5, 1, \"div\", 1);\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6, \"S\\u00E9lectionner des Ressources:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, HabilitationComponent_div_7_Template, 5, 5, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function HabilitationComponent_Template_button_click_8_listener() {\n            return ctx.submitHabilitation();\n          });\n          i0.ɵɵtext(9, \" Enregistrer \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, HabilitationComponent_div_10_Template, 2, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupe);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.ressources);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.selectedRessources.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedRessources.length === 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf],\n      styles: [\".habilitation-container[_ngcontent-%COMP%] {\\n    max-width: 600px;\\n    margin: 20px auto;\\n    padding: 20px;\\n    background-color: #f9f9f9;\\n    border-radius: 8px;\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n  }\\n  \\n  h2[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 20px;\\n    color: #333;\\n    font-size: 1.5rem;\\n  }\\n  \\n  .form-group[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n  \\n  label[_ngcontent-%COMP%] {\\n    display: block;\\n    margin-bottom: 5px;\\n    font-weight: bold;\\n    color: #555;\\n  }\\n  \\n  select[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 10px;\\n    border: 1px solid #ddd;\\n    border-radius: 4px;\\n    font-size: 1rem;\\n    background-color: #fff;\\n    transition: border 0.3s ease;\\n  }\\n  \\n  select[_ngcontent-%COMP%]:focus {\\n    border-color: #007bff;\\n    outline: none;\\n  }\\n  \\n  button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 12px;\\n    background-color: #007bff;\\n    color: white;\\n    font-size: 1.1rem;\\n    border: none;\\n    border-radius: 4px;\\n    cursor: pointer;\\n    transition: background-color 0.3s ease;\\n  }\\n  \\n  button[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n  }\\n  \\n  button[_ngcontent-%COMP%]:focus {\\n    outline: none;\\n  }\\n  \\n  @media (max-width: 600px) {\\n    .habilitation-container[_ngcontent-%COMP%] {\\n      padding: 15px;\\n    }\\n  \\n    h2[_ngcontent-%COMP%] {\\n      font-size: 1.25rem;\\n    }\\n  \\n    select[_ngcontent-%COMP%] {\\n      font-size: 0.9rem;\\n    }\\n  \\n    button[_ngcontent-%COMP%] {\\n      font-size: 1rem;\\n    }\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "groupe", "nomGroupe", "ɵɵlistener", "HabilitationComponent_div_7_Template_input_change_2_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r5", "ressource_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "onCheckboxChange", "idRessource", "ɵɵproperty", "ctx_r1", "selectedRessources", "includes", "ɵɵtextInterpolate2", "nomRessource", "link_path", "HabilitationComponent", "constructor", "route", "habilitationService", "router", "ressources", "habilitation", "id", "idGroupe", "ngOnInit", "idParam", "snapshot", "paramMap", "get", "groupeId", "Number", "getGroupeDetails", "getRessources", "getGroupeById", "subscribe", "next", "group", "error", "err", "console", "getAllRessources", "data", "ressourceId", "event", "target", "checked", "push", "filter", "log", "submitHabilitation", "length", "warn", "response", "navigate", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "HabilitationService", "Router", "selectors", "decls", "vars", "consts", "template", "HabilitationComponent_Template", "rf", "ctx", "ɵɵtemplate", "HabilitationComponent_div_3_Template", "HabilitationComponent_div_7_Template", "HabilitationComponent_Template_button_click_8_listener", "HabilitationComponent_div_10_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\habilitation\\habilitation.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\habilitation\\habilitation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { HabilitationService } from '../services/habilitation.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Ressource } from '../model/ressource.model';\nimport { Habilitation } from '../model/Habilitation.model';\n\n@Component({\n  selector: 'app-habilitation',\n  templateUrl: './habilitation.component.html',\n  styleUrls: ['./habilitation.component.css']\n})\nexport class HabilitationComponent implements OnInit {\n  groupeId!: number;\n  groupe?: Groupe;\n  ressources: Ressource[] = [];\n\n  habilitation: Habilitation = {\n    id: 0,\n    idGroupe: 0,\n    ressources: [] // Initialize as an empty array since we are selecting multiple resources\n  };\n\n  // Track selected ressources as IDs\n  selectedRessources: number[] = [];\n\n  constructor(\n    private route: ActivatedRoute,\n    private habilitationService: HabilitationService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    const idParam = this.route.snapshot.paramMap.get('id');\n    if (idParam) {\n      this.groupeId = Number(idParam);\n      this.getGroupeDetails(this.groupeId);\n      this.getRessources();\n    }\n  }\n\n  getGroupeDetails(id: number): void {\n    this.habilitationService.getGroupeById(id).subscribe({\n      next: (group: Groupe) => {\n        this.groupe = group;\n        // Populate the selected ressources based on the groupe's existing ressources (IDs)\n        if (this.groupe?.ressources) {\n          this.selectedRessources = this.groupe.ressources;\n        }\n      },\n      error: (err) => {\n        console.error('Error fetching group:', err);\n      }\n    });\n  }\n\n  getRessources(): void {\n    this.habilitationService.getAllRessources().subscribe({\n      next: (data: Ressource[]) => {\n        this.ressources = data;\n      },\n      error: (err) => {\n        console.error('Error fetching ressources:', err);\n      }\n    });\n  }\n\n  onCheckboxChange(ressourceId: number, event: any): void {\n    if (event.target.checked) {\n      // Add the ressource ID to the selected list if checked\n      this.selectedRessources.push(ressourceId);\n    } else {\n      // Remove the ressource ID from the selected list if unchecked\n      this.selectedRessources = this.selectedRessources.filter(id => id !== ressourceId);\n    }\n    console.log('Selected Ressources:', this.selectedRessources);\n  }\n\n  submitHabilitation(): void {\n    // Ensure the selected resources are valid\n    if (this.selectedRessources.length === 0) {\n      console.warn('Please select at least one valid ressource before submitting.');\n      return; // Exit early if no valid ressources are selected\n    }\n  \n    // Prepare the habilitation object with selected resources (IDs)\n    this.habilitation.idGroupe = this.groupeId;\n    this.habilitation.ressources = this.selectedRessources; // Use an array of resource IDs\n  \n    this.habilitationService.submitHabilitation(this.habilitation).subscribe({\n      next: (response) => {\n        console.log('Habilitation saved successfully', response);\n        // Navigate to the /groups page after successful save\n        this.router.navigate(['/groups']);\n      },\n      error: (err) => {\n        console.error('Error saving habilitation:', err);\n      }\n    });\n  }\n  \n}\n", "<div class=\"container mt-4\">\n  <h2>Créer une Habilitation</h2>\n\n  <!-- Display group info -->\n  <div *ngIf=\"groupe\">\n    <p><strong>Groupe:</strong> {{ groupe.nomGroupe }}</p>\n  </div>\n\n  <!-- Checkboxes for selecting ressources -->\n  <div class=\"mb-3\">\n    <label class=\"form-label\">Sélectionner des Ressources:</label>\n    <div *ngFor=\"let ressource of ressources\">\n      <div class=\"form-check\">\n        <input\n          type=\"checkbox\"\n          class=\"form-check-input\"\n          [id]=\"'ressource-' + ressource.idRessource\"\n          [checked]=\"selectedRessources.includes(ressource.idRessource)\"\n          (change)=\"onCheckboxChange(ressource.idRessource, $event)\"\n        />\n        <label class=\"form-check-label\" [for]=\"'ressource-' + ressource.idRessource\">\n          {{ ressource.nomRessource }} ({{ ressource.link_path }})\n        </label>\n      </div>\n    </div>\n  </div>\n\n  <!-- Submit button -->\n  <button\n    class=\"btn btn-primary\"\n    (click)=\"submitHabilitation()\"\n    [disabled]=\"selectedRessources.length === 0\"\n  >\n    Enregistrer\n  </button>\n\n  <!-- Optional: success or error message -->\n  <div *ngIf=\"selectedRessources.length === 0\" class=\"text-danger mt-2\">\n    Veuillez sélectionner au moins une ressource avant de soumettre.\n  </div>\n</div>\n"], "mappings": ";;;;;;ICIEA,EAAA,CAAAC,cAAA,UAAoB;IACPD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,MAAA,CAAAC,SAAA,KAAsB;;;;;;IAMlDR,EAAA,CAAAC,cAAA,UAA0C;IAOpCD,EAAA,CAAAS,UAAA,oBAAAC,6DAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,YAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAUlB,EAAA,CAAAmB,WAAA,CAAAF,MAAA,CAAAG,gBAAA,CAAAL,YAAA,CAAAM,WAAA,EAAAV,MAAA,CAA+C;IAAA,EAAC;IAL5DX,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IANNH,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAsB,UAAA,sBAAAP,YAAA,CAAAM,WAAA,CAA2C,YAAAE,MAAA,CAAAC,kBAAA,CAAAC,QAAA,CAAAV,YAAA,CAAAM,WAAA;IAIbrB,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAsB,UAAA,uBAAAP,YAAA,CAAAM,WAAA,CAA4C;IAC1ErB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA0B,kBAAA,MAAAX,YAAA,CAAAY,YAAA,QAAAZ,YAAA,CAAAa,SAAA,OACF;;;;;IAeN5B,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,MAAA,8EACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;AD3BR,OAAM,MAAO0B,qBAAqB;EAchCC,YACUC,KAAqB,EACrBC,mBAAwC,EACxCC,MAAc;IAFd,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IAdhB,KAAAC,UAAU,GAAgB,EAAE;IAE5B,KAAAC,YAAY,GAAiB;MAC3BC,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,CAAC;MACXH,UAAU,EAAE,EAAE,CAAC;KAChB;IAED;IACA,KAAAV,kBAAkB,GAAa,EAAE;EAM9B;EAEHc,QAAQA,CAAA;IACN,MAAMC,OAAO,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACtD,IAAIH,OAAO,EAAE;MACX,IAAI,CAACI,QAAQ,GAAGC,MAAM,CAACL,OAAO,CAAC;MAC/B,IAAI,CAACM,gBAAgB,CAAC,IAAI,CAACF,QAAQ,CAAC;MACpC,IAAI,CAACG,aAAa,EAAE;;EAExB;EAEAD,gBAAgBA,CAACT,EAAU;IACzB,IAAI,CAACJ,mBAAmB,CAACe,aAAa,CAACX,EAAE,CAAC,CAACY,SAAS,CAAC;MACnDC,IAAI,EAAGC,KAAa,IAAI;QACtB,IAAI,CAAC3C,MAAM,GAAG2C,KAAK;QACnB;QACA,IAAI,IAAI,CAAC3C,MAAM,EAAE2B,UAAU,EAAE;UAC3B,IAAI,CAACV,kBAAkB,GAAG,IAAI,CAACjB,MAAM,CAAC2B,UAAU;;MAEpD,CAAC;MACDiB,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;MAC7C;KACD,CAAC;EACJ;EAEAN,aAAaA,CAAA;IACX,IAAI,CAACd,mBAAmB,CAACsB,gBAAgB,EAAE,CAACN,SAAS,CAAC;MACpDC,IAAI,EAAGM,IAAiB,IAAI;QAC1B,IAAI,CAACrB,UAAU,GAAGqB,IAAI;MACxB,CAAC;MACDJ,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEC,GAAG,CAAC;MAClD;KACD,CAAC;EACJ;EAEAhC,gBAAgBA,CAACoC,WAAmB,EAAEC,KAAU;IAC9C,IAAIA,KAAK,CAACC,MAAM,CAACC,OAAO,EAAE;MACxB;MACA,IAAI,CAACnC,kBAAkB,CAACoC,IAAI,CAACJ,WAAW,CAAC;KAC1C,MAAM;MACL;MACA,IAAI,CAAChC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACqC,MAAM,CAACzB,EAAE,IAAIA,EAAE,KAAKoB,WAAW,CAAC;;IAEpFH,OAAO,CAACS,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACtC,kBAAkB,CAAC;EAC9D;EAEAuC,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACvC,kBAAkB,CAACwC,MAAM,KAAK,CAAC,EAAE;MACxCX,OAAO,CAACY,IAAI,CAAC,+DAA+D,CAAC;MAC7E,OAAO,CAAC;;IAGV;IACA,IAAI,CAAC9B,YAAY,CAACE,QAAQ,GAAG,IAAI,CAACM,QAAQ;IAC1C,IAAI,CAACR,YAAY,CAACD,UAAU,GAAG,IAAI,CAACV,kBAAkB,CAAC,CAAC;IAExD,IAAI,CAACQ,mBAAmB,CAAC+B,kBAAkB,CAAC,IAAI,CAAC5B,YAAY,CAAC,CAACa,SAAS,CAAC;MACvEC,IAAI,EAAGiB,QAAQ,IAAI;QACjBb,OAAO,CAACS,GAAG,CAAC,iCAAiC,EAAEI,QAAQ,CAAC;QACxD;QACA,IAAI,CAACjC,MAAM,CAACkC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACnC,CAAC;MACDhB,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEC,GAAG,CAAC;MAClD;KACD,CAAC;EACJ;;;uBAvFWvB,qBAAqB,EAAA7B,EAAA,CAAAoE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtE,EAAA,CAAAoE,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAxE,EAAA,CAAAoE,iBAAA,CAAAC,EAAA,CAAAI,MAAA;IAAA;EAAA;;;YAArB5C,qBAAqB;MAAA6C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlChF,EAAA,CAAAC,cAAA,aAA4B;UACtBD,EAAA,CAAAE,MAAA,kCAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG/BH,EAAA,CAAAkF,UAAA,IAAAC,oCAAA,iBAEM;UAGNnF,EAAA,CAAAC,cAAA,aAAkB;UACUD,EAAA,CAAAE,MAAA,wCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9DH,EAAA,CAAAkF,UAAA,IAAAE,oCAAA,iBAaM;UACRpF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,gBAIC;UAFCD,EAAA,CAAAS,UAAA,mBAAA4E,uDAAA;YAAA,OAASJ,GAAA,CAAAlB,kBAAA,EAAoB;UAAA,EAAC;UAG9B/D,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAkF,UAAA,KAAAI,qCAAA,iBAEM;UACRtF,EAAA,CAAAG,YAAA,EAAM;;;UApCEH,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAsB,UAAA,SAAA2D,GAAA,CAAA1E,MAAA,CAAY;UAOWP,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAsB,UAAA,YAAA2D,GAAA,CAAA/C,UAAA,CAAa;UAoBxClC,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAsB,UAAA,aAAA2D,GAAA,CAAAzD,kBAAA,CAAAwC,MAAA,OAA4C;UAMxChE,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAsB,UAAA,SAAA2D,GAAA,CAAAzD,kBAAA,CAAAwC,MAAA,OAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}