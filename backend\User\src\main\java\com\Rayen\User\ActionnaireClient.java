package com.Rayen.User;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(name="Actionnaire-Service",url="${application.config.actionnaire-url}")
public interface ActionnaireClient {

    @GetMapping("/user/{user-id}")
    List<Actionnaire>findActionnairesbyUser(@PathVariable("user-id") Long userId);
}
