package com.Aziz.Administratif.Services;

import com.Aziz.Administratif.Entity.User;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class EmailService {
    private final com.Aziz.Administratif.Repositories.UserRepository UserRepository;

    private final JavaMailSender mailSender;

    public void sendRegistrationEmail(String to, String username,String password,String matricule) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(to);
            helper.setSubject("Welcome to Our GRA Platform!");
            helper.setText("Hi Dear \n" + username + ",\n\nYour account has been successfully created by ADMIN!\n Your Matricule is "+
                    matricule+ " and your password is '" +password+"' and save it yehdik\n GOOD LUCK "+username, false);

            mailSender.send(message);
          //  throw new RuntimeException("Failed to send email");
        } catch (MessagingException e) {
            throw new RuntimeException("Failed to send email", e);
        }
    }



    public void sendAuthentificationEmail(String to, String username,String ipAddress, String dateTime,String location) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(to);
           // helper.setSubject("Security Alert: New Sign-In Detected");
            helper.setSubject("[GRA] Login Attempted from New IP address "+ipAddress+" - "+dateTime+" (UTC +1)");
            helper.setText("Dear user,\n\n" +username+"\n\n"+
                    "Did You Login From a New Device or Location?\n\n"+
                    "We noticed a new sign-in to your account "+to+ " from the following IP address:\n\n" +
                    "IP Address: " + ipAddress + "\n" +
                    "Date and Time: " + dateTime + "\n" +
                    "Location: " + location + "\n" +
                    "Stay safe,\n" +
                    "Your Security Team "+username, false);

            mailSender.send(message);
            //  throw new RuntimeException("Failed to send email");
        } catch (MessagingException e) {
            throw new RuntimeException("Failed to send email", e);
        }
    }




    public void BlockEmail(String to, String username,Boolean State) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(to);
            helper.setSubject("Welcome to Our GRA Platform!");
            if(State==Boolean.TRUE){
                helper.setText("Hi Dear \n" + username + ",\n\nYour account has been activated by ADMIN!\n ", false);

            }
            else {
                helper.setText("Hi Dear \n" + username + ",\n\nYour account has been Blocked by ADMIN!\n Wait until further notice", false);
            }
            mailSender.send(message);
            //  throw new RuntimeException("Failed to send email");
        } catch (MessagingException e) {
            throw new RuntimeException("Failed to send email", e);
        }
    }









    public void ForgetPasswordUserToAdmin(String ForgetUserEmail, Integer ForgetUserMatricule, User Admin) {



        try {User ForgetUser=UserRepository.findByMatricule(ForgetUserMatricule);
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(Admin.getEmail());
            helper.setSubject("Responsable Forget his Password!");
            helper.setText("Hi Dear Admin\n" + Admin.getNom()+" "+Admin.getPrenom() + ",\n\nThere is a Responsable "+ForgetUser.getNom()+":\n de Matricule "+ForgetUserMatricule +"\nEt de Email "+ForgetUserEmail+"\nhas forget his password!\n ", false);

            mailSender.send(message);
            //  throw new RuntimeException("Failed to send email");
        } catch (MessagingException e) {
            throw new RuntimeException("Failed to send email", e);
        }
    }



    public void ForgetPasswordAdminToUser(String ForgetUserEmail, String NewPassword,User Admin) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(ForgetUserEmail);
            helper.setSubject("Your New Password!");
            helper.setText("Hi Dear \n Your new password is: " + NewPassword +"\nBy Admin : "+Admin.getNom()+" "+Admin.getPrenom()+
                    "\n If there is any problem u can contact him the the follow email :  "+Admin.getEmail()+
                    "\nPlease change it after login.", false);

            mailSender.send(message);
            //  throw new RuntimeException("Failed to send email");
        } catch (MessagingException e) {
            throw new RuntimeException("Failed to send email", e);
        }
    }


}
