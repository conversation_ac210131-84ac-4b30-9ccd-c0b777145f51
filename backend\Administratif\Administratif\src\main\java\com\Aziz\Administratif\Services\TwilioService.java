package com.Aziz.Administratif.Services;

import com.twilio.Twilio;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service  // This will make the class a Spring-managed service
public class TwilioService {

    // Twilio Account SID, Auth Token, and Phone Number directly in the class
    private static final String ACCOUNT_SID = "**********************************";
    private static final String AUTH_TOKEN = "f2926005c778d47b4e32579e03d06975";
    private static final String TWILIO_PHONE_NUMBER = "+***********";

    private static final Logger logger = LoggerFactory.getLogger(TwilioService.class);

    public TwilioService() {
        // Initialize Twilio with your Account SID and Auth Token
        if (ACCOUNT_SID == null || AUTH_TOKEN == null || TWILIO_PHONE_NUMBER == null) {
            logger.error("Twilio credentials are missing.");
            throw new IllegalStateException("Twilio credentials are not configured.");
        }
        Twilio.init(ACCOUNT_SID, AUTH_TOKEN);
    }

    public String sendMessage(String toPhoneNumber, String messageBody) {
        try {
            // Create and send the message
            Message message = Message.creator(
                    new PhoneNumber(toPhoneNumber), // To phone number
                    new PhoneNumber(TWILIO_PHONE_NUMBER), // From phone number (use your Twilio number)
                    messageBody
            ).create();

            // Return the SID of the sent message for confirmation
            return message.getSid();
        } catch (Exception e) {
            logger.error("Failed to send message", e);
            throw new RuntimeException("Failed to send message", e);
        }
    }
}
