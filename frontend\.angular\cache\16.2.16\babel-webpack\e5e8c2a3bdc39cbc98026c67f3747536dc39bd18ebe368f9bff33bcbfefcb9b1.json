{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./authentication.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(next, state) {\n    const token = this.authService.getToken();\n    console.log(\"Token retrieved from storage:\", token);\n    if (!token || this.isTokenExpired(token)) {\n      console.warn(\"No token found or token expired. Logging out...\");\n      this.authService.logout();\n      return false;\n    }\n    const {\n      role,\n      groupe\n    } = this.authService.getUserDetailsFromToken(token);\n    console.log(\"User role from token:\", role, \"Group:\", groupe);\n    const currentPath = state.url.split('?')[0];\n    console.log(\"Current path (trimmed):\", currentPath);\n    // Check for Admin role for /adminDash route\n    if (currentPath === '/adminDash' && role.trim().toUpperCase() !== 'ADMIN') {\n      console.warn(\"Access denied: Admins only for /adminDash\");\n      this.router.navigate(['/not-authorized']); // Redirect to Not Authorized page\n      return false;\n    }\n    // Continue with group-based access control\n    const allowedGroups = {\n      '/transactions': 'TRANSACTION',\n      '/actions': 'ACTION',\n      '/actionnaires': 'ACTIONNAIRE',\n      '/reports': 'NOTIFICATION',\n      '/port': 'PORTEFEUILLE',\n      '/ResDash': 'RESPONSABLE'\n    };\n    const expectedGroup = allowedGroups[currentPath];\n    console.log(\"Expected group:\", expectedGroup);\n    if (expectedGroup && groupe.trim().toUpperCase() !== expectedGroup) {\n      console.warn(\"Access denied for group:\", groupe);\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n    return true;\n  }\n  isTokenExpired(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const expiryTime = payload.exp * 1000;\n      return Date.now() > expiryTime;\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return true;\n    }\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthenticationService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "next", "state", "token", "getToken", "console", "log", "isTokenExpired", "warn", "logout", "role", "groupe", "getUserDetailsFromToken", "currentPath", "url", "split", "trim", "toUpperCase", "navigate", "allowedGroups", "expectedGroup", "payload", "JSON", "parse", "atob", "expiryTime", "exp", "Date", "now", "e", "error", "i0", "ɵɵinject", "i1", "AuthenticationService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { AuthenticationService } from './authentication.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {\n    const token = this.authService.getToken();\n    console.log(\"Token retrieved from storage:\", token);\n\n    if (!token || this.isTokenExpired(token)) {\n      console.warn(\"No token found or token expired. Logging out...\");\n      this.authService.logout();\n      return false;\n    }\n\n    const { role, groupe } = this.authService.getUserDetailsFromToken(token);\n    console.log(\"User role from token:\", role, \"Group:\", groupe);\n\n    const currentPath = state.url.split('?')[0];\n    console.log(\"Current path (trimmed):\", currentPath);\n\n    // Check for Admin role for /adminDash route\n    if (currentPath === '/adminDash' && role.trim().toUpperCase() !== 'ADMIN') {\n      console.warn(\"Access denied: Admins only for /adminDash\");\n      this.router.navigate(['/not-authorized']);  // Redirect to Not Authorized page\n      return false;\n    }\n\n    // Continue with group-based access control\n    const allowedGroups: Record<string, string> = {\n      '/transactions': 'TRANSACTION',\n      '/actions': 'ACTION',\n      '/actionnaires': 'ACTIONNAIRE',\n      '/reports': 'NOTIFICATION',\n      '/port': 'PORTEFEUILLE',\n      '/ResDash': 'RESPONSABLE'\n    };\n\n    const expectedGroup = allowedGroups[currentPath];\n    console.log(\"Expected group:\", expectedGroup);\n\n    if (expectedGroup && groupe.trim().toUpperCase() !== expectedGroup) {\n      console.warn(\"Access denied for group:\", groupe);\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n\n    return true;\n  }\n\n  private isTokenExpired(token: string): boolean {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); \n      const expiryTime = payload.exp * 1000;\n      return Date.now() > expiryTime;\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return true;\n    }\n  }\n}\n"], "mappings": ";;;AAOA,OAAM,MAAOA,SAAS;EAEpBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;EAAW;EAEjFC,WAAWA,CAACC,IAA4B,EAAEC,KAA0B;IAClE,MAAMC,KAAK,GAAG,IAAI,CAACL,WAAW,CAACM,QAAQ,EAAE;IACzCC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,KAAK,CAAC;IAEnD,IAAI,CAACA,KAAK,IAAI,IAAI,CAACI,cAAc,CAACJ,KAAK,CAAC,EAAE;MACxCE,OAAO,CAACG,IAAI,CAAC,iDAAiD,CAAC;MAC/D,IAAI,CAACV,WAAW,CAACW,MAAM,EAAE;MACzB,OAAO,KAAK;;IAGd,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAE,GAAG,IAAI,CAACb,WAAW,CAACc,uBAAuB,CAACT,KAAK,CAAC;IACxEE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,IAAI,EAAE,QAAQ,EAAEC,MAAM,CAAC;IAE5D,MAAME,WAAW,GAAGX,KAAK,CAACY,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3CV,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEO,WAAW,CAAC;IAEnD;IACA,IAAIA,WAAW,KAAK,YAAY,IAAIH,IAAI,CAACM,IAAI,EAAE,CAACC,WAAW,EAAE,KAAK,OAAO,EAAE;MACzEZ,OAAO,CAACG,IAAI,CAAC,2CAA2C,CAAC;MACzD,IAAI,CAACT,MAAM,CAACmB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAE;MAC5C,OAAO,KAAK;;IAGd;IACA,MAAMC,aAAa,GAA2B;MAC5C,eAAe,EAAE,aAAa;MAC9B,UAAU,EAAE,QAAQ;MACpB,eAAe,EAAE,aAAa;MAC9B,UAAU,EAAE,cAAc;MAC1B,OAAO,EAAE,cAAc;MACvB,UAAU,EAAE;KACb;IAED,MAAMC,aAAa,GAAGD,aAAa,CAACN,WAAW,CAAC;IAChDR,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEc,aAAa,CAAC;IAE7C,IAAIA,aAAa,IAAIT,MAAM,CAACK,IAAI,EAAE,CAACC,WAAW,EAAE,KAAKG,aAAa,EAAE;MAClEf,OAAO,CAACG,IAAI,CAAC,0BAA0B,EAAEG,MAAM,CAAC;MAChD,IAAI,CAACZ,MAAM,CAACmB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;MACzC,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEQX,cAAcA,CAACJ,KAAa;IAClC,IAAI;MACF,MAAMkB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACrB,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMU,UAAU,GAAGJ,OAAO,CAACK,GAAG,GAAG,IAAI;MACrC,OAAOC,IAAI,CAACC,GAAG,EAAE,GAAGH,UAAU;KAC/B,CAAC,OAAOI,CAAC,EAAE;MACVxB,OAAO,CAACyB,KAAK,CAAC,uBAAuB,EAAED,CAAC,CAAC;MACzC,OAAO,IAAI;;EAEf;;;uBA1DWjC,SAAS,EAAAmC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATxC,SAAS;MAAAyC,OAAA,EAATzC,SAAS,CAAA0C,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}