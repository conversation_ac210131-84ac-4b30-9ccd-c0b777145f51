{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { JwtInterceptor } from './auth/jwt.interceptor';\nimport { AppRoutingModule } from './app-routing.module';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\nimport { AdminDashComponent } from './admin-dash/admin-dash.component';\nimport { ResponsableEditComponent } from './responsable-edit/responsable-edit.component';\nimport { UsersComponent } from './users/users.component';\nimport { GroupsComponent } from './groups/groups.component';\nimport { ModifyGroupComponent } from './modify-group/modify-group.component';\nimport { AddResponsableComponent } from './add-responsable/add-responsable.component';\nimport { HabilitationComponent } from './habilitation/habilitation.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: JwtInterceptor,\n        multi: true\n      }],\n      imports: [BrowserModule, FormsModule, HttpClientModule, AppRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, LoginComponent, SignupComponent, ResponDashboardComponent, NotAuthorizedComponent, TransactionsComponent, ActionsComponent, ActionnairesComponent, ReportsComponent, PortefeuillesComponent, AdminDashComponent, ResponsableEditComponent, UsersComponent, GroupsComponent, ModifyGroupComponent, AddResponsableComponent, HabilitationComponent],\n    imports: [BrowserModule, FormsModule, HttpClientModule, AppRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "AppComponent", "LoginComponent", "SignupComponent", "JwtInterceptor", "AppRoutingModule", "ResponDashboardComponent", "NotAuthorizedComponent", "TransactionsComponent", "ActionsComponent", "ActionnairesComponent", "ReportsComponent", "PortefeuillesComponent", "AdminDashComponent", "ResponsableEditComponent", "UsersComponent", "GroupsComponent", "ModifyGroupComponent", "AddResponsableComponent", "HabilitationComponent", "AppModule", "bootstrap", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { JwtInterceptor } from './auth/jwt.interceptor'; \nimport { AppRoutingModule } from './app-routing.module';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\nimport { AdminDashComponent } from './admin-dash/admin-dash.component';\nimport { ResponsableEditComponent } from './responsable-edit/responsable-edit.component';\nimport { UsersComponent } from './users/users.component';\nimport { GroupsComponent } from './groups/groups.component';\nimport { ModifyGroupComponent } from './modify-group/modify-group.component';\nimport { AddResponsableComponent } from './add-responsable/add-responsable.component';\nimport { HabilitationComponent } from './habilitation/habilitation.component';\n\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    SignupComponent,\n    ResponDashboardComponent,\n    NotAuthorizedComponent,\n    TransactionsComponent,\n    ActionsComponent,\n    ActionnairesComponent,\n    ReportsComponent,\n    PortefeuillesComponent,\n    AdminDashComponent,\n    ResponsableEditComponent,\n    UsersComponent,\n    GroupsComponent,\n    ModifyGroupComponent,\n    AddResponsableComponent,\n    HabilitationComponent,\n  \n    \n  ],\n  imports: [\n    BrowserModule,\n    FormsModule,\n    HttpClientModule,\n    AppRoutingModule,\n  \n     // Include it here\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: JwtInterceptor,\n      multi: true\n    }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,qBAAqB,QAAQ,uCAAuC;;AA0C7E,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRpB,YAAY;IAAA;EAAA;;;iBAPb,CACT;QACEqB,OAAO,EAAEtB,iBAAiB;QAC1BuB,QAAQ,EAAEnB,cAAc;QACxBoB,KAAK,EAAE;OACR,CACF;MAAAC,OAAA,GAbC5B,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChBM,gBAAgB;IAAA;EAAA;;;2EAaPe,SAAS;IAAAM,YAAA,GArClBzB,YAAY,EACZC,cAAc,EACdC,eAAe,EACfG,wBAAwB,EACxBC,sBAAsB,EACtBC,qBAAqB,EACrBC,gBAAgB,EAChBC,qBAAqB,EACrBC,gBAAgB,EAChBC,sBAAsB,EACtBC,kBAAkB,EAClBC,wBAAwB,EACxBC,cAAc,EACdC,eAAe,EACfC,oBAAoB,EACpBC,uBAAuB,EACvBC,qBAAqB;IAAAM,OAAA,GAKrB5B,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChBM,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}