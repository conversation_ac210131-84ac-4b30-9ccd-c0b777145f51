import { AfterViewInit, Component, ElementRef, ViewChild, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import * as feather from 'feather-icons';
import { Action } from '../model/action.model';
import { ActionService } from '../services/action.service';
import * as bootstrap from 'bootstrap';
import { AuthenticationService } from '../auth/authentication.service';

@Component({
  selector: 'app-actions',
  templateUrl: './actions.component.html',
  styleUrls: ['./actions.component.css']
})
export class ActionsComponent implements AfterViewInit, OnInit {
  @ViewChild('myChart') myChartRef!: ElementRef;
  actions: Action[] = [];

  newAction: Partial<Action> = {
    isinAction: '',
    nomSociete: '',
    prix: 0
  };

  constructor(
    private router: Router,
    private actionService: ActionService,
    public authService: AuthenticationService
  ) {}

  ngOnInit(): void {
    this.loadActions();
  }

  ngAfterViewInit() {
    feather.replace(); // Initialize feather icons
  }

  loadActions(): void {
    this.actionService.getAllActions().subscribe({
      next: (data) => {
        this.actions = data;
        console.log('Actions loaded:', this.actions);
      },
      error: (err) => {
        console.error('Erreur lors du chargement des actions :', err);
      }
    });
  }

  logout() {
    localStorage.removeItem('jwt_token');
    localStorage.removeItem('role');
    localStorage.removeItem('group');
    console.log(localStorage.getItem('jwt_token'));
    this.router.navigate(['/login']);
  }




  deleteAction(id: number): void {
    // Trouver l'action à supprimer pour afficher son nom
    const actionToDelete = this.actions.find(a => a.idAction === id);
    const actionName = actionToDelete ? actionToDelete.nomSociete : 'cette action';

    if (confirm(`Êtes-vous sûr de vouloir supprimer l'action "${actionName}" ?\n\nCette action est irréversible.`)) {
      console.log(`Tentative de suppression de l'action ID: ${id}`);

      this.actionService.deleteActionById(id).subscribe({
        next: (response) => {
          console.log('Réponse de suppression:', response);
          // Supprimer l'action de la liste locale
          this.actions = this.actions.filter(a => a.idAction !== id);
          alert(`Action "${actionName}" supprimée avec succès !`);
        },
        error: (err) => {
          console.error("Erreur lors de la suppression :", err);
          if (err.status === 404) {
            alert("Action non trouvée. Elle a peut-être déjà été supprimée.");
          } else if (err.status === 403) {
            alert("Vous n'avez pas les permissions pour supprimer cette action.");
          } else {
            alert(`Erreur lors de la suppression de l'action "${actionName}". Veuillez réessayer.`);
          }
        }
      });
    }
  }
submitAction(): void {
  const payload = {
    isinAction: this.newAction.isinAction,
    nomSociete: this.newAction.nomSociete,
    prix: this.newAction.prix
  };

  this.actionService.addAction(payload).subscribe({
    next: (response) => {
      console.log('Action ajoutée:', response);

      // Manually add the new action since the backend doesn't return it
      const newActionToAdd: Action = {
        idAction: 0, // or any placeholder if ID is generated
        isinAction: this.newAction.isinAction!,
        nomSociete: this.newAction.nomSociete!,
        prix: this.newAction.prix!,
        dateCreation: new Date().toISOString(), // Just a placeholder
        portefeuilles: []
      };

      this.actions.push(newActionToAdd);

      this.newAction = {}; // Clear the form
    },
    error: (err) => {
      if (err && err.error) {
        console.error('Error details:', err.error);
      } else {
        console.error('Unexpected error format:', err);
      }

      alert("Erreur lors de l'ajout de l'action !");
    }
  });
}
}