{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, tap, throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class GroupeService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth/Groupes'; // Backend URL\n  }\n  // Get all groupes\n  getAllGroupes() {\n    return this.http.get(`${this.apiUrl}/ALLGroupes`, {\n      headers: this.getHeaders()\n    });\n  }\n  // Get a groupe by ID\n  getGroupeById(id) {\n    return this.http.get(`${this.apiUrl}/find/${id}`).pipe(tap(data => console.log('Group data fetched:', data), error => {\n      console.error('Error fetching group:', error);\n    }));\n  }\n  // Add a new groupe\n  addGroupe(groupe) {\n    return this.http.post(`${this.apiUrl}/ajout`, groupe, {\n      headers: this.getHeaders()\n    });\n  }\n  getGroupeByIdFromBackend(groupeId) {\n    return this.http.get(`${this.apiUrl}/${groupeId}`, {\n      headers: this.getHeaders()\n    });\n  }\n  // Update an existing groupe\n  updateGroupe(id, groupe) {\n    const url = `${this.apiUrl}/edit/${id}`; // Ensure the correct URL format without extra \"Groupes\"\n    console.log('Sending request to URL:', url); // Debug log to check the URL\n    console.log('Payload:', groupe); // Log the data you're sending\n    return this.http.put(url, groupe, {\n      headers: this.getHeaders()\n    }).pipe(tap(updatedGroupe => {\n      console.log('Group updated successfully:', updatedGroupe); // Log the updated response\n    }, error => {\n      console.error('Error updating group:', error); // Log error if any\n    }));\n  }\n  // Delete a groupe\n  deleteGroupe(id) {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, {\n      responseType: 'text'\n    }) // Force response as text\n    .pipe(catchError(error => {\n      console.error('Error deleting group:', error);\n      return throwError(error); // Re-throw the error so we can handle it in the component\n    }));\n  }\n  // Helper function to get headers with Authorization\n  getHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.error('No token found!');\n      throw new Error('No token found!');\n    }\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`\n    });\n  }\n  static {\n    this.ɵfac = function GroupeService_Factory(t) {\n      return new (t || GroupeService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GroupeService,\n      factory: GroupeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "catchError", "tap", "throwError", "GroupeService", "constructor", "http", "apiUrl", "getAllGroupes", "get", "headers", "getHeaders", "getGroupeById", "id", "pipe", "data", "console", "log", "error", "addGroupe", "groupe", "post", "getGroupeByIdFromBackend", "groupeId", "updateGroupe", "url", "put", "updatedGroupe", "deleteGroupe", "delete", "responseType", "token", "localStorage", "getItem", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\services\\groupe.service.ts"], "sourcesContent": ["// services/groupe.service.ts\nimport { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { catchError, Observable, tap, throwError } from 'rxjs';\nimport { Groupe } from '../model/groupe.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class GroupeService {\n  private apiUrl = 'http://localhost:8000/api/v1/auth/Groupes'; // Backend URL\n\n  constructor(private http: HttpClient) {}\n\n  \n\n  // Get all groupes\n  getAllGroupes(): Observable<Groupe[]> {\n    return this.http.get<Groupe[]>(`${this.apiUrl}/ALLGroupes`, {\n      headers: this.getHeaders(),\n    });\n  }\n\n  // Get a groupe by ID\n  getGroupeById(id: number): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/find/${id}`).pipe(\n      tap(\n        (data: any) => console.log('Group data fetched:', data),\n        (error: any) => {\n          console.error('Error fetching group:', error);\n        }\n      )\n    );\n  }\n  \n  \n  // Add a new groupe\n  addGroupe(groupe: Groupe): Observable<Groupe> {\n    return this.http.post<Groupe>(`${this.apiUrl}/ajout`, groupe, {\n      headers: this.getHeaders(),\n    });\n  }\n\n  getGroupeByIdFromBackend(groupeId: number): Observable<Groupe> {\n    return this.http.get<Groupe>(`${this.apiUrl}/${groupeId}`, {\n      headers: this.getHeaders(),\n    });\n  }\n\n  // Update an existing groupe\n  updateGroupe(id: number, groupe: Groupe): Observable<Groupe> {\n    const url = `${this.apiUrl}/edit/${id}`; // Ensure the correct URL format without extra \"Groupes\"\n    console.log('Sending request to URL:', url);  // Debug log to check the URL\n    console.log('Payload:', groupe);  // Log the data you're sending\n  \n    return this.http.put<Groupe>(url, groupe, {\n      headers: this.getHeaders(),\n    }).pipe(\n      tap(\n        (updatedGroupe) => {\n          console.log('Group updated successfully:', updatedGroupe);  // Log the updated response\n        },\n        (error) => {\n          console.error('Error updating group:', error);  // Log error if any\n        }\n      )\n    );\n  }\n  \n  \n  // Delete a groupe\n  deleteGroupe(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, { responseType: 'text' }) // Force response as text\n      .pipe(\n        catchError((error) => {\n          console.error('Error deleting group:', error);\n          return throwError(error); // Re-throw the error so we can handle it in the component\n        })\n      );\n  }\n  \n\n  // Helper function to get headers with Authorization\n  private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.error('No token found!');\n      throw new Error('No token found!');\n    }\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n    });\n  }\n  \n  }\n\n"], "mappings": "AAEA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAcC,GAAG,EAAEC,UAAU,QAAQ,MAAM;;;AAM9D,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,2CAA2C,CAAC,CAAC;EAEvB;EAIvC;EACAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAW,GAAG,IAAI,CAACF,MAAM,aAAa,EAAE;MAC1DG,OAAO,EAAE,IAAI,CAACC,UAAU;KACzB,CAAC;EACJ;EAEA;EACAC,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACP,IAAI,CAACG,GAAG,CAAM,GAAG,IAAI,CAACF,MAAM,SAASM,EAAE,EAAE,CAAC,CAACC,IAAI,CACzDZ,GAAG,CACAa,IAAS,IAAKC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,IAAI,CAAC,EACtDG,KAAU,IAAI;MACbF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,CACF,CACF;EACH;EAGA;EACAC,SAASA,CAACC,MAAc;IACtB,OAAO,IAAI,CAACd,IAAI,CAACe,IAAI,CAAS,GAAG,IAAI,CAACd,MAAM,QAAQ,EAAEa,MAAM,EAAE;MAC5DV,OAAO,EAAE,IAAI,CAACC,UAAU;KACzB,CAAC;EACJ;EAEAW,wBAAwBA,CAACC,QAAgB;IACvC,OAAO,IAAI,CAACjB,IAAI,CAACG,GAAG,CAAS,GAAG,IAAI,CAACF,MAAM,IAAIgB,QAAQ,EAAE,EAAE;MACzDb,OAAO,EAAE,IAAI,CAACC,UAAU;KACzB,CAAC;EACJ;EAEA;EACAa,YAAYA,CAACX,EAAU,EAAEO,MAAc;IACrC,MAAMK,GAAG,GAAG,GAAG,IAAI,CAAClB,MAAM,SAASM,EAAE,EAAE,CAAC,CAAC;IACzCG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEQ,GAAG,CAAC,CAAC,CAAE;IAC9CT,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEG,MAAM,CAAC,CAAC,CAAE;IAElC,OAAO,IAAI,CAACd,IAAI,CAACoB,GAAG,CAASD,GAAG,EAAEL,MAAM,EAAE;MACxCV,OAAO,EAAE,IAAI,CAACC,UAAU;KACzB,CAAC,CAACG,IAAI,CACLZ,GAAG,CACAyB,aAAa,IAAI;MAChBX,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEU,aAAa,CAAC,CAAC,CAAE;IAC9D,CAAC,EACAT,KAAK,IAAI;MACRF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC,CAAC,CAAE;IAClD,CAAC,CACF,CACF;EACH;EAGA;EACAU,YAAYA,CAACf,EAAU;IACrB,OAAO,IAAI,CAACP,IAAI,CAACuB,MAAM,CAAC,GAAG,IAAI,CAACtB,MAAM,WAAWM,EAAE,EAAE,EAAE;MAAEiB,YAAY,EAAE;IAAM,CAAE,CAAC,CAAC;IAAA,CAC9EhB,IAAI,CACHb,UAAU,CAAEiB,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAOf,UAAU,CAACe,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CACH;EACL;EAGA;EACQP,UAAUA,CAAA;IAChB,MAAMoB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVf,OAAO,CAACE,KAAK,CAAC,iBAAiB,CAAC;MAChC,MAAM,IAAIgB,KAAK,CAAC,iBAAiB,CAAC;;IAEpC,OAAO,IAAIlC,WAAW,CAAC;MACrB,eAAe,EAAE,UAAU+B,KAAK;KACjC,CAAC;EACJ;;;uBAnFW3B,aAAa,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAblC,aAAa;MAAAmC,OAAA,EAAbnC,aAAa,CAAAoC,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}