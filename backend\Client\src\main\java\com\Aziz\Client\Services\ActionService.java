package com.Aziz.Client.Services;


import com.Aziz.Client.Entity.Action;
import com.Aziz.Client.Entity.Actionnaire;
import com.Aziz.Client.Entity.Portefeuille;
import com.Aziz.Client.Repositories.ActionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ActionService {

    @Autowired
    private ActionRepository ActionRepository;



    public Optional<Action> findById(Long idAction){
        return ActionRepository.findById(idAction);
    }


    public Action findByISIN(String ISINAction){
        return ActionRepository.findByIsinAction(ISINAction);
    }

    public void saveAction(Action action){
        Action action1=ActionRepository.findByIsinAction(action.getIsinAction());
        if(action1!=null){
            throw new RuntimeException("Action exist");
        }
        else{
            ActionRepository.save(action);
        }
    }


    public List<Action> findAllActions(){
        return ActionRepository.findAll();
    }


    public void deleteAction(Long idAction) {
        ActionRepository.deleteById(idAction);
    }










    public Action updateAction(Long idAction, Action updatedAction) {
        return ActionRepository.findById(idAction).map(existingAction -> {
            // Update basic user details
            existingAction.setPrix(updatedAction.getPrix());
            existingAction.setNomSociete(updatedAction.getNomSociete());

            Action savedAction = ActionRepository.save(existingAction);


            return savedAction;

        }).orElseThrow(() -> new RuntimeException("User non"));
    }



    public List<Portefeuille> getPortefeuillesByActionId(Long idAction) {
        return ActionRepository.findById(idAction)
                .map(Action::getPortefeuilles)
                .orElseThrow(() -> new RuntimeException("Action not found with ID: " + idAction));
    }



    public List<Actionnaire> getActionnairesByActionId(Long idAction) {
        return ActionRepository.findById(idAction)
                .map(Action::getActionnaires)
                .orElseThrow(() -> new RuntimeException("Action not found with ID: " + idAction));
    }

}
