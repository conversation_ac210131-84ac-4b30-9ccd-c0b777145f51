{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./authentication.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(next, state) {\n    const token = this.authService.getToken();\n    console.log(\"Token retrieved from storage:\", token);\n    if (!token || this.isTokenExpired(token)) {\n      console.warn(\"No token found or token expired. Logging out...\");\n      this.authService.logout();\n      return false;\n    }\n    const {\n      role,\n      groupe\n    } = this.authService.getUserDetailsFromToken(token);\n    console.log(\"User role from token:\", role);\n    console.log(\"Group from token:\", groupe); // Check the group type and content\n    // Ensure groupe is an object and has the expected property\n    if (typeof groupe !== 'object' || !groupe.nomGroupe) {\n      console.error(\"Invalid groupe object:\", groupe);\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n    const currentPath = state.url.split('?')[0];\n    console.log(\"Current path (trimmed):\", currentPath);\n    // Get allowed paths from local storage\n    const allowedPaths = this.authService.getAllowedPaths();\n    console.log(\"Allowed paths from local storage:\", allowedPaths);\n    if (allowedPaths.includes(currentPath)) {\n      console.log(\"Access granted to path:\", currentPath);\n      return true;\n    }\n    if (role.trim().toUpperCase() === 'ADMIN') {\n      console.log(\"Admin user detected, access granted.\");\n      return true;\n    }\n    if (currentPath === '/adminDash') {\n      console.warn(\"Access denied: Responsables cannot access /adminDash\");\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n    if (currentPath === '/users' || currentPath === '/groups') {\n      console.warn(\"Access denied: Only Admin can access\", currentPath);\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n    const allowedGroups = {\n      '/transactions': 'TRANSACTION',\n      '/actions': 'ACTION',\n      '/actionnaires': 'FD',\n      '/reports': 'NOTIFICATION',\n      '/port': 'PORTEFEUILLE',\n      '/ResDash': 'RESPONSABLE'\n    };\n    const expectedGroup = allowedGroups[currentPath];\n    console.log(\"Expected group:\", expectedGroup);\n    // Fix: Access 'nomGroupe' from 'groupe' and compare it\n    if (expectedGroup && groupe && groupe.nomGroupe.trim().toUpperCase() !== expectedGroup) {\n      console.warn(\"Access denied for group:\", groupe.nomGroupe);\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n    return true;\n  }\n  isTokenExpired(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const expiryTime = payload.exp * 1000;\n      return Date.now() > expiryTime;\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return true;\n    }\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthenticationService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "next", "state", "token", "getToken", "console", "log", "isTokenExpired", "warn", "logout", "role", "groupe", "getUserDetailsFromToken", "nomGroupe", "error", "navigate", "currentPath", "url", "split", "allowedPaths", "getAllowedPaths", "includes", "trim", "toUpperCase", "allowedGroups", "expectedGroup", "payload", "JSON", "parse", "atob", "expiryTime", "exp", "Date", "now", "e", "i0", "ɵɵinject", "i1", "AuthenticationService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\auth\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { AuthenticationService } from './authentication.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {\n    const token = this.authService.getToken();\n    console.log(\"Token retrieved from storage:\", token);\n\n    if (!token || this.isTokenExpired(token)) {\n      console.warn(\"No token found or token expired. Logging out...\");\n      this.authService.logout();\n      return false;\n    }\n\n    const { role, groupe } = this.authService.getUserDetailsFromToken(token);\n    console.log(\"User role from token:\", role);\n    console.log(\"Group from token:\", groupe);  // Check the group type and content\n\n    // Ensure groupe is an object and has the expected property\n    if (typeof groupe !== 'object' || !groupe.nomGroupe) {\n      console.error(\"Invalid groupe object:\", groupe);\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n\n    const currentPath = state.url.split('?')[0];\n    console.log(\"Current path (trimmed):\", currentPath);\n\n    // Get allowed paths from local storage\n    const allowedPaths = this.authService.getAllowedPaths();\n    console.log(\"Allowed paths from local storage:\", allowedPaths);\n\n    if (allowedPaths.includes(currentPath)) {\n      console.log(\"Access granted to path:\", currentPath);\n      return true;\n    }\n\n    if (role.trim().toUpperCase() === 'ADMIN') {\n      console.log(\"Admin user detected, access granted.\");\n      return true;\n    }\n\n    if (currentPath === '/adminDash') {\n      console.warn(\"Access denied: Responsables cannot access /adminDash\");\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n\n    if (currentPath === '/users' || currentPath === '/groups') {\n      console.warn(\"Access denied: Only Admin can access\", currentPath);\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n\n    const allowedGroups: Record<string, string> = {\n      '/transactions': 'TRANSACTION',\n      '/actions': 'ACTION',\n      '/actionnaires': 'FD',\n      '/reports': 'NOTIFICATION',\n      '/port': 'PORTEFEUILLE',\n      '/ResDash': 'RESPONSABLE'\n    };\n\n    const expectedGroup = allowedGroups[currentPath];\n    console.log(\"Expected group:\", expectedGroup);\n\n    // Fix: Access 'nomGroupe' from 'groupe' and compare it\n    if (expectedGroup && groupe && groupe.nomGroupe.trim().toUpperCase() !== expectedGroup) {\n      console.warn(\"Access denied for group:\", groupe.nomGroupe);\n      this.router.navigate(['/not-authorized']);\n      return false;\n    }\n\n    return true;\n  }\n\n  private isTokenExpired(token: string): boolean {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const expiryTime = payload.exp * 1000;\n      return Date.now() > expiryTime;\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return true;\n    }\n  }\n}\n"], "mappings": ";;;AAOA,OAAM,MAAOA,SAAS;EAEpBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;EAAW;EAEjFC,WAAWA,CAACC,IAA4B,EAAEC,KAA0B;IAClE,MAAMC,KAAK,GAAG,IAAI,CAACL,WAAW,CAACM,QAAQ,EAAE;IACzCC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,KAAK,CAAC;IAEnD,IAAI,CAACA,KAAK,IAAI,IAAI,CAACI,cAAc,CAACJ,KAAK,CAAC,EAAE;MACxCE,OAAO,CAACG,IAAI,CAAC,iDAAiD,CAAC;MAC/D,IAAI,CAACV,WAAW,CAACW,MAAM,EAAE;MACzB,OAAO,KAAK;;IAGd,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAE,GAAG,IAAI,CAACb,WAAW,CAACc,uBAAuB,CAACT,KAAK,CAAC;IACxEE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,IAAI,CAAC;IAC1CL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEK,MAAM,CAAC,CAAC,CAAE;IAE3C;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,CAACA,MAAM,CAACE,SAAS,EAAE;MACnDR,OAAO,CAACS,KAAK,CAAC,wBAAwB,EAAEH,MAAM,CAAC;MAC/C,IAAI,CAACZ,MAAM,CAACgB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;MACzC,OAAO,KAAK;;IAGd,MAAMC,WAAW,GAAGd,KAAK,CAACe,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3Cb,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEU,WAAW,CAAC;IAEnD;IACA,MAAMG,YAAY,GAAG,IAAI,CAACrB,WAAW,CAACsB,eAAe,EAAE;IACvDf,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEa,YAAY,CAAC;IAE9D,IAAIA,YAAY,CAACE,QAAQ,CAACL,WAAW,CAAC,EAAE;MACtCX,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEU,WAAW,CAAC;MACnD,OAAO,IAAI;;IAGb,IAAIN,IAAI,CAACY,IAAI,EAAE,CAACC,WAAW,EAAE,KAAK,OAAO,EAAE;MACzClB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,OAAO,IAAI;;IAGb,IAAIU,WAAW,KAAK,YAAY,EAAE;MAChCX,OAAO,CAACG,IAAI,CAAC,sDAAsD,CAAC;MACpE,IAAI,CAACT,MAAM,CAACgB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;MACzC,OAAO,KAAK;;IAGd,IAAIC,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,SAAS,EAAE;MACzDX,OAAO,CAACG,IAAI,CAAC,sCAAsC,EAAEQ,WAAW,CAAC;MACjE,IAAI,CAACjB,MAAM,CAACgB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;MACzC,OAAO,KAAK;;IAGd,MAAMS,aAAa,GAA2B;MAC5C,eAAe,EAAE,aAAa;MAC9B,UAAU,EAAE,QAAQ;MACpB,eAAe,EAAE,IAAI;MACrB,UAAU,EAAE,cAAc;MAC1B,OAAO,EAAE,cAAc;MACvB,UAAU,EAAE;KACb;IAED,MAAMC,aAAa,GAAGD,aAAa,CAACR,WAAW,CAAC;IAChDX,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEmB,aAAa,CAAC;IAE7C;IACA,IAAIA,aAAa,IAAId,MAAM,IAAIA,MAAM,CAACE,SAAS,CAACS,IAAI,EAAE,CAACC,WAAW,EAAE,KAAKE,aAAa,EAAE;MACtFpB,OAAO,CAACG,IAAI,CAAC,0BAA0B,EAAEG,MAAM,CAACE,SAAS,CAAC;MAC1D,IAAI,CAACd,MAAM,CAACgB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;MACzC,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEQR,cAAcA,CAACJ,KAAa;IAClC,IAAI;MACF,MAAMuB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC1B,KAAK,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMY,UAAU,GAAGJ,OAAO,CAACK,GAAG,GAAG,IAAI;MACrC,OAAOC,IAAI,CAACC,GAAG,EAAE,GAAGH,UAAU;KAC/B,CAAC,OAAOI,CAAC,EAAE;MACV7B,OAAO,CAACS,KAAK,CAAC,uBAAuB,EAAEoB,CAAC,CAAC;MACzC,OAAO,IAAI;;EAEf;;;uBArFWtC,SAAS,EAAAuC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAT5C,SAAS;MAAA6C,OAAA,EAAT7C,SAAS,CAAA8C,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}