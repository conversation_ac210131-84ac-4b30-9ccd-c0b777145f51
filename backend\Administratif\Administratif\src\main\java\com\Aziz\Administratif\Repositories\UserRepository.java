package com.Aziz.Administratif.Repositories;

import com.Aziz.Administratif.Entity.User;
import com.Aziz.Administratif.Enum.Role;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface UserRepository extends JpaRepository<User,Long>{
    Optional<User> findByEmail(String email);
    List<User> findAllByRole(Role role);
    void deleteById(Long id);



    User findByMatricule(Integer matricule);

//    User FindByEmail(String email);

}
