{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/groupe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = function (a1) {\n  return [\"/edit-groupe\", a1];\n};\nfunction GroupsComponent_tr_93_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"a\", 61);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_tr_93_Template_a_click_6_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const groupe_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(groupe_r1.idGroupe ? ctx_r2.deleteGroupe(groupe_r1.idGroupe) : null);\n    });\n    i0.ɵɵelementStart(7, \"i\", 62);\n    i0.ɵɵtext(8, \"\\uE872\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"a\", 63)(10, \"i\", 64);\n    i0.ɵɵtext(11, \"\\uE254\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const groupe_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r1.idGroupe || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r1.nomGroupe);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, groupe_r1.idGroupe));\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, .25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] { border-top: 1px solid #e5e5e5; }\\n.border-bottom[_ngcontent-%COMP%] { border-bottom: 1px solid #e5e5e5; }\\n\\n\\n.btn[_ngcontent-%COMP%] {\\npadding: 8px 16px;\\nfont-size: 14px;\\nborder: none;\\ncursor: pointer;\\nborder-radius: 4px;\\nfont-weight: bold;\\ntransition: all 0.3s ease;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\nbackground-color: #ff4d4d; \\n\\ncolor: white;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%]:hover {\\nbackground-color: #ff1a1a;\\n}\\n\\n.modify-btn[_ngcontent-%COMP%] {\\nbackground-color: #4d94ff; \\n\\ncolor: white;\\n}\\n\\n.modify-btn[_ngcontent-%COMP%]:hover {\\nbackground-color: #0066cc;\\n}\\n\\n\\n\\n.edit-icon[_ngcontent-%COMP%] {\\ncolor: #0b7defe2; \\n\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\ncolor: #F44336; \\n\\n}\\n\\n.modal[_ngcontent-%COMP%]   .modal-dialog[_ngcontent-%COMP%] {\\nmax-width: 400px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%], .modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%], .modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\\npadding: 20px 30px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\nborder-radius: 3px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\\nbackground: #ecf0f1;\\nborder-radius: 0 0 3px 3px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n    display: inline-block;\\n}\\n.modal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\nborder-radius: 2px;\\nbox-shadow: none;\\nborder-color: #dddddd;\\n}\\n.modal[_ngcontent-%COMP%]   textarea.form-control[_ngcontent-%COMP%] {\\nresize: vertical;\\n}\\n.modal[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\nborder-radius: 2px;\\nmin-width: 100px;\\n}\\t\\n.modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\nfont-weight: normal;\\n}\\t\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class GroupsComponent {\n  constructor(groupeService, router) {\n    this.groupeService = groupeService;\n    this.router = router;\n    this.groupes = []; // List of groups\n    this.newGroupe = {\n      nomGroupe: ''\n    }; // New group model\n    this.editGroupe = null; // Group being edited\n  }\n\n  ngOnInit() {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n  // Called after the view is initialized (ideal for initializing Feather icons)\n  ngAfterViewInit() {\n    feather.replace(); // Initialize Feather icons after the view is ready\n  }\n  // Load all groups\n  loadGroupes() {\n    this.groupeService.getAllGroupes().subscribe(data => {\n      this.groupes = data;\n    }, err => {\n      console.error('Error loading groups', err);\n    });\n  }\n  // Add a new group\n  addGroupe() {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(() => {\n        this.newGroupe = {\n          nomGroupe: ''\n        }; // Reset input\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error adding group', err);\n      });\n    }\n  }\n  // Set group to edit mode\n  setEditGroupe(groupe) {\n    this.editGroupe = {\n      ...groupe\n    }; // Clone object\n  }\n  // Update a group\n  updateGroupe() {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(() => {\n        this.editGroupe = null; // Reset edit mode\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error updating group', err);\n      });\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n  // Delete a group\n  deleteGroupe(id) {\n    if (confirm('Are you sure you want to delete this Group?')) {\n      this.groupeService.deleteGroupe(id).subscribe({\n        next: () => {\n          console.log('Group deleted successfully');\n          this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n        },\n        error: err => {\n          console.error('Error deleting group:', err);\n        }\n      });\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function GroupsComponent_Factory(t) {\n      return new (t || GroupsComponent)(i0.ɵɵdirectiveInject(i1.GroupeService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupsComponent,\n      selectors: [[\"app-groups\"]],\n      decls: 94,\n      vars: 2,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"form-control-dark\", \"w-100\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-light\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"dropdown-toggle\"], [\"data-feather\", \"calendar\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#addGroupeModal\", 1, \"btn\", \"btn-primary\"], [\"id\", \"addGroupeModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"addGroupeModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"addGroupeModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"close\"], [\"aria-hidden\", \"true\"], [1, \"modal-body\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"nomGroupe\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [1, \"text-center\", \"mt-5\", \"mb-4\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-sm\"], [4, \"ngFor\", \"ngForOf\"], [\"href\", \"#\", \"data-toggle\", \"modal\", 1, \"delete\", 3, \"click\"], [\"data-toggle\", \"tooltip\", \"title\", \"Supprimer\", 1, \"material-icons\", \"delete-icon\"], [\"data-toggle\", \"modal\", 1, \"edit\", 3, \"routerLink\"], [\"data-toggle\", \"tooltip\", \"title\", \"Modifier\", 1, \"material-icons\", \"edit-icon\"]],\n      template: function GroupsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"meta\", 6)(8, \"meta\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10)(12, \"link\", 11);\n          i0.ɵɵelementStart(13, \"title\");\n          i0.ɵɵtext(14, \"Gestion des Groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"link\", 12)(16, \"link\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"body\")(18, \"nav\", 14)(19, \"a\", 15);\n          i0.ɵɵtext(20, \"GTI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 16);\n          i0.ɵɵelementStart(22, \"ul\", 17)(23, \"li\", 18)(24, \"a\", 19);\n          i0.ɵɵlistener(\"click\", function GroupsComponent_Template_a_click_24_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(25, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21)(28, \"nav\", 22)(29, \"div\", 23)(30, \"ul\", 24)(31, \"li\", 25)(32, \"a\", 26);\n          i0.ɵɵelement(33, \"span\", 27);\n          i0.ɵɵtext(34, \" Dashboard \");\n          i0.ɵɵelementStart(35, \"span\", 28);\n          i0.ɵɵtext(36, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"li\", 25)(38, \"a\", 29);\n          i0.ɵɵelement(39, \"span\", 30);\n          i0.ɵɵtext(40, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\", 25)(42, \"a\", 31);\n          i0.ɵɵelement(43, \"span\", 32);\n          i0.ɵɵtext(44, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(45, \"main\", 33)(46, \"div\", 34)(47, \"h1\", 35);\n          i0.ɵɵtext(48, \"Gestion des groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 36)(50, \"div\", 37)(51, \"button\", 38);\n          i0.ɵɵtext(52, \"Import\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"button\", 38);\n          i0.ɵɵtext(54, \"Export\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"button\", 39);\n          i0.ɵɵelement(56, \"span\", 40);\n          i0.ɵɵtext(57, \" This week \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"button\", 41);\n          i0.ɵɵtext(59, \" Ajouter un groupe\\n\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 42)(61, \"div\", 43)(62, \"div\", 44)(63, \"div\", 45)(64, \"h5\", 46);\n          i0.ɵɵtext(65, \"Ajouter un groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"button\", 47)(67, \"span\", 48);\n          i0.ɵɵtext(68, \"\\u00D7\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(69, \"div\", 49)(70, \"form\", 50);\n          i0.ɵɵlistener(\"ngSubmit\", function GroupsComponent_Template_form_ngSubmit_70_listener() {\n            return ctx.addGroupe();\n          });\n          i0.ɵɵelementStart(71, \"div\", 51)(72, \"label\", 52);\n          i0.ɵɵtext(73, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"input\", 53);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_74_listener($event) {\n            return ctx.newGroupe.nomGroupe = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 54)(76, \"button\", 55);\n          i0.ɵɵtext(77, \"Fermer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"button\", 56);\n          i0.ɵɵtext(79, \"Ajouter\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(80, \"h2\", 57);\n          i0.ɵɵtext(81, \"Liste des groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"div\", 58)(83, \"table\", 59)(84, \"thead\")(85, \"tr\")(86, \"th\");\n          i0.ɵɵtext(87, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"th\");\n          i0.ɵɵtext(89, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"th\");\n          i0.ɵɵtext(91, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(92, \"tbody\");\n          i0.ɵɵtemplate(93, GroupsComponent_tr_93_Template, 12, 5, \"tr\", 60);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(74);\n          i0.ɵɵproperty(\"ngModel\", ctx.newGroupe.nomGroupe);\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groupes);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm, i2.RouterLink],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GroupsComponent_tr_93_Template_a_click_6_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "groupe_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "idGroupe", "deleteGroupe", "ɵɵadvance", "ɵɵtextInterpolate", "nomGroupe", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "GroupsComponent", "constructor", "groupeService", "router", "groupes", "newGroupe", "editGroupe", "ngOnInit", "loadGroupes", "ngAfterViewInit", "replace", "getAllGroupes", "subscribe", "data", "err", "console", "error", "addGroupe", "trim", "setEditGroupe", "groupe", "updateGroupe", "id", "confirm", "next", "log", "filter", "logout", "localStorage", "removeItem", "getItem", "navigate", "ɵɵdirectiveInject", "i1", "GroupeService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "GroupsComponent_Template", "rf", "ctx", "ɵɵelement", "GroupsComponent_Template_a_click_24_listener", "GroupsComponent_Template_form_ngSubmit_70_listener", "GroupsComponent_Template_input_ngModelChange_74_listener", "$event", "ɵɵtemplate", "GroupsComponent_tr_93_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\groups\\groups.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\groups\\groups.component.html"], "sourcesContent": ["import { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Component, OnInit, AfterViewInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\n\n@Component({\n  selector: 'app-groups',\n  templateUrl: './groups.component.html',\n  styleUrls: ['../../dashboard.css']\n})\nexport class GroupsComponent implements OnInit, AfterViewInit {\n  groupes: Groupe[] = []; // List of groups\n  newGroupe: Groupe = { nomGroupe: '' }; // New group model\n  editGroupe: Groupe | null = null; // Group being edited\n\n  constructor(private groupeService: GroupeService, private router: Router) {}\n\n  ngOnInit(): void {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n\n  // Called after the view is initialized (ideal for initializing Feather icons)\n  ngAfterViewInit(): void {\n    feather.replace(); // Initialize Feather icons after the view is ready\n  }\n\n  // Load all groups\n  loadGroupes(): void {\n    this.groupeService.getAllGroupes().subscribe(\n      (data) => {\n        this.groupes = data;\n      },\n      (err) => {\n        console.error('Error loading groups', err);\n      }\n    );\n  }\n\n  // Add a new group\n  addGroupe(): void {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(\n        () => {\n          this.newGroupe = { nomGroupe: '' }; // Reset input\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error adding group', err);\n        }\n      );\n    }\n  }\n\n  // Set group to edit mode\n  setEditGroupe(groupe: Groupe): void {\n    this.editGroupe = { ...groupe }; // Clone object\n  }\n\n  // Update a group\n  updateGroupe(): void {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(\n        () => {\n          this.editGroupe = null; // Reset edit mode\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error updating group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n\n  // Delete a group\n  deleteGroupe(id: number): void {\n    if (confirm('Are you sure you want to delete this Group?')) {\n      this.groupeService.deleteGroupe(id).subscribe({\n        next: () => {\n          console.log('Group deleted successfully');\n          this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n        },\n        error: (err) => {\n          console.error('Error deleting group:', err);\n        }\n      });\n    }\n  }\n  \n\n  logout(): void {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n\n   <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <script src=\"https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js\"></script>\n    <script src=\"https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js\"></script>\n    <link rel=\"stylesheet\" href=\"https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css\">\n\n    <title>Gestion des Groupes</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n\n    <!-- Custom styles for this template -->\n    <link href=\"../../dashboard.css\" rel=\"stylesheet\">\n  </head>\n\n  <body>\n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"adminDash\">GTI</a>\n      <input class=\"form-control form-control-dark w-100\" type=\"text\" placeholder=\"Search\" aria-label=\"Search\">\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block bg-light sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"users\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Gestion des groupes</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n                <button class=\"btn btn-sm btn-outline-secondary\">Import</button>\n                <button class=\"btn btn-sm btn-outline-secondary\">Export</button>\n              </div>\n              <button class=\"btn btn-sm btn-outline-secondary dropdown-toggle\">\n                <span data-feather=\"calendar\"></span>\n                This week\n              </button>\n            </div>\n          </div>\n          \n<!-- Button to trigger the modal -->\n<button type=\"button\" class=\"btn btn-primary\" data-toggle=\"modal\" data-target=\"#addGroupeModal\">\n  Ajouter un groupe\n</button>\n\n<!-- Modal -->\n<div class=\"modal fade\" id=\"addGroupeModal\" tabindex=\"-1\" aria-labelledby=\"addGroupeModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"addGroupeModalLabel\">Ajouter un groupe</h5>\n        <button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\">\n          <span aria-hidden=\"true\">&times;</span>\n        </button>\n      </div>\n      <div class=\"modal-body\">\n        <form (ngSubmit)=\"addGroupe()\">\n          <div class=\"form-group\">\n            <label for=\"nomGroupe\">Nom du groupe</label>\n            <input type=\"text\" id=\"nomGroupe\" [(ngModel)]=\"newGroupe.nomGroupe\" name=\"nomGroupe\" class=\"form-control\" required>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\">Fermer</button>\n            <button type=\"submit\" class=\"btn btn-primary\">Ajouter</button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n          \n          <h2 class=\"text-center mt-5 mb-4\">Liste des groupes</h2>\n          \n          <div class=\"table-responsive\">\n            <table class=\"table table-striped table-sm\">\n              <thead>\n                <tr>\n                  <th>ID</th>\n                  <th>Nom du groupe</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let groupe of groupes\">\n                  <td>{{ groupe.idGroupe || 'N/A' }}</td>\n                  <td>{{ groupe.nomGroupe }}</td>\n                  <td>\n                    <a href=\"#\" class=\"delete\" (click)=\"groupe.idGroupe ? deleteGroupe(groupe.idGroupe) : null\" data-toggle=\"modal\">\n                      <i class=\"material-icons delete-icon\" data-toggle=\"tooltip\" title=\"Supprimer\">&#xE872;</i>\n                    </a>\n                    <a [routerLink]=\"['/edit-groupe', groupe.idGroupe]\" class=\"edit\" data-toggle=\"modal\">\n                      <i class=\"material-icons edit-icon\" data-toggle=\"tooltip\" title=\"Modifier\">&#xE254;</i>\n                    </a>\n                  </td>\n                  \n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <!-- Bootstrap core JavaScript -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Feather Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>\n      feather.replace(); // Initialize Feather icons after page load\n    </script>\n    \n\n    <!-- Chart.js -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.min.js\"></script>\n  </body>\n</html>\n"], "mappings": "AAIA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;;;;;;;;;IC4HxBC,EAAA,CAAAC,cAAA,SAAmC;IAC7BD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IACyBD,EAAA,CAAAI,UAAA,mBAAAC,kDAAA;MAAA,MAAAC,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAJ,SAAA,CAAAK,QAAA,GAAkBH,MAAA,CAAAI,YAAA,CAAAN,SAAA,CAAAK,QAAA,CAA6B,GAAG,IAAI;IAAA,EAAC;IACzFd,EAAA,CAAAC,cAAA,YAA8E;IAAAD,EAAA,CAAAE,MAAA,aAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE5FH,EAAA,CAAAC,cAAA,YAAqF;IACRD,EAAA,CAAAE,MAAA,cAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAPvFH,EAAA,CAAAgB,SAAA,GAA8B;IAA9BhB,EAAA,CAAAiB,iBAAA,CAAAR,SAAA,CAAAK,QAAA,UAA8B;IAC9Bd,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAiB,iBAAA,CAAAR,SAAA,CAAAS,SAAA,CAAsB;IAKrBlB,EAAA,CAAAgB,SAAA,GAAgD;IAAhDhB,EAAA,CAAAmB,UAAA,eAAAnB,EAAA,CAAAoB,eAAA,IAAAC,GAAA,EAAAZ,SAAA,CAAAK,QAAA,EAAgD;;;;AD5HvE,OAAM,MAAOQ,eAAe;EAK1BC,YAAoBC,aAA4B,EAAUC,MAAc;IAApD,KAAAD,aAAa,GAAbA,aAAa;IAAyB,KAAAC,MAAM,GAANA,MAAM;IAJhE,KAAAC,OAAO,GAAa,EAAE,CAAC,CAAC;IACxB,KAAAC,SAAS,GAAW;MAAET,SAAS,EAAE;IAAE,CAAE,CAAC,CAAC;IACvC,KAAAU,UAAU,GAAkB,IAAI,CAAC,CAAC;EAEyC;;EAE3EC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;EACtB;EAEA;EACAC,eAAeA,CAAA;IACbhC,OAAO,CAACiC,OAAO,EAAE,CAAC,CAAC;EACrB;EAEA;EACAF,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACS,aAAa,EAAE,CAACC,SAAS,CACzCC,IAAI,IAAI;MACP,IAAI,CAACT,OAAO,GAAGS,IAAI;IACrB,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;IAC5C,CAAC,CACF;EACH;EAEA;EACAG,SAASA,CAAA;IACP,IAAI,IAAI,CAACZ,SAAS,CAACT,SAAS,CAACsB,IAAI,EAAE,EAAE;MACnC,IAAI,CAAChB,aAAa,CAACe,SAAS,CAAC,IAAI,CAACZ,SAAS,CAAC,CAACO,SAAS,CACpD,MAAK;QACH,IAAI,CAACP,SAAS,GAAG;UAAET,SAAS,EAAE;QAAE,CAAE,CAAC,CAAC;QACpC,IAAI,CAACY,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAM,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;MAC1C,CAAC,CACF;;EAEL;EAEA;EACAK,aAAaA,CAACC,MAAc;IAC1B,IAAI,CAACd,UAAU,GAAG;MAAE,GAAGc;IAAM,CAAE,CAAC,CAAC;EACnC;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACf,UAAU,IAAI,IAAI,CAACA,UAAU,CAACd,QAAQ,EAAE;MAC/C,IAAI,CAACU,aAAa,CAACmB,YAAY,CAAC,IAAI,CAACf,UAAU,CAACd,QAAQ,EAAE,IAAI,CAACc,UAAU,CAAC,CAACM,SAAS,CAClF,MAAK;QACH,IAAI,CAACN,UAAU,GAAG,IAAI,CAAC,CAAC;QACxB,IAAI,CAACE,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAM,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;;EAEtD;EAEA;EACAvB,YAAYA,CAAC6B,EAAU;IACrB,IAAIC,OAAO,CAAC,6CAA6C,CAAC,EAAE;MAC1D,IAAI,CAACrB,aAAa,CAACT,YAAY,CAAC6B,EAAE,CAAC,CAACV,SAAS,CAAC;QAC5CY,IAAI,EAAEA,CAAA,KAAK;UACTT,OAAO,CAACU,GAAG,CAAC,4BAA4B,CAAC;UACzC,IAAI,CAACrB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACsB,MAAM,CAACN,MAAM,IAAIA,MAAM,CAAC5B,QAAQ,KAAK8B,EAAE,CAAC;QACtE,CAAC;QACDN,KAAK,EAAGF,GAAG,IAAI;UACbC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,GAAG,CAAC;QAC7C;OACD,CAAC;;EAEN;EAGAa,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCd,OAAO,CAACU,GAAG,CAACG,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC3B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAvFW/B,eAAe,EAAAtB,EAAA,CAAAsD,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAxD,EAAA,CAAAsD,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfpC,eAAe;MAAAqC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5BjE,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAmE,SAAA,cAAsB;UAgBtBnE,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAElCH,EAAA,CAAAmE,SAAA,gBAAmF;UAOrFnE,EAAA,CAAAG,YAAA,EAAO;UAEPH,EAAA,CAAAC,cAAA,YAAM;UAE8DD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAmE,SAAA,iBAAyG;UACzGnE,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAI,UAAA,mBAAAgE,6CAAA;YAAA,OAASF,GAAA,CAAAjB,MAAA,EAAQ;UAAA,EAAC;UAACjD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKzDH,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAAmE,SAAA,gBAAiC;UACjCnE,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGpDH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAmE,SAAA,gBAAiC;UACjCnE,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAmE,SAAA,gBAAkC;UAClCnE,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMZH,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,eAAsC;UAEeD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChEH,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAElEH,EAAA,CAAAC,cAAA,kBAAiE;UAC/DD,EAAA,CAAAmE,SAAA,gBAAqC;UACrCnE,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKvBH,EAAA,CAAAC,cAAA,kBAAgG;UAC9FD,EAAA,CAAAE,MAAA,4BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAC,cAAA,eAAmH;UAI1DD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvEH,EAAA,CAAAC,cAAA,kBAA4E;UACjDD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG3CH,EAAA,CAAAC,cAAA,eAAwB;UAChBD,EAAA,CAAAI,UAAA,sBAAAiE,mDAAA;YAAA,OAAYH,GAAA,CAAA3B,SAAA,EAAW;UAAA,EAAC;UAC5BvC,EAAA,CAAAC,cAAA,eAAwB;UACCD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,iBAAmH;UAAjFD,EAAA,CAAAI,UAAA,2BAAAkE,yDAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAvC,SAAA,CAAAT,SAAA,GAAAqD,MAAA;UAAA,EAAiC;UAAnEvE,EAAA,CAAAG,YAAA,EAAmH;UAErHH,EAAA,CAAAC,cAAA,eAA0B;UAC6CD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpFH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAUhEH,EAAA,CAAAC,cAAA,cAAkC;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAExDH,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACXH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpBH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAwE,UAAA,KAAAC,8BAAA,kBAYK;UACPzE,EAAA,CAAAG,YAAA,EAAQ;;;UAvCwBH,EAAA,CAAAgB,SAAA,IAAiC;UAAjChB,EAAA,CAAAmB,UAAA,YAAA+C,GAAA,CAAAvC,SAAA,CAAAT,SAAA,CAAiC;UA0BxClB,EAAA,CAAAgB,SAAA,IAAU;UAAVhB,EAAA,CAAAmB,UAAA,YAAA+C,GAAA,CAAAxC,OAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}