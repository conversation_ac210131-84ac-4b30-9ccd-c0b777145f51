{"ast": null, "code": "/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\nexports.coerce = coerce;\nexports.disable = disable;\nexports.enable = enable;\nexports.enabled = enabled;\nexports.humanize = require('ms');\n\n/**\n * The currently active debug mode names, and names to skip.\n */\n\nexports.names = [];\nexports.skips = [];\n\n/**\n * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n *\n * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n */\n\nexports.formatters = {};\n\n/**\n * Previous log timestamp.\n */\n\nvar prevTime;\n\n/**\n * Select a color.\n * @param {String} namespace\n * @return {Number}\n * @api private\n */\n\nfunction selectColor(namespace) {\n  var hash = 0,\n    i;\n  for (i in namespace) {\n    hash = (hash << 5) - hash + namespace.charCodeAt(i);\n    hash |= 0; // Convert to 32bit integer\n  }\n\n  return exports.colors[Math.abs(hash) % exports.colors.length];\n}\n\n/**\n * Create a debugger with the given `namespace`.\n *\n * @param {String} namespace\n * @return {Function}\n * @api public\n */\n\nfunction createDebug(namespace) {\n  function debug() {\n    // disabled?\n    if (!debug.enabled) return;\n    var self = debug;\n\n    // set `diff` timestamp\n    var curr = +new Date();\n    var ms = curr - (prevTime || curr);\n    self.diff = ms;\n    self.prev = prevTime;\n    self.curr = curr;\n    prevTime = curr;\n\n    // turn the `arguments` into a proper Array\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n    args[0] = exports.coerce(args[0]);\n    if ('string' !== typeof args[0]) {\n      // anything else let's inspect with %O\n      args.unshift('%O');\n    }\n\n    // apply any `formatters` transformations\n    var index = 0;\n    args[0] = args[0].replace(/%([a-zA-Z%])/g, function (match, format) {\n      // if we encounter an escaped % then don't increase the array index\n      if (match === '%%') return match;\n      index++;\n      var formatter = exports.formatters[format];\n      if ('function' === typeof formatter) {\n        var val = args[index];\n        match = formatter.call(self, val);\n\n        // now we need to remove `args[index]` since it's inlined in the `format`\n        args.splice(index, 1);\n        index--;\n      }\n      return match;\n    });\n\n    // apply env-specific formatting (colors, etc.)\n    exports.formatArgs.call(self, args);\n    var logFn = debug.log || exports.log || console.log.bind(console);\n    logFn.apply(self, args);\n  }\n  debug.namespace = namespace;\n  debug.enabled = exports.enabled(namespace);\n  debug.useColors = exports.useColors();\n  debug.color = selectColor(namespace);\n\n  // env-specific initialization logic for debug instances\n  if ('function' === typeof exports.init) {\n    exports.init(debug);\n  }\n  return debug;\n}\n\n/**\n * Enables a debug mode by namespaces. This can include modes\n * separated by a colon and wildcards.\n *\n * @param {String} namespaces\n * @api public\n */\n\nfunction enable(namespaces) {\n  exports.save(namespaces);\n  exports.names = [];\n  exports.skips = [];\n  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n  var len = split.length;\n  for (var i = 0; i < len; i++) {\n    if (!split[i]) continue; // ignore empty strings\n    namespaces = split[i].replace(/\\*/g, '.*?');\n    if (namespaces[0] === '-') {\n      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n    } else {\n      exports.names.push(new RegExp('^' + namespaces + '$'));\n    }\n  }\n}\n\n/**\n * Disable debug output.\n *\n * @api public\n */\n\nfunction disable() {\n  exports.enable('');\n}\n\n/**\n * Returns true if the given mode name is enabled, false otherwise.\n *\n * @param {String} name\n * @return {Boolean}\n * @api public\n */\n\nfunction enabled(name) {\n  var i, len;\n  for (i = 0, len = exports.skips.length; i < len; i++) {\n    if (exports.skips[i].test(name)) {\n      return false;\n    }\n  }\n  for (i = 0, len = exports.names.length; i < len; i++) {\n    if (exports.names[i].test(name)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Coerce `val`.\n *\n * @param {Mixed} val\n * @return {Mixed}\n * @api private\n */\n\nfunction coerce(val) {\n  if (val instanceof Error) return val.stack || val.message;\n  return val;\n}", "map": {"version": 3, "names": ["exports", "module", "createDebug", "debug", "coerce", "disable", "enable", "enabled", "humanize", "require", "names", "skips", "formatters", "prevTime", "selectColor", "namespace", "hash", "i", "charCodeAt", "colors", "Math", "abs", "length", "self", "curr", "Date", "ms", "diff", "prev", "args", "Array", "arguments", "unshift", "index", "replace", "match", "format", "formatter", "val", "call", "splice", "formatArgs", "logFn", "log", "console", "bind", "apply", "useColors", "color", "init", "namespaces", "save", "split", "len", "push", "RegExp", "substr", "name", "test", "Error", "stack", "message"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/express/node_modules/debug/src/debug.js"], "sourcesContent": ["\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\nexports.coerce = coerce;\nexports.disable = disable;\nexports.enable = enable;\nexports.enabled = enabled;\nexports.humanize = require('ms');\n\n/**\n * The currently active debug mode names, and names to skip.\n */\n\nexports.names = [];\nexports.skips = [];\n\n/**\n * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n *\n * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n */\n\nexports.formatters = {};\n\n/**\n * Previous log timestamp.\n */\n\nvar prevTime;\n\n/**\n * Select a color.\n * @param {String} namespace\n * @return {Number}\n * @api private\n */\n\nfunction selectColor(namespace) {\n  var hash = 0, i;\n\n  for (i in namespace) {\n    hash  = ((hash << 5) - hash) + namespace.charCodeAt(i);\n    hash |= 0; // Convert to 32bit integer\n  }\n\n  return exports.colors[Math.abs(hash) % exports.colors.length];\n}\n\n/**\n * Create a debugger with the given `namespace`.\n *\n * @param {String} namespace\n * @return {Function}\n * @api public\n */\n\nfunction createDebug(namespace) {\n\n  function debug() {\n    // disabled?\n    if (!debug.enabled) return;\n\n    var self = debug;\n\n    // set `diff` timestamp\n    var curr = +new Date();\n    var ms = curr - (prevTime || curr);\n    self.diff = ms;\n    self.prev = prevTime;\n    self.curr = curr;\n    prevTime = curr;\n\n    // turn the `arguments` into a proper Array\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    args[0] = exports.coerce(args[0]);\n\n    if ('string' !== typeof args[0]) {\n      // anything else let's inspect with %O\n      args.unshift('%O');\n    }\n\n    // apply any `formatters` transformations\n    var index = 0;\n    args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {\n      // if we encounter an escaped % then don't increase the array index\n      if (match === '%%') return match;\n      index++;\n      var formatter = exports.formatters[format];\n      if ('function' === typeof formatter) {\n        var val = args[index];\n        match = formatter.call(self, val);\n\n        // now we need to remove `args[index]` since it's inlined in the `format`\n        args.splice(index, 1);\n        index--;\n      }\n      return match;\n    });\n\n    // apply env-specific formatting (colors, etc.)\n    exports.formatArgs.call(self, args);\n\n    var logFn = debug.log || exports.log || console.log.bind(console);\n    logFn.apply(self, args);\n  }\n\n  debug.namespace = namespace;\n  debug.enabled = exports.enabled(namespace);\n  debug.useColors = exports.useColors();\n  debug.color = selectColor(namespace);\n\n  // env-specific initialization logic for debug instances\n  if ('function' === typeof exports.init) {\n    exports.init(debug);\n  }\n\n  return debug;\n}\n\n/**\n * Enables a debug mode by namespaces. This can include modes\n * separated by a colon and wildcards.\n *\n * @param {String} namespaces\n * @api public\n */\n\nfunction enable(namespaces) {\n  exports.save(namespaces);\n\n  exports.names = [];\n  exports.skips = [];\n\n  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n  var len = split.length;\n\n  for (var i = 0; i < len; i++) {\n    if (!split[i]) continue; // ignore empty strings\n    namespaces = split[i].replace(/\\*/g, '.*?');\n    if (namespaces[0] === '-') {\n      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n    } else {\n      exports.names.push(new RegExp('^' + namespaces + '$'));\n    }\n  }\n}\n\n/**\n * Disable debug output.\n *\n * @api public\n */\n\nfunction disable() {\n  exports.enable('');\n}\n\n/**\n * Returns true if the given mode name is enabled, false otherwise.\n *\n * @param {String} name\n * @return {Boolean}\n * @api public\n */\n\nfunction enabled(name) {\n  var i, len;\n  for (i = 0, len = exports.skips.length; i < len; i++) {\n    if (exports.skips[i].test(name)) {\n      return false;\n    }\n  }\n  for (i = 0, len = exports.names.length; i < len; i++) {\n    if (exports.names[i].test(name)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Coerce `val`.\n *\n * @param {Mixed} val\n * @return {Mixed}\n * @api private\n */\n\nfunction coerce(val) {\n  if (val instanceof Error) return val.stack || val.message;\n  return val;\n}\n"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;;AAEAA,OAAO,GAAGC,MAAM,CAACD,OAAO,GAAGE,WAAW,CAACC,KAAK,GAAGD,WAAW,CAAC,SAAS,CAAC,GAAGA,WAAW;AACnFF,OAAO,CAACI,MAAM,GAAGA,MAAM;AACvBJ,OAAO,CAACK,OAAO,GAAGA,OAAO;AACzBL,OAAO,CAACM,MAAM,GAAGA,MAAM;AACvBN,OAAO,CAACO,OAAO,GAAGA,OAAO;AACzBP,OAAO,CAACQ,QAAQ,GAAGC,OAAO,CAAC,IAAI,CAAC;;AAEhC;AACA;AACA;;AAEAT,OAAO,CAACU,KAAK,GAAG,EAAE;AAClBV,OAAO,CAACW,KAAK,GAAG,EAAE;;AAElB;AACA;AACA;AACA;AACA;;AAEAX,OAAO,CAACY,UAAU,GAAG,CAAC,CAAC;;AAEvB;AACA;AACA;;AAEA,IAAIC,QAAQ;;AAEZ;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,WAAWA,CAACC,SAAS,EAAE;EAC9B,IAAIC,IAAI,GAAG,CAAC;IAAEC,CAAC;EAEf,KAAKA,CAAC,IAAIF,SAAS,EAAE;IACnBC,IAAI,GAAK,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAID,SAAS,CAACG,UAAU,CAACD,CAAC,CAAC;IACtDD,IAAI,IAAI,CAAC,CAAC,CAAC;EACb;;EAEA,OAAOhB,OAAO,CAACmB,MAAM,CAACC,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAGhB,OAAO,CAACmB,MAAM,CAACG,MAAM,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASpB,WAAWA,CAACa,SAAS,EAAE;EAE9B,SAASZ,KAAKA,CAAA,EAAG;IACf;IACA,IAAI,CAACA,KAAK,CAACI,OAAO,EAAE;IAEpB,IAAIgB,IAAI,GAAGpB,KAAK;;IAEhB;IACA,IAAIqB,IAAI,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;IACtB,IAAIC,EAAE,GAAGF,IAAI,IAAIX,QAAQ,IAAIW,IAAI,CAAC;IAClCD,IAAI,CAACI,IAAI,GAAGD,EAAE;IACdH,IAAI,CAACK,IAAI,GAAGf,QAAQ;IACpBU,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChBX,QAAQ,GAAGW,IAAI;;IAEf;IACA,IAAIK,IAAI,GAAG,IAAIC,KAAK,CAACC,SAAS,CAACT,MAAM,CAAC;IACtC,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,IAAI,CAACP,MAAM,EAAEL,CAAC,EAAE,EAAE;MACpCY,IAAI,CAACZ,CAAC,CAAC,GAAGc,SAAS,CAACd,CAAC,CAAC;IACxB;IAEAY,IAAI,CAAC,CAAC,CAAC,GAAG7B,OAAO,CAACI,MAAM,CAACyB,IAAI,CAAC,CAAC,CAAC,CAAC;IAEjC,IAAI,QAAQ,KAAK,OAAOA,IAAI,CAAC,CAAC,CAAC,EAAE;MAC/B;MACAA,IAAI,CAACG,OAAO,CAAC,IAAI,CAAC;IACpB;;IAEA;IACA,IAAIC,KAAK,GAAG,CAAC;IACbJ,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,CAACK,OAAO,CAAC,eAAe,EAAE,UAASC,KAAK,EAAEC,MAAM,EAAE;MACjE;MACA,IAAID,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;MAChCF,KAAK,EAAE;MACP,IAAII,SAAS,GAAGrC,OAAO,CAACY,UAAU,CAACwB,MAAM,CAAC;MAC1C,IAAI,UAAU,KAAK,OAAOC,SAAS,EAAE;QACnC,IAAIC,GAAG,GAAGT,IAAI,CAACI,KAAK,CAAC;QACrBE,KAAK,GAAGE,SAAS,CAACE,IAAI,CAAChB,IAAI,EAAEe,GAAG,CAAC;;QAEjC;QACAT,IAAI,CAACW,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC;QACrBA,KAAK,EAAE;MACT;MACA,OAAOE,KAAK;IACd,CAAC,CAAC;;IAEF;IACAnC,OAAO,CAACyC,UAAU,CAACF,IAAI,CAAChB,IAAI,EAAEM,IAAI,CAAC;IAEnC,IAAIa,KAAK,GAAGvC,KAAK,CAACwC,GAAG,IAAI3C,OAAO,CAAC2C,GAAG,IAAIC,OAAO,CAACD,GAAG,CAACE,IAAI,CAACD,OAAO,CAAC;IACjEF,KAAK,CAACI,KAAK,CAACvB,IAAI,EAAEM,IAAI,CAAC;EACzB;EAEA1B,KAAK,CAACY,SAAS,GAAGA,SAAS;EAC3BZ,KAAK,CAACI,OAAO,GAAGP,OAAO,CAACO,OAAO,CAACQ,SAAS,CAAC;EAC1CZ,KAAK,CAAC4C,SAAS,GAAG/C,OAAO,CAAC+C,SAAS,CAAC,CAAC;EACrC5C,KAAK,CAAC6C,KAAK,GAAGlC,WAAW,CAACC,SAAS,CAAC;;EAEpC;EACA,IAAI,UAAU,KAAK,OAAOf,OAAO,CAACiD,IAAI,EAAE;IACtCjD,OAAO,CAACiD,IAAI,CAAC9C,KAAK,CAAC;EACrB;EAEA,OAAOA,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASG,MAAMA,CAAC4C,UAAU,EAAE;EAC1BlD,OAAO,CAACmD,IAAI,CAACD,UAAU,CAAC;EAExBlD,OAAO,CAACU,KAAK,GAAG,EAAE;EAClBV,OAAO,CAACW,KAAK,GAAG,EAAE;EAElB,IAAIyC,KAAK,GAAG,CAAC,OAAOF,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAG,EAAE,EAAEE,KAAK,CAAC,QAAQ,CAAC;EAC9E,IAAIC,GAAG,GAAGD,KAAK,CAAC9B,MAAM;EAEtB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,GAAG,EAAEpC,CAAC,EAAE,EAAE;IAC5B,IAAI,CAACmC,KAAK,CAACnC,CAAC,CAAC,EAAE,SAAS,CAAC;IACzBiC,UAAU,GAAGE,KAAK,CAACnC,CAAC,CAAC,CAACiB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;IAC3C,IAAIgB,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACzBlD,OAAO,CAACW,KAAK,CAAC2C,IAAI,CAAC,IAAIC,MAAM,CAAC,GAAG,GAAGL,UAAU,CAACM,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;IAClE,CAAC,MAAM;MACLxD,OAAO,CAACU,KAAK,CAAC4C,IAAI,CAAC,IAAIC,MAAM,CAAC,GAAG,GAAGL,UAAU,GAAG,GAAG,CAAC,CAAC;IACxD;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;;AAEA,SAAS7C,OAAOA,CAAA,EAAG;EACjBL,OAAO,CAACM,MAAM,CAAC,EAAE,CAAC;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,OAAOA,CAACkD,IAAI,EAAE;EACrB,IAAIxC,CAAC,EAAEoC,GAAG;EACV,KAAKpC,CAAC,GAAG,CAAC,EAAEoC,GAAG,GAAGrD,OAAO,CAACW,KAAK,CAACW,MAAM,EAAEL,CAAC,GAAGoC,GAAG,EAAEpC,CAAC,EAAE,EAAE;IACpD,IAAIjB,OAAO,CAACW,KAAK,CAACM,CAAC,CAAC,CAACyC,IAAI,CAACD,IAAI,CAAC,EAAE;MAC/B,OAAO,KAAK;IACd;EACF;EACA,KAAKxC,CAAC,GAAG,CAAC,EAAEoC,GAAG,GAAGrD,OAAO,CAACU,KAAK,CAACY,MAAM,EAAEL,CAAC,GAAGoC,GAAG,EAAEpC,CAAC,EAAE,EAAE;IACpD,IAAIjB,OAAO,CAACU,KAAK,CAACO,CAAC,CAAC,CAACyC,IAAI,CAACD,IAAI,CAAC,EAAE;MAC/B,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASrD,MAAMA,CAACkC,GAAG,EAAE;EACnB,IAAIA,GAAG,YAAYqB,KAAK,EAAE,OAAOrB,GAAG,CAACsB,KAAK,IAAItB,GAAG,CAACuB,OAAO;EACzD,OAAOvB,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}