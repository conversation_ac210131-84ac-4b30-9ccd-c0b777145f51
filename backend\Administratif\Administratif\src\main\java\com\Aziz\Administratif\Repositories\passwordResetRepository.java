package com.Aziz.Administratif.Repositories;

import com.Aziz.Administratif.Security.auth.PasswordResetRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface passwordResetRepository extends JpaRepository<PasswordResetRequest, Long> {

    PasswordResetRequest findByMatriculeAndEmail(int matricule, String email);

    List<PasswordResetRequest> findByCompletedFalse();

   //PasswordResetRequest findByMatriculeAndEmail(int matricule, String email);

    PasswordResetRequest findByEmail(String email);


    List<PasswordResetRequest> findByCompletedTrue();
}
