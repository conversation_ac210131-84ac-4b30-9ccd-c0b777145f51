{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/forms\";\nfunction ForgotPasswordComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.message, \" \");\n  }\n}\nfunction ForgotPasswordComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nexport class ForgotPasswordComponent {\n  constructor(http) {\n    this.http = http;\n    this.message = '';\n    this.error = '';\n  }\n  submitRequest(form) {\n    if (!form.valid) {\n      this.error = \"Veuillez remplir tous les champs correctement.\";\n      this.message = '';\n      return;\n    }\n    const request = {\n      matricule: this.matricule,\n      email: this.email\n    };\n    this.http.post('http://localhost:8000/api/v1/auth/resetPassword/send_request_to_Admin', request).subscribe({\n      next: res => {\n        this.message = \"Votre demande a été envoyée à l'administrateur.\";\n        this.error = '';\n      },\n      error: err => {\n        console.error(err);\n        this.error = \"Erreur lors de l'envoi. Veuillez vérifier vos informations.\";\n        this.message = '';\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ForgotPasswordComponent_Factory(t) {\n      return new (t || ForgotPasswordComponent)(i0.ɵɵdirectiveInject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ForgotPasswordComponent,\n      selectors: [[\"app-forgot-password\"]],\n      decls: 32,\n      vars: 4,\n      consts: [[1, \"full-page-bg\"], [1, \"container\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"vh-100\"], [1, \"card\", \"p-4\", \"shadow-lg\", \"rounded-4\", \"custom-card\"], [1, \"text-center\", \"mb-4\"], [1, \"fw-bold\", \"text-primary\"], [1, \"text-muted\"], [3, \"ngSubmit\"], [\"form\", \"ngForm\"], [1, \"mb-3\"], [1, \"form-label\", \"fw-semibold\"], [1, \"input-group\"], [1, \"input-group-text\", \"bg-light\"], [1, \"bi\", \"bi-person-badge\"], [\"type\", \"text\", \"name\", \"matricule\", \"required\", \"\", \"placeholder\", \"Votre matricule\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"bi\", \"bi-envelope\"], [\"type\", \"email\", \"name\", \"email\", \"required\", \"\", \"placeholder\", \"Votre email\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\", \"fw-bold\", \"rounded-pill\"], [1, \"bi\", \"bi-envelope-arrow-up\"], [\"class\", \"alert alert-success text-center mt-3 rounded-pill py-2\", 4, \"ngIf\"], [\"class\", \"alert alert-danger text-center mt-3 rounded-pill py-2\", 4, \"ngIf\"], [1, \"text-center\", \"mt-3\"], [\"routerLink\", \"/login\", 1, \"text-decoration-none\", \"text-secondary\"], [1, \"alert\", \"alert-success\", \"text-center\", \"mt-3\", \"rounded-pill\", \"py-2\"], [1, \"alert\", \"alert-danger\", \"text-center\", \"mt-3\", \"rounded-pill\", \"py-2\"]],\n      template: function ForgotPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r3 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h3\", 4);\n          i0.ɵɵtext(5, \"Mot de passe oubli\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Entrez votre matricule et votre adresse email pour envoyer une demande.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"form\", 6, 7);\n          i0.ɵɵlistener(\"ngSubmit\", function ForgotPasswordComponent_Template_form_ngSubmit_8_listener() {\n            i0.ɵɵrestoreView(_r3);\n            const _r0 = i0.ɵɵreference(9);\n            return i0.ɵɵresetView(ctx.submitRequest(_r0));\n          });\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"label\", 9);\n          i0.ɵɵtext(12, \"Matricule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"span\", 11);\n          i0.ɵɵelement(15, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function ForgotPasswordComponent_Template_input_ngModelChange_16_listener($event) {\n            return ctx.matricule = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 8)(18, \"label\", 9);\n          i0.ɵɵtext(19, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 10)(21, \"span\", 11);\n          i0.ɵɵelement(22, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"input\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function ForgotPasswordComponent_Template_input_ngModelChange_23_listener($event) {\n            return ctx.email = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"button\", 16);\n          i0.ɵɵelement(25, \"i\", 17);\n          i0.ɵɵtext(26, \" Envoyer la demande \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, ForgotPasswordComponent_div_27_Template, 2, 1, \"div\", 18);\n          i0.ɵɵtemplate(28, ForgotPasswordComponent_div_28_Template, 2, 1, \"div\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 20)(30, \"a\", 21);\n          i0.ɵɵtext(31, \"\\u2190 Retour \\u00E0 la connexion\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngModel\", ctx.matricule);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n        }\n      },\n      dependencies: [i2.NgIf, i3.RouterLink, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n      styles: [\".full-page-bg[_ngcontent-%COMP%] {\\n    background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n    min-height: 100vh;\\n  }\\n  \\n  .custom-card[_ngcontent-%COMP%] {\\n    background-color: #ffffff;\\n    border: none;\\n    border-radius: 1.5rem;\\n    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\\n    width: 100%;\\n    max-width: 420px;\\n  }\\n  \\n  .input-group-text[_ngcontent-%COMP%] {\\n    border-radius: 0.375rem 0 0 0.375rem;\\n  }\\n  \\n  input.form-control[_ngcontent-%COMP%] {\\n    border-radius: 0 0.375rem 0.375rem 0;\\n  }\\n  .full-page-bg[_ngcontent-%COMP%] {\\n    height: 100vh;\\n    width: 100%;\\n    background-image: url('/assets/images/test.jpg');\\n    background-size: cover;\\n    background-position: center;\\n    background-repeat: no-repeat;\\n    background-attachment: fixed;\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZm9yZ290LXBhc3N3b3JkL2ZvcmdvdC1wYXNzd29yZC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0lBQ0ksdUVBQXVFO0lBQ3ZFLGlCQUFpQjtFQUNuQjs7RUFFQTtJQUNFLHlCQUF5QjtJQUN6QixZQUFZO0lBQ1oscUJBQXFCO0lBQ3JCLDBDQUEwQztJQUMxQyxXQUFXO0lBQ1gsZ0JBQWdCO0VBQ2xCOztFQUVBO0lBQ0Usb0NBQW9DO0VBQ3RDOztFQUVBO0lBQ0Usb0NBQW9DO0VBQ3RDO0VBQ0E7SUFDRSxhQUFhO0lBQ2IsV0FBVztJQUNYLGdEQUFnRDtJQUNoRCxzQkFBc0I7SUFDdEIsMkJBQTJCO0lBQzNCLDRCQUE0QjtJQUM1Qiw0QkFBNEI7RUFDOUIiLCJzb3VyY2VzQ29udGVudCI6WyIuZnVsbC1wYWdlLWJnIHtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgtNDVkZWcsICMyNDM3NWEsICMxMDIwM2EsICMxNDIxM2IsICMwYzE3MjkpO1xyXG4gICAgbWluLWhlaWdodDogMTAwdmg7XHJcbiAgfVxyXG4gIFxyXG4gIC5jdXN0b20tY2FyZCB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xyXG4gICAgYm9yZGVyOiBub25lO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMS41cmVtO1xyXG4gICAgYm94LXNoYWRvdzogMCAxMHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBtYXgtd2lkdGg6IDQyMHB4O1xyXG4gIH1cclxuICBcclxuICAuaW5wdXQtZ3JvdXAtdGV4dCB7XHJcbiAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbSAwIDAgMC4zNzVyZW07XHJcbiAgfVxyXG4gIFxyXG4gIGlucHV0LmZvcm0tY29udHJvbCB7XHJcbiAgICBib3JkZXItcmFkaXVzOiAwIDAuMzc1cmVtIDAuMzc1cmVtIDA7XHJcbiAgfVxyXG4gIC5mdWxsLXBhZ2UtYmcge1xyXG4gICAgaGVpZ2h0OiAxMDB2aDtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCcvYXNzZXRzL2ltYWdlcy90ZXN0LmpwZycpO1xyXG4gICAgYmFja2dyb3VuZC1zaXplOiBjb3ZlcjtcclxuICAgIGJhY2tncm91bmQtcG9zaXRpb246IGNlbnRlcjtcclxuICAgIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7XHJcbiAgICBiYWNrZ3JvdW5kLWF0dGFjaG1lbnQ6IGZpeGVkO1xyXG4gIH1cclxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "message", "ctx_r2", "error", "ForgotPasswordComponent", "constructor", "http", "submitRequest", "form", "valid", "request", "matricule", "email", "post", "subscribe", "next", "res", "err", "console", "ɵɵdirectiveInject", "i1", "HttpClient", "selectors", "decls", "vars", "consts", "template", "ForgotPasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ForgotPasswordComponent_Template_form_ngSubmit_8_listener", "ɵɵrestoreView", "_r3", "_r0", "ɵɵreference", "ɵɵresetView", "ɵɵelement", "ForgotPasswordComponent_Template_input_ngModelChange_16_listener", "$event", "ForgotPasswordComponent_Template_input_ngModelChange_23_listener", "ɵɵtemplate", "ForgotPasswordComponent_div_27_Template", "ForgotPasswordComponent_div_28_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\forgot-password\\forgot-password.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\forgot-password\\forgot-password.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\n\n@Component({\n  selector: 'app-forgot-password',\n  templateUrl: './forgot-password.component.html',\n  styleUrls: ['./forgot-password.component.css']  // Ensure your styles are referenced here\n\n})\nexport class ForgotPasswordComponent {\n  matricule!: number;\n  email!: string;\n  message = '';\n  error = '';\n\n  constructor(private http: HttpClient) {}\n\n  submitRequest(form: any) {\n    if (!form.valid) {\n      this.error = \"Veuillez remplir tous les champs correctement.\";\n      this.message = '';\n      return;\n    }\n  \n    const request = {\n      matricule: this.matricule,\n      email: this.email\n    };\n  \n    this.http.post('http://localhost:8000/api/v1/auth/resetPassword/send_request_to_Admin', request)\n      .subscribe({\n        next: (res: any) => {\n          this.message = \"Votre demande a été envoyée à l'administrateur.\";\n          this.error = '';\n        },\n        error: (err) => {\n          console.error(err);\n          this.error = \"Erreur lors de l'envoi. Veuillez vérifier vos informations.\";\n          this.message = '';\n        }\n      });\n  }\n}  ", "<div class=\"full-page-bg\">\n    <div class=\"container d-flex justify-content-center align-items-center vh-100\">\n      <div class=\"card p-4 shadow-lg rounded-4 custom-card\">\n        <div class=\"text-center mb-4\">\n          <h3 class=\"fw-bold text-primary\">Mot de passe oublié</h3>\n          <p class=\"text-muted\">Entrez votre matricule et votre adresse email pour envoyer une demande.</p>\n        </div>\n  \n        <form #form=\"ngForm\" (ngSubmit)=\"submitRequest(form)\">\n          <!-- Matricule -->\n          <div class=\"mb-3\">\n            <label class=\"form-label fw-semibold\">Matricule</label>\n            <div class=\"input-group\">\n              <span class=\"input-group-text bg-light\"><i class=\"bi bi-person-badge\"></i></span>\n              <input type=\"text\" [(ngModel)]=\"matricule\" name=\"matricule\" required class=\"form-control\" placeholder=\"Votre matricule\">\n            </div>\n          </div>\n  \n          <!-- Email -->\n          <div class=\"mb-3\">\n            <label class=\"form-label fw-semibold\">Email</label>\n            <div class=\"input-group\">\n              <span class=\"input-group-text bg-light\"><i class=\"bi bi-envelope\"></i></span>\n              <input type=\"email\" [(ngModel)]=\"email\" name=\"email\" required class=\"form-control\" placeholder=\"Votre email\">\n            </div>\n          </div>\n  \n          <!-- Submit -->\n          <button type=\"submit\" class=\"btn btn-primary w-100 fw-bold rounded-pill\">\n            <i class=\"bi bi-envelope-arrow-up\"></i> Envoyer la demande\n          </button>\n  \n          <!-- Messages -->\n          <div *ngIf=\"message\" class=\"alert alert-success text-center mt-3 rounded-pill py-2\">\n            {{ message }}\n          </div>\n          <div *ngIf=\"error\" class=\"alert alert-danger text-center mt-3 rounded-pill py-2\">\n            {{ error }}\n          </div>\n        </form>\n  \n        <!-- Back Link -->\n        <div class=\"text-center mt-3\">\n          <a routerLink=\"/login\" class=\"text-decoration-none text-secondary\">← Retour à la connexion</a>\n        </div>\n      </div>\n    </div>\n  </div>\n  "], "mappings": ";;;;;;;ICiCUA,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;IACAP,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAG,MAAA,CAAAC,KAAA,MACF;;;AD7BV,OAAM,MAAOC,uBAAuB;EAMlCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHxB,KAAAL,OAAO,GAAG,EAAE;IACZ,KAAAE,KAAK,GAAG,EAAE;EAE6B;EAEvCI,aAAaA,CAACC,IAAS;IACrB,IAAI,CAACA,IAAI,CAACC,KAAK,EAAE;MACf,IAAI,CAACN,KAAK,GAAG,gDAAgD;MAC7D,IAAI,CAACF,OAAO,GAAG,EAAE;MACjB;;IAGF,MAAMS,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,KAAK,EAAE,IAAI,CAACA;KACb;IAED,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,uEAAuE,EAAEH,OAAO,CAAC,CAC7FI,SAAS,CAAC;MACTC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACf,OAAO,GAAG,iDAAiD;QAChE,IAAI,CAACE,KAAK,GAAG,EAAE;MACjB,CAAC;MACDA,KAAK,EAAGc,GAAG,IAAI;QACbC,OAAO,CAACf,KAAK,CAACc,GAAG,CAAC;QAClB,IAAI,CAACd,KAAK,GAAG,6DAA6D;QAC1E,IAAI,CAACF,OAAO,GAAG,EAAE;MACnB;KACD,CAAC;EACN;;;uBAhCWG,uBAAuB,EAAAV,EAAA,CAAAyB,iBAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAvBjB,uBAAuB;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCTpClC,EAAA,CAAAC,cAAA,aAA0B;UAIiBD,EAAA,CAAAE,MAAA,+BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,WAAsB;UAAAD,EAAA,CAAAE,MAAA,8EAAuE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGnGH,EAAA,CAAAC,cAAA,iBAAsD;UAAjCD,EAAA,CAAAoC,UAAA,sBAAAC,0DAAA;YAAArC,EAAA,CAAAsC,aAAA,CAAAC,GAAA;YAAA,MAAAC,GAAA,GAAAxC,EAAA,CAAAyC,WAAA;YAAA,OAAYzC,EAAA,CAAA0C,WAAA,CAAAP,GAAA,CAAAtB,aAAA,CAAA2B,GAAA,CAAmB;UAAA,EAAC;UAEnDxC,EAAA,CAAAC,cAAA,cAAkB;UACsBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,eAAyB;UACiBD,EAAA,CAAA2C,SAAA,aAAkC;UAAA3C,EAAA,CAAAG,YAAA,EAAO;UACjFH,EAAA,CAAAC,cAAA,iBAAwH;UAArGD,EAAA,CAAAoC,UAAA,2BAAAQ,iEAAAC,MAAA;YAAA,OAAAV,GAAA,CAAAlB,SAAA,GAAA4B,MAAA;UAAA,EAAuB;UAA1C7C,EAAA,CAAAG,YAAA,EAAwH;UAK5HH,EAAA,CAAAC,cAAA,cAAkB;UACsBD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,eAAyB;UACiBD,EAAA,CAAA2C,SAAA,aAA8B;UAAA3C,EAAA,CAAAG,YAAA,EAAO;UAC7EH,EAAA,CAAAC,cAAA,iBAA6G;UAAzFD,EAAA,CAAAoC,UAAA,2BAAAU,iEAAAD,MAAA;YAAA,OAAAV,GAAA,CAAAjB,KAAA,GAAA2B,MAAA;UAAA,EAAmB;UAAvC7C,EAAA,CAAAG,YAAA,EAA6G;UAKjHH,EAAA,CAAAC,cAAA,kBAAyE;UACvED,EAAA,CAAA2C,SAAA,aAAuC;UAAC3C,EAAA,CAAAE,MAAA,4BAC1C;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAA+C,UAAA,KAAAC,uCAAA,kBAEM;UACNhD,EAAA,CAAA+C,UAAA,KAAAE,uCAAA,kBAEM;UACRjD,EAAA,CAAAG,YAAA,EAAO;UAGPH,EAAA,CAAAC,cAAA,eAA8B;UACuCD,EAAA,CAAAE,MAAA,yCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UA7BvEH,EAAA,CAAAI,SAAA,IAAuB;UAAvBJ,EAAA,CAAAkD,UAAA,YAAAf,GAAA,CAAAlB,SAAA,CAAuB;UAStBjB,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAkD,UAAA,YAAAf,GAAA,CAAAjB,KAAA,CAAmB;UAUrClB,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAkD,UAAA,SAAAf,GAAA,CAAA5B,OAAA,CAAa;UAGbP,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAkD,UAAA,SAAAf,GAAA,CAAA1B,KAAA,CAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}