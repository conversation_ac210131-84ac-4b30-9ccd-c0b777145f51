{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/groupe.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction ModifyGroupComponent_form_4_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtext(1, \" Group name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModifyGroupComponent_form_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 4);\n    i0.ɵɵlistener(\"ngSubmit\", function ModifyGroupComponent_form_4_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.updateGroupe());\n    });\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"label\", 6);\n    i0.ɵɵtext(3, \"Nom du groupe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 7, 8);\n    i0.ɵɵlistener(\"ngModelChange\", function ModifyGroupComponent_form_4_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.groupe.nomGroupe = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ModifyGroupComponent_form_4_div_6_Template, 2, 0, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 10)(8, \"button\", 11);\n    i0.ɵɵtext(9, \"Mettre \\u00E0 jour\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ModifyGroupComponent_form_4_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.cancel());\n    });\n    i0.ɵɵtext(11, \"Annuler\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r1 = i0.ɵɵreference(5);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.groupe.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", _r1.invalid && _r1.touched);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", _r1.invalid);\n  }\n}\nexport class ModifyGroupComponent {\n  constructor(route, groupeService, router) {\n    this.route = route;\n    this.groupeService = groupeService;\n    this.router = router;\n    this.groupeId = 0; // Group ID to be fetched from route\n    this.groupe = {\n      idGroupe: 0,\n      nomGroupe: ''\n    }; // Default group structure\n    this.loading = true; // Flag for loading state\n  }\n\n  ngOnInit() {\n    // Fetch the group ID from the route parameter\n    this.groupeId = Number(this.route.snapshot.paramMap.get('id'));\n    this.getGroupe(this.groupeId); // Fetch group data by ID\n  }\n  // Fetch the group by ID\n  getGroupe(id) {\n    this.groupeService.getGroupeById(id).subscribe(data => {\n      console.log(\"Group data:\", data);\n      this.groupe = data; // Bind the fetched group data to the groupe object\n      this.loading = false; // Set loading to false once data is loaded\n    }, err => {\n      console.error('Error loading group data', err);\n      this.loading = false;\n    });\n  }\n  // Update the group data\n  updateGroupe() {\n    this.groupeService.updateGroupe(this.groupeId, this.groupe).subscribe(response => {\n      console.log(\"Group updated successfully\", response);\n      setTimeout(() => {\n        this.router.navigate(['/groups']); // Navigate to the groups page after successful update\n      }, 100); // Delay navigation to ensure update completes first\n    }, error => {\n      console.error('Error updating group', error);\n    });\n  }\n  // Handle cancel action\n  cancel() {\n    this.router.navigate(['/groups']); // Navigate back to the groups list\n  }\n\n  static {\n    this.ɵfac = function ModifyGroupComponent_Factory(t) {\n      return new (t || ModifyGroupComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.GroupeService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModifyGroupComponent,\n      selectors: [[\"app-modify-group\"]],\n      decls: 5,\n      vars: 1,\n      consts: [[1, \"container\", \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"100vh\"], [1, \"card\", \"p-4\", 2, \"width\", \"100%\", \"max-width\", \"500px\"], [1, \"text-center\", \"mb-4\"], [3, \"ngSubmit\", 4, \"ngIf\"], [3, \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"nomGroupe\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", \"placeholder\", \"Enter group name\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"nomGroupe\", \"ngModel\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"mb-3\", \"d-flex\", \"justify-content-between\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"text-danger\"]],\n      template: function ModifyGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2);\n          i0.ɵɵtext(3, \"Modifier le groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, ModifyGroupComponent_form_4_Template, 12, 3, \"form\", 3);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupe);\n        }\n      },\n      dependencies: [i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ModifyGroupComponent_form_4_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "updateGroupe", "ModifyGroupComponent_form_4_Template_input_ngModelChange_4_listener", "$event", "ctx_r5", "groupe", "nomGroupe", "ɵɵtemplate", "ModifyGroupComponent_form_4_div_6_Template", "ModifyGroupComponent_form_4_Template_button_click_10_listener", "ctx_r6", "cancel", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "_r1", "invalid", "touched", "ModifyGroupComponent", "constructor", "route", "groupeService", "router", "groupeId", "idGroupe", "loading", "ngOnInit", "Number", "snapshot", "paramMap", "get", "getGroupe", "id", "getGroupeById", "subscribe", "data", "console", "log", "err", "error", "response", "setTimeout", "navigate", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "GroupeService", "Router", "selectors", "decls", "vars", "consts", "template", "ModifyGroupComponent_Template", "rf", "ctx", "ModifyGroupComponent_form_4_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\modify-group\\modify-group.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\modify-group\\modify-group.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\n\n@Component({\n  selector: 'app-modify-group',\n  templateUrl: './modify-group.component.html',\n  styleUrls: ['./modify-group.component.css']\n})\nexport class ModifyGroupComponent implements OnInit {\n  groupeId: number = 0; // Group ID to be fetched from route\n  groupe: Groupe = { idGroupe: 0, nomGroupe: '' }; // Default group structure\n  loading: boolean = true; // Flag for loading state\n\n  constructor(\n    private route: ActivatedRoute,\n    private groupeService: GroupeService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Fetch the group ID from the route parameter\n    this.groupeId = Number(this.route.snapshot.paramMap.get('id'));\n    this.getGroupe(this.groupeId); // Fetch group data by ID\n  }\n\n  // Fetch the group by ID\n  getGroupe(id: number): void {\n    this.groupeService.getGroupeById(id).subscribe(\n      (data: Groupe) => {\n        console.log(\"Group data:\", data);\n        this.groupe = data; // Bind the fetched group data to the groupe object\n        this.loading = false; // Set loading to false once data is loaded\n      },\n      (err) => {\n        console.error('Error loading group data', err);\n        this.loading = false;\n      }\n    );\n  }\n\n  // Update the group data\n  updateGroupe(): void {\n    this.groupeService.updateGroupe(this.groupeId, this.groupe).subscribe(\n      (response) => {\n        console.log(\"Group updated successfully\", response);\n        setTimeout(() => {\n          this.router.navigate(['/groups']); // Navigate to the groups page after successful update\n        }, 100); // Delay navigation to ensure update completes first\n      },\n      (error) => {\n        console.error('Error updating group', error);\n      }\n    );\n  }\n  \n  // Handle cancel action\n  cancel(): void {\n    this.router.navigate(['/groups']); // Navigate back to the groups list\n  }\n}\n", "<div class=\"container d-flex justify-content-center align-items-center\" style=\"height: 100vh;\">\n    <div class=\"card p-4\" style=\"width: 100%; max-width: 500px;\">\n      <h2 class=\"text-center mb-4\">Modifier le groupe</h2>\n  \n      <form (ngSubmit)=\"updateGroupe()\" *ngIf=\"groupe\">\n        <!-- Group Name -->\n        <div class=\"mb-3\">\n          <label for=\"nomGroupe\" class=\"form-label\">Nom du groupe</label>\n          <input\n            type=\"text\"\n            id=\"nomGroupe\"\n            [(ngModel)]=\"groupe.nomGroupe\"\n            name=\"nomGroupe\"\n            class=\"form-control\"\n            required\n            placeholder=\"Enter group name\"\n            #nomGroupe=\"ngModel\"\n          />\n          <div *ngIf=\"nomGroupe.invalid && nomGroupe.touched\" class=\"text-danger\">\n            Group name is required.\n          </div>\n        </div>\n  \n        <!-- Submit and Cancel buttons -->\n        <div class=\"mb-3 d-flex justify-content-between\">\n          <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"nomGroupe.invalid\">Mettre à jour</button>\n          <button type=\"button\" class=\"btn btn-secondary\" (click)=\"cancel()\">Annuler</button>\n        </div>\n      </form>\n    </div>\n  </div>\n  "], "mappings": ";;;;;;;ICkBUA,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAhBVH,EAAA,CAAAC,cAAA,cAAiD;IAA3CD,EAAA,CAAAI,UAAA,sBAAAC,8DAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAYT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAE/BX,EAAA,CAAAC,cAAA,aAAkB;IAC0BD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,kBASE;IANAD,EAAA,CAAAI,UAAA,2BAAAQ,oEAAAC,MAAA;MAAAb,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAd,EAAA,CAAAS,aAAA;MAAA,OAAaT,EAAA,CAAAU,WAAA,CAAAI,MAAA,CAAAC,MAAA,CAAAC,SAAA,GAAAH,MAAA,CACnB;IAAA,EADoC;IAHhCb,EAAA,CAAAG,YAAA,EASE;IACFH,EAAA,CAAAiB,UAAA,IAAAC,0CAAA,iBAEM;IACRlB,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAiD;IAC8BD,EAAA,CAAAE,MAAA,yBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnGH,EAAA,CAAAC,cAAA,kBAAmE;IAAnBD,EAAA,CAAAI,UAAA,mBAAAe,8DAAA;MAAAnB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAa,MAAA,GAAApB,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAU,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAACrB,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAfjFH,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAuB,UAAA,YAAAC,MAAA,CAAAT,MAAA,CAAAC,SAAA,CAA8B;IAO1BhB,EAAA,CAAAsB,SAAA,GAA4C;IAA5CtB,EAAA,CAAAuB,UAAA,SAAAE,GAAA,CAAAC,OAAA,IAAAD,GAAA,CAAAE,OAAA,CAA4C;IAOJ3B,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAuB,UAAA,aAAAE,GAAA,CAAAC,OAAA,CAA8B;;;ADftF,OAAM,MAAOE,oBAAoB;EAK/BC,YACUC,KAAqB,EACrBC,aAA4B,EAC5BC,MAAc;IAFd,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,QAAQ,GAAW,CAAC,CAAC,CAAC;IACtB,KAAAlB,MAAM,GAAW;MAAEmB,QAAQ,EAAE,CAAC;MAAElB,SAAS,EAAE;IAAE,CAAE,CAAC,CAAC;IACjD,KAAAmB,OAAO,GAAY,IAAI,CAAC,CAAC;EAMtB;;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,QAAQ,GAAGI,MAAM,CAAC,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC9D,IAAI,CAACC,SAAS,CAAC,IAAI,CAACR,QAAQ,CAAC,CAAC,CAAC;EACjC;EAEA;EACAQ,SAASA,CAACC,EAAU;IAClB,IAAI,CAACX,aAAa,CAACY,aAAa,CAACD,EAAE,CAAC,CAACE,SAAS,CAC3CC,IAAY,IAAI;MACfC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEF,IAAI,CAAC;MAChC,IAAI,CAAC9B,MAAM,GAAG8B,IAAI,CAAC,CAAC;MACpB,IAAI,CAACV,OAAO,GAAG,KAAK,CAAC,CAAC;IACxB,CAAC,EACAa,GAAG,IAAI;MACNF,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAED,GAAG,CAAC;MAC9C,IAAI,CAACb,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEA;EACAxB,YAAYA,CAAA;IACV,IAAI,CAACoB,aAAa,CAACpB,YAAY,CAAC,IAAI,CAACsB,QAAQ,EAAE,IAAI,CAAClB,MAAM,CAAC,CAAC6B,SAAS,CAClEM,QAAQ,IAAI;MACXJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEG,QAAQ,CAAC;MACnDC,UAAU,CAAC,MAAK;QACd,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,EACAH,KAAK,IAAI;MACRH,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,CACF;EACH;EAEA;EACA5B,MAAMA,CAAA;IACJ,IAAI,CAACW,MAAM,CAACoB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;EACrC;;;;uBAlDWxB,oBAAoB,EAAA5B,EAAA,CAAAqD,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvD,EAAA,CAAAqD,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAzD,EAAA,CAAAqD,iBAAA,CAAAC,EAAA,CAAAI,MAAA;IAAA;EAAA;;;YAApB9B,oBAAoB;MAAA+B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVjCjE,EAAA,CAAAC,cAAA,aAA+F;UAE5DD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEpDH,EAAA,CAAAiB,UAAA,IAAAkD,oCAAA,mBAwBO;UACTnE,EAAA,CAAAG,YAAA,EAAM;;;UAzB+BH,EAAA,CAAAsB,SAAA,GAAY;UAAZtB,EAAA,CAAAuB,UAAA,SAAA2C,GAAA,CAAAnD,MAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}