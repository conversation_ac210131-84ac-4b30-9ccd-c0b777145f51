package com.Rayen.Actionnaire;

import com.Rayen.Actionnaire.Entity.Actionnaire;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ActionnaireService {
    private final ActionnaireRepository ActionnaireRepository;


    public void saveUser(Actionnaire actionnaire){
        ActionnaireRepository.save(actionnaire);
    }

    public List<Actionnaire> findAllUsers(){
        return ActionnaireRepository.findAll();
    }



    public List<Actionnaire> findActionnairesbyUser(Long userId) {
      return  ActionnaireRepository.findAllByUserId(userId);
    }
}
