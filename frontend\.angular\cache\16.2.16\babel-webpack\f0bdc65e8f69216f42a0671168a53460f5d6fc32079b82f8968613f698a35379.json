{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../auth/authentication.service\";\nimport * as i3 from \"../services/portefeuille.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"myChart\"];\nfunction PortefeuillesComponent_li_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 28)(1, \"a\", 29);\n    i0.ɵɵelement(2, \"span\", 30);\n    i0.ɵɵtext(3, \" Dashboard \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 28)(1, \"a\", 31);\n    i0.ɵɵelement(2, \"span\", 32);\n    i0.ɵɵtext(3, \" Gestion des utilisateurs \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 28)(1, \"a\", 33);\n    i0.ɵɵelement(2, \"span\", 34);\n    i0.ɵɵtext(3, \" Gestion des groupes \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 28)(1, \"a\", 35);\n    i0.ɵɵelement(2, \"span\", 36);\n    i0.ɵɵtext(3, \" Gestion des transactions \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 28)(1, \"a\", 37);\n    i0.ɵɵelement(2, \"span\", 38);\n    i0.ɵɵtext(3, \" Gestion des actions \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 28)(1, \"a\", 39);\n    i0.ɵɵelement(2, \"span\", 40);\n    i0.ɵɵtext(3, \" Gestion des actionnaires \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_li_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 28)(1, \"a\", 41);\n    i0.ɵɵelement(2, \"span\", 42);\n    i0.ɵɵtext(3, \" Gestion des Portefeuilles \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PortefeuillesComponent_tr_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function PortefeuillesComponent_tr_54_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const p_r8 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.deletePortefeuille(p_r8.idPortefeuille));\n    });\n    i0.ɵɵtext(14, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const p_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(p_r8.idPortefeuille);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(p_r8.solde);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(p_r8.quantite);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 5, p_r8.dateCreation, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(p_r8.action == null ? null : p_r8.action.idAction);\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, 0.25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n    border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n    border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n    font-size: 14px;\\n    border: none;\\n    cursor: pointer;\\n    border-radius: 4px;\\n    font-weight: bold;\\n    transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n    font-family: 'Poppins', sans-serif;\\n    margin: 0;\\n    padding: 0;\\n    min-height: 100vh;\\n    background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n    background-size: 400% 400%;\\n    animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n    color: #fff;\\n}\\n\\n\\n.modal-content.border-danger[_ngcontent-%COMP%] {\\n    border: 2px solid #dc3545;\\n    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);\\n  }\\n  \\n  .modal-header.bg-danger[_ngcontent-%COMP%] {\\n    background-color: #dc3545 !important;\\n    color: white;\\n  }\\n  \\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n    0% {\\n        background-position: 0% 50%;\\n    }\\n\\n    50% {\\n        background-position: 100% 50%;\\n    }\\n\\n    100% {\\n        background-position: 0% 50%;\\n    }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 2rem;\\n    margin-top: 20px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 1.5rem;\\n    -webkit-backdrop-filter: blur(12px);\\n            backdrop-filter: blur(12px);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n    color: #fff;\\n    border-radius: 15px;\\n    overflow: hidden;\\n    border-collapse: separate;\\n    border-spacing: 0 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.15);\\n    color: #ffffff;\\n    font-weight: 600;\\n    text-align: center;\\n    border: none;\\n    padding: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.1);\\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\\n    text-align: center;\\n    border-radius: 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.01);\\n    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    vertical-align: middle;\\n    padding: 0.9rem;\\n    font-size: 0.95rem;\\n}\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n    background-color: #28a745;\\n    color: white;\\n    border: none;\\n    padding: 12px 25px;\\n    border-radius: 30px;\\n    text-transform: uppercase;\\n    font-weight: bold;\\n    transition: background 0.3s, transform 0.2s;\\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n    background-color: #218838;\\n    transform: translateY(-2px);\\n}\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n    color: #206ee1;\\n    cursor: pointer;\\n    font-size: 20px;\\n    margin: 0 10px;\\n    transition: color 0.3s;\\n}\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n    color: #d22d2d;\\n    cursor: pointer;\\n    font-size: 20px;\\n    transition: color 0.3s;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n    background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n    color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n    text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n    background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n    background-color: #fff;\\n    padding: 1.5rem;\\n    color: #333;\\n    font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n    background-color: #f1f1f1;\\n    padding: 1rem;\\n    border-top: 1px solid #ddd;\\n    display: flex;\\n    justify-content: center; \\n\\n    gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem;\\n    font-size: 1rem;\\n    border-radius: 0.3rem;\\n    transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    color: #fff;\\n    border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n    transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n    background-color: #007bff;\\n    color: #fff;\\n    border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n    transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column; \\n\\n    align-items: flex-start; \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n    margin-top: 10px;\\n    background-color: rgba(255, 255, 255, 0.1); \\n\\n    border: 1px solid rgba(255, 255, 255, 0.3); \\n\\n    border-radius: 30px; \\n\\n    color: #fff; \\n\\n    padding: 10px 20px; \\n\\n    font-size: 1rem; \\n\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); \\n\\n    width: 100%; \\n\\n    max-width: 400px; \\n\\n    transition: all 0.3s ease; \\n\\n}\\n\\n.search-bar[_ngcontent-%COMP%]:focus {\\n    outline: none; \\n\\n    border-color: #007bff; \\n\\n    box-shadow: 0 0 8px rgba(0, 123, 255, 0.6); \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%]::placeholder {\\n    color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n    background: rgba(20, 33, 59, 0.9); \\n\\n    color: #fff;\\n    min-height: 100vh;\\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #ccc;\\n    transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, 0.1);\\n    border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n    background-color: #000 !important;\\n    color: #fff;\\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    color: #fff !important;\\n    position: relative;\\n    padding: 0.5rem 1rem;\\n    transition: color 0.3s ease;\\n    font-weight: 500;\\n    letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n    content: '';\\n    position: absolute;\\n    left: 0;\\n    bottom: 0;\\n    height: 2px;\\n    width: 0;\\n    background: #ff4c60;\\n    transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n    color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n    width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n    height: 30px;\\n    width: 30px;\\n    object-fit: cover;\\n    border-radius: 50%; \\n\\n    margin-right: 8px;\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcG9ydGVmZXVpbGxlcy9wb3J0ZWZldWlsbGVzLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7SUFDSSxrQkFBa0I7QUFDdEI7O0FBRUE7SUFDSSxXQUFXO0lBQ1gsWUFBWTtJQUNaLDJCQUEyQjtBQUMvQjs7QUFFQTs7RUFFRTs7QUFFRjtJQUNJLGVBQWU7SUFDZixNQUFNO0lBQ04sU0FBUztJQUNULE9BQU87SUFDUCxZQUFZLEVBQUUsc0JBQXNCO0lBQ3BDLFVBQVU7SUFDViw2Q0FBNkM7QUFDakQ7O0FBRUE7SUFFSSxnQkFBZ0I7SUFDaEIsU0FBUyxFQUFFLHFCQUFxQjtJQUNoQywwQkFBMEI7SUFDMUIsa0JBQWtCO0lBQ2xCLGtCQUFrQjtJQUNsQixnQkFBZ0IsRUFBRSw2REFBNkQ7QUFDbkY7O0FBRUE7SUFDSSxnQkFBZ0I7SUFDaEIsY0FBYztBQUNsQjs7QUFFQTtJQUNJLGlCQUFpQjtJQUNqQixXQUFXO0FBQ2Y7O0FBRUE7SUFDSSxjQUFjO0FBQ2xCOztBQUVBOztJQUVJLGNBQWM7QUFDbEI7O0FBRUE7SUFDSSxpQkFBaUI7SUFDakIseUJBQXlCO0FBQzdCOztBQUVBOztFQUVFOztBQUVGO0lBQ0ksbUJBQW1CO0lBQ25CLHNCQUFzQjtJQUN0QixlQUFlO0lBQ2YscUNBQXFDO0lBQ3JDLDhDQUE4QztBQUNsRDs7QUFFQTtJQUNJLG9CQUFvQjtJQUNwQixlQUFlO0lBQ2YsZ0JBQWdCO0FBQ3BCOztBQUVBO0lBQ0ksV0FBVztJQUNYLHlDQUF5QztJQUN6QyxxQ0FBcUM7QUFDekM7O0FBRUE7SUFDSSx5QkFBeUI7SUFDekIsOENBQThDO0FBQ2xEOztBQUVBOztFQUVFOztBQUVGO0lBQ0ksNkJBQTZCO0FBQ2pDOztBQUVBO0lBQ0ksZ0NBQWdDO0FBQ3BDOztBQUVBLHNCQUFzQjtBQUN0QjtJQUNJLGlCQUFpQjtJQUNqQixlQUFlO0lBQ2YsWUFBWTtJQUNaLGVBQWU7SUFDZixrQkFBa0I7SUFDbEIsaUJBQWlCO0lBQ2pCLHlCQUF5QjtBQUM3Qjs7QUFFQTtJQUNJLGtDQUFrQztJQUNsQyxTQUFTO0lBQ1QsVUFBVTtJQUNWLGlCQUFpQjtJQUNqQix1RUFBdUU7SUFDdkUsMEJBQTBCO0lBQzFCLHlDQUF5QztJQUN6QyxXQUFXO0FBQ2Y7OztBQUdBO0lBQ0kseUJBQXlCO0lBQ3pCLDJDQUEyQztFQUM3Qzs7RUFFQTtJQUNFLG9DQUFvQztJQUNwQyxZQUFZO0VBQ2Q7OztBQUdGO0lBQ0k7UUFDSSwyQkFBMkI7SUFDL0I7O0lBRUE7UUFDSSw2QkFBNkI7SUFDakM7O0lBRUE7UUFDSSwyQkFBMkI7SUFDL0I7QUFDSjs7QUFFQTtJQUNJLHFDQUFxQztJQUNyQyxtQkFBbUI7SUFDbkIsYUFBYTtJQUNiLGdCQUFnQjtBQUNwQjs7QUFFQTtJQUNJLHFDQUFxQztJQUNyQyxtQkFBbUI7SUFDbkIsZUFBZTtJQUNmLG1DQUEyQjtZQUEzQiwyQkFBMkI7SUFDM0IsMENBQTBDO0FBQzlDOztBQUVBO0lBQ0ksV0FBVztJQUNYLG1CQUFtQjtJQUNuQixnQkFBZ0I7SUFDaEIseUJBQXlCO0lBQ3pCLHNCQUFzQjtBQUMxQjs7QUFFQTtJQUNJLDJDQUEyQztJQUMzQyxjQUFjO0lBQ2QsZ0JBQWdCO0lBQ2hCLGtCQUFrQjtJQUNsQixZQUFZO0lBQ1osYUFBYTtBQUNqQjs7QUFFQTtJQUNJLDBDQUEwQztJQUMxQyxxREFBcUQ7SUFDckQsa0JBQWtCO0lBQ2xCLG1CQUFtQjtBQUN2Qjs7QUFFQTtJQUNJLHNCQUFzQjtJQUN0QiwrQ0FBK0M7QUFDbkQ7O0FBRUE7SUFDSSxzQkFBc0I7SUFDdEIsZUFBZTtJQUNmLGtCQUFrQjtBQUN0Qjs7QUFFQTtJQUNJLHlCQUF5QjtJQUN6QixZQUFZO0lBQ1osWUFBWTtJQUNaLGtCQUFrQjtJQUNsQixtQkFBbUI7SUFDbkIseUJBQXlCO0lBQ3pCLGlCQUFpQjtJQUNqQiwyQ0FBMkM7SUFDM0MseUNBQXlDO0FBQzdDOztBQUVBO0lBQ0kseUJBQXlCO0lBQ3pCLDJCQUEyQjtBQUMvQjs7QUFFQTtJQUNJLGNBQWM7SUFDZCxlQUFlO0lBQ2YsZUFBZTtJQUNmLGNBQWM7SUFDZCxzQkFBc0I7QUFDMUI7O0FBRUE7SUFDSSxjQUFjO0FBQ2xCOztBQUVBO0lBQ0ksY0FBYztJQUNkLGVBQWU7SUFDZixlQUFlO0lBQ2Ysc0JBQXNCO0FBQzFCOztBQUVBO0lBQ0ksY0FBYztBQUNsQjs7QUFFQTtJQUNJLG9DQUFvQztBQUN4Qzs7QUFFQTtJQUNJLFdBQVc7QUFDZjs7QUFFQTtJQUNJLHFCQUFxQjtBQUN6Qjs7QUFFQTtJQUNJLHlCQUF5QjtBQUM3Qjs7QUFFQSxlQUFlO0FBQ2Y7SUFDSSxzQkFBc0I7SUFDdEIsZUFBZTtJQUNmLFdBQVc7SUFDWCxlQUFlO0FBQ25COztBQUVBLGlCQUFpQjtBQUNqQjtJQUNJLHlCQUF5QjtJQUN6QixhQUFhO0lBQ2IsMEJBQTBCO0lBQzFCLGFBQWE7SUFDYix1QkFBdUIsRUFBRSxnQ0FBZ0M7SUFDekQsU0FBUyxFQUFFLDhCQUE4QjtBQUM3Qzs7QUFFQTtJQUNJLG9CQUFvQjtJQUNwQixlQUFlO0lBQ2YscUJBQXFCO0lBQ3JCLGlEQUFpRDtBQUNyRDs7QUFFQTtJQUNJLHlCQUF5QjtJQUN6QixXQUFXO0lBQ1gsWUFBWTtBQUNoQjs7QUFFQTtJQUNJLHlCQUF5QjtJQUN6QiwyQkFBMkI7QUFDL0I7O0FBRUE7SUFDSSx5QkFBeUI7SUFDekIsV0FBVztJQUNYLFlBQVk7QUFDaEI7O0FBRUE7SUFDSSx5QkFBeUI7SUFDekIsMkJBQTJCO0FBQy9COztBQUVBLCtDQUErQztBQUMvQztJQUNJLGFBQWE7SUFDYixzQkFBc0IsRUFBRSwyQkFBMkI7SUFDbkQsdUJBQXVCLEVBQUUsaUNBQWlDO0FBQzlEOztBQUVBLHNCQUFzQjtBQUN0QjtJQUNJLGdCQUFnQjtJQUNoQiwwQ0FBMEMsRUFBRSxpQ0FBaUM7SUFDN0UsMENBQTBDLEVBQUUsa0JBQWtCO0lBQzlELG1CQUFtQixFQUFFLG9CQUFvQjtJQUN6QyxXQUFXLEVBQUUsZUFBZTtJQUM1QixrQkFBa0IsRUFBRSw2QkFBNkI7SUFDakQsZUFBZSxFQUFFLHFDQUFxQztJQUN0RCx3Q0FBd0MsRUFBRSw0QkFBNEI7SUFDdEUsV0FBVyxFQUFFLHVCQUF1QjtJQUNwQyxnQkFBZ0IsRUFBRSw0REFBNEQ7SUFDOUUseUJBQXlCLEVBQUUsc0JBQXNCO0FBQ3JEOztBQUVBO0lBQ0ksYUFBYSxFQUFFLDJCQUEyQjtJQUMxQyxxQkFBcUIsRUFBRSx3Q0FBd0M7SUFDL0QsMENBQTBDLEVBQUUscUJBQXFCO0FBQ3JFOztBQUVBLDJCQUEyQjtBQUMzQjtJQUNJLCtCQUErQixFQUFFLDJCQUEyQjtBQUNoRTs7O0FBR0E7SUFDSSxpQ0FBaUMsRUFBRSwrQkFBK0I7SUFDbEUsV0FBVztJQUNYLGlCQUFpQjtJQUNqQixnREFBZ0Q7QUFDcEQ7O0FBRUE7SUFDSSxXQUFXO0lBQ1gsaURBQWlEO0FBQ3JEOztBQUVBOztJQUVJLFdBQVc7SUFDWCwwQ0FBMEM7SUFDMUMsa0JBQWtCO0FBQ3RCOzs7QUFHQTtJQUNJLGlDQUFpQztJQUNqQyxXQUFXO0lBQ1gsaURBQWlEO0FBQ3JEOztBQUVBOztJQUVJLHNCQUFzQjtBQUMxQjs7QUFFQTtJQUNJLDBCQUEwQjtBQUM5Qjs7OztBQUlBO0lBQ0ksc0JBQXNCO0lBQ3RCLGtCQUFrQjtJQUNsQixvQkFBb0I7SUFDcEIsMkJBQTJCO0lBQzNCLGdCQUFnQjtJQUNoQixxQkFBcUI7QUFDekI7O0FBRUE7SUFDSSxXQUFXO0lBQ1gsa0JBQWtCO0lBQ2xCLE9BQU87SUFDUCxTQUFTO0lBQ1QsV0FBVztJQUNYLFFBQVE7SUFDUixtQkFBbUI7SUFDbkIsMkJBQTJCO0FBQy9COztBQUVBO0lBQ0kseUJBQXlCO0FBQzdCOztBQUVBO0lBQ0ksV0FBVztBQUNmOztBQUVBO0lBQ0ksWUFBWTtJQUNaLFdBQVc7SUFDWCxpQkFBaUI7SUFDakIsa0JBQWtCLEVBQUUsNkJBQTZCO0lBQ2pELGlCQUFpQjtFQUNuQiIsInNvdXJjZXNDb250ZW50IjpbImJvZHkge1xyXG4gICAgZm9udC1zaXplOiAuODc1cmVtO1xyXG59XHJcblxyXG4uZmVhdGhlciB7XHJcbiAgICB3aWR0aDogMTZweDtcclxuICAgIGhlaWdodDogMTZweDtcclxuICAgIHZlcnRpY2FsLWFsaWduOiB0ZXh0LWJvdHRvbTtcclxufVxyXG5cclxuLypcclxuICogU2lkZWJhclxyXG4gKi9cclxuXHJcbi5zaWRlYmFyIHtcclxuICAgIHBvc2l0aW9uOiBmaXhlZDtcclxuICAgIHRvcDogMDtcclxuICAgIGJvdHRvbTogMDtcclxuICAgIGxlZnQ6IDA7XHJcbiAgICB6LWluZGV4OiAxMDA7IC8qIEJlaGluZCB0aGUgbmF2YmFyICovXHJcbiAgICBwYWRkaW5nOiAwO1xyXG4gICAgYm94LXNoYWRvdzogaW5zZXQgLTFweCAwIDAgcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG59XHJcblxyXG4uc2lkZWJhci1zdGlja3kge1xyXG4gICAgcG9zaXRpb246IC13ZWJraXQtc3RpY2t5O1xyXG4gICAgcG9zaXRpb246IHN0aWNreTtcclxuICAgIHRvcDogNDhweDsgLyogSGVpZ2h0IG9mIG5hdmJhciAqL1xyXG4gICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNDhweCk7XHJcbiAgICBwYWRkaW5nLXRvcDogLjVyZW07XHJcbiAgICBvdmVyZmxvdy14OiBoaWRkZW47XHJcbiAgICBvdmVyZmxvdy15OiBhdXRvOyAvKiBTY3JvbGxhYmxlIGNvbnRlbnRzIGlmIHZpZXdwb3J0IGlzIHNob3J0ZXIgdGhhbiBjb250ZW50LiAqL1xyXG59XHJcblxyXG4uc2lkZWJhciAubmF2LWxpbmsge1xyXG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgIGNvbG9yOiAjMDAwMDAwO1xyXG59XHJcblxyXG4uc2lkZWJhciAubmF2LWxpbmsgLmZlYXRoZXIge1xyXG4gICAgbWFyZ2luLXJpZ2h0OiA0cHg7XHJcbiAgICBjb2xvcjogIzk5OTtcclxufVxyXG5cclxuLnNpZGViYXIgLm5hdi1saW5rLmFjdGl2ZSB7XHJcbiAgICBjb2xvcjogIzAwN2JmZjtcclxufVxyXG5cclxuLnNpZGViYXIgLm5hdi1saW5rOmhvdmVyIC5mZWF0aGVyLFxyXG4uc2lkZWJhciAubmF2LWxpbmsuYWN0aXZlIC5mZWF0aGVyIHtcclxuICAgIGNvbG9yOiBpbmhlcml0O1xyXG59XHJcblxyXG4uc2lkZWJhci1oZWFkaW5nIHtcclxuICAgIGZvbnQtc2l6ZTogLjc1cmVtO1xyXG4gICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcclxufVxyXG5cclxuLypcclxuICogTmF2YmFyXHJcbiAqL1xyXG5cclxuLm5hdmJhci1icmFuZCB7XHJcbiAgICBwYWRkaW5nLXRvcDogLjc1cmVtO1xyXG4gICAgcGFkZGluZy1ib3R0b206IC43NXJlbTtcclxuICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMC4yNSk7XHJcbiAgICBib3gtc2hhZG93OiBpbnNldCAtMXB4IDAgMCByZ2JhKDAsIDAsIDAsIDAuMjUpO1xyXG59XHJcblxyXG4ubmF2YmFyIC5mb3JtLWNvbnRyb2wge1xyXG4gICAgcGFkZGluZzogLjc1cmVtIDFyZW07XHJcbiAgICBib3JkZXItd2lkdGg6IDA7XHJcbiAgICBib3JkZXItcmFkaXVzOiAwO1xyXG59XHJcblxyXG4uZm9ybS1jb250cm9sLWRhcmsge1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIC4xKTtcclxuICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAuMSk7XHJcbn1cclxuXHJcbi5mb3JtLWNvbnRyb2wtZGFyazpmb2N1cyB7XHJcbiAgICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xyXG4gICAgYm94LXNoYWRvdzogMCAwIDAgM3B4IHJnYmEoMjU1LCAyNTUsIDI1NSwgLjI1KTtcclxufVxyXG5cclxuLypcclxuICogVXRpbGl0aWVzXHJcbiAqL1xyXG5cclxuLmJvcmRlci10b3Age1xyXG4gICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlNWU1ZTU7XHJcbn1cclxuXHJcbi5ib3JkZXItYm90dG9tIHtcclxuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTVlNWU1O1xyXG59XHJcblxyXG4vKiBCYXNlIGJ1dHRvbiBzdHlsZSAqL1xyXG4uYnRuIHtcclxuICAgIHBhZGRpbmc6IDhweCAxNnB4O1xyXG4gICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgYm9yZGVyOiBub25lO1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgYm9yZGVyLXJhZGl1czogNHB4O1xyXG4gICAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG59XHJcblxyXG5ib2R5IHtcclxuICAgIGZvbnQtZmFtaWx5OiAnUG9wcGlucycsIHNhbnMtc2VyaWY7XHJcbiAgICBtYXJnaW46IDA7XHJcbiAgICBwYWRkaW5nOiAwO1xyXG4gICAgbWluLWhlaWdodDogMTAwdmg7XHJcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoLTQ1ZGVnLCAjMjQzNzVhLCAjMTAyMDNhLCAjMTQyMTNiLCAjMGMxNzI5KTtcclxuICAgIGJhY2tncm91bmQtc2l6ZTogNDAwJSA0MDAlO1xyXG4gICAgYW5pbWF0aW9uOiBncmFkaWVudE1vdmUgMjBzIGVhc2UgaW5maW5pdGU7XHJcbiAgICBjb2xvcjogI2ZmZjtcclxufVxyXG5cclxuXHJcbi5tb2RhbC1jb250ZW50LmJvcmRlci1kYW5nZXIge1xyXG4gICAgYm9yZGVyOiAycHggc29saWQgI2RjMzU0NTtcclxuICAgIGJveC1zaGFkb3c6IDAgMCAxMHB4IHJnYmEoMjIwLCA1MywgNjksIDAuNSk7XHJcbiAgfVxyXG4gIFxyXG4gIC5tb2RhbC1oZWFkZXIuYmctZGFuZ2VyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNkYzM1NDUgIWltcG9ydGFudDtcclxuICAgIGNvbG9yOiB3aGl0ZTtcclxuICB9XHJcbiAgXHJcblxyXG5Aa2V5ZnJhbWVzIGdyYWRpZW50TW92ZSB7XHJcbiAgICAwJSB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMCUgNTAlO1xyXG4gICAgfVxyXG5cclxuICAgIDUwJSB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMTAwJSA1MCU7XHJcbiAgICB9XHJcblxyXG4gICAgMTAwJSB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMCUgNTAlO1xyXG4gICAgfVxyXG59XHJcblxyXG4uY29udGFpbmVyLWZsdWlkIHtcclxuICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNik7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxNXB4O1xyXG4gICAgcGFkZGluZzogMnJlbTtcclxuICAgIG1hcmdpbi10b3A6IDIwcHg7XHJcbn1cclxuXHJcbi50YWJsZS1yZXNwb25zaXZlIHtcclxuICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNik7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxNXB4O1xyXG4gICAgcGFkZGluZzogMS41cmVtO1xyXG4gICAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEycHgpO1xyXG4gICAgYm94LXNoYWRvdzogMCA4cHggMzJweCByZ2JhKDAsIDAsIDAsIDAuMjUpO1xyXG59XHJcblxyXG4udGFibGUge1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxNXB4O1xyXG4gICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuICAgIGJvcmRlci1jb2xsYXBzZTogc2VwYXJhdGU7XHJcbiAgICBib3JkZXItc3BhY2luZzogMCAxMnB4O1xyXG59XHJcblxyXG4udGFibGUgdGhlYWQgdGgge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjE1KTtcclxuICAgIGNvbG9yOiAjZmZmZmZmO1xyXG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgIGJvcmRlcjogbm9uZTtcclxuICAgIHBhZGRpbmc6IDFyZW07XHJcbn1cclxuXHJcbi50YWJsZSB0Ym9keSB0ciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XHJcbiAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlLCBib3gtc2hhZG93IDAuMnMgZWFzZTtcclxuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbn1cclxuXHJcbi50YWJsZSB0Ym9keSB0cjpob3ZlciB7XHJcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDEpO1xyXG4gICAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XHJcbn1cclxuXHJcbi50YWJsZSB0ZCB7XHJcbiAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xyXG4gICAgcGFkZGluZzogMC45cmVtO1xyXG4gICAgZm9udC1zaXplOiAwLjk1cmVtO1xyXG59XHJcblxyXG4uYnRuLWN1c3RvbSB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjhhNzQ1O1xyXG4gICAgY29sb3I6IHdoaXRlO1xyXG4gICAgYm9yZGVyOiBub25lO1xyXG4gICAgcGFkZGluZzogMTJweCAyNXB4O1xyXG4gICAgYm9yZGVyLXJhZGl1czogMzBweDtcclxuICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XHJcbiAgICBmb250LXdlaWdodDogYm9sZDtcclxuICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQgMC4zcywgdHJhbnNmb3JtIDAuMnM7XHJcbiAgICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHJnYmEoMCwgMCwgMCwgMC4yKTtcclxufVxyXG5cclxuLmJ0bi1jdXN0b206aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzIxODgzODtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcclxufVxyXG5cclxuLmVkaXQtaWNvbiB7XHJcbiAgICBjb2xvcjogIzIwNmVlMTtcclxuICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIGZvbnQtc2l6ZTogMjBweDtcclxuICAgIG1hcmdpbjogMCAxMHB4O1xyXG4gICAgdHJhbnNpdGlvbjogY29sb3IgMC4zcztcclxufVxyXG5cclxuLmVkaXQtaWNvbjpob3ZlciB7XHJcbiAgICBjb2xvcjogI2ZmZmZmZjtcclxufVxyXG5cclxuLmRlbGV0ZS1pY29uIHtcclxuICAgIGNvbG9yOiAjZDIyZDJkO1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgZm9udC1zaXplOiAyMHB4O1xyXG4gICAgdHJhbnNpdGlvbjogY29sb3IgMC4zcztcclxufVxyXG5cclxuLmRlbGV0ZS1pY29uOmhvdmVyIHtcclxuICAgIGNvbG9yOiAjZmZmZmZmO1xyXG59XHJcblxyXG4ubmF2YmFyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMxYzFjMWMgIWltcG9ydGFudDtcclxufVxyXG5cclxuaDIge1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbn1cclxuXHJcbmEucm91dGVyLWxpbmsge1xyXG4gICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG59XHJcblxyXG4ubW9kYWwtaGVhZGVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMyNTI1Mjg7XHJcbn1cclxuXHJcbi8qIE1vZGFsIEJvZHkgKi9cclxuLm1vZGFsLWJvZHkge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjtcclxuICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuICAgIGNvbG9yOiAjMzMzO1xyXG4gICAgZm9udC1zaXplOiAxcmVtO1xyXG59XHJcblxyXG4vKiBNb2RhbCBGb290ZXIgKi9cclxuLm1vZGFsLWZvb3RlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjFmMWYxO1xyXG4gICAgcGFkZGluZzogMXJlbTtcclxuICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZGRkO1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOyAvKiBDZW50ZXIgYnV0dG9ucyBob3Jpem9udGFsbHkgKi9cclxuICAgIGdhcDogMXJlbTsgLyogU3BhY2UgYmV0d2VlbiB0aGUgYnV0dG9ucyAqL1xyXG59XHJcblxyXG4ubW9kYWwtZm9vdGVyIGJ1dHRvbiB7XHJcbiAgICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcclxuICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDAuM3JlbTtcclxuICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4zcywgdHJhbnNmb3JtIDAuMnM7XHJcbn1cclxuXHJcbi5tb2RhbC1mb290ZXIgLmJ0bi1zZWNvbmRhcnkge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzZjNzU3ZDtcclxuICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgYm9yZGVyOiBub25lO1xyXG59XHJcblxyXG4ubW9kYWwtZm9vdGVyIC5idG4tc2Vjb25kYXJ5OmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICM1YTYyNjg7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbn1cclxuXHJcbi5tb2RhbC1mb290ZXIgLmJ0bi1wcmltYXJ5IHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMwMDdiZmY7XHJcbiAgICBjb2xvcjogI2ZmZjtcclxuICAgIGJvcmRlcjogbm9uZTtcclxufVxyXG5cclxuLm1vZGFsLWZvb3RlciAuYnRuLXByaW1hcnk6aG92ZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzAwNTZiMztcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcclxufVxyXG5cclxuLyogRW5zdXJlIHNlYXJjaCBiYXIgYXBwZWFycyBiZWxvdyB0aGUgYnV0dG9uICovXHJcbi5tYi0zIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOyAvKiBTdGFjayBpdGVtcyB2ZXJ0aWNhbGx5ICovXHJcbiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsgLyogQWxpZ24gdGhlIGl0ZW1zIHRvIHRoZSBzdGFydCAqL1xyXG59XHJcblxyXG4vKiBTZWFyY2ggQmFyIFN0eWxlcyAqL1xyXG4uc2VhcmNoLWJhciB7XHJcbiAgICBtYXJnaW4tdG9wOiAxMHB4O1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpOyAvKiBUcmFuc3BhcmVudCB3aGl0ZSBiYWNrZ3JvdW5kICovXHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7IC8qIFN1YnRsZSBib3JkZXIgKi9cclxuICAgIGJvcmRlci1yYWRpdXM6IDMwcHg7IC8qIFJvdW5kZWQgY29ybmVycyAqL1xyXG4gICAgY29sb3I6ICNmZmY7IC8qIFdoaXRlIHRleHQgKi9cclxuICAgIHBhZGRpbmc6IDEwcHggMjBweDsgLyogUGFkZGluZyBpbnNpZGUgdGhlIGlucHV0ICovXHJcbiAgICBmb250LXNpemU6IDFyZW07IC8qIEZvbnQgc2l6ZSBmb3IgYmV0dGVyIHJlYWRhYmlsaXR5ICovXHJcbiAgICBib3gtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLCAwLCAwLCAwLjMpOyAvKiBTbGlnaHQgc2hhZG93IGZvciBkZXB0aCAqL1xyXG4gICAgd2lkdGg6IDEwMCU7IC8qIE1ha2UgaXQgcmVzcG9uc2l2ZSAqL1xyXG4gICAgbWF4LXdpZHRoOiA0MDBweDsgLyogU2V0IGEgbWF4IHdpZHRoIGZvciBiZXR0ZXIgYXBwZWFyYW5jZSBvbiBsYXJnZXIgc2NyZWVucyAqL1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsgLyogU21vb3RoIHRyYW5zaXRpb24gKi9cclxufVxyXG5cclxuLnNlYXJjaC1iYXI6Zm9jdXMge1xyXG4gICAgb3V0bGluZTogbm9uZTsgLyogUmVtb3ZlIGRlZmF1bHQgb3V0bGluZSAqL1xyXG4gICAgYm9yZGVyLWNvbG9yOiAjMDA3YmZmOyAvKiBIaWdobGlnaHQgd2l0aCBibHVlIGJvcmRlciBvbiBmb2N1cyAqL1xyXG4gICAgYm94LXNoYWRvdzogMCAwIDhweCByZ2JhKDAsIDEyMywgMjU1LCAwLjYpOyAvKiBCbHVlIGdsb3cgZWZmZWN0ICovXHJcbn1cclxuXHJcbi8qIFBsYWNlaG9sZGVyIHRleHQgY29sb3IgKi9cclxuLnNlYXJjaC1iYXI6OnBsYWNlaG9sZGVyIHtcclxuICAgIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNyk7IC8qIExpZ2h0IGdyYXkgcGxhY2Vob2xkZXIgKi9cclxufVxyXG5cclxuXHJcbi5jdXN0b20tc2lkZWJhciB7XHJcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDIwLCAzMywgNTksIDAuOSk7IC8qIGRhcmssIHNsaWdodGx5IHRyYW5zcGFyZW50ICovXHJcbiAgICBjb2xvcjogI2ZmZjtcclxuICAgIG1pbi1oZWlnaHQ6IDEwMHZoO1xyXG4gICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xyXG59XHJcblxyXG4uY3VzdG9tLXNpZGViYXIgLm5hdi1saW5rIHtcclxuICAgIGNvbG9yOiAjY2NjO1xyXG4gICAgdHJhbnNpdGlvbjogY29sb3IgMC4zcyBlYXNlLCBiYWNrZ3JvdW5kIDAuM3MgZWFzZTtcclxufVxyXG5cclxuLmN1c3RvbS1zaWRlYmFyIC5uYXYtbGluazpob3ZlcixcclxuLmN1c3RvbS1zaWRlYmFyIC5uYXYtbGluay5hY3RpdmUge1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XHJcbiAgICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbn1cclxuXHJcblxyXG4uY3VzdG9tLXRvcGJhciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDAwICFpbXBvcnRhbnQ7XHJcbiAgICBjb2xvcjogI2ZmZjtcclxuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XHJcbn1cclxuXHJcbi5jdXN0b20tdG9wYmFyIC5uYXZiYXItYnJhbmQsXHJcbi5jdXN0b20tdG9wYmFyIC5uYXYtbGluayB7XHJcbiAgICBjb2xvcjogI2ZmZiAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uY3VzdG9tLXRvcGJhciAubmF2LWxpbms6aG92ZXIge1xyXG4gICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbn1cclxuXHJcblxyXG5cclxuLm5hdmJhciAubmF2LWxpbmsge1xyXG4gICAgY29sb3I6ICNmZmYgIWltcG9ydGFudDtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xyXG4gICAgdHJhbnNpdGlvbjogY29sb3IgMC4zcyBlYXNlO1xyXG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgIGxldHRlci1zcGFjaW5nOiAwLjVweDtcclxufVxyXG5cclxuLm5hdmJhciAubmF2LWxpbms6OmFmdGVyIHtcclxuICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgbGVmdDogMDtcclxuICAgIGJvdHRvbTogMDtcclxuICAgIGhlaWdodDogMnB4O1xyXG4gICAgd2lkdGg6IDA7XHJcbiAgICBiYWNrZ3JvdW5kOiAjZmY0YzYwO1xyXG4gICAgdHJhbnNpdGlvbjogd2lkdGggMC4zcyBlYXNlO1xyXG59XHJcblxyXG4ubmF2YmFyIC5uYXYtbGluazpob3ZlciB7XHJcbiAgICBjb2xvcjogI2ZmNGM2MCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4ubmF2YmFyIC5uYXYtbGluazpob3Zlcjo6YWZ0ZXIge1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbn1cclxuXHJcbi5sb2dvLWltZyB7XHJcbiAgICBoZWlnaHQ6IDMwcHg7XHJcbiAgICB3aWR0aDogMzBweDtcclxuICAgIG9iamVjdC1maXQ6IGNvdmVyO1xyXG4gICAgYm9yZGVyLXJhZGl1czogNTAlOyAvKiBPcHRpb25hbDogbWFrZXMgaXQgcm91bmQgKi9cclxuICAgIG1hcmdpbi1yaWdodDogOHB4O1xyXG4gIH1cclxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\";\nexport class PortefeuillesComponent {\n  constructor(router, authService, ngZone, portefeuilleService, cdr // Inject ChangeDetectorRef\n  ) {\n    this.router = router;\n    this.authService = authService;\n    this.ngZone = ngZone;\n    this.portefeuilleService = portefeuilleService;\n    this.cdr = cdr;\n    this.portefeuilles = [];\n  }\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  ngOnInit() {\n    this.portefeuilleService.getAllPortefeuilles().subscribe({\n      next: data => {\n        this.portefeuilles = data;\n        console.log('Portefeuilles:', this.portefeuilles);\n      },\n      error: err => {\n        console.error('Erreur lors du chargement des portefeuilles', err);\n      }\n    });\n  }\n  logout() {\n    // Clear token and other data\n    localStorage.removeItem('jwt_token'); // Fix: Remove 'jwt_token'\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    // Optionally, check that these items are removed:\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    // Navigate to login page\n    this.router.navigate(['/login']);\n  }\n  deletePortefeuille(id) {\n    if (confirm('Voulez-vous vraiment supprimer ce portefeuille ?')) {\n      this.portefeuilleService.deletePortefeuille(id, {\n        responseType: 'text'\n      }).subscribe({\n        next: data => {\n          console.log(data); // \"Portefeuille supprimée\"\n          // Remove the deleted portefeuille directly from the local list\n          this.portefeuilles = this.portefeuilles.filter(portefeuille => portefeuille.idPortefeuille !== id);\n          this.cdr.detectChanges(); // Explicitly trigger change detection\n          alert('Portefeuille supprimé avec succès');\n        },\n        error: err => {\n          console.error('Erreur lors de la suppression du portefeuille', err);\n        }\n      });\n    }\n  }\n  loadPortefeuilles() {\n    this.portefeuilleService.getAllPortefeuilles().subscribe({\n      next: data => {\n        this.portefeuilles = data;\n        this.cdr.detectChanges(); // Explicitly trigger change detection to update the view\n      },\n\n      error: err => {\n        console.error('Erreur lors du chargement des portefeuilles', err);\n      }\n    });\n  }\n  trackById(index, item) {\n    return item.idPortefeuille;\n  }\n  static {\n    this.ɵfac = function PortefeuillesComponent_Factory(t) {\n      return new (t || PortefeuillesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthenticationService), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.PortefeuilleService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PortefeuillesComponent,\n      selectors: [[\"app-actions\"]],\n      viewQuery: function PortefeuillesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 55,\n      vars: 9,\n      consts: [[\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [\"class\", \"nav-item\", 4, \"ngIf\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"thead-dark\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\"], [\"data-feather\", \"home\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"book\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"]],\n      template: function PortefeuillesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"head\");\n          i0.ɵɵelement(1, \"meta\", 0)(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"link\", 5)(7, \"link\", 6)(8, \"link\", 7)(9, \"link\", 8);\n          i0.ɵɵelementStart(10, \"title\");\n          i0.ɵɵtext(11, \"Dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"body\")(13, \"nav\", 9)(14, \"a\", 10);\n          i0.ɵɵelement(15, \"img\", 11);\n          i0.ɵɵtext(16, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"ul\", 12)(18, \"li\", 13)(19, \"a\", 14);\n          i0.ɵɵlistener(\"click\", function PortefeuillesComponent_Template_a_click_19_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(20, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16)(23, \"nav\", 17)(24, \"div\", 18)(25, \"ul\", 19);\n          i0.ɵɵtemplate(26, PortefeuillesComponent_li_26_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(27, PortefeuillesComponent_li_27_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(28, PortefeuillesComponent_li_28_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(29, PortefeuillesComponent_li_29_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(30, PortefeuillesComponent_li_30_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(31, PortefeuillesComponent_li_31_Template, 4, 0, \"li\", 20);\n          i0.ɵɵtemplate(32, PortefeuillesComponent_li_32_Template, 4, 0, \"li\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"main\", 21)(34, \"div\", 22)(35, \"h1\", 23);\n          i0.ɵɵtext(36, \"Gestion des portefeuilles\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 24)(38, \"table\", 25)(39, \"thead\", 26)(40, \"tr\")(41, \"th\");\n          i0.ɵɵtext(42, \"R\\u00E9f\\u00E9rence\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"th\");\n          i0.ɵɵtext(44, \"Solde\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"th\");\n          i0.ɵɵtext(46, \"Quantit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"th\");\n          i0.ɵɵtext(48, \"Date Cr\\u00E9ation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"th\");\n          i0.ɵɵtext(50, \"ID Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"th\");\n          i0.ɵɵtext(52, \"Action\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"tbody\");\n          i0.ɵɵtemplate(54, PortefeuillesComponent_tr_54_Template, 15, 8, \"tr\", 27);\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/adminDash\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/users\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/groups\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/transactions\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actions\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actionnaires\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/port\"));\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngForOf\", ctx.portefeuilles)(\"ngForTrackBy\", ctx.trackById);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i4.DatePipe],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "PortefeuillesComponent_tr_54_Template_button_click_13_listener", "restoredCtx", "ɵɵrestoreView", "_r10", "p_r8", "$implicit", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "deletePortefeuille", "idPortefeuille", "ɵɵadvance", "ɵɵtextInterpolate", "solde", "quantite", "ɵɵpipeBind2", "dateCreation", "action", "idAction", "PortefeuillesComponent", "constructor", "router", "authService", "ngZone", "portefeuilleService", "cdr", "portefeuilles", "ngAfterViewInit", "replace", "ngOnInit", "getAllPortefeuilles", "subscribe", "next", "data", "console", "log", "error", "err", "logout", "localStorage", "removeItem", "getItem", "navigate", "id", "confirm", "responseType", "filter", "portefeuille", "detectChanges", "alert", "loadPortefeuilles", "trackById", "index", "item", "ɵɵdirectiveInject", "i1", "Router", "i2", "AuthenticationService", "NgZone", "i3", "PortefeuilleService", "ChangeDetectorRef", "selectors", "viewQuery", "PortefeuillesComponent_Query", "rf", "ctx", "PortefeuillesComponent_Template_a_click_19_listener", "ɵɵtemplate", "PortefeuillesComponent_li_26_Template", "PortefeuillesComponent_li_27_Template", "PortefeuillesComponent_li_28_Template", "PortefeuillesComponent_li_29_Template", "PortefeuillesComponent_li_30_Template", "PortefeuillesComponent_li_31_Template", "PortefeuillesComponent_li_32_Template", "PortefeuillesComponent_tr_54_Template", "ɵɵproperty", "isRouteAllowed"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\portefeuilles\\portefeuilles.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\portefeuilles\\portefeuilles.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, ViewChild } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Chart } from 'chart.js';\nimport * as feather from 'feather-icons';\nimport { PortefeuilleService } from '../services/portefeuille.service';\nimport { Portefeuille } from '../model/portefeuille.model';\nimport { ChangeDetectorRef } from '@angular/core';\nimport { NgZone } from '@angular/core';\n\nimport { AuthenticationService } from '../auth/authentication.service';\n\n\n@Component({\n  selector: 'app-actions',\n  templateUrl: './portefeuilles.component.html',\n  styleUrls: ['./portefeuilles.component.css']\n})\nexport class PortefeuillesComponent implements AfterViewInit {\n  portefeuilles: Portefeuille[] = [];\n\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  constructor(\n        private router: Router,\n        public authService: AuthenticationService,\n        private ngZone: NgZone,\n        private portefeuilleService: PortefeuilleService,\n        private cdr: ChangeDetectorRef // Inject ChangeDetectorRef\n      ) {}\n\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  \n  }\n\n\n  ngOnInit(): void {\n    this.portefeuilleService.getAllPortefeuilles().subscribe({\n      next: (data) => {\n        this.portefeuilles = data;\n        console.log('Portefeuilles:', this.portefeuilles);\n      },\n      error: (err) => {\n        console.error('Erreur lors du chargement des portefeuilles', err);\n      }\n    });\n  }\n\n  logout() {\n    // Clear token and other data\n    localStorage.removeItem('jwt_token');  // Fix: Remove 'jwt_token'\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    \n    // Optionally, check that these items are removed:\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n  \n    // Navigate to login page\n    this.router.navigate(['/login']);\n  }\n  deletePortefeuille(id: number): void {\n    if (confirm('Voulez-vous vraiment supprimer ce portefeuille ?')) {\n      this.portefeuilleService.deletePortefeuille(id, { responseType: 'text' }).subscribe({\n        next: (data) => {\n          console.log(data); // \"Portefeuille supprimée\"\n          // Remove the deleted portefeuille directly from the local list\n          this.portefeuilles = this.portefeuilles.filter(portefeuille => portefeuille.idPortefeuille !== id);\n          this.cdr.detectChanges();  // Explicitly trigger change detection\n          alert('Portefeuille supprimé avec succès');\n        },\n        error: (err) => {\n          console.error('Erreur lors de la suppression du portefeuille', err);\n        }\n      });\n    }\n  }\n  \n  \n  loadPortefeuilles(): void {\n    this.portefeuilleService.getAllPortefeuilles().subscribe({\n      next: (data) => {\n        this.portefeuilles = data;\n        this.cdr.detectChanges();  // Explicitly trigger change detection to update the view\n      },\n      error: (err) => {\n        console.error('Erreur lors du chargement des portefeuilles', err);\n      }\n    });\n  }\n  \n  \n  trackById(index: number, item: Portefeuille): number {\n    return item.idPortefeuille;\n  }\n  \n}\n", "<head>\n  <meta charset=\"utf-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n  <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n  <meta http-equiv=\"Pragma\" content=\"no-cache\">\n  <meta http-equiv=\"Expires\" content=\"0\">\n  <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n  <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n  <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n  <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n  <link href=\"./portefeuilles.component.css\" rel=\"stylesheet\">\n  <title>Dashboard</title>\n</head>\n\n<body>\n  <!-- Navbar -->\n  <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n    <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\">\n      <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n      GTI\n    </a>\n    <ul class=\"navbar-nav px-3\">\n      <li class=\"nav-item text-nowrap\">\n        <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n      </li>\n    </ul>\n  </nav>\n\n  <div class=\"container-fluid\">\n    <div class=\"row\">\n      <!-- Sidebar -->\n      <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n        <div class=\"sidebar-sticky\">\n          <ul class=\"nav flex-column\">\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/adminDash')\">\n              <a class=\"nav-link\" href=\"/adminDash\">\n                <span data-feather=\"home\"></span> Dashboard\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/users')\">\n              <a class=\"nav-link\" href=\"/users\">\n                <span data-feather=\"user\"></span> Gestion des utilisateurs\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/groups')\">\n              <a class=\"nav-link\" href=\"/groups\">\n                <span data-feather=\"grid\"></span> Gestion des groupes\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/transactions')\">\n              <a class=\"nav-link\" href=\"/transactions\">\n                <span data-feather=\"dollar-sign\"></span> Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/actions')\">\n              <a class=\"nav-link\" href=\"/actions\">\n                <span data-feather=\"trending-up\"></span> Gestion des actions\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/actionnaires')\">\n              <a class=\"nav-link\" href=\"/actionnaires\">\n                <span data-feather=\"users\"></span> Gestion des actionnaires\n              </a>\n            </li>\n            <li class=\"nav-item\" *ngIf=\"authService.isRouteAllowed('/port')\">\n              <a class=\"nav-link\" href=\"/port\">\n                <span data-feather=\"book\"></span> Gestion des Portefeuilles\n              </a>\n            </li>\n          </ul>\n        </div>\n      </nav>\n\n      <!-- Main Content -->\n      <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n        <div class=\"d-flex justify-content-between align-items-center pb-2 mb-3 border-bottom\">\n          <h1 class=\"h2\">Gestion des portefeuilles</h1>\n        </div>\n\n        <div class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead class=\"thead-dark\">\n              <tr>\n                <th>Référence</th>\n                <th>Solde</th>\n                <th>Quantité</th>\n                <th>Date Création</th>\n                <th>ID Action</th>\n                <th>Action</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let p of portefeuilles; trackBy: trackById\">\n                <td>{{ p.idPortefeuille }}</td>\n                <td>{{ p.solde }}</td>\n                <td>{{ p.quantite }}</td>\n                <td>{{ p.dateCreation | date: 'short' }}</td>\n                <td>{{ p.action?.idAction }}</td>\n                <td>\n                  <button class=\"btn btn-danger btn-sm\" (click)=\"deletePortefeuille(p.idPortefeuille)\">\n                    Supprimer\n                  </button>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </main>\n    </div>\n  </div>\n\n  <!-- Scripts -->\n  <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js\"></script>\n  <script src=\"https://unpkg.com/feather-icons\"></script>\n  <script>feather.replace();</script>\n</body>\n"], "mappings": "AAGA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;;;;;;ICgC5BC,EAAA,CAAAC,cAAA,aAAsE;IAElED,EAAA,CAAAE,SAAA,eAAiC;IAACF,EAAA,CAAAG,MAAA,kBACpC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAkE;IAE9DD,EAAA,CAAAE,SAAA,eAAiC;IAACF,EAAA,CAAAG,MAAA,iCACpC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAmE;IAE/DD,EAAA,CAAAE,SAAA,eAAiC;IAACF,EAAA,CAAAG,MAAA,4BACpC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAyE;IAErED,EAAA,CAAAE,SAAA,eAAwC;IAACF,EAAA,CAAAG,MAAA,iCAC3C;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAoE;IAEhED,EAAA,CAAAE,SAAA,eAAwC;IAACF,EAAA,CAAAG,MAAA,4BAC3C;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAyE;IAErED,EAAA,CAAAE,SAAA,eAAkC;IAACF,EAAA,CAAAG,MAAA,iCACrC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,aAAiE;IAE7DD,EAAA,CAAAE,SAAA,eAAiC;IAACF,EAAA,CAAAG,MAAA,kCACpC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAyBJJ,EAAA,CAAAC,cAAA,SAAwD;IAClDD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAoC;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjCJ,EAAA,CAAAC,cAAA,UAAI;IACoCD,EAAA,CAAAK,UAAA,mBAAAC,+DAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,IAAA;MAAA,MAAAC,IAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,MAAA,CAAAG,kBAAA,CAAAL,IAAA,CAAAM,cAAA,CAAoC;IAAA,EAAC;IAClFhB,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IARPJ,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,iBAAA,CAAAR,IAAA,CAAAM,cAAA,CAAsB;IACtBhB,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAkB,iBAAA,CAAAR,IAAA,CAAAS,KAAA,CAAa;IACbnB,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,iBAAA,CAAAR,IAAA,CAAAU,QAAA,CAAgB;IAChBpB,EAAA,CAAAiB,SAAA,GAAoC;IAApCjB,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAqB,WAAA,OAAAX,IAAA,CAAAY,YAAA,WAAoC;IACpCtB,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAkB,iBAAA,CAAAR,IAAA,CAAAa,MAAA,kBAAAb,IAAA,CAAAa,MAAA,CAAAC,QAAA,CAAwB;;;;ADjF5C,OAAM,MAAOC,sBAAsB;EAIjCC,YACcC,MAAc,EACfC,WAAkC,EACjCC,MAAc,EACdC,mBAAwC,EACxCC,GAAsB,CAAC;EAAA,E;IAJvB,KAAAJ,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,GAAG,GAAHA,GAAG;IARjB,KAAAC,aAAa,GAAmB,EAAE;EAS3B;EAEPC,eAAeA,CAAA;IACblC,OAAO,CAACmC,OAAO,EAAE,CAAC,CAAC;EAErB;;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACL,mBAAmB,CAACM,mBAAmB,EAAE,CAACC,SAAS,CAAC;MACvDC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACP,aAAa,GAAGO,IAAI;QACzBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACT,aAAa,CAAC;MACnD,CAAC;MACDU,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,6CAA6C,EAAEC,GAAG,CAAC;MACnE;KACD,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJ;IACAC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE;IACvCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAEhC;IACAN,OAAO,CAACC,GAAG,CAACI,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAEhD;IACA,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EACAjC,kBAAkBA,CAACkC,EAAU;IAC3B,IAAIC,OAAO,CAAC,kDAAkD,CAAC,EAAE;MAC/D,IAAI,CAACpB,mBAAmB,CAACf,kBAAkB,CAACkC,EAAE,EAAE;QAAEE,YAAY,EAAE;MAAM,CAAE,CAAC,CAACd,SAAS,CAAC;QAClFC,IAAI,EAAGC,IAAI,IAAI;UACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC,CAAC,CAAC;UACnB;UACA,IAAI,CAACP,aAAa,GAAG,IAAI,CAACA,aAAa,CAACoB,MAAM,CAACC,YAAY,IAAIA,YAAY,CAACrC,cAAc,KAAKiC,EAAE,CAAC;UAClG,IAAI,CAAClB,GAAG,CAACuB,aAAa,EAAE,CAAC,CAAE;UAC3BC,KAAK,CAAC,mCAAmC,CAAC;QAC5C,CAAC;QACDb,KAAK,EAAGC,GAAG,IAAI;UACbH,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEC,GAAG,CAAC;QACrE;OACD,CAAC;;EAEN;EAGAa,iBAAiBA,CAAA;IACf,IAAI,CAAC1B,mBAAmB,CAACM,mBAAmB,EAAE,CAACC,SAAS,CAAC;MACvDC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACP,aAAa,GAAGO,IAAI;QACzB,IAAI,CAACR,GAAG,CAACuB,aAAa,EAAE,CAAC,CAAE;MAC7B,CAAC;;MACDZ,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,6CAA6C,EAAEC,GAAG,CAAC;MACnE;KACD,CAAC;EACJ;EAGAc,SAASA,CAACC,KAAa,EAAEC,IAAkB;IACzC,OAAOA,IAAI,CAAC3C,cAAc;EAC5B;;;uBA3EWS,sBAAsB,EAAAzB,EAAA,CAAA4D,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA9D,EAAA,CAAA4D,iBAAA,CAAAG,EAAA,CAAAC,qBAAA,GAAAhE,EAAA,CAAA4D,iBAAA,CAAA5D,EAAA,CAAAiE,MAAA,GAAAjE,EAAA,CAAA4D,iBAAA,CAAAM,EAAA,CAAAC,mBAAA,GAAAnE,EAAA,CAAA4D,iBAAA,CAAA5D,EAAA,CAAAoE,iBAAA;IAAA;EAAA;;;YAAtB3C,sBAAsB;MAAA4C,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCjBnCxE,EAAA,CAAAC,cAAA,WAAM;UACJD,EAAA,CAAAE,SAAA,cAAsB;UAWtBF,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAG1BJ,EAAA,CAAAC,cAAA,YAAM;UAIAD,EAAA,CAAAE,SAAA,eAAwE;UACxEF,EAAA,CAAAG,MAAA,aACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAK,UAAA,mBAAAqE,oDAAA;YAAA,OAASD,GAAA,CAAA7B,MAAA,EAAQ;UAAA,EAAC;UAAC5C,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAKzDJ,EAAA,CAAAC,cAAA,eAA6B;UAMnBD,EAAA,CAAA2E,UAAA,KAAAC,qCAAA,iBAIK;UACL5E,EAAA,CAAA2E,UAAA,KAAAE,qCAAA,iBAIK;UACL7E,EAAA,CAAA2E,UAAA,KAAAG,qCAAA,iBAIK;UACL9E,EAAA,CAAA2E,UAAA,KAAAI,qCAAA,iBAIK;UACL/E,EAAA,CAAA2E,UAAA,KAAAK,qCAAA,iBAIK;UACLhF,EAAA,CAAA2E,UAAA,KAAAM,qCAAA,iBAIK;UACLjF,EAAA,CAAA2E,UAAA,KAAAO,qCAAA,iBAIK;UACPlF,EAAA,CAAAI,YAAA,EAAK;UAKTJ,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAG,MAAA,iCAAyB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAG/CJ,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAG,MAAA,2BAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,qBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,0BAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACtBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGnBJ,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA2E,UAAA,KAAAQ,qCAAA,kBAWK;UACPnF,EAAA,CAAAI,YAAA,EAAQ;;;UAtEcJ,EAAA,CAAAiB,SAAA,IAA8C;UAA9CjB,EAAA,CAAAoF,UAAA,SAAAX,GAAA,CAAA7C,WAAA,CAAAyD,cAAA,eAA8C;UAK9CrF,EAAA,CAAAiB,SAAA,GAA0C;UAA1CjB,EAAA,CAAAoF,UAAA,SAAAX,GAAA,CAAA7C,WAAA,CAAAyD,cAAA,WAA0C;UAK1CrF,EAAA,CAAAiB,SAAA,GAA2C;UAA3CjB,EAAA,CAAAoF,UAAA,SAAAX,GAAA,CAAA7C,WAAA,CAAAyD,cAAA,YAA2C;UAK3CrF,EAAA,CAAAiB,SAAA,GAAiD;UAAjDjB,EAAA,CAAAoF,UAAA,SAAAX,GAAA,CAAA7C,WAAA,CAAAyD,cAAA,kBAAiD;UAKjDrF,EAAA,CAAAiB,SAAA,GAA4C;UAA5CjB,EAAA,CAAAoF,UAAA,SAAAX,GAAA,CAAA7C,WAAA,CAAAyD,cAAA,aAA4C;UAK5CrF,EAAA,CAAAiB,SAAA,GAAiD;UAAjDjB,EAAA,CAAAoF,UAAA,SAAAX,GAAA,CAAA7C,WAAA,CAAAyD,cAAA,kBAAiD;UAKjDrF,EAAA,CAAAiB,SAAA,GAAyC;UAAzCjB,EAAA,CAAAoF,UAAA,SAAAX,GAAA,CAAA7C,WAAA,CAAAyD,cAAA,UAAyC;UA4B3CrF,EAAA,CAAAiB,SAAA,IAAkB;UAAlBjB,EAAA,CAAAoF,UAAA,YAAAX,GAAA,CAAAzC,aAAA,CAAkB,iBAAAyC,GAAA,CAAAhB,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}