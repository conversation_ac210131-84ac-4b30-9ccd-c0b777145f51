package com.Aziz.Client.Services;

import com.Aziz.Client.Entity.Action;
import com.Aziz.Client.Entity.Actionnaire;
import com.Aziz.Client.Entity.Portefeuille;
import com.Aziz.Client.Entity.Transaction;
import com.Aziz.Client.Repositories.*;
import com.Aziz.Client.Repositories.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;



@Service
@RequiredArgsConstructor
public class PortefeuilleService {





    @Autowired
    private PortefeuilleRepository portefeuilleRepository;

    @Autowired
    private com.Aziz.Client.Repositories.TransactionRepository TransactionRepository;


    @Autowired
    private com.Aziz.Client.Repositories.ActionRepository ActionRepository;

    @Autowired
    private com.Aziz.Client.Repositories.ActionnaireRepository ActionnaireRepository;

    @Autowired
    private com.Aziz.Client.Repositories.CotationRepository CotationRepository;

    public Optional<Portefeuille> findById(Long idPortefeuille){
        return portefeuilleRepository.findById(idPortefeuille);
    }


    public Optional<Portefeuille> findByISINAndIdActionnaire(String ISINAction,Long idActionnaire){
        return portefeuilleRepository
                .findByAction_IsinActionAndActionnaire_IdActionnaire(ISINAction, idActionnaire);
               // .orElseThrow(() -> new RuntimeException("Portefeuille not found"));
    }

    public List<Portefeuille> findByIdActionnaire(Long idActionnaire){
        return portefeuilleRepository.findAllByActionnaire_IdActionnaire(idActionnaire);
    }


    public void savePortefeuille(Portefeuille portefeuille){
        Optional<Action> action=ActionRepository.findById(portefeuille.getAction().getIdAction());
        Optional<Actionnaire> actionnaire=ActionnaireRepository.findById(portefeuille.getActionnaire().getIdActionnaire());
        if(action.isEmpty()){
            throw new RuntimeException("action n'est pas trouvée");
        }
        else{
            if(actionnaire.isEmpty()){
                throw new RuntimeException("actionnaire n'est pas trouvée");
            }
            else{
                Optional<Portefeuille> portefeuille1=portefeuilleRepository.findByAction_IsinActionAndActionnaire_IdActionnaire(action.get().getIsinAction(),actionnaire.get().getIdActionnaire());
                if(portefeuille1.isPresent()){
                    throw new RuntimeException("Portefeuille exist déja");
                }
                else{
                    portefeuilleRepository.save(portefeuille);
                }

            }

        }

    }


    public List<Portefeuille> findAllPortefeuilles(){
        return portefeuilleRepository.findAll();
    }


    public void deletePortefeuille(Long idPortefeuille) {
        portefeuilleRepository.deleteById(idPortefeuille);
    }













    public Portefeuille withdraw(String isinAction, Long idActionnaire, Integer quantite, Actionnaire actionnaire, Action action) {

        Action actionisin = ActionRepository.findByIsinAction(isinAction);

// Chercher tous les portefeuilles de cet actionnaire
        List<Portefeuille> listePortefeuilles = portefeuilleRepository.findAllByActionnaire_IdActionnaire(actionnaire.getIdActionnaire());

        Portefeuille presPort = null;

        for (Portefeuille portefeuilleTemp : listePortefeuilles) {
            if (portefeuilleTemp.getAction().getIdAction()==action.getIdAction()) {
                presPort = portefeuilleTemp;
                break; // Pas besoin de continuer la boucle
            }
        }

        Portefeuille portefeuille;
        if (presPort == null) {
            // Si pas trouvé, créer un nouveau portefeuille
            portefeuille = new Portefeuille();
            portefeuille.setActionnaire(actionnaire);
            portefeuille.setAction(actionisin);
            portefeuille.setQuantite(0);

            portefeuille.setDateCreation(LocalDateTime.now());
            // portefeuilleRepository.save(portefeuille);  // Tu peux sauvegarder ici si tu veux
        } else {
            // Sinon, utiliser celui trouvé

            portefeuille = presPort;
        }





        if (portefeuille.getQuantite() < quantite) {
            ActionnaireRepository.deleteById(portefeuille.getActionnaire().getIdActionnaire());
            throw new RuntimeException("Quantité insuffisante dans le portefeuille !");

        }
        else {

            // Mise à jour de la quantité
            portefeuille.setQuantite(portefeuille.getQuantite() - quantite);

            // Mettre à jour la date de modification
            portefeuille.setDateModification(LocalDateTime.now());

            BigDecimal total = BigDecimal.valueOf(portefeuille.getQuantite())
                    .multiply(BigDecimal.valueOf(portefeuille.getAction().getPrix()));
            portefeuille.setSolde(total);

            // Sauvegarder les modifications

        }

        portefeuilleRepository.save(portefeuille);
System.out.println("5555555555"+portefeuille);
return portefeuille;



    }

    public Portefeuille deposit(String ISINAction, Long idActionnaire, Integer quantite, Actionnaire actionnaire, Action action, Transaction transaction){

        // Chercher le portefeuille
        Optional<Portefeuille> optionalPortefeuille = portefeuilleRepository.findByAction_IsinActionAndActionnaire_IdActionnaire(action.getIsinAction(),actionnaire.getIdActionnaire());
        Portefeuille portefeuille;

        if (optionalPortefeuille.isEmpty()) {


            portefeuille = new Portefeuille();
            portefeuille.setActionnaire(actionnaire);
            portefeuille.setAction(action);


            portefeuille.setQuantite(0);
            portefeuille.setDateCreation(LocalDateTime.now());

            int qts=portefeuille.getQuantite()+quantite;
            portefeuille.setQuantite(portefeuille.getQuantite() + quantite);

            BigDecimal total = BigDecimal.valueOf(qts)
                    .multiply(BigDecimal.valueOf(portefeuille.getAction().getPrix()));
            portefeuille.setSolde(total);

            return portefeuilleRepository.save(portefeuille);
           // throw new RuntimeException("Portefeuille non trouvé !");
        } else {

             portefeuille = optionalPortefeuille.get();




                // Mise à jour de la quantité

            int qts=portefeuille.getQuantite()+quantite;
                portefeuille.setQuantite(portefeuille.getQuantite() + quantite);

                // Mettre à jour la date de modification
                portefeuille.setDateModification(LocalDateTime.now());

            BigDecimal total = BigDecimal.valueOf(qts)
                    .multiply(BigDecimal.valueOf(portefeuille.getAction().getPrix()));
            portefeuille.setSolde(total);
            transaction.setPortefeuille(portefeuille);
            TransactionRepository.save(transaction);


                // Sauvegarder les modifications
                return portefeuilleRepository.save(portefeuille);

        }

    }



    public void deleteActionnaire(Long idPortefeuille) {

        Optional<Portefeuille> Opportefeuille=portefeuilleRepository.findById(idPortefeuille);
        Portefeuille portefeuille=Opportefeuille.get();

        if (portefeuille.getQuantite() > 0) {
            throw new RuntimeException("Il y'a des actions dans le portefeuille !");
        }
        else {

            portefeuilleRepository.deleteById(idPortefeuille);
        }
    }



}
