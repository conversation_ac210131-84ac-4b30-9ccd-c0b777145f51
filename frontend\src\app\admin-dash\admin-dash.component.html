<!doctype html>
<html lang="en">
  <head>
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="/docs/4.0/assets/img/favicons/favicon.ico">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">

    <title>Dashboard Template for Bootstrap</title>

    <link rel="canonical" href="https://getbootstrap.com/docs/4.0/examples/dashboard/">

    <!-- Bootstrap core CSS -->
    <link href="../../../dist/css/bootstrap.min.css" >

    <!-- Custom styles for this template -->
    <link href="./admin-dash.component.css" rel="stylesheet">
  </head>

  <body>
     <!-- Navbar -->
     <nav class="navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0">
      <a class="navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center">
        <img src="assets/images/gti.jpg" alt="GTI Logo" class="logo-img mr-2" />
        GTI
      </a>
      <ul class="navbar-nav px-3">
        <li class="nav-item text-nowrap">
          <a class="nav-link" (click)="logout()">Sign out</a>
        </li>
      </ul>
    </nav>
    <div class="container-fluid">
      <div class="row">
        <nav class="col-md-2 d-none d-md-block custom-sidebar sidebar">
          <div class="sidebar-sticky">
            <ul class="nav flex-column">
              <li class="nav-item">
                <a class="nav-link" href="/adminDash" *ngIf="authService.isRouteAllowed('/adminDash')">
                  <span data-feather="home"></span>
                  Dashboard <span class="sr-only">(current)</span>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/users" *ngIf="authService.isRouteAllowed('/users')">
                  <span data-feather="user"></span>
                  Gestion des utilisateurs
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/groups" *ngIf="authService.isRouteAllowed('/groups')">
                  <span data-feather="grid"></span>
                  Gestion des groupes
                </a>
              </li>
            
            <li class="nav-item">
              <a class="nav-link" href="/transactions" *ngIf="authService.isRouteAllowed('/transactions')">
                <span data-feather="dollar-sign"></span>
                Gestion des transactions
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/actions" *ngIf="authService.isRouteAllowed('/actions')">
                <span data-feather="trending-up"></span>
               Gestion des actions
              </a>
              <li class="nav-item">
                <a class="nav-link" href="/actionnaires" *ngIf="authService.isRouteAllowed('/actionnaires')">
                  <span data-feather="users"></span>
                 Gestion des actionnaires
                </a>
                     <li class="nav-item">
              <a class="nav-link" href="/port" *ngIf="authService.isRouteAllowed('/port')">
                <span data-feather="book"></span>
               Gestion des Portefeuilles
              </a>
            </ul>
          </div>
        </nav>

        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 pt-3 px-4">
          <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">Welcome, {{ adminName }}</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
              <div class="btn-group mr-2">

              </div>
            </div>
          </div>

          <!-- EN HAUT : Données des transactions -->
          <div class="row mb-4">
            <div class="col-12">
              <h3>Données des Transactions</h3>
              <div class="row">
                <div class="col-md-3">
                  <div class="card stats-card">
                    <div class="card-body text-center">
                      <h5>Total Transactions</h5>
                      <h2 class="text-primary">{{ totalTransactions }}</h2>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card stats-card">
                    <div class="card-body text-center">
                      <h5>Montant Total</h5>
                      <h2 class="text-success">{{ totalAmount | currency:'EUR':'symbol':'1.0-0' }}</h2>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card stats-card">
                    <div class="card-body text-center">
                      <h5>Transactions Achat</h5>
                      <h2 class="text-info">{{ achatTransactions }}</h2>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="card stats-card">
                    <div class="card-body text-center">
                      <h5>Transactions Vente</h5>
                      <h2 class="text-warning">{{ venteTransactions }}</h2>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- DIVISION EN DEUX PARTIES -->
          <div class="row">
            <!-- GAUCHE : Courbe existante -->
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">
                  <h5>Graphique en Courbe - Évolution des Transactions</h5>
                </div>
                <div class="card-body">
                  <canvas #myChart width="400" height="300"></canvas>
                </div>
              </div>
            </div>

            <!-- DROITE : Barres liées aux transactions de la courbe -->
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">
                  <h5>Graphique en Barres - Répartition par Type</h5>
                </div>
                <div class="card-body">
                  <canvas #barChart width="400" height="300"></canvas>
                </div>
              </div>
            </div>
          </div>

        </main>
      </div>
    </div>

    <!-- Bootstrap core JavaScript -->
    <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
    <script>window.jQuery || document.write('<script src="../../assets/js/vendor/jquery-slim.min.js"><\/script>')</script>
    <script src="../../assets/js/vendor/popper.min.js"></script>
    <script src="../../dist/js/bootstrap.min.js"></script>

    <!-- Icons -->
    <script src="https://unpkg.com/feather-icons/dist/feather.min.js"></script>

  </body>
</html>