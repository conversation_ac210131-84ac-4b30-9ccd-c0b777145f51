package com.Aziz.Administratif.Services;

import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class IpLocationService {

    public String getLocation(String ip) {
        try {
            String url = "https://ipwho.is/" + ip;
            RestTemplate restTemplate = new RestTemplate();
            String response = restTemplate.getForObject(url, String.class);

            JSONObject json = new JSONObject(response);
            if (json.getBoolean("success")) {
                String city = json.optString("city", "Unknown");
                String country = json.optString("country", "Unknown");
                String continent=json.optString("continent","Unknown");
                return city + ", " + country + ", " + continent;
            } else {
                return "Unknown";
            }
        } catch (Exception e) {
            return "Unknown";
        }
    }


    public String getIp() {
        try {
            String url = "https://ipwho.is/";
            RestTemplate restTemplate = new RestTemplate();
            String response = restTemplate.getForObject(url, String.class);

            JSONObject json = new JSONObject(response);
            if (json.getBoolean("success")) {
                String city = json.optString("city", "Unknown");
                String country = json.optString("country", "Unknown");
                String ip=json.optString("ip","Unknown");
                return  ip;
            } else {
                return "Unknown";
            }
        } catch (Exception e) {
            return "Unknown";
        }
    }
}
