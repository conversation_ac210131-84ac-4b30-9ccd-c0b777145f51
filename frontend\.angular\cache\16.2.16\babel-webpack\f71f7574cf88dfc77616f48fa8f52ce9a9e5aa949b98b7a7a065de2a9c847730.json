{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction AddResponsableComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1, \"Matricule requis.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1, \"Mot de passe requis (minimum 8 caract\\u00E8res).\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1, \"Un nom est requis.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1, \"Un prenom est requis. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1, \"Une adresse email valide est requise. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_79_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Le t\\u00E9l\\u00E9phone est requis. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_79_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone doit \\u00EAtre un num\\u00E9ro et jusqu'\\u00E0 8 chiffres. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtemplate(1, AddResponsableComponent_div_79_div_1_Template, 2, 0, \"div\", 70);\n    i0.ɵɵtemplate(2, AddResponsableComponent_div_79_div_2_Template, 2, 0, \"div\", 70);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r11 = i0.ɵɵreference(78);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r11.errors == null ? null : _r11.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r11.errors == null ? null : _r11.errors[\"pattern\"]);\n  }\n}\nfunction AddResponsableComponent_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1, \"Le r\\u00F4le est requis.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_option_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r20.idGroupe);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(group_r20.nomGroupe);\n  }\n}\nfunction AddResponsableComponent_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1, \"Le groupe est requis.\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class AddResponsableComponent {\n  constructor(responsableService, router) {\n    this.responsableService = responsableService;\n    this.router = router;\n    this.newResponsable = {\n      id: 0,\n      firstName: '',\n      lastName: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupeId: 0,\n      matricule: null,\n      state: true,\n      flag: true,\n      canCreate: false,\n      canRead: false,\n      canUpdate: false,\n      canDelete: false\n    };\n    this.groups = [];\n  }\n  ngOnInit() {\n    this.responsableService.getGroups().subscribe(data => {\n      this.groups = data;\n    });\n  }\n  addResponsable() {\n    if (!this.newResponsable.groupeId) {\n      alert('Veuillez sélectionner un groupe.');\n      return;\n    }\n    if (!this.newResponsable.password || this.newResponsable.password.length < 8) {\n      alert('Le mot de passe est requis (minimum 8 caractères).');\n      return;\n    }\n    console.log('Responsable à ajouter:', this.newResponsable);\n    this.responsableService.addResponsable(this.newResponsable).subscribe(response => {\n      console.log('Responsable ajouté avec succès:', response);\n      this.newResponsable = {\n        id: 0,\n        firstName: '',\n        lastName: '',\n        email: '',\n        telephone: '',\n        role: 'RESPONSABLE',\n        password: '',\n        groupeId: 0,\n        matricule: null,\n        state: true,\n        flag: true,\n        canCreate: false,\n        canRead: false,\n        canUpdate: false,\n        canDelete: false\n      };\n      this.router.navigate(['/users']);\n    }, error => {\n      console.error('Erreur lors de l’ajout:', error);\n      alert('Une erreur s’est produite lors de l’ajout du responsable.');\n    });\n  }\n  get matriculeDisplay() {\n    return this.newResponsable.matricule === null ? '' : this.newResponsable.matricule;\n  }\n  cancelEdit() {\n    this.router.navigate(['/users']);\n  }\n  static {\n    this.ɵfac = function AddResponsableComponent_Factory(t) {\n      return new (t || AddResponsableComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddResponsableComponent,\n      selectors: [[\"app-add-responsable\"]],\n      decls: 132,\n      vars: 22,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"UTF-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1.0\"], [\"http-equiv\", \"X-UA-Compatible\", \"content\", \"ie=edge\"], [\"href\", \"https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css\", \"rel\", \"stylesheet\"], [\"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css\", \"rel\", \"stylesheet\"], [1, \"background-image\"], [1, \"container\", \"mt-5\"], [1, \"form-container\", \"mx-auto\", 2, \"max-width\", \"900px\"], [1, \"text-center\", \"mb-4\"], [1, \"form-group\", 3, \"ngSubmit\"], [\"responsableForm\", \"ngForm\"], [1, \"form-row\"], [1, \"col-md-6\"], [\"for\", \"matricule\"], [1, \"input-group\"], [1, \"input-group-prepend\"], [1, \"input-group-text\"], [1, \"fa\", \"fa-id-badge\"], [\"type\", \"text\", \"id\", \"matricule\", \"name\", \"matricule\", \"required\", \"\", \"placeholder\", \"Entrez le matricule\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"matricule\", \"ngModel\"], [\"class\", \"text-danger small\", 4, \"ngIf\"], [\"for\", \"password\"], [1, \"fa\", \"fa-lock\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"minlength\", \"8\", \"placeholder\", \"Entrez un mot de passe\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"password\", \"ngModel\"], [1, \"form-row\", \"mt-3\"], [\"for\", \"nom\"], [1, \"fa\", \"fa-user\"], [\"type\", \"text\", \"id\", \"nom\", \"name\", \"nom\", \"required\", \"\", \"placeholder\", \"Entrez le nom\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"nom\", \"ngModel\"], [\"for\", \"prenom\"], [\"type\", \"text\", \"id\", \"prenom\", \"name\", \"prenom\", \"required\", \"\", \"placeholder\", \"Entrez le pr\\u00E9nom\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"prenom\", \"ngModel\"], [\"for\", \"email\"], [1, \"fa\", \"fa-envelope\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"Entrez l'email\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"email\", \"ngModel\"], [\"for\", \"telephone\"], [1, \"fa\", \"fa-phone\"], [\"type\", \"tel\", \"id\", \"telephone\", \"name\", \"telephone\", \"required\", \"\", \"pattern\", \"^[0-9]{1,8}$\", \"placeholder\", \"Entrez le t\\u00E9l\\u00E9phone\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"telephone\", \"ngModel\"], [\"for\", \"role\"], [1, \"fa\", \"fa-address-card\"], [\"id\", \"role\", \"name\", \"role\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"role\", \"ngModel\"], [\"value\", \"RESPONSABLE\"], [\"value\", \"ADMIN\"], [\"for\", \"groupe\"], [1, \"fa\", \"fa-users\"], [\"id\", \"groupe\", \"name\", \"groupeId\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"groupe\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-row\", \"mt-4\"], [1, \"col-md-12\"], [2, \"margin-bottom\", \"15px\", \"display\", \"block\"], [1, \"form-check\", \"form-check-inline\", \"ml-3\"], [\"type\", \"checkbox\", \"id\", \"canCreate\", \"name\", \"canCreate\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canCreate\", 1, \"form-check-label\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"checkbox\", \"id\", \"canRead\", \"name\", \"canRead\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canRead\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"canUpdate\", \"name\", \"canUpdate\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canUpdate\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"canDelete\", \"name\", \"canDelete\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canDelete\", 1, \"form-check-label\"], [1, \"col-md-12\", \"text-center\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"ml-3\", 3, \"click\"], [1, \"text-danger\", \"small\"], [4, \"ngIf\"], [3, \"value\"]],\n      template: function AddResponsableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3);\n          i0.ɵɵelementStart(5, \"title\");\n          i0.ɵɵtext(6, \"Ajouter Un Nouveau Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"link\", 4)(8, \"link\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"body\")(10, \"div\", 6)(11, \"div\", 7)(12, \"div\", 8)(13, \"h2\", 9);\n          i0.ɵɵtext(14, \"Ajouter un nouveau utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"form\", 10, 11);\n          i0.ɵɵlistener(\"ngSubmit\", function AddResponsableComponent_Template_form_ngSubmit_15_listener() {\n            return ctx.addResponsable();\n          });\n          i0.ɵɵelementStart(17, \"div\", 12)(18, \"div\", 13)(19, \"label\", 14);\n          i0.ɵɵtext(20, \"Matricule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16)(23, \"span\", 17);\n          i0.ɵɵelement(24, \"i\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"input\", 19, 20);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_25_listener($event) {\n            return ctx.newResponsable.matricule = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(27, AddResponsableComponent_div_27_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 13)(29, \"label\", 22);\n          i0.ɵɵtext(30, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 15)(32, \"div\", 16)(33, \"span\", 17);\n          i0.ɵɵelement(34, \"i\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"input\", 24, 25);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_35_listener($event) {\n            return ctx.newResponsable.password = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(37, AddResponsableComponent_div_37_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 26)(39, \"div\", 13)(40, \"label\", 27);\n          i0.ɵɵtext(41, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 15)(43, \"div\", 16)(44, \"span\", 17);\n          i0.ɵɵelement(45, \"i\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"input\", 29, 30);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_46_listener($event) {\n            return ctx.newResponsable.firstName = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(48, AddResponsableComponent_div_48_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 13)(50, \"label\", 31);\n          i0.ɵɵtext(51, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\", 15)(53, \"div\", 16)(54, \"span\", 17);\n          i0.ɵɵelement(55, \"i\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"input\", 32, 33);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_56_listener($event) {\n            return ctx.newResponsable.lastName = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(58, AddResponsableComponent_div_58_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"div\", 26)(60, \"div\", 13)(61, \"label\", 34);\n          i0.ɵɵtext(62, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 15)(64, \"div\", 16)(65, \"span\", 17);\n          i0.ɵɵelement(66, \"i\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"input\", 36, 37);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_67_listener($event) {\n            return ctx.newResponsable.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(69, AddResponsableComponent_div_69_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"div\", 13)(71, \"label\", 38);\n          i0.ɵɵtext(72, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 15)(74, \"div\", 16)(75, \"span\", 17);\n          i0.ɵɵelement(76, \"i\", 39);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"input\", 40, 41);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_77_listener($event) {\n            return ctx.newResponsable.telephone = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(79, AddResponsableComponent_div_79_Template, 3, 2, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"div\", 26)(81, \"div\", 13)(82, \"label\", 42);\n          i0.ɵɵtext(83, \"Role\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"div\", 15)(85, \"div\", 16)(86, \"span\", 17);\n          i0.ɵɵelement(87, \"i\", 43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(88, \"select\", 44, 45);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_select_ngModelChange_88_listener($event) {\n            return ctx.newResponsable.role = $event;\n          });\n          i0.ɵɵelementStart(90, \"option\", 46);\n          i0.ɵɵtext(91, \"RESPONSABLE\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"option\", 47);\n          i0.ɵɵtext(93, \"ADMIN\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(94, AddResponsableComponent_div_94_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"div\", 13)(96, \"label\", 48);\n          i0.ɵɵtext(97, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"div\", 15)(99, \"div\", 16)(100, \"span\", 17);\n          i0.ɵɵelement(101, \"i\", 49);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(102, \"select\", 50, 51);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_select_ngModelChange_102_listener($event) {\n            return ctx.newResponsable.groupeId = $event;\n          });\n          i0.ɵɵtemplate(104, AddResponsableComponent_option_104_Template, 2, 2, \"option\", 52);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(105, AddResponsableComponent_div_105_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(106, \"div\", 53)(107, \"div\", 54)(108, \"label\", 55);\n          i0.ɵɵtext(109, \"Droits\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"div\", 56)(111, \"input\", 57);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_111_listener($event) {\n            return ctx.newResponsable.canCreate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"label\", 58);\n          i0.ɵɵtext(113, \"Cr\\u00E9er\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(114, \"div\", 59)(115, \"input\", 60);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_115_listener($event) {\n            return ctx.newResponsable.canRead = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"label\", 61);\n          i0.ɵɵtext(117, \"Lire\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(118, \"div\", 59)(119, \"input\", 62);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_119_listener($event) {\n            return ctx.newResponsable.canUpdate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"label\", 63);\n          i0.ɵɵtext(121, \"Mettre \\u00E0 jour\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(122, \"div\", 59)(123, \"input\", 64);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_123_listener($event) {\n            return ctx.newResponsable.canDelete = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"label\", 65);\n          i0.ɵɵtext(125, \"Supprimer\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(126, \"div\", 53)(127, \"div\", 66)(128, \"button\", 67);\n          i0.ɵɵtext(129, \"Ajouter\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"button\", 68);\n          i0.ɵɵlistener(\"click\", function AddResponsableComponent_Template_button_click_130_listener() {\n            return ctx.cancelEdit();\n          });\n          i0.ɵɵtext(131, \"Annuler\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(16);\n          const _r1 = i0.ɵɵreference(26);\n          const _r3 = i0.ɵɵreference(36);\n          const _r5 = i0.ɵɵreference(47);\n          const _r7 = i0.ɵɵreference(57);\n          const _r9 = i0.ɵɵreference(68);\n          const _r11 = i0.ɵɵreference(78);\n          const _r13 = i0.ɵɵreference(89);\n          const _r15 = i0.ɵɵreference(103);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.matricule);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r1.invalid && _r1.touched);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r3.invalid && _r3.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.firstName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r5.invalid && _r5.touched);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.lastName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r7.invalid && _r7.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r9.invalid && _r9.touched);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.telephone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r11.invalid && _r11.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.role);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", _r13.invalid && _r13.touched);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.groupeId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groups);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r15.invalid && _r15.touched);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.canCreate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.canRead);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.canUpdate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.canDelete);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", _r0.invalid);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.ɵNgNoValidate, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.DefaultValueAccessor, i4.CheckboxControlValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.MinLengthValidator, i4.PatternValidator, i4.EmailValidator, i4.NgModel, i4.NgForm],\n      styles: [\"\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n  background: linear-gradient(135deg, #1e3c72, #0c2147); \\n\\n  color: #333; \\n\\n  height: 100vh;\\n  margin: 0;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n\\n\\n.form-container[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95); \\n\\n  padding: 60px;\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  max-width: 1000px; \\n\\n  width: 100%;\\n}\\n\\n\\n\\nh2[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 600;\\n  text-align: center;\\n  color: #333;\\n  margin-bottom: 30px;\\n}\\n\\n\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n}\\n\\n.input-group[_ngcontent-%COMP%] {\\n  border-radius: 25px; \\n\\n}\\n\\n.input-group-text[_ngcontent-%COMP%] {\\n  background-color: #24375a;\\n  color: white;\\n  border-radius: 10px 0 0 10px; \\n\\n}\\n\\n.input-group-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  border: 1px solid #ddd;\\n  padding: 9px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff; \\n\\n  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);\\n}\\n\\n\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #e74c3c;\\n}\\n\\n\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border: none;\\n  padding: 12px 30px;\\n  font-size: 1rem;\\n  border-radius: 25px;\\n  transition: background-color 0.3s;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border: none;\\n  padding: 12px 30px;\\n  font-size: 1rem;\\n  border-radius: 25px;\\n  transition: background-color 0.3s;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n}\\n\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "AddResponsableComponent_div_79_div_1_Template", "AddResponsableComponent_div_79_div_2_Template", "ɵɵadvance", "ɵɵproperty", "_r11", "errors", "group_r20", "idGroupe", "ɵɵtextInterpolate", "nomGroupe", "AddResponsableComponent", "constructor", "responsableService", "router", "newResponsable", "id", "firstName", "lastName", "email", "telephone", "role", "password", "groupeId", "matricule", "state", "flag", "canCreate", "canRead", "canUpdate", "canDelete", "groups", "ngOnInit", "getGroups", "subscribe", "data", "addResponsable", "alert", "length", "console", "log", "response", "navigate", "error", "matriculeDisplay", "cancelEdit", "ɵɵdirectiveInject", "i1", "ResponsableService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AddResponsableComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "AddResponsableComponent_Template_form_ngSubmit_15_listener", "AddResponsableComponent_Template_input_ngModelChange_25_listener", "$event", "AddResponsableComponent_div_27_Template", "AddResponsableComponent_Template_input_ngModelChange_35_listener", "AddResponsableComponent_div_37_Template", "AddResponsableComponent_Template_input_ngModelChange_46_listener", "AddResponsableComponent_div_48_Template", "AddResponsableComponent_Template_input_ngModelChange_56_listener", "AddResponsableComponent_div_58_Template", "AddResponsableComponent_Template_input_ngModelChange_67_listener", "AddResponsableComponent_div_69_Template", "AddResponsableComponent_Template_input_ngModelChange_77_listener", "AddResponsableComponent_div_79_Template", "AddResponsableComponent_Template_select_ngModelChange_88_listener", "AddResponsableComponent_div_94_Template", "AddResponsableComponent_Template_select_ngModelChange_102_listener", "AddResponsableComponent_option_104_Template", "AddResponsableComponent_div_105_Template", "AddResponsableComponent_Template_input_ngModelChange_111_listener", "AddResponsableComponent_Template_input_ngModelChange_115_listener", "AddResponsableComponent_Template_input_ngModelChange_119_listener", "AddResponsableComponent_Template_input_ngModelChange_123_listener", "AddResponsableComponent_Template_button_click_130_listener", "_r1", "invalid", "touched", "_r3", "_r5", "_r7", "_r9", "_r13", "_r15", "_r0"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { Responsable } from '../model/respons.model';\nimport { Groupe } from '../model/groupe.model';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-add-responsable',\n  templateUrl: './add-responsable.component.html',\n  styleUrls: ['./add-responsable.component.css']\n})\nexport class AddResponsableComponent implements OnInit {\n  newResponsable: Responsable = {\n    id: 0,\n    firstName: '',\n    lastName: '',\n    email: '',\n    telephone: '',\n    role: 'RESPONSABLE',\n    password: '',\n    groupeId: 0,\n    matricule: null, // This ensures the field is empty initially\n    state: true,\n    flag: true,\n    canCreate: false,\n    canRead: false,\n    canUpdate: false,\n    canDelete: false\n  };\n\n  groups: Groupe[] = [];\n\n  constructor(\n    private responsableService: ResponsableService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.responsableService.getGroups().subscribe((data) => {\n      this.groups = data;\n    });\n  }\n\n  addResponsable(): void {\n    if (!this.newResponsable.groupeId) {\n      alert('Veuillez sélectionner un groupe.');\n      return;\n    }\n\n    if (!this.newResponsable.password || this.newResponsable.password.length < 8) {\n      alert('Le mot de passe est requis (minimum 8 caractères).');\n      return;\n    }\n\n    console.log('Responsable à ajouter:', this.newResponsable);\n\n    this.responsableService.addResponsable(this.newResponsable).subscribe(\n      (response) => {\n        console.log('Responsable ajouté avec succès:', response);\n\n        this.newResponsable = {\n          id: 0,\n          firstName: '',\n          lastName: '',\n          email: '',\n          telephone: '',\n          role: 'RESPONSABLE',\n          password: '',\n          groupeId: 0,\n          matricule: null, // Set this to null to avoid the default 0\n          state: true,\n          flag: true,\n          canCreate: false,\n          canRead: false,\n          canUpdate: false,\n          canDelete: false\n        };\n\n        this.router.navigate(['/users']);\n      },\n      (error) => {\n        console.error('Erreur lors de l’ajout:', error);\n        alert('Une erreur s’est produite lors de l’ajout du responsable.');\n      }\n    );\n  }\n  get matriculeDisplay() {\n    return this.newResponsable.matricule === null ? '' : this.newResponsable.matricule;\n  }\n  \n\n  cancelEdit(): void {\n    this.router.navigate(['/users']);\n  }\n}", "<!DOCTYPE html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\" />\n    <title>Ajouter Un Nouveau Utilisateur</title>\n    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\" />\n    <link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css\" rel=\"stylesheet\" />\n  </head>\n\n  <body>\n    <div class=\"background-image\">\n      <div class=\"container mt-5\">\n        <div class=\"form-container mx-auto\" style=\"max-width: 900px;\">\n          <h2 class=\"text-center mb-4\">Ajouter un nouveau utilisateur</h2>\n          <form (ngSubmit)=\"addResponsable()\" #responsableForm=\"ngForm\" class=\"form-group\">\n\n            <!-- Row 1: Matricule & Password -->\n            <div class=\"form-row\">\n              <div class=\"col-md-6\">\n                <label for=\"matricule\">Matricule</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-id-badge\"></i></span>\n                  </div>\n                  <input type=\"text\" class=\"form-control\" id=\"matricule\" [(ngModel)]=\"newResponsable.matricule\" name=\"matricule\" required #matricule=\"ngModel\" placeholder=\"Entrez le matricule\" />\n                </div>\n                <div *ngIf=\"matricule.invalid && matricule.touched\" class=\"text-danger small\">Matricule requis.</div>\n              </div>\n\n              <div class=\"col-md-6\">\n                <label for=\"password\">Mot de passe</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-lock\"></i></span>\n                  </div>\n                  <input type=\"password\" class=\"form-control\" id=\"password\" [(ngModel)]=\"newResponsable.password\" name=\"password\" required minlength=\"8\" #password=\"ngModel\" placeholder=\"Entrez un mot de passe\" />\n                </div>\n                <div *ngIf=\"password.invalid && password.touched\" class=\"text-danger small\">Mot de passe requis (minimum 8 caractères).</div>\n              </div>\n            </div>\n\n            <!-- Row 2: Nom & Prénom -->\n            <div class=\"form-row mt-3\">\n              <div class=\"col-md-6\">\n                <label for=\"nom\">Nom</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-user\"></i></span>\n                  </div>\n                  <input type=\"text\" class=\"form-control\" id=\"nom\" [(ngModel)]=\"newResponsable.firstName\" name=\"nom\" required #nom=\"ngModel\" placeholder=\"Entrez le nom\" />\n                </div>\n                <div *ngIf=\"nom.invalid && nom.touched\" class=\"text-danger small\">Un nom est requis.</div>\n              </div>\n\n              <div class=\"col-md-6\">\n                <label for=\"prenom\">Prénom</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-user\"></i></span>\n                  </div>\n                  <input type=\"text\" class=\"form-control\" id=\"prenom\" [(ngModel)]=\"newResponsable.lastName\" name=\"prenom\" required #prenom=\"ngModel\" placeholder=\"Entrez le prénom\" />\n                </div>\n                <div *ngIf=\"prenom.invalid && prenom.touched\" class=\"text-danger small\">Un prenom est requis.\n                </div>\n              </div>\n            </div>\n\n            <!-- Row 3: Email & Téléphone -->\n            <div class=\"form-row mt-3\">\n              <div class=\"col-md-6\">\n                <label for=\"email\">Email</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-envelope\"></i></span>\n                  </div>\n                  <input type=\"email\" class=\"form-control\" id=\"email\" [(ngModel)]=\"newResponsable.email\" name=\"email\" required email #email=\"ngModel\" placeholder=\"Entrez l'email\" />\n                </div>\n                <div *ngIf=\"email.invalid && email.touched\" class=\"text-danger small\">Une adresse email valide est requise.\n                </div>\n              </div>\n\n              <div class=\"col-md-6\">\n                <label for=\"telephone\">Téléphone</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-phone\"></i></span>\n                  </div>\n                  <input type=\"tel\" class=\"form-control\" id=\"telephone\" [(ngModel)]=\"newResponsable.telephone\" name=\"telephone\" required #telephone=\"ngModel\" pattern=\"^[0-9]{1,8}$\" placeholder=\"Entrez le téléphone\" />\n                </div>\n                <div *ngIf=\"telephone.invalid && telephone.touched\" class=\"text-danger small\">\n                  <div *ngIf=\"telephone.errors?.['required']\">Le téléphone est requis.\n                  </div>\n                  <div *ngIf=\"telephone.errors?.['pattern']\">Téléphone doit être un numéro et jusqu'à 8 chiffres.\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Row 4: Role & Groupe -->\n            <div class=\"form-row mt-3\">\n              <div class=\"col-md-6\">\n                <label for=\"role\">Role</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-address-card\"></i></span>\n                  </div>\n                  <select id=\"role\" class=\"form-control\" [(ngModel)]=\"newResponsable.role\" name=\"role\" required #role=\"ngModel\">\n                    <option value=\"RESPONSABLE\">RESPONSABLE</option>\n                    <option value=\"ADMIN\">ADMIN</option>\n                  </select>\n                </div>\n                <div *ngIf=\"role.invalid && role.touched\" class=\"text-danger small\">Le rôle est requis.</div>\n              </div>\n\n              <div class=\"col-md-6\">\n                <label for=\"groupe\">Groupe</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-users\"></i></span>\n                  </div>\n                  <select id=\"groupe\" class=\"form-control\" [(ngModel)]=\"newResponsable.groupeId\" name=\"groupeId\" required #groupe=\"ngModel\">\n                    <option *ngFor=\"let group of groups\" [value]=\"group.idGroupe\">{{ group.nomGroupe }}</option>\n                  </select>\n                </div>\n                <div *ngIf=\"groupe.invalid && groupe.touched\" class=\"text-danger small\">Le groupe est requis.</div>\n              </div>\n            </div>\n\n            <!-- Droits Section -->\n            <div class=\"form-row mt-4\">\n              <div class=\"col-md-12\">\n                <label style=\"margin-bottom: 15px; display: block;\">Droits</label>\n                <div class=\"form-check form-check-inline ml-3\">\n                  <input class=\"form-check-input\" type=\"checkbox\" id=\"canCreate\" [(ngModel)]=\"newResponsable.canCreate\" name=\"canCreate\" />\n                  <label class=\"form-check-label\" for=\"canCreate\">Créer</label>\n                </div>\n                <div class=\"form-check form-check-inline\">\n                  <input class=\"form-check-input\" type=\"checkbox\" id=\"canRead\" [(ngModel)]=\"newResponsable.canRead\" name=\"canRead\" />\n                  <label class=\"form-check-label\" for=\"canRead\">Lire</label>\n                </div>\n                <div class=\"form-check form-check-inline\">\n                  <input class=\"form-check-input\" type=\"checkbox\" id=\"canUpdate\" [(ngModel)]=\"newResponsable.canUpdate\" name=\"canUpdate\" />\n                  <label class=\"form-check-label\" for=\"canUpdate\">Mettre à jour</label>\n                </div>\n                <div class=\"form-check form-check-inline\">\n                  <input class=\"form-check-input\" type=\"checkbox\" id=\"canDelete\" [(ngModel)]=\"newResponsable.canDelete\" name=\"canDelete\" />\n                  <label class=\"form-check-label\" for=\"canDelete\">Supprimer</label>\n                </div>\n              </div>\n            </div>\n\n            <!-- Buttons -->\n            <div class=\"form-row mt-4\">\n              <div class=\"col-md-12 text-center\">\n                <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"responsableForm.invalid\">Ajouter</button>\n                <button type=\"button\" class=\"btn btn-secondary ml-3\" (click)=\"cancelEdit()\">Annuler</button>\n              </div>\n            </div>\n\n          </form>\n        </div>\n      </div>\n    </div>\n\n    \n\n    <script src=\"https://code.jquery.com/jquery-3.5.1.slim.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/js/bootstrap.bundle.min.js\"></script>\n  </body>\n</html>"], "mappings": ";;;;;;;IC4BgBA,EAAA,CAAAC,cAAA,cAA8E;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWrGH,EAAA,CAAAC,cAAA,cAA4E;IAAAD,EAAA,CAAAE,MAAA,uDAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAc7HH,EAAA,CAAAC,cAAA,cAAkE;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAW1FH,EAAA,CAAAC,cAAA,cAAwE;IAAAD,EAAA,CAAAE,MAAA,6BACxE;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAcNH,EAAA,CAAAC,cAAA,cAAsE;IAAAD,EAAA,CAAAE,MAAA,6CACtE;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYJH,EAAA,CAAAC,cAAA,UAA4C;IAAAD,EAAA,CAAAE,MAAA,0CAC5C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAA2C;IAAAD,EAAA,CAAAE,MAAA,qFAC3C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAI,UAAA,IAAAC,6CAAA,kBACM;IACNL,EAAA,CAAAI,UAAA,IAAAE,6CAAA,kBACM;IACRN,EAAA,CAAAG,YAAA,EAAM;;;;;IAJEH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAC,IAAA,CAAAC,MAAA,kBAAAD,IAAA,CAAAC,MAAA,aAAoC;IAEpCV,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAC,IAAA,CAAAC,MAAA,kBAAAD,IAAA,CAAAC,MAAA,YAAmC;;;;;IAmB3CV,EAAA,CAAAC,cAAA,cAAoE;IAAAD,EAAA,CAAAE,MAAA,+BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAUzFH,EAAA,CAAAC,cAAA,iBAA8D;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAvDH,EAAA,CAAAQ,UAAA,UAAAG,SAAA,CAAAC,QAAA,CAAwB;IAACZ,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAa,iBAAA,CAAAF,SAAA,CAAAG,SAAA,CAAqB;;;;;IAGvFd,EAAA,CAAAC,cAAA,cAAwE;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;ADnHnH,OAAM,MAAOY,uBAAuB;EAqBlCC,YACUC,kBAAsC,EACtCC,MAAc;IADd,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IAtBhB,KAAAC,cAAc,GAAgB;MAC5BC,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,IAAI;MACfC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;KACZ;IAED,KAAAC,MAAM,GAAa,EAAE;EAKlB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACnB,kBAAkB,CAACoB,SAAS,EAAE,CAACC,SAAS,CAAEC,IAAI,IAAI;MACrD,IAAI,CAACJ,MAAM,GAAGI,IAAI;IACpB,CAAC,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACrB,cAAc,CAACQ,QAAQ,EAAE;MACjCc,KAAK,CAAC,kCAAkC,CAAC;MACzC;;IAGF,IAAI,CAAC,IAAI,CAACtB,cAAc,CAACO,QAAQ,IAAI,IAAI,CAACP,cAAc,CAACO,QAAQ,CAACgB,MAAM,GAAG,CAAC,EAAE;MAC5ED,KAAK,CAAC,oDAAoD,CAAC;MAC3D;;IAGFE,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACzB,cAAc,CAAC;IAE1D,IAAI,CAACF,kBAAkB,CAACuB,cAAc,CAAC,IAAI,CAACrB,cAAc,CAAC,CAACmB,SAAS,CAClEO,QAAQ,IAAI;MACXF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAAC;MAExD,IAAI,CAAC1B,cAAc,GAAG;QACpBC,EAAE,EAAE,CAAC;QACLC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,CAAC;QACXC,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,IAAI;QACVC,SAAS,EAAE,KAAK;QAChBC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE;OACZ;MAED,IAAI,CAAChB,MAAM,CAAC4B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC,EACAC,KAAK,IAAI;MACRJ,OAAO,CAACI,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CN,KAAK,CAAC,2DAA2D,CAAC;IACpE,CAAC,CACF;EACH;EACA,IAAIO,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC7B,cAAc,CAACS,SAAS,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAACT,cAAc,CAACS,SAAS;EACpF;EAGAqB,UAAUA,CAAA;IACR,IAAI,CAAC/B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAlFW/B,uBAAuB,EAAAf,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAApD,EAAA,CAAAkD,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvBvC,uBAAuB;MAAAwC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVpC7D,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAA+D,SAAA,cAAwB;UAGxB/D,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,qCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAA+D,SAAA,cAAwG;UAE1G/D,EAAA,CAAAG,YAAA,EAAO;UAEPH,EAAA,CAAAC,cAAA,WAAM;UAI+BD,EAAA,CAAAE,MAAA,sCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAC,cAAA,oBAAiF;UAA3ED,EAAA,CAAAgE,UAAA,sBAAAC,2DAAA;YAAA,OAAYH,GAAA,CAAAtB,cAAA,EAAgB;UAAA,EAAC;UAGjCxC,EAAA,CAAAC,cAAA,eAAsB;UAEKD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAA+D,SAAA,aAA8B;UAAA/D,EAAA,CAAAG,YAAA,EAAO;UAEtEH,EAAA,CAAAC,cAAA,qBAAiL;UAA1HD,EAAA,CAAAgE,UAAA,2BAAAE,iEAAAC,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAS,SAAA,GAAAuC,MAAA;UAAA,EAAsC;UAA7FnE,EAAA,CAAAG,YAAA,EAAiL;UAEnLH,EAAA,CAAAI,UAAA,KAAAgE,uCAAA,kBAAqG;UACvGpE,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAsB;UACED,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAA+D,SAAA,aAA0B;UAAA/D,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,qBAAkM;UAAxID,EAAA,CAAAgE,UAAA,2BAAAK,iEAAAF,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAO,QAAA,GAAAyC,MAAA;UAAA,EAAqC;UAA/FnE,EAAA,CAAAG,YAAA,EAAkM;UAEpMH,EAAA,CAAAI,UAAA,KAAAkE,uCAAA,kBAA6H;UAC/HtE,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAA2B;UAEND,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5BH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAA+D,SAAA,aAA0B;UAAA/D,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,qBAAyJ;UAAxGD,EAAA,CAAAgE,UAAA,2BAAAO,iEAAAJ,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAE,SAAA,GAAA8C,MAAA;UAAA,EAAsC;UAAvFnE,EAAA,CAAAG,YAAA,EAAyJ;UAE3JH,EAAA,CAAAI,UAAA,KAAAoE,uCAAA,kBAA0F;UAC5FxE,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAsB;UACAD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAA+D,SAAA,aAA0B;UAAA/D,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,qBAAoK;UAAhHD,EAAA,CAAAgE,UAAA,2BAAAS,iEAAAN,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAG,QAAA,GAAA6C,MAAA;UAAA,EAAqC;UAAzFnE,EAAA,CAAAG,YAAA,EAAoK;UAEtKH,EAAA,CAAAI,UAAA,KAAAsE,uCAAA,kBACM;UACR1E,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAA2B;UAEJD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAA+D,SAAA,aAA8B;UAAA/D,EAAA,CAAAG,YAAA,EAAO;UAEtEH,EAAA,CAAAC,cAAA,qBAAmK;UAA/GD,EAAA,CAAAgE,UAAA,2BAAAW,iEAAAR,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAI,KAAA,GAAA4C,MAAA;UAAA,EAAkC;UAAtFnE,EAAA,CAAAG,YAAA,EAAmK;UAErKH,EAAA,CAAAI,UAAA,KAAAwE,uCAAA,kBACM;UACR5E,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAsB;UACGD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAA+D,SAAA,aAA2B;UAAA/D,EAAA,CAAAG,YAAA,EAAO;UAEnEH,EAAA,CAAAC,cAAA,qBAAuM;UAAjJD,EAAA,CAAAgE,UAAA,2BAAAa,iEAAAV,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAK,SAAA,GAAA2C,MAAA;UAAA,EAAsC;UAA5FnE,EAAA,CAAAG,YAAA,EAAuM;UAEzMH,EAAA,CAAAI,UAAA,KAAA0E,uCAAA,kBAKM;UACR9E,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAA2B;UAELD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAA+D,SAAA,aAAkC;UAAA/D,EAAA,CAAAG,YAAA,EAAO;UAE1EH,EAAA,CAAAC,cAAA,sBAA8G;UAAvED,EAAA,CAAAgE,UAAA,2BAAAe,kEAAAZ,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAM,IAAA,GAAA0C,MAAA;UAAA,EAAiC;UACtEnE,EAAA,CAAAC,cAAA,kBAA4B;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChDH,EAAA,CAAAC,cAAA,kBAAsB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGxCH,EAAA,CAAAI,UAAA,KAAA4E,uCAAA,kBAA6F;UAC/FhF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAsB;UACAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAA+D,SAAA,cAA2B;UAAA/D,EAAA,CAAAG,YAAA,EAAO;UAEnEH,EAAA,CAAAC,cAAA,uBAA0H;UAAjFD,EAAA,CAAAgE,UAAA,2BAAAiB,mEAAAd,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAQ,QAAA,GAAAwC,MAAA;UAAA,EAAqC;UAC5EnE,EAAA,CAAAI,UAAA,MAAA8E,2CAAA,qBAA4F;UAC9FlF,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAI,UAAA,MAAA+E,wCAAA,kBAAmG;UACrGnF,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,gBAA2B;UAE6BD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClEH,EAAA,CAAAC,cAAA,gBAA+C;UACkBD,EAAA,CAAAgE,UAAA,2BAAAoB,kEAAAjB,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAY,SAAA,GAAAoC,MAAA;UAAA,EAAsC;UAArGnE,EAAA,CAAAG,YAAA,EAAyH;UACzHH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,mBAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE/DH,EAAA,CAAAC,cAAA,gBAA0C;UACqBD,EAAA,CAAAgE,UAAA,2BAAAqB,kEAAAlB,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAa,OAAA,GAAAmC,MAAA;UAAA,EAAoC;UAAjGnE,EAAA,CAAAG,YAAA,EAAmH;UACnHH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE5DH,EAAA,CAAAC,cAAA,gBAA0C;UACuBD,EAAA,CAAAgE,UAAA,2BAAAsB,kEAAAnB,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAc,SAAA,GAAAkC,MAAA;UAAA,EAAsC;UAArGnE,EAAA,CAAAG,YAAA,EAAyH;UACzHH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,2BAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEvEH,EAAA,CAAAC,cAAA,gBAA0C;UACuBD,EAAA,CAAAgE,UAAA,2BAAAuB,kEAAApB,MAAA;YAAA,OAAAL,GAAA,CAAA3C,cAAA,CAAAe,SAAA,GAAAiC,MAAA;UAAA,EAAsC;UAArGnE,EAAA,CAAAG,YAAA,EAAyH;UACzHH,EAAA,CAAAC,cAAA,kBAAgD;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAMvEH,EAAA,CAAAC,cAAA,gBAA2B;UAE4DD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnGH,EAAA,CAAAC,cAAA,mBAA4E;UAAvBD,EAAA,CAAAgE,UAAA,mBAAAwB,2DAAA;YAAA,OAAS1B,GAAA,CAAAb,UAAA,EAAY;UAAA,EAAC;UAACjD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;;;UAnInCH,EAAA,CAAAO,SAAA,IAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAS,SAAA,CAAsC;UAEzF5B,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,UAAA,SAAAiF,GAAA,CAAAC,OAAA,IAAAD,GAAA,CAAAE,OAAA,CAA4C;UASU3F,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAO,QAAA,CAAqC;UAE3F1B,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAQ,UAAA,SAAAoF,GAAA,CAAAF,OAAA,IAAAE,GAAA,CAAAD,OAAA,CAA0C;UAYG3F,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAE,SAAA,CAAsC;UAEnFrB,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,UAAA,SAAAqF,GAAA,CAAAH,OAAA,IAAAG,GAAA,CAAAF,OAAA,CAAgC;UASgB3F,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAG,QAAA,CAAqC;UAErFtB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,SAAAsF,GAAA,CAAAJ,OAAA,IAAAI,GAAA,CAAAH,OAAA,CAAsC;UAaU3F,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAI,KAAA,CAAkC;UAElFvB,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,SAAAuF,GAAA,CAAAL,OAAA,IAAAK,GAAA,CAAAJ,OAAA,CAAoC;UAUc3F,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAK,SAAA,CAAsC;UAExFxB,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,UAAA,SAAAC,IAAA,CAAAiF,OAAA,IAAAjF,IAAA,CAAAkF,OAAA,CAA4C;UAiBT3F,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAM,IAAA,CAAiC;UAKpEzB,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,SAAAwF,IAAA,CAAAN,OAAA,IAAAM,IAAA,CAAAL,OAAA,CAAkC;UASG3F,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAQ,QAAA,CAAqC;UAClD3B,EAAA,CAAAO,SAAA,GAAS;UAATP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3B,MAAA,CAAS;UAGjCnC,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,SAAAyF,IAAA,CAAAP,OAAA,IAAAO,IAAA,CAAAN,OAAA,CAAsC;UASqB3F,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAY,SAAA,CAAsC;UAIxC/B,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAa,OAAA,CAAoC;UAIlChC,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAc,SAAA,CAAsC;UAItCjC,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAsD,GAAA,CAAA3C,cAAA,CAAAe,SAAA,CAAsC;UASzDlC,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,aAAA0F,GAAA,CAAAR,OAAA,CAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}