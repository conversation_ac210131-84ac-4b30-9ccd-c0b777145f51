{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/habilitation.service\";\nimport * as i3 from \"@angular/common\";\nfunction HabilitationComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"p\", 9);\n    i0.ɵɵtext(2, \" Groupe s\\u00E9lectionn\\u00E9 : \");\n    i0.ɵɵelementStart(3, \"span\", 10);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.groupe.nomGroupe);\n  }\n}\nfunction HabilitationComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"input\", 12);\n    i0.ɵɵlistener(\"change\", function HabilitationComponent_div_7_Template_input_change_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const ressource_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onCheckboxChange(ressource_r3.idRessource, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"small\", 14);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ressource_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"id\", \"ressource-\" + ressource_r3.idRessource)(\"checked\", ctx_r1.selectedRessources.includes(ressource_r3.idRessource));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"ressource-\" + ressource_r3.idRessource);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ressource_r3.nomRessource, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", ressource_r3.link_path, \")\");\n  }\n}\nfunction HabilitationComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtext(1, \" Veuillez s\\u00E9lectionner au moins une ressource avant de soumettre. \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HabilitationComponent {\n  constructor(route, habilitationService, router) {\n    this.route = route;\n    this.habilitationService = habilitationService;\n    this.router = router;\n    this.ressources = [];\n    this.habilitation = {\n      id: 0,\n      idGroupe: 0,\n      ressources: [] // Initialize as an empty array since we are selecting multiple resources\n    };\n    // Track selected ressources as IDs\n    this.selectedRessources = [];\n  }\n  ngOnInit() {\n    const idParam = this.route.snapshot.paramMap.get('id');\n    if (idParam) {\n      this.groupeId = Number(idParam);\n      this.getGroupeDetails(this.groupeId);\n      this.getRessources();\n    }\n  }\n  getGroupeDetails(id) {\n    this.habilitationService.getGroupeById(id).subscribe({\n      next: group => {\n        // Check if the group exists\n        if (group) {\n          this.groupe = group;\n          // Populate selected ressources based on existing group\n          if (this.groupe?.ressources) {\n            this.selectedRessources = this.groupe.ressources;\n          }\n        } else {\n          console.error('No groupe found with id:', id);\n        }\n      },\n      error: err => {\n        console.error('Error fetching group:', err);\n      }\n    });\n  }\n  getRessources() {\n    this.habilitationService.getAllRessources().subscribe({\n      next: data => {\n        this.ressources = data;\n      },\n      error: err => {\n        console.error('Error fetching ressources:', err);\n      }\n    });\n  }\n  onCheckboxChange(ressourceId, event) {\n    if (event.target.checked) {\n      // Add the ressource ID to the selected list if checked\n      this.selectedRessources.push(ressourceId);\n    } else {\n      // Remove the ressource ID from the selected list if unchecked\n      this.selectedRessources = this.selectedRessources.filter(id => id !== ressourceId);\n    }\n    // Log the selected resources to the console\n    console.log('Selected Ressources:', this.selectedRessources);\n  }\n  updateGroupe() {\n    if (!this.groupe) {\n      console.error(\"No groupe found!\");\n      return;\n    }\n    if (this.selectedRessources.length === 0) {\n      console.warn('Please select at least one valid ressource.');\n      return;\n    }\n    // Prepare habilitations manually using selected ressource IDs\n    const habilitations = this.selectedRessources.map(ressourceId => ({\n      idGroupe: this.groupeId,\n      ressources: [ressourceId]\n    }));\n    // Attach the habilitations to the groupe\n    const updatedGroupe = {\n      nomGroupe: this.groupe.nomGroupe,\n      habilitations: habilitations\n    };\n    this.habilitationService.updateGroupe(this.groupeId, updatedGroupe).subscribe({\n      next: response => {\n        console.log(\"Group updated successfully:\", response);\n        this.router.navigate(['/groups']);\n      },\n      error: err => {\n        console.error('Error updating group:', err);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function HabilitationComponent_Factory(t) {\n      return new (t || HabilitationComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.HabilitationService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HabilitationComponent,\n      selectors: [[\"app-habilitation\"]],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"container\", \"mt-4\"], [1, \"mb-4\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"form-label\", \"fw-semibold\"], [\"class\", \"form-check\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-success\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-save\"], [\"class\", \"text-danger mt-2\", 4, \"ngIf\"], [1, \"mb-3\"], [1, \"fw-bold\", \"text-primary\"], [1, \"text-dark\"], [1, \"form-check\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"id\", \"checked\", \"change\"], [1, \"form-check-label\", 3, \"for\"], [1, \"text-muted\"], [1, \"text-danger\", \"mt-2\"]],\n      template: function HabilitationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1);\n          i0.ɵɵtext(2, \"Mettre \\u00E0 jour les Habilitations du Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, HabilitationComponent_div_3_Template, 5, 1, \"div\", 2);\n          i0.ɵɵelementStart(4, \"div\", 1)(5, \"label\", 3);\n          i0.ɵɵtext(6, \"S\\u00E9lectionnez les Ressources :\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, HabilitationComponent_div_7_Template, 6, 5, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function HabilitationComponent_Template_button_click_8_listener() {\n            return ctx.updateGroupe();\n          });\n          i0.ɵɵelement(9, \"i\", 6);\n          i0.ɵɵtext(10, \" Enregistrer les changements \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, HabilitationComponent_div_11_Template, 2, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupe);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.ressources);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.selectedRessources.length === 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedRessources.length === 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf],\n      styles: [\".habilitation-container[_ngcontent-%COMP%] {\\n    max-width: 600px;\\n    margin: 20px auto;\\n    padding: 20px;\\n    background-color: #f9f9f9;\\n    border-radius: 8px;\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n  }\\n  \\n  h2[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 20px;\\n    color: #333;\\n    font-size: 1.5rem;\\n  }\\n  \\n  .form-group[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n  \\n  label[_ngcontent-%COMP%] {\\n    display: block;\\n    margin-bottom: 5px;\\n    font-weight: bold;\\n    color: #555;\\n  }\\n  \\n  select[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 10px;\\n    border: 1px solid #ddd;\\n    border-radius: 4px;\\n    font-size: 1rem;\\n    background-color: #fff;\\n    transition: border 0.3s ease;\\n  }\\n  \\n  select[_ngcontent-%COMP%]:focus {\\n    border-color: #007bff;\\n    outline: none;\\n  }\\n  \\n  button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 12px;\\n    background-color: #007bff;\\n    color: white;\\n    font-size: 1.1rem;\\n    border: none;\\n    border-radius: 4px;\\n    cursor: pointer;\\n    transition: background-color 0.3s ease;\\n  }\\n  \\n  button[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n  }\\n  \\n  button[_ngcontent-%COMP%]:focus {\\n    outline: none;\\n  }\\n  \\n  @media (max-width: 600px) {\\n    .habilitation-container[_ngcontent-%COMP%] {\\n      padding: 15px;\\n    }\\n  \\n    h2[_ngcontent-%COMP%] {\\n      font-size: 1.25rem;\\n    }\\n  \\n    select[_ngcontent-%COMP%] {\\n      font-size: 0.9rem;\\n    }\\n  \\n    button[_ngcontent-%COMP%] {\\n      font-size: 1rem;\\n    }\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "groupe", "nomGroupe", "ɵɵlistener", "HabilitationComponent_div_7_Template_input_change_1_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r5", "ressource_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "onCheckboxChange", "idRessource", "ɵɵproperty", "ctx_r1", "selectedRessources", "includes", "ɵɵtextInterpolate1", "nomRessource", "link_path", "HabilitationComponent", "constructor", "route", "habilitationService", "router", "ressources", "habilitation", "id", "idGroupe", "ngOnInit", "idParam", "snapshot", "paramMap", "get", "groupeId", "Number", "getGroupeDetails", "getRessources", "getGroupeById", "subscribe", "next", "group", "console", "error", "err", "getAllRessources", "data", "ressourceId", "event", "target", "checked", "push", "filter", "log", "updateGroupe", "length", "warn", "habilitations", "map", "updatedGroupe", "response", "navigate", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "HabilitationService", "Router", "selectors", "decls", "vars", "consts", "template", "HabilitationComponent_Template", "rf", "ctx", "ɵɵtemplate", "HabilitationComponent_div_3_Template", "HabilitationComponent_div_7_Template", "HabilitationComponent_Template_button_click_8_listener", "ɵɵelement", "HabilitationComponent_div_11_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\habilitation\\habilitation.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\habilitation\\habilitation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { HabilitationService } from '../services/habilitation.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Ressource } from '../model/ressource.model';\nimport { Habilitation } from '../model/Habilitation.model';\n\n@Component({\n  selector: 'app-habilitation',\n  templateUrl: './habilitation.component.html',\n  styleUrls: ['./habilitation.component.css']\n})\nexport class HabilitationComponent implements OnInit {\n  groupeId!: number;\n  groupe?: Groupe;\n  ressources: Ressource[] = [];\n\n  habilitation: Habilitation = {\n    id: 0,\n    idGroupe: 0,\n    ressources: [] // Initialize as an empty array since we are selecting multiple resources\n  };\n\n  // Track selected ressources as IDs\n  selectedRessources: number[] = [];\n\n  constructor(\n    private route: ActivatedRoute,\n    private habilitationService: HabilitationService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    const idParam = this.route.snapshot.paramMap.get('id');\n    if (idParam) {\n      this.groupeId = Number(idParam);\n      this.getGroupeDetails(this.groupeId);\n      this.getRessources();\n    }\n  }\n\n  getGroupeDetails(id: number): void {\n    this.habilitationService.getGroupeById(id).subscribe({\n      next: (group: Groupe) => {\n        // Check if the group exists\n        if (group) {\n          this.groupe = group;\n          // Populate selected ressources based on existing group\n          if (this.groupe?.ressources) {\n            this.selectedRessources = this.groupe.ressources;\n          }\n        } else {\n          console.error('No groupe found with id:', id);\n        }\n      },\n      error: (err) => {\n        console.error('Error fetching group:', err);\n      }\n    });\n  }\n  \n  getRessources(): void {\n    this.habilitationService.getAllRessources().subscribe({\n      next: (data: Ressource[]) => {\n        this.ressources = data;\n      },\n      error: (err) => {\n        console.error('Error fetching ressources:', err);\n      }\n    });\n  }\n\n  onCheckboxChange(ressourceId: number, event: any): void {\n    if (event.target.checked) {\n      // Add the ressource ID to the selected list if checked\n      this.selectedRessources.push(ressourceId);\n    } else {\n      // Remove the ressource ID from the selected list if unchecked\n      this.selectedRessources = this.selectedRessources.filter(id => id !== ressourceId);\n    }\n    \n    // Log the selected resources to the console\n    console.log('Selected Ressources:', this.selectedRessources);\n  }\n  \n\n  updateGroupe(): void {\n    if (!this.groupe) {\n      console.error(\"No groupe found!\");\n      return;\n    }\n  \n    if (this.selectedRessources.length === 0) {\n      console.warn('Please select at least one valid ressource.');\n      return;\n    }\n  \n    // Prepare habilitations manually using selected ressource IDs\n    const habilitations = this.selectedRessources.map((ressourceId): Habilitation => ({\n      idGroupe: this.groupeId,\n      ressources: [ressourceId]\n    }));\n  \n    // Attach the habilitations to the groupe\n    const updatedGroupe: Groupe = {\n      nomGroupe: this.groupe!.nomGroupe,\n      habilitations: habilitations\n    };\n  \n    this.habilitationService.updateGroupe(this.groupeId, updatedGroupe).subscribe({\n      next: (response: Groupe) => {\n        console.log(\"Group updated successfully:\", response);\n        this.router.navigate(['/groups']);\n      },\n      error: (err) => {\n        console.error('Error updating group:', err);\n      }\n    });\n  }\n  \n  \n}\n", "<div class=\"container mt-4\">\n  <h2 class=\"mb-4\">Mettre à jour les Habilitations du Groupe</h2>\n\n  <!-- Display group info -->\n  <div *ngIf=\"groupe\" class=\"mb-3\">\n    <p class=\"fw-bold text-primary\">\n      Groupe sélectionné : <span class=\"text-dark\">{{ groupe.nomGroupe }}</span>\n    </p>\n  </div>\n\n  <!-- Ressource selection checkboxes -->\n  <div class=\"mb-4\">\n    <label class=\"form-label fw-semibold\">Sélectionnez les Ressources :</label>\n    <div *ngFor=\"let ressource of ressources\" class=\"form-check\">\n      <input\n        type=\"checkbox\"\n        class=\"form-check-input\"\n        [id]=\"'ressource-' + ressource.idRessource\"\n        [checked]=\"selectedRessources.includes(ressource.idRessource)\"\n        (change)=\"onCheckboxChange(ressource.idRessource, $event)\"\n      />\n      <label class=\"form-check-label\" [for]=\"'ressource-' + ressource.idRessource\">\n        {{ ressource.nomRessource }} <small class=\"text-muted\">({{ ressource.link_path }})</small>\n      </label>\n    </div>\n  </div>\n\n  <!-- Submit button -->\n  <button\n    class=\"btn btn-success\"\n    (click)=\"updateGroupe()\"\n    [disabled]=\"selectedRessources.length === 0\"\n  >\n    <i class=\"bi bi-save\"></i> Enregistrer les changements\n  </button>\n\n  <!-- Warning message -->\n  <div *ngIf=\"selectedRessources.length === 0\" class=\"text-danger mt-2\">\n    Veuillez sélectionner au moins une ressource avant de soumettre.\n  </div>\n</div>\n"], "mappings": ";;;;;;ICIEA,EAAA,CAAAC,cAAA,aAAiC;IAE7BD,EAAA,CAAAE,MAAA,uCAAqB;IAAAF,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA7BH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,MAAA,CAAAC,SAAA,CAAsB;;;;;;IAOrER,EAAA,CAAAC,cAAA,cAA6D;IAMzDD,EAAA,CAAAS,UAAA,oBAAAC,6DAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,YAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAUlB,EAAA,CAAAmB,WAAA,CAAAF,MAAA,CAAAG,gBAAA,CAAAL,YAAA,CAAAM,WAAA,EAAAV,MAAA,CAA+C;IAAA,EAAC;IAL5DX,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAL1FH,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAsB,UAAA,sBAAAP,YAAA,CAAAM,WAAA,CAA2C,YAAAE,MAAA,CAAAC,kBAAA,CAAAC,QAAA,CAAAV,YAAA,CAAAM,WAAA;IAIbrB,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAsB,UAAA,uBAAAP,YAAA,CAAAM,WAAA,CAA4C;IAC1ErB,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAA0B,kBAAA,MAAAX,YAAA,CAAAY,YAAA,MAA6B;IAA0B3B,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAA0B,kBAAA,MAAAX,YAAA,CAAAa,SAAA,MAA2B;;;;;IAexF5B,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,MAAA,8EACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;AD3BR,OAAM,MAAO0B,qBAAqB;EAchCC,YACUC,KAAqB,EACrBC,mBAAwC,EACxCC,MAAc;IAFd,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IAdhB,KAAAC,UAAU,GAAgB,EAAE;IAE5B,KAAAC,YAAY,GAAiB;MAC3BC,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,CAAC;MACXH,UAAU,EAAE,EAAE,CAAC;KAChB;IAED;IACA,KAAAV,kBAAkB,GAAa,EAAE;EAM9B;EAEHc,QAAQA,CAAA;IACN,MAAMC,OAAO,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACtD,IAAIH,OAAO,EAAE;MACX,IAAI,CAACI,QAAQ,GAAGC,MAAM,CAACL,OAAO,CAAC;MAC/B,IAAI,CAACM,gBAAgB,CAAC,IAAI,CAACF,QAAQ,CAAC;MACpC,IAAI,CAACG,aAAa,EAAE;;EAExB;EAEAD,gBAAgBA,CAACT,EAAU;IACzB,IAAI,CAACJ,mBAAmB,CAACe,aAAa,CAACX,EAAE,CAAC,CAACY,SAAS,CAAC;MACnDC,IAAI,EAAGC,KAAa,IAAI;QACtB;QACA,IAAIA,KAAK,EAAE;UACT,IAAI,CAAC3C,MAAM,GAAG2C,KAAK;UACnB;UACA,IAAI,IAAI,CAAC3C,MAAM,EAAE2B,UAAU,EAAE;YAC3B,IAAI,CAACV,kBAAkB,GAAG,IAAI,CAACjB,MAAM,CAAC2B,UAAU;;SAEnD,MAAM;UACLiB,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEhB,EAAE,CAAC;;MAEjD,CAAC;MACDgB,KAAK,EAAGC,GAAG,IAAI;QACbF,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;MAC7C;KACD,CAAC;EACJ;EAEAP,aAAaA,CAAA;IACX,IAAI,CAACd,mBAAmB,CAACsB,gBAAgB,EAAE,CAACN,SAAS,CAAC;MACpDC,IAAI,EAAGM,IAAiB,IAAI;QAC1B,IAAI,CAACrB,UAAU,GAAGqB,IAAI;MACxB,CAAC;MACDH,KAAK,EAAGC,GAAG,IAAI;QACbF,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEC,GAAG,CAAC;MAClD;KACD,CAAC;EACJ;EAEAjC,gBAAgBA,CAACoC,WAAmB,EAAEC,KAAU;IAC9C,IAAIA,KAAK,CAACC,MAAM,CAACC,OAAO,EAAE;MACxB;MACA,IAAI,CAACnC,kBAAkB,CAACoC,IAAI,CAACJ,WAAW,CAAC;KAC1C,MAAM;MACL;MACA,IAAI,CAAChC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACqC,MAAM,CAACzB,EAAE,IAAIA,EAAE,KAAKoB,WAAW,CAAC;;IAGpF;IACAL,OAAO,CAACW,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACtC,kBAAkB,CAAC;EAC9D;EAGAuC,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACxD,MAAM,EAAE;MAChB4C,OAAO,CAACC,KAAK,CAAC,kBAAkB,CAAC;MACjC;;IAGF,IAAI,IAAI,CAAC5B,kBAAkB,CAACwC,MAAM,KAAK,CAAC,EAAE;MACxCb,OAAO,CAACc,IAAI,CAAC,6CAA6C,CAAC;MAC3D;;IAGF;IACA,MAAMC,aAAa,GAAG,IAAI,CAAC1C,kBAAkB,CAAC2C,GAAG,CAAEX,WAAW,KAAoB;MAChFnB,QAAQ,EAAE,IAAI,CAACM,QAAQ;MACvBT,UAAU,EAAE,CAACsB,WAAW;KACzB,CAAC,CAAC;IAEH;IACA,MAAMY,aAAa,GAAW;MAC5B5D,SAAS,EAAE,IAAI,CAACD,MAAO,CAACC,SAAS;MACjC0D,aAAa,EAAEA;KAChB;IAED,IAAI,CAAClC,mBAAmB,CAAC+B,YAAY,CAAC,IAAI,CAACpB,QAAQ,EAAEyB,aAAa,CAAC,CAACpB,SAAS,CAAC;MAC5EC,IAAI,EAAGoB,QAAgB,IAAI;QACzBlB,OAAO,CAACW,GAAG,CAAC,6BAA6B,EAAEO,QAAQ,CAAC;QACpD,IAAI,CAACpC,MAAM,CAACqC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACnC,CAAC;MACDlB,KAAK,EAAGC,GAAG,IAAI;QACbF,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;MAC7C;KACD,CAAC;EACJ;;;uBA1GWxB,qBAAqB,EAAA7B,EAAA,CAAAuE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzE,EAAA,CAAAuE,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAA3E,EAAA,CAAAuE,iBAAA,CAAAC,EAAA,CAAAI,MAAA;IAAA;EAAA;;;YAArB/C,qBAAqB;MAAAgD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlCnF,EAAA,CAAAC,cAAA,aAA4B;UACTD,EAAA,CAAAE,MAAA,qDAAyC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG/DH,EAAA,CAAAqF,UAAA,IAAAC,oCAAA,iBAIM;UAGNtF,EAAA,CAAAC,cAAA,aAAkB;UACsBD,EAAA,CAAAE,MAAA,yCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3EH,EAAA,CAAAqF,UAAA,IAAAE,oCAAA,iBAWM;UACRvF,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,gBAIC;UAFCD,EAAA,CAAAS,UAAA,mBAAA+E,uDAAA;YAAA,OAASJ,GAAA,CAAArB,YAAA,EAAc;UAAA,EAAC;UAGxB/D,EAAA,CAAAyF,SAAA,WAA0B;UAACzF,EAAA,CAAAE,MAAA,qCAC7B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAqF,UAAA,KAAAK,qCAAA,iBAEM;UACR1F,EAAA,CAAAG,YAAA,EAAM;;;UApCEH,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAsB,UAAA,SAAA8D,GAAA,CAAA7E,MAAA,CAAY;UASWP,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAsB,UAAA,YAAA8D,GAAA,CAAAlD,UAAA,CAAa;UAkBxClC,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAsB,UAAA,aAAA8D,GAAA,CAAA5D,kBAAA,CAAAwC,MAAA,OAA4C;UAMxChE,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAsB,UAAA,SAAA8D,GAAA,CAAA5D,kBAAA,CAAAwC,MAAA,OAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}