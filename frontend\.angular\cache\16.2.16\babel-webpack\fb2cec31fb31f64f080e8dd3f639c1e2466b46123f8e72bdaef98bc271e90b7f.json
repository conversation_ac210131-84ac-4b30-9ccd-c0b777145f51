{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class RessourceService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = 'http://localhost:8000/api/v1/auth/Ressources';\n  }\n  getAllRessources() {\n    return this.http.get(`${this.baseUrl}/all`); // Updated endpoint\n  }\n\n  static {\n    this.ɵfac = function RessourceService_Factory(t) {\n      return new (t || RessourceService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: RessourceService,\n      factory: RessourceService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["RessourceService", "constructor", "http", "baseUrl", "getAllRessources", "get", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\services\\ressource.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\n\nexport interface Ressource {\n  idRessource: number;\n  nomRessource: string;\n  link_path: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RessourceService {\n\n  private baseUrl = 'http://localhost:8000/api/v1/auth/Ressources';\n\n  constructor(private http: HttpClient) {}\n\n  getAllRessources(): Observable<Ressource[]> {\n    return this.http.get<Ressource[]>(`${this.baseUrl}/all`);  // Updated endpoint\n  }\n}\n"], "mappings": ";;AAaA,OAAM,MAAOA,gBAAgB;EAI3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,OAAO,GAAG,8CAA8C;EAEzB;EAEvCC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAc,GAAG,IAAI,CAACF,OAAO,MAAM,CAAC,CAAC,CAAE;EAC7D;;;;uBARWH,gBAAgB,EAAAM,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAhBT,gBAAgB;MAAAU,OAAA,EAAhBV,gBAAgB,CAAAW,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}