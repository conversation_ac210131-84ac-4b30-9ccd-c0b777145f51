{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/action.service\";\nimport * as i3 from \"../auth/authentication.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"myChart\"];\nfunction ActionsComponent_a_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 58);\n    i0.ɵɵelement(1, \"span\", 59);\n    i0.ɵɵtext(2, \" Dashboard \");\n    i0.ɵɵelementStart(3, \"span\", 60);\n    i0.ɵɵtext(4, \"(current)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActionsComponent_a_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 61);\n    i0.ɵɵelement(1, \"span\", 62);\n    i0.ɵɵtext(2, \" Gestion des utilisateurs \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_a_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 63);\n    i0.ɵɵelement(1, \"span\", 64);\n    i0.ɵɵtext(2, \" Gestion des groupes \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_a_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 65);\n    i0.ɵɵelement(1, \"span\", 66);\n    i0.ɵɵtext(2, \" Gestion des transactions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_a_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 67);\n    i0.ɵɵelement(1, \"span\", 68);\n    i0.ɵɵtext(2, \" Gestion des actions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_a_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 69);\n    i0.ɵɵelement(1, \"span\", 70);\n    i0.ɵɵtext(2, \" Gestion des actionnaires \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_a_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 71);\n    i0.ɵɵelement(1, \"span\", 72);\n    i0.ɵɵtext(2, \" Gestion des Portefeuilles \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_tr_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\")(11, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function ActionsComponent_tr_65_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const action_r9 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.deleteAction(action_r9.idAction));\n    });\n    i0.ɵɵelement(12, \"i\", 74);\n    i0.ɵɵtext(13, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const action_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r9.isinAction);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r9.nomSociete);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r9.prix);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 4, action_r9.dateCreation, \"short\"));\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, 0.25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: 'Poppins', sans-serif;\\n  margin: 0;\\n  padding: 0;\\n  min-height: 100vh;\\n  background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n  background-size: 400% 400%;\\n  animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n  color: #fff;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n  0% {\\n      background-position: 0% 50%;\\n  }\\n\\n  50% {\\n      background-position: 100% 50%;\\n  }\\n\\n  100% {\\n      background-position: 0% 50%;\\n  }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  margin-top: 20px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 1.5rem;\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  color: #fff;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  border-collapse: separate;\\n  border-spacing: 0 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.15);\\n  color: #ffffff;\\n  font-weight: 600;\\n  text-align: center;\\n  border: none;\\n  padding: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  text-align: center;\\n  border-radius: 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.01);\\n  box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  vertical-align: middle;\\n  padding: 0.9rem;\\n  font-size: 0.95rem;\\n}\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n  border: none;\\n  padding: 12px 25px;\\n  border-radius: 30px;\\n  text-transform: uppercase;\\n  font-weight: bold;\\n  transition: background 0.3s, transform 0.2s;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n  background-color: #218838;\\n  transform: translateY(-2px);\\n}\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n  color: #206ee1;\\n  cursor: pointer;\\n  font-size: 20px;\\n  margin: 0 10px;\\n  transition: color 0.3s;\\n}\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffffff;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n  color: #d22d2d;\\n  cursor: pointer;\\n  font-size: 20px;\\n  transition: color 0.3s;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffffff;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 1.5rem;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n  background-color: #f1f1f1;\\n  padding: 1rem;\\n  border-top: 1px solid #ddd;\\n  display: flex;\\n  justify-content: center; \\n\\n  gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  font-size: 1rem;\\n  border-radius: 0.3rem;\\n  transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n  transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column; \\n\\n  align-items: flex-start; \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  background-color: rgba(255, 255, 255, 0.1); \\n\\n  border: 1px solid rgba(255, 255, 255, 0.3); \\n\\n  border-radius: 30px; \\n\\n  color: #fff; \\n\\n  padding: 10px 20px; \\n\\n  font-size: 1rem; \\n\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); \\n\\n  width: 100%; \\n\\n  max-width: 400px; \\n\\n  transition: all 0.3s ease; \\n\\n}\\n\\n.search-bar[_ngcontent-%COMP%]:focus {\\n  outline: none; \\n\\n  border-color: #007bff; \\n\\n  box-shadow: 0 0 8px rgba(0, 123, 255, 0.6); \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n  background: rgba(20, 33, 59, 0.9); \\n\\n  color: #fff;\\n  min-height: 100vh;\\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #ccc;\\n  transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n  background-color: #000 !important;\\n  color: #fff;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n  position: relative;\\n  padding: 0.5rem 1rem;\\n  transition: color 0.3s ease;\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  width: 0;\\n  background: #ff4c60;\\n  transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 30px;\\n  object-fit: cover;\\n  border-radius: 50%; \\n\\n  margin-right: 8px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class ActionsComponent {\n  constructor(router, actionService, authService) {\n    this.router = router;\n    this.actionService = actionService;\n    this.authService = authService;\n    this.actions = [];\n    this.selectedAction = null;\n    this.newAction = {\n      isinAction: '',\n      nomSociete: '',\n      prix: 0\n    };\n  }\n  ngOnInit() {\n    this.loadActions();\n  }\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  loadActions() {\n    this.actionService.getAllActions().subscribe({\n      next: data => {\n        this.actions = data;\n        console.log('Actions loaded:', this.actions);\n      },\n      error: err => {\n        console.error('Erreur lors du chargement des actions :', err);\n      }\n    });\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token'));\n    this.router.navigate(['/login']);\n  }\n  deleteAction(id) {\n    if (confirm(\"Es-tu sûr de vouloir supprimer cette action ?\")) {\n      this.actionService.deleteActionById(id).subscribe({\n        next: () => {\n          this.actions = this.actions.filter(a => a.idAction !== id);\n          alert(\"Action supprimée avec succès !\");\n        },\n        error: err => {\n          console.error(\"Erreur lors de la suppression :\", err);\n          alert(\"Erreur lors de la suppression !\");\n        }\n      });\n    }\n  }\n  submitAction() {\n    const payload = {\n      isinAction: this.newAction.isinAction,\n      nomSociete: this.newAction.nomSociete,\n      prix: this.newAction.prix\n    };\n    this.actionService.addAction(payload).subscribe({\n      next: response => {\n        console.log('Action ajoutée:', response);\n        // Manually add the new action since the backend doesn't return it\n        const newActionToAdd = {\n          idAction: 0,\n          isinAction: this.newAction.isinAction,\n          nomSociete: this.newAction.nomSociete,\n          prix: this.newAction.prix,\n          dateCreation: new Date().toISOString(),\n          portefeuilles: []\n        };\n        this.actions.push(newActionToAdd);\n        this.newAction = {}; // Clear the form\n      },\n\n      error: err => {\n        if (err && err.error) {\n          console.error('Error details:', err.error);\n        } else {\n          console.error('Unexpected error format:', err);\n        }\n        alert(\"Erreur lors de l'ajout de l'action !\");\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ActionsComponent_Factory(t) {\n      return new (t || ActionsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ActionService), i0.ɵɵdirectiveInject(i3.AuthenticationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionsComponent,\n      selectors: [[\"app-actions\"]],\n      viewQuery: function ActionsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 91,\n      vars: 11,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"class\", \"nav-link\", \"href\", \"/adminDash\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/users\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/groups\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/transactions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actionnaires\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/port\", 4, \"ngIf\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"d-flex\", \"justify-content-end\", \"mb-2\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#addActionModal\", 1, \"btn\", \"btn-custom\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"thead-dark\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"myChart\", \"width\", \"900\", \"height\", \"380\", 1, \"my-4\"], [\"id\", \"addActionModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"addActionModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [3, \"ngSubmit\"], [\"actionForm\", \"ngForm\"], [1, \"modal-header\"], [\"id\", \"addActionModalLabel\", 1, \"modal-title\"], [1, \"modal-body\"], [1, \"mb-3\"], [\"for\", \"isinAction\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"isinAction\", \"name\", \"isinAction\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"nomSociete\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"nomSociete\", \"name\", \"nomSociete\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"prix\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"prix\", \"name\", \"prix\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\"], [\"href\", \"/adminDash\", 1, \"nav-link\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"book\"], [\"title\", \"Supprimer cette action\", 1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\"]],\n      template: function ActionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"link\", 5)(7, \"link\", 6)(8, \"link\", 7)(9, \"link\", 8);\n          i0.ɵɵelementStart(10, \"title\");\n          i0.ɵɵtext(11, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"link\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"body\")(14, \"nav\", 10)(15, \"a\", 11);\n          i0.ɵɵelement(16, \"img\", 12);\n          i0.ɵɵtext(17, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"ul\", 13)(19, \"li\", 14)(20, \"a\", 15);\n          i0.ɵɵlistener(\"click\", function ActionsComponent_Template_a_click_20_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(21, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"nav\", 18)(25, \"div\", 19)(26, \"ul\", 20)(27, \"li\", 21);\n          i0.ɵɵtemplate(28, ActionsComponent_a_28_Template, 5, 0, \"a\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"li\", 21);\n          i0.ɵɵtemplate(30, ActionsComponent_a_30_Template, 3, 0, \"a\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"li\", 21);\n          i0.ɵɵtemplate(32, ActionsComponent_a_32_Template, 3, 0, \"a\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"li\", 21);\n          i0.ɵɵtemplate(34, ActionsComponent_a_34_Template, 3, 0, \"a\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"li\", 21);\n          i0.ɵɵtemplate(36, ActionsComponent_a_36_Template, 3, 0, \"a\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"li\", 21);\n          i0.ɵɵtemplate(38, ActionsComponent_a_38_Template, 3, 0, \"a\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"li\", 21);\n          i0.ɵɵtemplate(40, ActionsComponent_a_40_Template, 3, 0, \"a\", 28);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"main\", 29)(42, \"div\", 30)(43, \"h1\", 31);\n          i0.ɵɵtext(44, \"Gestion des Actions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 32);\n          i0.ɵɵelement(46, \"div\", 33);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 34)(48, \"button\", 35);\n          i0.ɵɵtext(49, \" Ajouter un Action\\n\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 36)(51, \"table\", 37)(52, \"thead\", 38)(53, \"tr\")(54, \"th\");\n          i0.ɵɵtext(55, \"ISIN\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"th\");\n          i0.ɵɵtext(57, \"Nom Soci\\u00E9t\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"th\");\n          i0.ɵɵtext(59, \"Prix\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"th\");\n          i0.ɵɵtext(61, \"Date de Cr\\u00E9ation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"th\");\n          i0.ɵɵtext(63, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(64, \"tbody\");\n          i0.ɵɵtemplate(65, ActionsComponent_tr_65_Template, 14, 7, \"tr\", 39);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(66, \"canvas\", 40);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(67, \"div\", 41)(68, \"div\", 42)(69, \"div\", 43)(70, \"form\", 44, 45);\n          i0.ɵɵlistener(\"ngSubmit\", function ActionsComponent_Template_form_ngSubmit_70_listener() {\n            return ctx.submitAction();\n          });\n          i0.ɵɵelementStart(72, \"div\", 46)(73, \"h5\", 47);\n          i0.ɵɵtext(74, \"Ajouter une Action\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 48)(76, \"div\", 49)(77, \"label\", 50);\n          i0.ɵɵtext(78, \"ISIN Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"input\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function ActionsComponent_Template_input_ngModelChange_79_listener($event) {\n            return ctx.newAction.isinAction = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"div\", 49)(81, \"label\", 52);\n          i0.ɵɵtext(82, \"Nom de la Soci\\u00E9t\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"input\", 53);\n          i0.ɵɵlistener(\"ngModelChange\", function ActionsComponent_Template_input_ngModelChange_83_listener($event) {\n            return ctx.newAction.nomSociete = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"div\", 49)(85, \"label\", 54);\n          i0.ɵɵtext(86, \"Prix\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"input\", 55);\n          i0.ɵɵlistener(\"ngModelChange\", function ActionsComponent_Template_input_ngModelChange_87_listener($event) {\n            return ctx.newAction.prix = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(88, \"div\", 56)(89, \"button\", 57);\n          i0.ɵɵtext(90, \"Ajouter\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/adminDash\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/users\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/groups\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/transactions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actionnaires\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/port\"));\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngForOf\", ctx.actions);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.newAction.isinAction);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newAction.nomSociete);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newAction.prix);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NumberValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm, i4.DatePipe],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ActionsComponent_tr_65_Template_button_click_11_listener", "restoredCtx", "ɵɵrestoreView", "_r11", "action_r9", "$implicit", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "deleteAction", "idAction", "ɵɵadvance", "ɵɵtextInterpolate", "isinAction", "nomSociete", "prix", "ɵɵpipeBind2", "dateCreation", "ActionsComponent", "constructor", "router", "actionService", "authService", "actions", "selectedAction", "newAction", "ngOnInit", "loadActions", "ngAfterViewInit", "replace", "getAllActions", "subscribe", "next", "data", "console", "log", "error", "err", "logout", "localStorage", "removeItem", "getItem", "navigate", "id", "confirm", "deleteActionById", "filter", "a", "alert", "submitAction", "payload", "addAction", "response", "newActionToAdd", "Date", "toISOString", "portefeuilles", "push", "ɵɵdirectiveInject", "i1", "Router", "i2", "ActionService", "i3", "AuthenticationService", "selectors", "viewQuery", "ActionsComponent_Query", "rf", "ctx", "ActionsComponent_Template_a_click_20_listener", "ɵɵtemplate", "ActionsComponent_a_28_Template", "ActionsComponent_a_30_Template", "ActionsComponent_a_32_Template", "ActionsComponent_a_34_Template", "ActionsComponent_a_36_Template", "ActionsComponent_a_38_Template", "ActionsComponent_a_40_Template", "ActionsComponent_tr_65_Template", "ActionsComponent_Template_form_ngSubmit_70_listener", "ActionsComponent_Template_input_ngModelChange_79_listener", "$event", "ActionsComponent_Template_input_ngModelChange_83_listener", "ActionsComponent_Template_input_ngModelChange_87_listener", "ɵɵproperty", "isRouteAllowed"], "sources": ["C:\\Users\\<USER>\\Desktop\\Final - Copie\\frontend\\src\\app\\actions\\actions.component.ts", "C:\\Users\\<USER>\\Desktop\\Final - Copie\\frontend\\src\\app\\actions\\actions.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, ViewChild, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\nimport { Action } from '../model/action.model';\nimport { ActionService } from '../services/action.service';\nimport * as bootstrap from 'bootstrap';\nimport { AuthenticationService } from '../auth/authentication.service';\n\n@Component({\n  selector: 'app-actions',\n  templateUrl: './actions.component.html',\n  styleUrls: ['./actions.component.css']\n})\nexport class ActionsComponent implements AfterViewInit, OnInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  actions: Action[] = [];\n  selectedAction: Action | null = null;\n\n  newAction: Partial<Action> = {\n    isinAction: '',\n    nomSociete: '',\n    prix: 0\n  };\n\n  constructor(\n    private router: Router,\n    private actionService: ActionService,\n    public authService: AuthenticationService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadActions();\n  }\n\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  loadActions(): void {\n    this.actionService.getAllActions().subscribe({\n      next: (data) => {\n        this.actions = data;\n        console.log('Actions loaded:', this.actions);\n      },\n      error: (err) => {\n        console.error('Erreur lors du chargement des actions :', err);\n      }\n    });\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token'));\n    this.router.navigate(['/login']);\n  }\n\n\n\n\n  deleteAction(id: number) {\n    if (confirm(\"Es-tu sûr de vouloir supprimer cette action ?\")) {\n      this.actionService.deleteActionById(id).subscribe({\n        next: () => {\n          this.actions = this.actions.filter(a => a.idAction !== id);\n          alert(\"Action supprimée avec succès !\");\n        },\n        error: err => {\n          console.error(\"Erreur lors de la suppression :\", err);\n          alert(\"Erreur lors de la suppression !\");\n        }\n      });\n    }\n  }\nsubmitAction(): void {\n  const payload = {\n    isinAction: this.newAction.isinAction,\n    nomSociete: this.newAction.nomSociete,\n    prix: this.newAction.prix\n  };\n\n  this.actionService.addAction(payload).subscribe({\n    next: (response) => {\n      console.log('Action ajoutée:', response);\n\n      // Manually add the new action since the backend doesn't return it\n      const newActionToAdd: Action = {\n        idAction: 0, // or any placeholder if ID is generated\n        isinAction: this.newAction.isinAction!,\n        nomSociete: this.newAction.nomSociete!,\n        prix: this.newAction.prix!,\n        dateCreation: new Date().toISOString(), // Just a placeholder\n        portefeuilles: []\n      };\n\n      this.actions.push(newActionToAdd);\n\n      this.newAction = {}; // Clear the form\n    },\n    error: (err) => {\n      if (err && err.error) {\n        console.error('Error details:', err.error);\n      } else {\n        console.error('Unexpected error format:', err);\n      }\n\n      alert(\"Erreur lors de l'ajout de l'action !\");\n    }\n  });\n}\n}", "\n<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n<link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n    <!-- Bootstrap core CSS -->\n\n    <!-- Custom styles for this template -->\n    <link href=\"./actions.component.css\" rel=\"stylesheet\">\n\n  </head>\n\n  <body>\n    \n     <!-- Navbar -->\n     <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/adminDash\" *ngIf=\"authService.isRouteAllowed('/adminDash')\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\" *ngIf=\"authService.isRouteAllowed('/users')\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\" *ngIf=\"authService.isRouteAllowed('/groups')\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            \n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/transactions\" *ngIf=\"authService.isRouteAllowed('/transactions')\">\n                <span data-feather=\"dollar-sign\"></span>\n                Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/actions\" *ngIf=\"authService.isRouteAllowed('/actions')\">\n                <span data-feather=\"trending-up\"></span>\n               Gestion des actions\n              </a>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\" *ngIf=\"authService.isRouteAllowed('/actionnaires')\">\n                  <span data-feather=\"users\"></span>\n                 Gestion des actionnaires\n                </a>\n                     <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/port\" *ngIf=\"authService.isRouteAllowed('/port')\">\n                <span data-feather=\"book\"></span>\n               Gestion des Portefeuilles\n              </a>\n            </ul>\n          </div>\n        </nav>\n\n        \n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Gestion des Actions</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n\n              </div>\n            </div>\n          </div>\n               <div class=\"d-flex justify-content-end mb-2\">\n<button type=\"button\" class=\"btn btn-custom\" data-toggle=\"modal\" data-target=\"#addActionModal\">\n  Ajouter un Action\n</button>\n</div>\n   <div class=\"table-responsive\">\n\n  <table class=\"table table-hover\">\n\n    \n    <thead class=\"thead-dark\">\n      <tr>\n        <th>ISIN</th>\n        <th>Nom Société</th>\n        <th>Prix</th>\n        <th>Date de Création</th>\n        <th>Actions</th>\n      </tr>\n    </thead>\n    <tbody>\n      <tr *ngFor=\"let action of actions\">\n        <td>{{ action.isinAction }}</td>\n        <td>{{ action.nomSociete }}</td>\n        <td>{{ action.prix }}</td>\n        <td>{{ action.dateCreation | date: 'short' }}</td>\n        <td>\n          <button class=\"btn btn-danger btn-sm\" (click)=\"deleteAction(action.idAction)\" title=\"Supprimer cette action\">\n            <i class=\"fas fa-trash-alt\"></i> Supprimer\n          </button>\n        </td>\n      </tr>\n    </tbody>\n  </table>\n</div>\n\n\n          <canvas class=\"my-4\" id=\"myChart\" width=\"900\" height=\"380\"></canvas>\n          \n        \n        </main>\n      </div>\n    </div>\n\n\n<!-- Add Action Modal -->\n<div class=\"modal fade\" id=\"addActionModal\" tabindex=\"-1\" aria-labelledby=\"addActionModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <form (ngSubmit)=\"submitAction()\" #actionForm=\"ngForm\">\n        <div class=\"modal-header\">\n          <h5 class=\"modal-title\" id=\"addActionModalLabel\">Ajouter une Action</h5>\n        </div>\n        <div class=\"modal-body\">\n\n          <div class=\"mb-3\">\n            <label for=\"isinAction\" class=\"form-label\">ISIN Action</label>\n            <input type=\"text\" id=\"isinAction\" class=\"form-control\" [(ngModel)]=\"newAction.isinAction\" name=\"isinAction\" required>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"nomSociete\" class=\"form-label\">Nom de la Société</label>\n            <input type=\"text\" id=\"nomSociete\" class=\"form-control\" [(ngModel)]=\"newAction.nomSociete\" name=\"nomSociete\" required>\n          </div>\n\n          <div class=\"mb-3\">\n            <label for=\"prix\" class=\"form-label\">Prix</label>\n            <input type=\"number\" id=\"prix\" class=\"form-control\" [(ngModel)]=\"newAction.prix\" name=\"prix\" required>\n          </div>\n\n        </div>\n        <div class=\"modal-footer\">\n          <button type=\"submit\" class=\"btn btn-success\">Ajouter</button>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n\n    <!-- Bootstrap core JavaScript\n    ================================================== -->\n    <!-- Placed at the end of the document so the pages load faster -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>    \n\n    </script>\n  </body>\n</html>\n"], "mappings": "AAEA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;;;;;;;ICwCxBC,EAAA,CAAAC,cAAA,YAAuF;IACrFD,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIlDJ,EAAA,CAAAC,cAAA,YAA+E;IAC7ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAiF;IAC/ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAINJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAwC;IACxCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAmF;IACjFD,EAAA,CAAAE,SAAA,eAAwC;IACzCF,EAAA,CAAAG,MAAA,4BACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAEFJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAkC;IACnCF,EAAA,CAAAG,MAAA,iCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,YAA6E;IAC3ED,EAAA,CAAAE,SAAA,eAAiC;IAClCF,EAAA,CAAAG,MAAA,kCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAmCZJ,EAAA,CAAAC,cAAA,SAAmC;IAC7BD,EAAA,CAAAG,MAAA,GAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAyC;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClDJ,EAAA,CAAAC,cAAA,UAAI;IACoCD,EAAA,CAAAK,UAAA,mBAAAC,yDAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,OAAA,CAAAG,YAAA,CAAAL,SAAA,CAAAM,QAAA,CAA6B;IAAA,EAAC;IAC3EhB,EAAA,CAAAE,SAAA,aAAgC;IAACF,EAAA,CAAAG,MAAA,mBACnC;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAPPJ,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAkB,iBAAA,CAAAR,SAAA,CAAAS,UAAA,CAAuB;IACvBnB,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAkB,iBAAA,CAAAR,SAAA,CAAAU,UAAA,CAAuB;IACvBpB,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,iBAAA,CAAAR,SAAA,CAAAW,IAAA,CAAiB;IACjBrB,EAAA,CAAAiB,SAAA,GAAyC;IAAzCjB,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAAsB,WAAA,OAAAZ,SAAA,CAAAa,YAAA,WAAyC;;;;AD1GrD,OAAM,MAAOC,gBAAgB;EAW3BC,YACUC,MAAc,EACdC,aAA4B,EAC7BC,WAAkC;IAFjC,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACd,KAAAC,WAAW,GAAXA,WAAW;IAZpB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,cAAc,GAAkB,IAAI;IAEpC,KAAAC,SAAS,GAAoB;MAC3BZ,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,EAAE;MACdC,IAAI,EAAE;KACP;EAME;EAEHW,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,eAAeA,CAAA;IACbnC,OAAO,CAACoC,OAAO,EAAE,CAAC,CAAC;EACrB;;EAEAF,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACS,aAAa,EAAE,CAACC,SAAS,CAAC;MAC3CC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACV,OAAO,GAAGU,IAAI;QACnBC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACZ,OAAO,CAAC;MAC9C,CAAC;MACDa,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;MAC/D;KACD,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCN,OAAO,CAACC,GAAG,CAACI,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAKAjC,YAAYA,CAACkC,EAAU;IACrB,IAAIC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MAC5D,IAAI,CAACvB,aAAa,CAACwB,gBAAgB,CAACF,EAAE,CAAC,CAACZ,SAAS,CAAC;QAChDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACT,OAAO,GAAG,IAAI,CAACA,OAAO,CAACuB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrC,QAAQ,KAAKiC,EAAE,CAAC;UAC1DK,KAAK,CAAC,gCAAgC,CAAC;QACzC,CAAC;QACDZ,KAAK,EAAEC,GAAG,IAAG;UACXH,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEC,GAAG,CAAC;UACrDW,KAAK,CAAC,iCAAiC,CAAC;QAC1C;OACD,CAAC;;EAEN;EACFC,YAAYA,CAAA;IACV,MAAMC,OAAO,GAAG;MACdrC,UAAU,EAAE,IAAI,CAACY,SAAS,CAACZ,UAAU;MACrCC,UAAU,EAAE,IAAI,CAACW,SAAS,CAACX,UAAU;MACrCC,IAAI,EAAE,IAAI,CAACU,SAAS,CAACV;KACtB;IAED,IAAI,CAACM,aAAa,CAAC8B,SAAS,CAACD,OAAO,CAAC,CAACnB,SAAS,CAAC;MAC9CC,IAAI,EAAGoB,QAAQ,IAAI;QACjBlB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEiB,QAAQ,CAAC;QAExC;QACA,MAAMC,cAAc,GAAW;UAC7B3C,QAAQ,EAAE,CAAC;UACXG,UAAU,EAAE,IAAI,CAACY,SAAS,CAACZ,UAAW;UACtCC,UAAU,EAAE,IAAI,CAACW,SAAS,CAACX,UAAW;UACtCC,IAAI,EAAE,IAAI,CAACU,SAAS,CAACV,IAAK;UAC1BE,YAAY,EAAE,IAAIqC,IAAI,EAAE,CAACC,WAAW,EAAE;UACtCC,aAAa,EAAE;SAChB;QAED,IAAI,CAACjC,OAAO,CAACkC,IAAI,CAACJ,cAAc,CAAC;QAEjC,IAAI,CAAC5B,SAAS,GAAG,EAAE,CAAC,CAAC;MACvB,CAAC;;MACDW,KAAK,EAAGC,GAAG,IAAI;QACb,IAAIA,GAAG,IAAIA,GAAG,CAACD,KAAK,EAAE;UACpBF,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEC,GAAG,CAACD,KAAK,CAAC;SAC3C,MAAM;UACLF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEC,GAAG,CAAC;;QAGhDW,KAAK,CAAC,sCAAsC,CAAC;MAC/C;KACD,CAAC;EACJ;;;uBAjGa9B,gBAAgB,EAAAxB,EAAA,CAAAgE,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAlE,EAAA,CAAAgE,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAApE,EAAA,CAAAgE,iBAAA,CAAAK,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAAhB9C,gBAAgB;MAAA+C,SAAA;MAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCX7B1E,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAE,SAAA,cAAsB;UAQtBF,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAG,MAAA,wCAAgC;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAE/CJ,EAAA,CAAAE,SAAA,eAAmF;UAMrFF,EAAA,CAAAI,YAAA,EAAO;UAEPJ,EAAA,CAAAC,cAAA,YAAM;UAKAD,EAAA,CAAAE,SAAA,eAAwE;UACxEF,EAAA,CAAAG,MAAA,aACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAK,UAAA,mBAAAuE,8CAAA;YAAA,OAASD,GAAA,CAAA/B,MAAA,EAAQ;UAAA,EAAC;UAAC5C,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAIzDJ,EAAA,CAAAC,cAAA,eAA6B;UAMjBD,EAAA,CAAA6E,UAAA,KAAAC,8BAAA,gBAGI;UACN9E,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA6E,UAAA,KAAAE,8BAAA,gBAGI;UACN/E,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA6E,UAAA,KAAAG,8BAAA,gBAGI;UACNhF,EAAA,CAAAI,YAAA,EAAK;UAEPJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA6E,UAAA,KAAAI,8BAAA,gBAGI;UACNjF,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA6E,UAAA,KAAAK,8BAAA,gBAGI;UAJNlF,EAAA,CAAAI,YAAA,EAAqB;UAKnBJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA6E,UAAA,KAAAM,8BAAA,gBAGI;UAJNnF,EAAA,CAAAI,YAAA,EAAqB;UAKdJ,EAAA,CAAAC,cAAA,cAAqB;UAC5BD,EAAA,CAAA6E,UAAA,KAAAO,8BAAA,gBAGI;UAJGpF,EAAA,CAAAI,YAAA,EAAqB;UAUlCJ,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAG,MAAA,2BAAmB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvCJ,EAAA,CAAAC,cAAA,eAAsC;UACpCD,EAAA,CAAAE,SAAA,eAEM;UACRF,EAAA,CAAAI,YAAA,EAAM;UAEHJ,EAAA,CAAAC,cAAA,eAA6C;UAE1DD,EAAA,CAAAG,MAAA,4BACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAENJ,EAAA,CAAAC,cAAA,eAA8B;UAOrBD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACbJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,6BAAW;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACbJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,6BAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGpBJ,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA6E,UAAA,KAAAQ,+BAAA,kBAUK;UACPrF,EAAA,CAAAI,YAAA,EAAQ;UAKFJ,EAAA,CAAAE,SAAA,kBAAoE;UAGtEF,EAAA,CAAAI,YAAA,EAAO;UAMfJ,EAAA,CAAAC,cAAA,eAAmH;UAGvGD,EAAA,CAAAK,UAAA,sBAAAiF,oDAAA;YAAA,OAAYX,GAAA,CAAApB,YAAA,EAAc;UAAA,EAAC;UAC/BvD,EAAA,CAAAC,cAAA,eAA0B;UACyBD,EAAA,CAAAG,MAAA,0BAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAE1EJ,EAAA,CAAAC,cAAA,eAAwB;UAGuBD,EAAA,CAAAG,MAAA,mBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC9DJ,EAAA,CAAAC,cAAA,iBAAsH;UAA9DD,EAAA,CAAAK,UAAA,2BAAAkF,0DAAAC,MAAA;YAAA,OAAAb,GAAA,CAAA5C,SAAA,CAAAZ,UAAA,GAAAqE,MAAA;UAAA,EAAkC;UAA1FxF,EAAA,CAAAI,YAAA,EAAsH;UAGxHJ,EAAA,CAAAC,cAAA,eAAkB;UAC2BD,EAAA,CAAAG,MAAA,mCAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACpEJ,EAAA,CAAAC,cAAA,iBAAsH;UAA9DD,EAAA,CAAAK,UAAA,2BAAAoF,0DAAAD,MAAA;YAAA,OAAAb,GAAA,CAAA5C,SAAA,CAAAX,UAAA,GAAAoE,MAAA;UAAA,EAAkC;UAA1FxF,EAAA,CAAAI,YAAA,EAAsH;UAGxHJ,EAAA,CAAAC,cAAA,eAAkB;UACqBD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACjDJ,EAAA,CAAAC,cAAA,iBAAsG;UAAlDD,EAAA,CAAAK,UAAA,2BAAAqF,0DAAAF,MAAA;YAAA,OAAAb,GAAA,CAAA5C,SAAA,CAAAV,IAAA,GAAAmE,MAAA;UAAA,EAA4B;UAAhFxF,EAAA,CAAAI,YAAA,EAAsG;UAI1GJ,EAAA,CAAAC,cAAA,eAA0B;UACsBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAS;;;UA5HjBJ,EAAA,CAAAiB,SAAA,IAA8C;UAA9CjB,EAAA,CAAA2F,UAAA,SAAAhB,GAAA,CAAA/C,WAAA,CAAAgE,cAAA,eAA8C;UAMlD5F,EAAA,CAAAiB,SAAA,GAA0C;UAA1CjB,EAAA,CAAA2F,UAAA,SAAAhB,GAAA,CAAA/C,WAAA,CAAAgE,cAAA,WAA0C;UAMzC5F,EAAA,CAAAiB,SAAA,GAA2C;UAA3CjB,EAAA,CAAA2F,UAAA,SAAAhB,GAAA,CAAA/C,WAAA,CAAAgE,cAAA,YAA2C;UAOvC5F,EAAA,CAAAiB,SAAA,GAAiD;UAAjDjB,EAAA,CAAA2F,UAAA,SAAAhB,GAAA,CAAA/C,WAAA,CAAAgE,cAAA,kBAAiD;UAMtD5F,EAAA,CAAAiB,SAAA,GAA4C;UAA5CjB,EAAA,CAAA2F,UAAA,SAAAhB,GAAA,CAAA/C,WAAA,CAAAgE,cAAA,aAA4C;UAKrC5F,EAAA,CAAAiB,SAAA,GAAiD;UAAjDjB,EAAA,CAAA2F,UAAA,SAAAhB,GAAA,CAAA/C,WAAA,CAAAgE,cAAA,kBAAiD;UAK3D5F,EAAA,CAAAiB,SAAA,GAAyC;UAAzCjB,EAAA,CAAA2F,UAAA,SAAAhB,GAAA,CAAA/C,WAAA,CAAAgE,cAAA,UAAyC;UAsC5D5F,EAAA,CAAAiB,SAAA,IAAU;UAAVjB,EAAA,CAAA2F,UAAA,YAAAhB,GAAA,CAAA9C,OAAA,CAAU;UAoC6B7B,EAAA,CAAAiB,SAAA,IAAkC;UAAlCjB,EAAA,CAAA2F,UAAA,YAAAhB,GAAA,CAAA5C,SAAA,CAAAZ,UAAA,CAAkC;UAKlCnB,EAAA,CAAAiB,SAAA,GAAkC;UAAlCjB,EAAA,CAAA2F,UAAA,YAAAhB,GAAA,CAAA5C,SAAA,CAAAX,UAAA,CAAkC;UAKtCpB,EAAA,CAAAiB,SAAA,GAA4B;UAA5BjB,EAAA,CAAA2F,UAAA,YAAAhB,GAAA,CAAA5C,SAAA,CAAAV,IAAA,CAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}