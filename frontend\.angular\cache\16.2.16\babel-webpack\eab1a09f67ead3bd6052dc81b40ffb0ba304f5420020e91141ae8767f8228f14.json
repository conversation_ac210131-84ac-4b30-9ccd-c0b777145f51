{"ast": null, "code": "/*!\n * body-parser\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module dependencies.\n * @private\n */\nvar createError = require('http-errors');\nvar destroy = require('destroy');\nvar getBody = require('raw-body');\nvar iconv = require('iconv-lite');\nvar onFinished = require('on-finished');\nvar unpipe = require('unpipe');\nvar zlib = require('zlib');\n\n/**\n * Module exports.\n */\n\nmodule.exports = read;\n\n/**\n * Read a request into a buffer and parse.\n *\n * @param {object} req\n * @param {object} res\n * @param {function} next\n * @param {function} parse\n * @param {function} debug\n * @param {object} options\n * @private\n */\n\nfunction read(req, res, next, parse, debug, options) {\n  var length;\n  var opts = options;\n  var stream;\n\n  // flag as parsed\n  req._body = true;\n\n  // read options\n  var encoding = opts.encoding !== null ? opts.encoding : null;\n  var verify = opts.verify;\n  try {\n    // get the content stream\n    stream = contentstream(req, debug, opts.inflate);\n    length = stream.length;\n    stream.length = undefined;\n  } catch (err) {\n    return next(err);\n  }\n\n  // set raw-body options\n  opts.length = length;\n  opts.encoding = verify ? null : encoding;\n\n  // assert charset is supported\n  if (opts.encoding === null && encoding !== null && !iconv.encodingExists(encoding)) {\n    return next(createError(415, 'unsupported charset \"' + encoding.toUpperCase() + '\"', {\n      charset: encoding.toLowerCase(),\n      type: 'charset.unsupported'\n    }));\n  }\n\n  // read body\n  debug('read body');\n  getBody(stream, opts, function (error, body) {\n    if (error) {\n      var _error;\n      if (error.type === 'encoding.unsupported') {\n        // echo back charset\n        _error = createError(415, 'unsupported charset \"' + encoding.toUpperCase() + '\"', {\n          charset: encoding.toLowerCase(),\n          type: 'charset.unsupported'\n        });\n      } else {\n        // set status code on error\n        _error = createError(400, error);\n      }\n\n      // unpipe from stream and destroy\n      if (stream !== req) {\n        unpipe(req);\n        destroy(stream, true);\n      }\n\n      // read off entire request\n      dump(req, function onfinished() {\n        next(createError(400, _error));\n      });\n      return;\n    }\n\n    // verify\n    if (verify) {\n      try {\n        debug('verify body');\n        verify(req, res, body, encoding);\n      } catch (err) {\n        next(createError(403, err, {\n          body: body,\n          type: err.type || 'entity.verify.failed'\n        }));\n        return;\n      }\n    }\n\n    // parse\n    var str = body;\n    try {\n      debug('parse body');\n      str = typeof body !== 'string' && encoding !== null ? iconv.decode(body, encoding) : body;\n      req.body = parse(str);\n    } catch (err) {\n      next(createError(400, err, {\n        body: str,\n        type: err.type || 'entity.parse.failed'\n      }));\n      return;\n    }\n    next();\n  });\n}\n\n/**\n * Get the content stream of the request.\n *\n * @param {object} req\n * @param {function} debug\n * @param {boolean} [inflate=true]\n * @return {object}\n * @api private\n */\n\nfunction contentstream(req, debug, inflate) {\n  var encoding = (req.headers['content-encoding'] || 'identity').toLowerCase();\n  var length = req.headers['content-length'];\n  var stream;\n  debug('content-encoding \"%s\"', encoding);\n  if (inflate === false && encoding !== 'identity') {\n    throw createError(415, 'content encoding unsupported', {\n      encoding: encoding,\n      type: 'encoding.unsupported'\n    });\n  }\n  switch (encoding) {\n    case 'deflate':\n      stream = zlib.createInflate();\n      debug('inflate body');\n      req.pipe(stream);\n      break;\n    case 'gzip':\n      stream = zlib.createGunzip();\n      debug('gunzip body');\n      req.pipe(stream);\n      break;\n    case 'identity':\n      stream = req;\n      stream.length = length;\n      break;\n    default:\n      throw createError(415, 'unsupported content encoding \"' + encoding + '\"', {\n        encoding: encoding,\n        type: 'encoding.unsupported'\n      });\n  }\n  return stream;\n}\n\n/**\n * Dump the contents of a request.\n *\n * @param {object} req\n * @param {function} callback\n * @api private\n */\n\nfunction dump(req, callback) {\n  if (onFinished.isFinished(req)) {\n    callback(null);\n  } else {\n    onFinished(req, callback);\n    req.resume();\n  }\n}", "map": {"version": 3, "names": ["createError", "require", "destroy", "getBody", "iconv", "onFinished", "unpipe", "zlib", "module", "exports", "read", "req", "res", "next", "parse", "debug", "options", "length", "opts", "stream", "_body", "encoding", "verify", "contentstream", "inflate", "undefined", "err", "encodingExists", "toUpperCase", "charset", "toLowerCase", "type", "error", "body", "_error", "dump", "onfinished", "str", "decode", "headers", "createInflate", "pipe", "createGunzip", "callback", "isFinished", "resume"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/body-parser/lib/read.js"], "sourcesContent": ["/*!\n * body-parser\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict'\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar createError = require('http-errors')\nvar destroy = require('destroy')\nvar getBody = require('raw-body')\nvar iconv = require('iconv-lite')\nvar onFinished = require('on-finished')\nvar unpipe = require('unpipe')\nvar zlib = require('zlib')\n\n/**\n * Module exports.\n */\n\nmodule.exports = read\n\n/**\n * Read a request into a buffer and parse.\n *\n * @param {object} req\n * @param {object} res\n * @param {function} next\n * @param {function} parse\n * @param {function} debug\n * @param {object} options\n * @private\n */\n\nfunction read (req, res, next, parse, debug, options) {\n  var length\n  var opts = options\n  var stream\n\n  // flag as parsed\n  req._body = true\n\n  // read options\n  var encoding = opts.encoding !== null\n    ? opts.encoding\n    : null\n  var verify = opts.verify\n\n  try {\n    // get the content stream\n    stream = contentstream(req, debug, opts.inflate)\n    length = stream.length\n    stream.length = undefined\n  } catch (err) {\n    return next(err)\n  }\n\n  // set raw-body options\n  opts.length = length\n  opts.encoding = verify\n    ? null\n    : encoding\n\n  // assert charset is supported\n  if (opts.encoding === null && encoding !== null && !iconv.encodingExists(encoding)) {\n    return next(createError(415, 'unsupported charset \"' + encoding.toUpperCase() + '\"', {\n      charset: encoding.toLowerCase(),\n      type: 'charset.unsupported'\n    }))\n  }\n\n  // read body\n  debug('read body')\n  getBody(stream, opts, function (error, body) {\n    if (error) {\n      var _error\n\n      if (error.type === 'encoding.unsupported') {\n        // echo back charset\n        _error = createError(415, 'unsupported charset \"' + encoding.toUpperCase() + '\"', {\n          charset: encoding.toLowerCase(),\n          type: 'charset.unsupported'\n        })\n      } else {\n        // set status code on error\n        _error = createError(400, error)\n      }\n\n      // unpipe from stream and destroy\n      if (stream !== req) {\n        unpipe(req)\n        destroy(stream, true)\n      }\n\n      // read off entire request\n      dump(req, function onfinished () {\n        next(createError(400, _error))\n      })\n      return\n    }\n\n    // verify\n    if (verify) {\n      try {\n        debug('verify body')\n        verify(req, res, body, encoding)\n      } catch (err) {\n        next(createError(403, err, {\n          body: body,\n          type: err.type || 'entity.verify.failed'\n        }))\n        return\n      }\n    }\n\n    // parse\n    var str = body\n    try {\n      debug('parse body')\n      str = typeof body !== 'string' && encoding !== null\n        ? iconv.decode(body, encoding)\n        : body\n      req.body = parse(str)\n    } catch (err) {\n      next(createError(400, err, {\n        body: str,\n        type: err.type || 'entity.parse.failed'\n      }))\n      return\n    }\n\n    next()\n  })\n}\n\n/**\n * Get the content stream of the request.\n *\n * @param {object} req\n * @param {function} debug\n * @param {boolean} [inflate=true]\n * @return {object}\n * @api private\n */\n\nfunction contentstream (req, debug, inflate) {\n  var encoding = (req.headers['content-encoding'] || 'identity').toLowerCase()\n  var length = req.headers['content-length']\n  var stream\n\n  debug('content-encoding \"%s\"', encoding)\n\n  if (inflate === false && encoding !== 'identity') {\n    throw createError(415, 'content encoding unsupported', {\n      encoding: encoding,\n      type: 'encoding.unsupported'\n    })\n  }\n\n  switch (encoding) {\n    case 'deflate':\n      stream = zlib.createInflate()\n      debug('inflate body')\n      req.pipe(stream)\n      break\n    case 'gzip':\n      stream = zlib.createGunzip()\n      debug('gunzip body')\n      req.pipe(stream)\n      break\n    case 'identity':\n      stream = req\n      stream.length = length\n      break\n    default:\n      throw createError(415, 'unsupported content encoding \"' + encoding + '\"', {\n        encoding: encoding,\n        type: 'encoding.unsupported'\n      })\n  }\n\n  return stream\n}\n\n/**\n * Dump the contents of a request.\n *\n * @param {object} req\n * @param {function} callback\n * @api private\n */\n\nfunction dump (req, callback) {\n  if (onFinished.isFinished(req)) {\n    callback(null)\n  } else {\n    onFinished(req, callback)\n    req.resume()\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AAEA,IAAIA,WAAW,GAAGC,OAAO,CAAC,aAAa,CAAC;AACxC,IAAIC,OAAO,GAAGD,OAAO,CAAC,SAAS,CAAC;AAChC,IAAIE,OAAO,GAAGF,OAAO,CAAC,UAAU,CAAC;AACjC,IAAIG,KAAK,GAAGH,OAAO,CAAC,YAAY,CAAC;AACjC,IAAII,UAAU,GAAGJ,OAAO,CAAC,aAAa,CAAC;AACvC,IAAIK,MAAM,GAAGL,OAAO,CAAC,QAAQ,CAAC;AAC9B,IAAIM,IAAI,GAAGN,OAAO,CAAC,MAAM,CAAC;;AAE1B;AACA;AACA;;AAEAO,MAAM,CAACC,OAAO,GAAGC,IAAI;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACpD,IAAIC,MAAM;EACV,IAAIC,IAAI,GAAGF,OAAO;EAClB,IAAIG,MAAM;;EAEV;EACAR,GAAG,CAACS,KAAK,GAAG,IAAI;;EAEhB;EACA,IAAIC,QAAQ,GAAGH,IAAI,CAACG,QAAQ,KAAK,IAAI,GACjCH,IAAI,CAACG,QAAQ,GACb,IAAI;EACR,IAAIC,MAAM,GAAGJ,IAAI,CAACI,MAAM;EAExB,IAAI;IACF;IACAH,MAAM,GAAGI,aAAa,CAACZ,GAAG,EAAEI,KAAK,EAAEG,IAAI,CAACM,OAAO,CAAC;IAChDP,MAAM,GAAGE,MAAM,CAACF,MAAM;IACtBE,MAAM,CAACF,MAAM,GAAGQ,SAAS;EAC3B,CAAC,CAAC,OAAOC,GAAG,EAAE;IACZ,OAAOb,IAAI,CAACa,GAAG,CAAC;EAClB;;EAEA;EACAR,IAAI,CAACD,MAAM,GAAGA,MAAM;EACpBC,IAAI,CAACG,QAAQ,GAAGC,MAAM,GAClB,IAAI,GACJD,QAAQ;;EAEZ;EACA,IAAIH,IAAI,CAACG,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,IAAI,IAAI,CAACjB,KAAK,CAACuB,cAAc,CAACN,QAAQ,CAAC,EAAE;IAClF,OAAOR,IAAI,CAACb,WAAW,CAAC,GAAG,EAAE,uBAAuB,GAAGqB,QAAQ,CAACO,WAAW,CAAC,CAAC,GAAG,GAAG,EAAE;MACnFC,OAAO,EAAER,QAAQ,CAACS,WAAW,CAAC,CAAC;MAC/BC,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL;;EAEA;EACAhB,KAAK,CAAC,WAAW,CAAC;EAClBZ,OAAO,CAACgB,MAAM,EAAED,IAAI,EAAE,UAAUc,KAAK,EAAEC,IAAI,EAAE;IAC3C,IAAID,KAAK,EAAE;MACT,IAAIE,MAAM;MAEV,IAAIF,KAAK,CAACD,IAAI,KAAK,sBAAsB,EAAE;QACzC;QACAG,MAAM,GAAGlC,WAAW,CAAC,GAAG,EAAE,uBAAuB,GAAGqB,QAAQ,CAACO,WAAW,CAAC,CAAC,GAAG,GAAG,EAAE;UAChFC,OAAO,EAAER,QAAQ,CAACS,WAAW,CAAC,CAAC;UAC/BC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAG,MAAM,GAAGlC,WAAW,CAAC,GAAG,EAAEgC,KAAK,CAAC;MAClC;;MAEA;MACA,IAAIb,MAAM,KAAKR,GAAG,EAAE;QAClBL,MAAM,CAACK,GAAG,CAAC;QACXT,OAAO,CAACiB,MAAM,EAAE,IAAI,CAAC;MACvB;;MAEA;MACAgB,IAAI,CAACxB,GAAG,EAAE,SAASyB,UAAUA,CAAA,EAAI;QAC/BvB,IAAI,CAACb,WAAW,CAAC,GAAG,EAAEkC,MAAM,CAAC,CAAC;MAChC,CAAC,CAAC;MACF;IACF;;IAEA;IACA,IAAIZ,MAAM,EAAE;MACV,IAAI;QACFP,KAAK,CAAC,aAAa,CAAC;QACpBO,MAAM,CAACX,GAAG,EAAEC,GAAG,EAAEqB,IAAI,EAAEZ,QAAQ,CAAC;MAClC,CAAC,CAAC,OAAOK,GAAG,EAAE;QACZb,IAAI,CAACb,WAAW,CAAC,GAAG,EAAE0B,GAAG,EAAE;UACzBO,IAAI,EAAEA,IAAI;UACVF,IAAI,EAAEL,GAAG,CAACK,IAAI,IAAI;QACpB,CAAC,CAAC,CAAC;QACH;MACF;IACF;;IAEA;IACA,IAAIM,GAAG,GAAGJ,IAAI;IACd,IAAI;MACFlB,KAAK,CAAC,YAAY,CAAC;MACnBsB,GAAG,GAAG,OAAOJ,IAAI,KAAK,QAAQ,IAAIZ,QAAQ,KAAK,IAAI,GAC/CjB,KAAK,CAACkC,MAAM,CAACL,IAAI,EAAEZ,QAAQ,CAAC,GAC5BY,IAAI;MACRtB,GAAG,CAACsB,IAAI,GAAGnB,KAAK,CAACuB,GAAG,CAAC;IACvB,CAAC,CAAC,OAAOX,GAAG,EAAE;MACZb,IAAI,CAACb,WAAW,CAAC,GAAG,EAAE0B,GAAG,EAAE;QACzBO,IAAI,EAAEI,GAAG;QACTN,IAAI,EAAEL,GAAG,CAACK,IAAI,IAAI;MACpB,CAAC,CAAC,CAAC;MACH;IACF;IAEAlB,IAAI,CAAC,CAAC;EACR,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASU,aAAaA,CAAEZ,GAAG,EAAEI,KAAK,EAAES,OAAO,EAAE;EAC3C,IAAIH,QAAQ,GAAG,CAACV,GAAG,CAAC4B,OAAO,CAAC,kBAAkB,CAAC,IAAI,UAAU,EAAET,WAAW,CAAC,CAAC;EAC5E,IAAIb,MAAM,GAAGN,GAAG,CAAC4B,OAAO,CAAC,gBAAgB,CAAC;EAC1C,IAAIpB,MAAM;EAEVJ,KAAK,CAAC,uBAAuB,EAAEM,QAAQ,CAAC;EAExC,IAAIG,OAAO,KAAK,KAAK,IAAIH,QAAQ,KAAK,UAAU,EAAE;IAChD,MAAMrB,WAAW,CAAC,GAAG,EAAE,8BAA8B,EAAE;MACrDqB,QAAQ,EAAEA,QAAQ;MAClBU,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;EAEA,QAAQV,QAAQ;IACd,KAAK,SAAS;MACZF,MAAM,GAAGZ,IAAI,CAACiC,aAAa,CAAC,CAAC;MAC7BzB,KAAK,CAAC,cAAc,CAAC;MACrBJ,GAAG,CAAC8B,IAAI,CAACtB,MAAM,CAAC;MAChB;IACF,KAAK,MAAM;MACTA,MAAM,GAAGZ,IAAI,CAACmC,YAAY,CAAC,CAAC;MAC5B3B,KAAK,CAAC,aAAa,CAAC;MACpBJ,GAAG,CAAC8B,IAAI,CAACtB,MAAM,CAAC;MAChB;IACF,KAAK,UAAU;MACbA,MAAM,GAAGR,GAAG;MACZQ,MAAM,CAACF,MAAM,GAAGA,MAAM;MACtB;IACF;MACE,MAAMjB,WAAW,CAAC,GAAG,EAAE,gCAAgC,GAAGqB,QAAQ,GAAG,GAAG,EAAE;QACxEA,QAAQ,EAAEA,QAAQ;QAClBU,IAAI,EAAE;MACR,CAAC,CAAC;EACN;EAEA,OAAOZ,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASgB,IAAIA,CAAExB,GAAG,EAAEgC,QAAQ,EAAE;EAC5B,IAAItC,UAAU,CAACuC,UAAU,CAACjC,GAAG,CAAC,EAAE;IAC9BgC,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC,MAAM;IACLtC,UAAU,CAACM,GAAG,EAAEgC,QAAQ,CAAC;IACzBhC,GAAG,CAACkC,MAAM,CAAC,CAAC;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}