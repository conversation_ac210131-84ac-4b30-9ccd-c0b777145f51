{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ActionnaireService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth/Actionnaire';\n  }\n  getAllActionnaires() {\n    return this.http.get(`${this.apiUrl}/All_Actionnaires`);\n  }\n  createActionnaire(actionnaire) {\n    const headers = new HttpHeaders().set('Content-Type', 'application/json');\n    return this.http.post('http://localhost:8000/api/v1/auth/Actionnaire/create', actionnaire, {\n      headers,\n      responseType: 'text' // This avoids JSON parse errors if backend returns plain text\n    });\n  }\n\n  static {\n    this.ɵfac = function ActionnaireService_Factory(t) {\n      return new (t || ActionnaireService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ActionnaireService,\n      factory: ActionnaireService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "ActionnaireService", "constructor", "http", "apiUrl", "getAllActionnaires", "get", "createActionnaire", "actionnaire", "headers", "set", "post", "responseType", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\Final\\frontend\\src\\app\\services\\actionnaire.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Actionnaire } from '../model/actionnaire.model'; // Update the path as needed\nimport { Observable } from 'rxjs';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ActionnaireService {\n  private apiUrl = 'http://localhost:8000/api/v1/auth/Actionnaire';\n\n  constructor(private http: HttpClient) {}\n\n  getAllActionnaires(): Observable<Actionnaire[]> {\n    return this.http.get<Actionnaire[]>(`${this.apiUrl}/All_Actionnaires`);\n  }\n\ncreateActionnaire(actionnaire: Partial<Actionnaire>) {\n  const headers = new HttpHeaders().set('Content-Type', 'application/json');\n\n  return this.http.post('http://localhost:8000/api/v1/auth/Actionnaire/create', actionnaire, {\n    headers,\n    responseType: 'text' as 'json' // This avoids JSON parse errors if backend returns plain text\n  });\n}\n\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;;;AAO9D,OAAM,MAAOC,kBAAkB;EAG7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,+CAA+C;EAEzB;EAEvCC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAgB,GAAG,IAAI,CAACF,MAAM,mBAAmB,CAAC;EACxE;EAEFG,iBAAiBA,CAACC,WAAiC;IACjD,MAAMC,OAAO,GAAG,IAAIT,WAAW,EAAE,CAACU,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAEzE,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAC,sDAAsD,EAAEH,WAAW,EAAE;MACzFC,OAAO;MACPG,YAAY,EAAE,MAAgB,CAAC;KAChC,CAAC;EACJ;;;;uBAhBaX,kBAAkB,EAAAY,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBf,kBAAkB;MAAAgB,OAAA,EAAlBhB,kBAAkB,CAAAiB,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}