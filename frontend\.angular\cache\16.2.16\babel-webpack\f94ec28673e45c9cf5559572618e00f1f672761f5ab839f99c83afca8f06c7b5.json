{"ast": null, "code": "import { Notification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function materialize() {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      subscriber.next(Notification.createNext(value));\n    }, () => {\n      subscriber.next(Notification.createComplete());\n      subscriber.complete();\n    }, err => {\n      subscriber.next(Notification.createError(err));\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["Notification", "operate", "createOperatorSubscriber", "materialize", "source", "subscriber", "subscribe", "value", "next", "createNext", "createComplete", "complete", "err", "createError"], "sources": ["C:/Users/<USER>/Desktop/Final - Copie/frontend/node_modules/rxjs/dist/esm/internal/operators/materialize.js"], "sourcesContent": ["import { Notification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function materialize() {\n    return operate((source, subscriber) => {\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            subscriber.next(Notification.createNext(value));\n        }, () => {\n            subscriber.next(Notification.createComplete());\n            subscriber.complete();\n        }, (err) => {\n            subscriber.next(Notification.createError(err));\n            subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC1B,OAAOF,OAAO,CAAC,CAACG,MAAM,EAAEC,UAAU,KAAK;IACnCD,MAAM,CAACE,SAAS,CAACJ,wBAAwB,CAACG,UAAU,EAAGE,KAAK,IAAK;MAC7DF,UAAU,CAACG,IAAI,CAACR,YAAY,CAACS,UAAU,CAACF,KAAK,CAAC,CAAC;IACnD,CAAC,EAAE,MAAM;MACLF,UAAU,CAACG,IAAI,CAACR,YAAY,CAACU,cAAc,CAAC,CAAC,CAAC;MAC9CL,UAAU,CAACM,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAGC,GAAG,IAAK;MACRP,UAAU,CAACG,IAAI,CAACR,YAAY,CAACa,WAAW,CAACD,GAAG,CAAC,CAAC;MAC9CP,UAAU,CAACM,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}