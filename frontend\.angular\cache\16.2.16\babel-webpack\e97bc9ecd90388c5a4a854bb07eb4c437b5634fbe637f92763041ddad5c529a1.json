{"ast": null, "code": "import { concatAll } from '../operators/concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function concat(...args) {\n  return concatAll()(from(args, popScheduler(args)));\n}", "map": {"version": 3, "names": ["concatAll", "popScheduler", "from", "concat", "args"], "sources": ["C:/Users/<USER>/Desktop/GRA/frontend/node_modules/rxjs/dist/esm/internal/observable/concat.js"], "sourcesContent": ["import { concatAll } from '../operators/concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from './from';\nexport function concat(...args) {\n    return concatAll()(from(args, popScheduler(args)));\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAO,SAASC,MAAMA,CAAC,GAAGC,IAAI,EAAE;EAC5B,OAAOJ,SAAS,CAAC,CAAC,CAACE,IAAI,CAACE,IAAI,EAAEH,YAAY,CAACG,IAAI,CAAC,CAAC,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}