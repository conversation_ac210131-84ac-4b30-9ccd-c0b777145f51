{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { AuthGuard } from './auth/auth.guard';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'signup',\n  component: SignupComponent\n}, {\n  path: 'transactions',\n  component: TransactionsComponent,\n  canActivate: [AuthGuard],\n  data: {\n    role: 'RESPONSABLE'\n  }\n}, {\n  path: 'actions',\n  component: ActionsComponent,\n  canActivate: [AuthGuard],\n  data: {\n    role: 'RESPONSABLE'\n  }\n}, {\n  path: 'actionnaires',\n  component: ActionnairesComponent,\n  canActivate: [AuthGuard],\n  data: {\n    role: 'RESPONSABLE'\n  }\n}, {\n  path: 'reports',\n  component: ReportsComponent,\n  canActivate: [AuthGuard],\n  data: {\n    role: 'RESPONSABLE'\n  }\n}, {\n  path: 'port',\n  component: PortefeuillesComponent,\n  canActivate: [AuthGuard],\n  data: {\n    role: 'RESPONSABLE'\n  }\n}, {\n  path: 'ResDash',\n  component: ResponDashboardComponent,\n  canActivate: [AuthGuard],\n  data: {\n    role: 'RESPONSABLE'\n  }\n}, {\n  path: 'not-authorized',\n  component: NotAuthorizedComponent\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "SignupComponent", "ResponDashboardComponent", "NotAuthorizedComponent", "<PERSON><PERSON><PERSON><PERSON>", "TransactionsComponent", "ActionsComponent", "ActionnairesComponent", "ReportsComponent", "PortefeuillesComponent", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "data", "role", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { AuthGuard } from './auth/auth.guard';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\n\nconst routes: Routes = [\n  { path: '', redirectTo: 'login', pathMatch: 'full' }, // ✅ Login is the default page\n  { path: 'login', component: LoginComponent },\n  { path: 'signup', component: SignupComponent },\n  { path: 'transactions', component: TransactionsComponent, canActivate: [AuthGuard], data: { role: 'RESPONSABLE' } },\n  { path: 'actions', component: ActionsComponent, canActivate: [AuthGuard], data: { role: 'RESPONSABLE' } },\n  { path: 'actionnaires', component: ActionnairesComponent, canActivate: [AuthGuard], data: { role: 'RESPONSABLE' } },\n  { path: 'reports', component: ReportsComponent, canActivate: [AuthGuard], data: { role: 'RESPONSABLE' } },\n  { path: 'port', component: PortefeuillesComponent, canActivate: [AuthGuard], data: { role: 'RESPONSABLE' } },\n\n\n\n  { \n    path: 'ResDash', \n    component: ResponDashboardComponent, \n    canActivate: [AuthGuard], \n    data: { role: 'RESPONSABLE' },\n  \n  },\n  { path: 'not-authorized', component: NotAuthorizedComponent }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,sBAAsB,QAAQ,yCAAyC;;;AAEhF,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE;AAAM,CAAE,EACpD;EAAEF,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEd;AAAc,CAAE,EAC5C;EAAEW,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAEb;AAAe,CAAE,EAC9C;EAAEU,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAET,qBAAqB;EAAEU,WAAW,EAAE,CAACX,SAAS,CAAC;EAAEY,IAAI,EAAE;IAAEC,IAAI,EAAE;EAAa;AAAE,CAAE,EACnH;EAAEN,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAER,gBAAgB;EAAES,WAAW,EAAE,CAACX,SAAS,CAAC;EAAEY,IAAI,EAAE;IAAEC,IAAI,EAAE;EAAa;AAAE,CAAE,EACzG;EAAEN,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAEP,qBAAqB;EAAEQ,WAAW,EAAE,CAACX,SAAS,CAAC;EAAEY,IAAI,EAAE;IAAEC,IAAI,EAAE;EAAa;AAAE,CAAE,EACnH;EAAEN,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEN,gBAAgB;EAAEO,WAAW,EAAE,CAACX,SAAS,CAAC;EAAEY,IAAI,EAAE;IAAEC,IAAI,EAAE;EAAa;AAAE,CAAE,EACzG;EAAEN,IAAI,EAAE,MAAM;EAAEG,SAAS,EAAEL,sBAAsB;EAAEM,WAAW,EAAE,CAACX,SAAS,CAAC;EAAEY,IAAI,EAAE;IAAEC,IAAI,EAAE;EAAa;AAAE,CAAE,EAI5G;EACEN,IAAI,EAAE,SAAS;EACfG,SAAS,EAAEZ,wBAAwB;EACnCa,WAAW,EAAE,CAACX,SAAS,CAAC;EACxBY,IAAI,EAAE;IAAEC,IAAI,EAAE;EAAa;CAE5B,EACD;EAAEN,IAAI,EAAE,gBAAgB;EAAEG,SAAS,EAAEX;AAAsB,CAAE,CAC9D;AAMD,OAAM,MAAOe,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBnB,YAAY,CAACoB,OAAO,CAACT,MAAM,CAAC,EAC5BX,YAAY;IAAA;EAAA;;;2EAEXmB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAtB,YAAA;IAAAuB,OAAA,GAFjBvB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}