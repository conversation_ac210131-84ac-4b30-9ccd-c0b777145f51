{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/groupe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nconst _c0 = function (a1) {\n  return [\"/edit-groupe\", a1];\n};\nfunction GroupsComponent_tr_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"a\", 52)(7, \"i\", 53);\n    i0.ɵɵtext(8, \"\\uE254\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"a\", 54);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_tr_62_Template_a_click_9_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const groupe_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(groupe_r1.idGroupe ? ctx_r2.deleteGroupe(groupe_r1.idGroupe, $event) : null);\n    });\n    i0.ɵɵelementStart(10, \"i\", 55);\n    i0.ɵɵtext(11, \"\\uE872\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const groupe_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r1.idGroupe || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r1.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c0, groupe_r1.idGroupe));\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, 0.25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(0, 0, 0, 0.1);\\n    border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n    border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n    border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n    padding: 8px 16px;\\n    font-size: 14px;\\n    border: none;\\n    cursor: pointer;\\n    border-radius: 4px;\\n    font-weight: bold;\\n    transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n    font-family: 'Poppins', sans-serif;\\n    margin: 0;\\n    padding: 0;\\n    min-height: 100vh;\\n    background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n    background-size: 400% 400%;\\n    animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n    color: #fff;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n    0% {\\n        background-position: 0% 50%;\\n    }\\n\\n    50% {\\n        background-position: 100% 50%;\\n    }\\n\\n    100% {\\n        background-position: 0% 50%;\\n    }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 2rem;\\n    margin-top: 20px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n    background: rgba(255, 255, 255, 0.06);\\n    border-radius: 15px;\\n    padding: 1.5rem;\\n    -webkit-backdrop-filter: blur(12px);\\n            backdrop-filter: blur(12px);\\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n    color: #fff;\\n    border-radius: 15px;\\n    overflow: hidden;\\n    border-collapse: separate;\\n    border-spacing: 0 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.15);\\n    color: #ffffff;\\n    font-weight: 600;\\n    text-align: center;\\n    border: none;\\n    padding: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n    background-color: rgba(255, 255, 255, 0.1);\\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\\n    text-align: center;\\n    border-radius: 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.01);\\n    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    vertical-align: middle;\\n    padding: 0.9rem;\\n    font-size: 0.95rem;\\n}\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n    background-color: #28a745;\\n    color: white;\\n    border: none;\\n    padding: 12px 25px;\\n    border-radius: 30px;\\n    text-transform: uppercase;\\n    font-weight: bold;\\n    transition: background 0.3s, transform 0.2s;\\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n    background-color: #218838;\\n    transform: translateY(-2px);\\n}\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n    color: #299216;\\n    cursor: pointer;\\n    font-size: 20px;\\n    margin: 0 10px;\\n    transition: color 0.3s;\\n}\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n    color: #d22d2d;\\n    cursor: pointer;\\n    font-size: 20px;\\n    transition: color 0.3s;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n    color: #ffffff;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n    background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n    color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n    text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n    background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n    background-color: #fff;\\n    padding: 1.5rem;\\n    color: #333;\\n    font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n    background-color: #f1f1f1;\\n    padding: 1rem;\\n    border-top: 1px solid #ddd;\\n    display: flex;\\n    justify-content: center; \\n\\n    gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    padding: 0.5rem 1rem;\\n    font-size: 1rem;\\n    border-radius: 0.3rem;\\n    transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n    background-color: #6c757d;\\n    color: #fff;\\n    border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n    background-color: #5a6268;\\n    transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n    background-color: #007bff;\\n    color: #fff;\\n    border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n    transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column; \\n\\n    align-items: flex-start; \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n    margin-top: 10px;\\n    background-color: rgba(255, 255, 255, 0.1); \\n\\n    border: 1px solid rgba(255, 255, 255, 0.3); \\n\\n    border-radius: 30px; \\n\\n    color: #fff; \\n\\n    padding: 10px 20px; \\n\\n    font-size: 1rem; \\n\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); \\n\\n    width: 100%; \\n\\n    max-width: 400px; \\n\\n    transition: all 0.3s ease; \\n\\n}\\n\\n.search-bar[_ngcontent-%COMP%]:focus {\\n    outline: none; \\n\\n    border-color: #007bff; \\n\\n    box-shadow: 0 0 8px rgba(0, 123, 255, 0.6); \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%]::placeholder {\\n    color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class GroupsComponent {\n  constructor(groupeService, router) {\n    this.groupeService = groupeService;\n    this.router = router;\n    this.groupes = []; // List of groups\n    this.filteredGroupes = []; // Filtered groups based on search\n    this.newGroupe = {\n      nomGroupe: ''\n    }; // New group model\n    this.editGroupe = null; // Group being edited\n    this.searchQuery = ''; // Search query for filtering\n  }\n\n  ngOnInit() {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n  // Called after the view is initialized (ideal for initializing Feather icons)\n  ngAfterViewInit() {\n    feather.replace(); // Initialize Feather icons after the view is ready\n  }\n  // Load all groups\n  loadGroupes() {\n    this.groupeService.getAllGroupes().subscribe(data => {\n      this.groupes = data;\n      this.filteredGroupes = data; // Initially, show all groups\n    }, err => {\n      console.error('Error loading groups', err);\n    });\n  }\n  // Filter groups based on the search query\n  filterGroups() {\n    if (!this.searchQuery) {\n      this.filteredGroupes = this.groupes; // If no search, show all groups\n    } else {\n      this.filteredGroupes = this.groupes.filter(groupe => groupe.nomGroupe.toLowerCase().includes(this.searchQuery.toLowerCase()));\n    }\n  }\n  // Add a new group\n  addGroupe() {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(() => {\n        this.newGroupe = {\n          nomGroupe: ''\n        }; // Reset input\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error adding group', err);\n      });\n    }\n  }\n  // Set group to edit mode\n  setEditGroupe(groupe) {\n    this.editGroupe = {\n      ...groupe\n    }; // Clone object\n  }\n  // Update a group\n  updateGroupe() {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(() => {\n        this.editGroupe = null; // Reset edit mode\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error updating group', err);\n      });\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n  // Delete a group\n  deleteGroupe(id, event) {\n    event.preventDefault(); // 🔥 prevents <a> tag default behavior\n    this.groupeService.deleteGroupe(id).subscribe({\n      next: () => {\n        console.log('Group deleted successfully');\n        this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n        this.filterGroups(); // Reapply filter after deletion\n      },\n\n      error: err => {\n        console.error('Error deleting group', err);\n      }\n    });\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function GroupsComponent_Factory(t) {\n      return new (t || GroupsComponent)(i0.ɵɵdirectiveInject(i1.GroupeService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupsComponent,\n      selectors: [[\"app-groups\"]],\n      decls: 80,\n      vars: 3,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/css/bootstrap.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"href\", \"https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\", \"rel\", \"stylesheet\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-light\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"text-center\", \"mt-4\", \"mb-4\"], [1, \"mb-3\", \"d-flex\", \"justify-content-between\"], [\"type\", \"button\", \"data-toggle\", \"modal\", \"data-target\", \"#addGroupeModal\", 1, \"btn\", \"btn-custom\"], [1, \"material-icons\", \"align-middle\", 2, \"font-size\", \"20px\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"w-50\", \"search-bar\", 3, \"ngModel\", \"ngModelChange\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"addGroupeModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"addGroupeModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"addGroupeModalLabel\", 1, \"modal-title\"], [1, \"modal-body\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"nomGroupe\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [1, \"edit\", 3, \"routerLink\"], [\"data-toggle\", \"tooltip\", \"title\", \"Modifier\", 1, \"material-icons\", \"edit-icon\"], [1, \"delete\", 3, \"click\"], [\"data-toggle\", \"tooltip\", \"title\", \"Supprimer\", 1, \"material-icons\", \"delete-icon\"]],\n      template: function GroupsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"link\", 6)(8, \"link\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"link\", 10)(12, \"link\", 11);\n          i0.ɵɵelementStart(13, \"title\");\n          i0.ɵɵtext(14, \"Gestion des Groupes\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"body\")(16, \"nav\", 12)(17, \"a\", 13);\n          i0.ɵɵtext(18, \"GTI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"ul\", 14)(20, \"li\", 15)(21, \"a\", 16);\n          i0.ɵɵlistener(\"click\", function GroupsComponent_Template_a_click_21_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(22, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 17)(24, \"div\", 18)(25, \"nav\", 19)(26, \"div\", 20)(27, \"ul\", 21)(28, \"li\", 22)(29, \"a\", 23);\n          i0.ɵɵelement(30, \"span\", 24);\n          i0.ɵɵtext(31, \" Dashboard \");\n          i0.ɵɵelementStart(32, \"span\", 25);\n          i0.ɵɵtext(33, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"li\", 22)(35, \"a\", 26);\n          i0.ɵɵelement(36, \"span\", 27);\n          i0.ɵɵtext(37, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"li\", 22)(39, \"a\", 28);\n          i0.ɵɵelement(40, \"span\", 29);\n          i0.ɵɵtext(41, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(42, \"main\", 30)(43, \"h2\", 31);\n          i0.ɵɵtext(44, \"Gestion des groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 32)(46, \"button\", 33)(47, \"i\", 34);\n          i0.ɵɵtext(48, \"add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" Ajouter un groupe \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"input\", 35);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_50_listener($event) {\n            return ctx.searchQuery = $event;\n          })(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_50_listener() {\n            return ctx.filterGroups();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 36)(52, \"table\", 37)(53, \"thead\")(54, \"tr\")(55, \"th\");\n          i0.ɵɵtext(56, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"th\");\n          i0.ɵɵtext(58, \"Nom \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\");\n          i0.ɵɵtext(60, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(61, \"tbody\");\n          i0.ɵɵtemplate(62, GroupsComponent_tr_62_Template, 12, 5, \"tr\", 38);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(63, \"div\", 39)(64, \"div\", 40)(65, \"div\", 41)(66, \"div\", 42)(67, \"h5\", 43);\n          i0.ɵɵtext(68, \"Ajouter un groupe\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"div\", 44)(70, \"form\", 45);\n          i0.ɵɵlistener(\"ngSubmit\", function GroupsComponent_Template_form_ngSubmit_70_listener() {\n            return ctx.addGroupe();\n          });\n          i0.ɵɵelementStart(71, \"div\", 46)(72, \"label\", 47);\n          i0.ɵɵtext(73, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"input\", 48);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_74_listener($event) {\n            return ctx.newGroupe.nomGroupe = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 49)(76, \"button\", 50);\n          i0.ɵɵtext(77, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"button\", 51);\n          i0.ɵɵtext(79, \"Ajouter\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(50);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredGroupes);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngModel\", ctx.newGroupe.nomGroupe);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm, i2.RouterLink],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GroupsComponent_tr_62_Template_a_click_9_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r3", "groupe_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "idGroupe", "deleteGroupe", "ɵɵadvance", "ɵɵtextInterpolate", "nomGroupe", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "GroupsComponent", "constructor", "groupeService", "router", "groupes", "filteredGroupes", "newGroupe", "editGroupe", "searchQuery", "ngOnInit", "loadGroupes", "ngAfterViewInit", "replace", "getAllGroupes", "subscribe", "data", "err", "console", "error", "filterGroups", "filter", "groupe", "toLowerCase", "includes", "addGroupe", "trim", "setEditGroupe", "updateGroupe", "id", "event", "preventDefault", "next", "log", "logout", "localStorage", "removeItem", "getItem", "navigate", "ɵɵdirectiveInject", "i1", "GroupeService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "GroupsComponent_Template", "rf", "ctx", "ɵɵelement", "GroupsComponent_Template_a_click_21_listener", "GroupsComponent_Template_input_ngModelChange_50_listener", "ɵɵtemplate", "GroupsComponent_tr_62_Template", "GroupsComponent_Template_form_ngSubmit_70_listener", "GroupsComponent_Template_input_ngModelChange_74_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\groups\\groups.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\groups\\groups.component.html"], "sourcesContent": ["import { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Component, OnInit, AfterViewInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\n\n@Component({\n  selector: 'app-groups',\n  templateUrl: './groups.component.html',\n  styleUrls: ['./groups.component.css']\n})\nexport class GroupsComponent implements OnInit, AfterViewInit {\n  groupes: Groupe[] = []; // List of groups\n  filteredGroupes: Groupe[] = []; // Filtered groups based on search\n  newGroupe: Groupe = { nomGroupe: '' }; // New group model\n  editGroupe: Groupe | null = null; // Group being edited\n  searchQuery: string = ''; // Search query for filtering\n\n  constructor(private groupeService: GroupeService, private router: Router) {}\n\n  ngOnInit(): void {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n\n  // Called after the view is initialized (ideal for initializing Feather icons)\n  ngAfterViewInit(): void {\n    feather.replace(); // Initialize Feather icons after the view is ready\n  }\n\n  // Load all groups\n  loadGroupes(): void {\n    this.groupeService.getAllGroupes().subscribe(\n      (data) => {\n        this.groupes = data;\n        this.filteredGroupes = data; // Initially, show all groups\n      },\n      (err) => {\n        console.error('Error loading groups', err);\n      }\n    );\n  }\n\n  // Filter groups based on the search query\n  filterGroups(): void {\n    if (!this.searchQuery) {\n      this.filteredGroupes = this.groupes; // If no search, show all groups\n    } else {\n      this.filteredGroupes = this.groupes.filter(groupe =>\n        groupe.nomGroupe.toLowerCase().includes(this.searchQuery.toLowerCase())\n      );\n    }\n  }\n\n  // Add a new group\n  addGroupe(): void {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(\n        () => {\n          this.newGroupe = { nomGroupe: '' }; // Reset input\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error adding group', err);\n        }\n      );\n    }\n  }\n\n  // Set group to edit mode\n  setEditGroupe(groupe: Groupe): void {\n    this.editGroupe = { ...groupe }; // Clone object\n  }\n\n  // Update a group\n  updateGroupe(): void {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(\n        () => {\n          this.editGroupe = null; // Reset edit mode\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error updating group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n\n  // Delete a group\n  deleteGroupe(id: number, event: Event): void {\n    event.preventDefault(); // 🔥 prevents <a> tag default behavior\n  \n    this.groupeService.deleteGroupe(id).subscribe({\n      next: () => {\n        console.log('Group deleted successfully');\n        this.groupes = this.groupes.filter(groupe => groupe.idGroupe !== id);\n        this.filterGroups(); // Reapply filter after deletion\n      },\n      error: (err) => {\n        console.error('Error deleting group', err);\n      }\n    });\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/css/bootstrap.min.css\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n    <link href=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css\" rel=\"stylesheet\">\n    <link href=\"./groups.component.css\" rel=\"stylesheet\">\n    <title>Gestion des Groupes</title>\n  </head>\n\n  <body>\n    <!-- Navbar -->\n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"adminDash\">GTI</a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block bg-light sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"users\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <h2 class=\"text-center mt-4 mb-4\">Gestion des groupes</h2>\n\n<!-- Search Bar and Add Button -->\n<div class=\"mb-3 d-flex justify-content-between\">\n  <button type=\"button\" class=\"btn btn-custom\" data-toggle=\"modal\" data-target=\"#addGroupeModal\">\n    <i class=\"material-icons align-middle\" style=\"font-size: 20px;\">add</i>\n    Ajouter un groupe\n  </button>\n\n  <!-- Add class 'search-bar' to search input -->\n  <input class=\"form-control w-50 search-bar\" \n         type=\"text\" \n         placeholder=\"Search\" \n         aria-label=\"Search\" \n         [(ngModel)]=\"searchQuery\" \n         (ngModelChange)=\"filterGroups()\">\n</div>\n\n          <!-- Table -->\n          <div class=\"table-responsive\">\n            <table class=\"table table-hover\">\n              <thead>\n                <tr>\n                  <th>ID</th>\n                  <th>Nom </th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let groupe of filteredGroupes\">\n                  <td>{{ groupe.idGroupe || 'N/A' }}</td>\n                  <td>{{ groupe.nomGroupe }}</td>\n                  <td>\n                    <a [routerLink]=\"['/edit-groupe', groupe.idGroupe]\" class=\"edit\">\n                      <i class=\"material-icons edit-icon\" data-toggle=\"tooltip\" title=\"Modifier\">&#xE254;</i>\n                    </a>\n                    <a  class=\"delete\" (click)=\"groupe.idGroupe ? deleteGroupe(groupe.idGroupe, $event) : null\">\n                      <i class=\"material-icons delete-icon\" data-toggle=\"tooltip\" title=\"Supprimer\">&#xE872;</i>\n                    </a>\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </main>\n      </div>\n    </div>\n\n    <!-- Modal -->\n    <div class=\"modal fade\" id=\"addGroupeModal\" tabindex=\"-1\" aria-labelledby=\"addGroupeModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"addGroupeModalLabel\">Ajouter un groupe</h5>\n          </div>\n          <div class=\"modal-body\">\n            <form (ngSubmit)=\"addGroupe()\">\n              <div class=\"form-group\">\n                <label for=\"nomGroupe\">Nom du groupe</label>\n                <input type=\"text\" id=\"nomGroupe\" [(ngModel)]=\"newGroupe.nomGroupe\" name=\"nomGroupe\" class=\"form-control\" required>\n              </div>\n              <div class=\"modal-footer\">\n                <button type=\"button\" class=\"btn btn-secondary\" data-dismiss=\"modal\">Annuler</button>\n                <button type=\"submit\" class=\"btn btn-primary\">Ajouter</button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/@popperjs/core@2.10.2/dist/umd/popper.min.js\"></script>\n    <script src=\"https://maxcdn.bootstrapcdn.com/bootstrap/4.6.2/js/bootstrap.min.js\"></script>\n  </body>\n</html>\n"], "mappings": "AAIA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;;;;;;;;;ICmFxBC,EAAA,CAAAC,cAAA,SAA2C;IACrCD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAE2ED,EAAA,CAAAE,MAAA,aAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEzFH,EAAA,CAAAC,cAAA,YAA4F;IAAzED,EAAA,CAAAI,UAAA,mBAAAC,kDAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAJ,SAAA,CAAAK,QAAA,GAAkBH,MAAA,CAAAI,YAAA,CAAAN,SAAA,CAAAK,QAAA,EAAAT,MAAA,CAAqC,GAAG,IAAI;IAAA,EAAC;IACzFN,EAAA,CAAAC,cAAA,aAA8E;IAAAD,EAAA,CAAAE,MAAA,cAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAP1FH,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAkB,iBAAA,CAAAR,SAAA,CAAAK,QAAA,UAA8B;IAC9Bf,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,iBAAA,CAAAR,SAAA,CAAAS,SAAA,CAAsB;IAErBnB,EAAA,CAAAiB,SAAA,GAAgD;IAAhDjB,EAAA,CAAAoB,UAAA,eAAApB,EAAA,CAAAqB,eAAA,IAAAC,GAAA,EAAAZ,SAAA,CAAAK,QAAA,EAAgD;;;;ADhFvE,OAAM,MAAOQ,eAAe;EAO1BC,YAAoBC,aAA4B,EAAUC,MAAc;IAApD,KAAAD,aAAa,GAAbA,aAAa;IAAyB,KAAAC,MAAM,GAANA,MAAM;IANhE,KAAAC,OAAO,GAAa,EAAE,CAAC,CAAC;IACxB,KAAAC,eAAe,GAAa,EAAE,CAAC,CAAC;IAChC,KAAAC,SAAS,GAAW;MAAEV,SAAS,EAAE;IAAE,CAAE,CAAC,CAAC;IACvC,KAAAW,UAAU,GAAkB,IAAI,CAAC,CAAC;IAClC,KAAAC,WAAW,GAAW,EAAE,CAAC,CAAC;EAEiD;;EAE3EC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;EACtB;EAEA;EACAC,eAAeA,CAAA;IACbnC,OAAO,CAACoC,OAAO,EAAE,CAAC,CAAC;EACrB;EAEA;EACAF,WAAWA,CAAA;IACT,IAAI,CAACR,aAAa,CAACW,aAAa,EAAE,CAACC,SAAS,CACzCC,IAAI,IAAI;MACP,IAAI,CAACX,OAAO,GAAGW,IAAI;MACnB,IAAI,CAACV,eAAe,GAAGU,IAAI,CAAC,CAAC;IAC/B,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;IAC5C,CAAC,CACF;EACH;EAEA;EACAG,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE;MACrB,IAAI,CAACH,eAAe,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC;KACtC,MAAM;MACL,IAAI,CAACC,eAAe,GAAG,IAAI,CAACD,OAAO,CAACgB,MAAM,CAACC,MAAM,IAC/CA,MAAM,CAACzB,SAAS,CAAC0B,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACf,WAAW,CAACc,WAAW,EAAE,CAAC,CACxE;;EAEL;EAEA;EACAE,SAASA,CAAA;IACP,IAAI,IAAI,CAAClB,SAAS,CAACV,SAAS,CAAC6B,IAAI,EAAE,EAAE;MACnC,IAAI,CAACvB,aAAa,CAACsB,SAAS,CAAC,IAAI,CAAClB,SAAS,CAAC,CAACQ,SAAS,CACpD,MAAK;QACH,IAAI,CAACR,SAAS,GAAG;UAAEV,SAAS,EAAE;QAAE,CAAE,CAAC,CAAC;QACpC,IAAI,CAACc,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAM,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;MAC1C,CAAC,CACF;;EAEL;EAEA;EACAU,aAAaA,CAACL,MAAc;IAC1B,IAAI,CAACd,UAAU,GAAG;MAAE,GAAGc;IAAM,CAAE,CAAC,CAAC;EACnC;EAEA;EACAM,YAAYA,CAAA;IACV,IAAI,IAAI,CAACpB,UAAU,IAAI,IAAI,CAACA,UAAU,CAACf,QAAQ,EAAE;MAC/C,IAAI,CAACU,aAAa,CAACyB,YAAY,CAAC,IAAI,CAACpB,UAAU,CAACf,QAAQ,EAAE,IAAI,CAACe,UAAU,CAAC,CAACO,SAAS,CAClF,MAAK;QACH,IAAI,CAACP,UAAU,GAAG,IAAI,CAAC,CAAC;QACxB,IAAI,CAACG,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAM,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;;EAEtD;EAEA;EACAzB,YAAYA,CAACmC,EAAU,EAAEC,KAAY;IACnCA,KAAK,CAACC,cAAc,EAAE,CAAC,CAAC;IAExB,IAAI,CAAC5B,aAAa,CAACT,YAAY,CAACmC,EAAE,CAAC,CAACd,SAAS,CAAC;MAC5CiB,IAAI,EAAEA,CAAA,KAAK;QACTd,OAAO,CAACe,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAAC5B,OAAO,GAAG,IAAI,CAACA,OAAO,CAACgB,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC7B,QAAQ,KAAKoC,EAAE,CAAC;QACpE,IAAI,CAACT,YAAY,EAAE,CAAC,CAAC;MACvB,CAAC;;MACDD,KAAK,EAAGF,GAAG,IAAI;QACbC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C;KACD,CAAC;EACJ;EACAiB,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChClB,OAAO,CAACe,GAAG,CAACE,YAAY,CAACE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACjC,MAAM,CAACkC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBApGWrC,eAAe,EAAAvB,EAAA,CAAA6D,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA/D,EAAA,CAAA6D,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAf1C,eAAe;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5BxE,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAA0E,SAAA,cAAsB;UAatB1E,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGpCH,EAAA,CAAAC,cAAA,YAAM;UAG8DD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAI,UAAA,mBAAAuE,6CAAA;YAAA,OAASF,GAAA,CAAAjB,MAAA,EAAQ;UAAA,EAAC;UAACxD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKzDH,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAA0E,SAAA,gBAAiC;UACjC1E,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGpDH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAA0E,SAAA,gBAAiC;UACjC1E,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAA0E,SAAA,gBAAkC;UAClC1E,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMZH,EAAA,CAAAC,cAAA,gBAAkE;UAC9BD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpEH,EAAA,CAAAC,cAAA,eAAiD;UAEmBD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAC,cAAA,iBAKwC;UADjCD,EAAA,CAAAI,UAAA,2BAAAwE,yDAAAtE,MAAA;YAAA,OAAAmE,GAAA,CAAA1C,WAAA,GAAAzB,MAAA;UAAA,EAAyB,2BAAAsE,yDAAA;YAAA,OACRH,GAAA,CAAA/B,YAAA,EAAc;UAAA,EADN;UAJhC1C,EAAA,CAAAG,YAAA,EAKwC;UAIhCH,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACXH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACbH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpBH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA6E,UAAA,KAAAC,8BAAA,kBAWK;UACP9E,EAAA,CAAAG,YAAA,EAAQ;UAQlBH,EAAA,CAAAC,cAAA,eAAmH;UAI1DD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEzEH,EAAA,CAAAC,cAAA,eAAwB;UAChBD,EAAA,CAAAI,UAAA,sBAAA2E,mDAAA;YAAA,OAAYN,GAAA,CAAA1B,SAAA,EAAW;UAAA,EAAC;UAC5B/C,EAAA,CAAAC,cAAA,eAAwB;UACCD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,iBAAmH;UAAjFD,EAAA,CAAAI,UAAA,2BAAA4E,yDAAA1E,MAAA;YAAA,OAAAmE,GAAA,CAAA5C,SAAA,CAAAV,SAAA,GAAAb,MAAA;UAAA,EAAiC;UAAnEN,EAAA,CAAAG,YAAA,EAAmH;UAErHH,EAAA,CAAAC,cAAA,eAA0B;UAC6CD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrFH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UAjDrEH,EAAA,CAAAiB,SAAA,IAAyB;UAAzBjB,EAAA,CAAAoB,UAAA,YAAAqD,GAAA,CAAA1C,WAAA,CAAyB;UAeK/B,EAAA,CAAAiB,SAAA,IAAkB;UAAlBjB,EAAA,CAAAoB,UAAA,YAAAqD,GAAA,CAAA7C,eAAA,CAAkB;UA8BP5B,EAAA,CAAAiB,SAAA,IAAiC;UAAjCjB,EAAA,CAAAoB,UAAA,YAAAqD,GAAA,CAAA5C,SAAA,CAAAV,SAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}