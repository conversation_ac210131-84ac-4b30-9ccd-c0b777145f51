{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, map, throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ResponsableService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth/Users_Responsables'; // Your API URL\n  }\n  // Helper function to get the token and set headers\n  getAuthHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.error('No token found!');\n      throw new Error('No token found');\n    }\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json',\n      'Accept': 'application/json'\n    });\n  }\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId) {\n    return this.http.get(`${this.apiUrl}/${responsableId}`, {\n      headers: this.getAuthHeaders() // Add auth headers\n    }).pipe(catchError(error => this.handleError(error)) // Handle any error\n    );\n  }\n  // Get all responsibles\n  getResponsables() {\n    return this.http.get(this.apiUrl, {\n      headers: this.getAuthHeaders() // Add auth headers\n    }).pipe(catchError(error => this.handleError(error)) // Handle any error\n    );\n  }\n  // Delete a responsable\n  deleteResponsable(id) {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, {\n      headers: this.getAuthHeaders(),\n      responseType: 'text'\n    }).pipe(catchError(error => this.handleError(error)) // Handle any error\n    );\n  }\n  // Update a responsable\n  updateResponsable(id, responsable) {\n    return this.http.put(`${this.apiUrl}/edit/${id}`, responsable, {\n      headers: this.getAuthHeaders() // Add auth headers\n    }).pipe(catchError(error => this.handleError(error)) // Handle any error\n    );\n  }\n  // Add a responsable\n  addResponsable(responsable) {\n    return this.http.post('http://localhost:8000/api/v1/auth/register', responsable, {\n      headers: this.getAuthHeaders() // Add auth headers\n    }).pipe(catchError(error => this.handleError(error)) // Handle any error\n    );\n  }\n  // Error handling method\n  handleError(error) {\n    console.error('An error occurred:', error);\n    // Optionally, show a user-friendly error message here\n    return throwError(() => new Error(error.message || 'Something went wrong'));\n  }\n  // Inside ResponsableService\n  register(responsable) {\n    return this.http.post('http://localhost:8000/api/v1/auth/register', responsable, {\n      headers: this.getAuthHeaders() // Add auth headers\n    }).pipe(catchError(error => this.handleError(error)) // Handle any error\n    );\n  }\n  // Add this method in ResponsableService\n  // ResponsableService\n  // ResponsableService\n  getGroups() {\n    return this.http.get('http://localhost:8000/api/v1/auth/Groupes/ALLGroupes', {\n      headers: this.getAuthHeaders()\n    }).pipe(map(response => {\n      // Map the response to the required format, extracting the idGroupe and nomGroupe\n      return response.map(group => ({\n        idGroupe: group.idGroupe,\n        nomGroupe: group.nomGroupe\n      }));\n    }), catchError(error => this.handleError(error)));\n  }\n  static {\n    this.ɵfac = function ResponsableService_Factory(t) {\n      return new (t || ResponsableService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ResponsableService,\n      factory: ResponsableService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "catchError", "map", "throwError", "ResponsableService", "constructor", "http", "apiUrl", "getAuthHeaders", "token", "localStorage", "getItem", "console", "error", "Error", "getResponsableById", "responsableId", "get", "headers", "pipe", "handleError", "getResponsables", "deleteResponsable", "id", "delete", "responseType", "updateResponsable", "responsable", "put", "addResponsable", "post", "message", "register", "getGroups", "response", "group", "idGroupe", "nomGroupe", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\responsable.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { catchError, map, Observable, throwError } from 'rxjs';\nimport { User } from './model/user.model'; // Import the User model\nimport { Groupe } from './model/groupe.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ResponsableService {\n  private apiUrl = 'http://localhost:8000/api/v1/auth/Users_Responsables'; // Your API URL\n\n  constructor(private http: HttpClient) {}\n\n  // Helper function to get the token and set headers\n  private getAuthHeaders(): HttpHeaders {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      console.error('No token found!');\n      throw new Error('No token found');\n    }\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n    });\n  }\n\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId: number): Observable<User> {\n    return this.http.get<User>(`${this.apiUrl}/${responsableId}`, {\n      headers: this.getAuthHeaders(), // Add auth headers\n    }).pipe(\n      catchError((error) => this.handleError(error)) // Handle any error\n    );\n  }\n\n  // Get all responsibles\n  getResponsables(): Observable<User[]> {\n    return this.http.get<User[]>(this.apiUrl, {\n      headers: this.getAuthHeaders(), // Add auth headers\n    }).pipe(\n      catchError((error) => this.handleError(error)) // Handle any error\n    );\n  }\n\n  // Delete a responsable\n  deleteResponsable(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, {\n      headers: this.getAuthHeaders(), // Add auth headers\n      responseType: 'text',\n    }).pipe(\n      catchError((error) => this.handleError(error)) // Handle any error\n    );\n  }\n\n  // Update a responsable\n  updateResponsable(id: number, responsable: User): Observable<any> {\n    return this.http.put(`${this.apiUrl}/edit/${id}`, responsable, {\n      headers: this.getAuthHeaders(), // Add auth headers\n    }).pipe(\n      catchError((error) => this.handleError(error)) // Handle any error\n    );\n  }\n\n  // Add a responsable\n  addResponsable(responsable: User): Observable<User> {\n    return this.http.post<User>('http://localhost:8000/api/v1/auth/register', responsable, {\n      headers: this.getAuthHeaders(), // Add auth headers\n    }).pipe(\n      catchError((error) => this.handleError(error)) // Handle any error\n    );\n  }\n\n  // Error handling method\n  private handleError(error: any) {\n    console.error('An error occurred:', error);\n    // Optionally, show a user-friendly error message here\n    return throwError(() => new Error(error.message || 'Something went wrong'));\n  }\n  // Inside ResponsableService\nregister(responsable: User): Observable<any> {\n  return this.http.post<any>('http://localhost:8000/api/v1/auth/register', responsable, {\n    headers: this.getAuthHeaders(), // Add auth headers\n  }).pipe(\n    catchError((error) => this.handleError(error)) // Handle any error\n  );\n}\n\n// Add this method in ResponsableService\n// ResponsableService\n// ResponsableService\ngetGroups(): Observable<Groupe[]> {\n  return this.http.get<any>('http://localhost:8000/api/v1/auth/Groupes/ALLGroupes', {\n    headers: this.getAuthHeaders(),\n  }).pipe(\n    map(response => {\n      // Map the response to the required format, extracting the idGroupe and nomGroupe\n      return response.map((group: { idGroupe: any; nomGroupe: any; }) => ({\n        idGroupe: group.idGroupe,\n        nomGroupe: group.nomGroupe,\n      }));\n    }),\n    catchError((error) => this.handleError(error))\n  );\n}\n\n\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAEC,GAAG,EAAcC,UAAU,QAAQ,MAAM;;;AAO9D,OAAM,MAAOC,kBAAkB;EAG7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,sDAAsD,CAAC,CAAC;EAElC;EAEvC;EACQC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,EAAE;MACVG,OAAO,CAACC,KAAK,CAAC,iBAAiB,CAAC;MAChC,MAAM,IAAIC,KAAK,CAAC,gBAAgB,CAAC;;IAEnC,OAAO,IAAId,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUS,KAAK,EAAE;MAClC,cAAc,EAAE,kBAAkB;MAClC,QAAQ,EAAE;KACX,CAAC;EACJ;EAEA;EACAM,kBAAkBA,CAACC,aAAqB;IACtC,OAAO,IAAI,CAACV,IAAI,CAACW,GAAG,CAAO,GAAG,IAAI,CAACV,MAAM,IAAIS,aAAa,EAAE,EAAE;MAC5DE,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE,CAAE;KACjC,CAAC,CAACW,IAAI,CACLlB,UAAU,CAAEY,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC;KAChD;EACH;EAEA;EACAQ,eAAeA,CAAA;IACb,OAAO,IAAI,CAACf,IAAI,CAACW,GAAG,CAAS,IAAI,CAACV,MAAM,EAAE;MACxCW,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE,CAAE;KACjC,CAAC,CAACW,IAAI,CACLlB,UAAU,CAAEY,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC;KAChD;EACH;EAEA;EACAS,iBAAiBA,CAACC,EAAU;IAC1B,OAAO,IAAI,CAACjB,IAAI,CAACkB,MAAM,CAAC,GAAG,IAAI,CAACjB,MAAM,WAAWgB,EAAE,EAAE,EAAE;MACrDL,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE;MAC9BiB,YAAY,EAAE;KACf,CAAC,CAACN,IAAI,CACLlB,UAAU,CAAEY,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC;KAChD;EACH;EAEA;EACAa,iBAAiBA,CAACH,EAAU,EAAEI,WAAiB;IAC7C,OAAO,IAAI,CAACrB,IAAI,CAACsB,GAAG,CAAC,GAAG,IAAI,CAACrB,MAAM,SAASgB,EAAE,EAAE,EAAEI,WAAW,EAAE;MAC7DT,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE,CAAE;KACjC,CAAC,CAACW,IAAI,CACLlB,UAAU,CAAEY,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC;KAChD;EACH;EAEA;EACAgB,cAAcA,CAACF,WAAiB;IAC9B,OAAO,IAAI,CAACrB,IAAI,CAACwB,IAAI,CAAO,4CAA4C,EAAEH,WAAW,EAAE;MACrFT,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE,CAAE;KACjC,CAAC,CAACW,IAAI,CACLlB,UAAU,CAAEY,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC;KAChD;EACH;EAEA;EACQO,WAAWA,CAACP,KAAU;IAC5BD,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC1C;IACA,OAAOV,UAAU,CAAC,MAAM,IAAIW,KAAK,CAACD,KAAK,CAACkB,OAAO,IAAI,sBAAsB,CAAC,CAAC;EAC7E;EACA;EACFC,QAAQA,CAACL,WAAiB;IACxB,OAAO,IAAI,CAACrB,IAAI,CAACwB,IAAI,CAAM,4CAA4C,EAAEH,WAAW,EAAE;MACpFT,OAAO,EAAE,IAAI,CAACV,cAAc,EAAE,CAAE;KACjC,CAAC,CAACW,IAAI,CACLlB,UAAU,CAAEY,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAAC;KAChD;EACH;EAEA;EACA;EACA;EACAoB,SAASA,CAAA;IACP,OAAO,IAAI,CAAC3B,IAAI,CAACW,GAAG,CAAM,sDAAsD,EAAE;MAChFC,OAAO,EAAE,IAAI,CAACV,cAAc;KAC7B,CAAC,CAACW,IAAI,CACLjB,GAAG,CAACgC,QAAQ,IAAG;MACb;MACA,OAAOA,QAAQ,CAAChC,GAAG,CAAEiC,KAAyC,KAAM;QAClEC,QAAQ,EAAED,KAAK,CAACC,QAAQ;QACxBC,SAAS,EAAEF,KAAK,CAACE;OAClB,CAAC,CAAC;IACL,CAAC,CAAC,EACFpC,UAAU,CAAEY,KAAK,IAAK,IAAI,CAACO,WAAW,CAACP,KAAK,CAAC,CAAC,CAC/C;EACH;;;uBAhGaT,kBAAkB,EAAAkC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlBrC,kBAAkB;MAAAsC,OAAA,EAAlBtC,kBAAkB,CAAAuC,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}