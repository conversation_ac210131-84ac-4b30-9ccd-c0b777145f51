{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as bootstrap from 'bootstrap';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/action.service\";\nimport * as i3 from \"../auth/authentication.service\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"myChart\"];\nfunction ActionsComponent_a_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 49);\n    i0.ɵɵelement(1, \"span\", 50);\n    i0.ɵɵtext(2, \" Dashboard \");\n    i0.ɵɵelementStart(3, \"span\", 51);\n    i0.ɵɵtext(4, \"(current)\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ActionsComponent_a_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 52);\n    i0.ɵɵelement(1, \"span\", 53);\n    i0.ɵɵtext(2, \" Gestion des utilisateurs \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_a_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 54);\n    i0.ɵɵelement(1, \"span\", 55);\n    i0.ɵɵtext(2, \" Gestion des groupes \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_a_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 56);\n    i0.ɵɵelement(1, \"span\", 57);\n    i0.ɵɵtext(2, \" Gestion des transactions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_a_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 58);\n    i0.ɵɵelement(1, \"span\", 59);\n    i0.ɵɵtext(2, \" Gestion des actions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_a_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 60);\n    i0.ɵɵelement(1, \"span\", 61);\n    i0.ɵɵtext(2, \" Gestion des actionnaires \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_a_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 62);\n    i0.ɵɵelement(1, \"span\", 63);\n    i0.ɵɵtext(2, \" Gestion des Portefeuilles \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsComponent_tr_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\")(11, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function ActionsComponent_tr_66_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const action_r10 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.viewActionDetails(action_r10.idAction));\n    });\n    i0.ɵɵtext(12, \" Voir D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function ActionsComponent_tr_66_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const action_r10 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.deleteAction(action_r10.idAction));\n    });\n    i0.ɵɵtext(14, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const action_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r10.isinAction);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r10.nomSociete);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r10.prix);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 4, action_r10.dateCreation, \"short\"));\n  }\n}\nfunction ActionsComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"ID:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\")(6, \"strong\");\n    i0.ɵɵtext(7, \"ISIN:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\")(10, \"strong\");\n    i0.ɵɵtext(11, \"Nom Soci\\u00E9t\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\")(14, \"strong\");\n    i0.ɵɵtext(15, \"Prix:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\")(18, \"strong\");\n    i0.ɵɵtext(19, \"Date de Cr\\u00E9ation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.selectedAction.idAction, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.selectedAction.isinAction, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.selectedAction.nomSociete, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.selectedAction.prix, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(21, 5, ctx_r9.selectedAction.dateCreation, \"short\"), \"\");\n  }\n}\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #000000;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, 0.25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e5e5e5;\\n}\\n\\n.border-bottom[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5e5e5;\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 4px;\\n  font-weight: bold;\\n  transition: all 0.3s ease;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: 'Poppins', sans-serif;\\n  margin: 0;\\n  padding: 0;\\n  min-height: 100vh;\\n  background: linear-gradient(-45deg, #24375a, #10203a, #14213b, #0c1729);\\n  background-size: 400% 400%;\\n  animation: _ngcontent-%COMP%_gradientMove 20s ease infinite;\\n  color: #fff;\\n}\\n\\n@keyframes _ngcontent-%COMP%_gradientMove {\\n  0% {\\n      background-position: 0% 50%;\\n  }\\n\\n  50% {\\n      background-position: 100% 50%;\\n  }\\n\\n  100% {\\n      background-position: 0% 50%;\\n  }\\n}\\n\\n.container-fluid[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 2rem;\\n  margin-top: 20px;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.06);\\n  border-radius: 15px;\\n  padding: 1.5rem;\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  color: #fff;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  border-collapse: separate;\\n  border-spacing: 0 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.15);\\n  color: #ffffff;\\n  font-weight: 600;\\n  text-align: center;\\n  border: none;\\n  padding: 1rem;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  text-align: center;\\n  border-radius: 12px;\\n}\\n\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.01);\\n  box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  vertical-align: middle;\\n  padding: 0.9rem;\\n  font-size: 0.95rem;\\n}\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n  border: none;\\n  padding: 12px 25px;\\n  border-radius: 30px;\\n  text-transform: uppercase;\\n  font-weight: bold;\\n  transition: background 0.3s, transform 0.2s;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n  background-color: #218838;\\n  transform: translateY(-2px);\\n}\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n  color: #206ee1;\\n  cursor: pointer;\\n  font-size: 20px;\\n  margin: 0 10px;\\n  transition: color 0.3s;\\n}\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffffff;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n  color: #d22d2d;\\n  cursor: pointer;\\n  font-size: 20px;\\n  transition: color 0.3s;\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n  color: #ffffff;\\n}\\n\\n.navbar[_ngcontent-%COMP%] {\\n  background-color: #1c1c1c !important;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n\\na.router-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  background-color: #252528;\\n}\\n\\n\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  padding: 1.5rem;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.modal-footer[_ngcontent-%COMP%] {\\n  background-color: #f1f1f1;\\n  padding: 1rem;\\n  border-top: 1px solid #ddd;\\n  display: flex;\\n  justify-content: center; \\n\\n  gap: 1rem; \\n\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  font-size: 1rem;\\n  border-radius: 0.3rem;\\n  transition: background-color 0.3s, transform 0.2s;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n  transform: translateY(-2px);\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: #fff;\\n  border: none;\\n}\\n\\n.modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column; \\n\\n  align-items: flex-start; \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  background-color: rgba(255, 255, 255, 0.1); \\n\\n  border: 1px solid rgba(255, 255, 255, 0.3); \\n\\n  border-radius: 30px; \\n\\n  color: #fff; \\n\\n  padding: 10px 20px; \\n\\n  font-size: 1rem; \\n\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); \\n\\n  width: 100%; \\n\\n  max-width: 400px; \\n\\n  transition: all 0.3s ease; \\n\\n}\\n\\n.search-bar[_ngcontent-%COMP%]:focus {\\n  outline: none; \\n\\n  border-color: #007bff; \\n\\n  box-shadow: 0 0 8px rgba(0, 123, 255, 0.6); \\n\\n}\\n\\n\\n\\n.search-bar[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(255, 255, 255, 0.7); \\n\\n}\\n\\n\\n.custom-sidebar[_ngcontent-%COMP%] {\\n  background: rgba(20, 33, 59, 0.9); \\n\\n  color: #fff;\\n  min-height: 100vh;\\n  border-right: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #ccc;\\n  transition: color 0.3s ease, background 0.3s ease;\\n}\\n\\n.custom-sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover, .custom-sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 4px;\\n}\\n\\n\\n.custom-topbar[_ngcontent-%COMP%] {\\n  background-color: #000 !important;\\n  color: #fff;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%], .custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n}\\n\\n.custom-topbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff !important;\\n  position: relative;\\n  padding: 0.5rem 1rem;\\n  transition: color 0.3s ease;\\n  font-weight: 500;\\n  letter-spacing: 0.5px;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  width: 0;\\n  background: #ff4c60;\\n  transition: width 0.3s ease;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #ff4c60 !important;\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 30px;\\n  object-fit: cover;\\n  border-radius: 50%; \\n\\n  margin-right: 8px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class ActionsComponent {\n  constructor(router, actionService, authService) {\n    this.router = router;\n    this.actionService = actionService;\n    this.authService = authService;\n    this.actions = [];\n    this.selectedAction = null;\n  }\n  ngOnInit() {\n    this.loadActions();\n  }\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  loadActions() {\n    this.actionService.getAllActions().subscribe({\n      next: data => {\n        this.actions = data;\n        console.log('Actions loaded:', this.actions);\n      },\n      error: err => {\n        console.error('Erreur lors du chargement des actions :', err);\n      }\n    });\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    this.router.navigate(['/login']);\n  }\n  viewActionDetails(id) {\n    this.actionService.getActionById(id).subscribe({\n      next: action => {\n        this.selectedAction = action;\n        const modal = new bootstrap.Modal(document.getElementById('actionModal'));\n        modal.show();\n      },\n      error: err => {\n        console.error('Erreur récupération action:', err);\n      }\n    });\n  }\n  onModalClose() {\n    // Reset the selected action or any temp variables if needed\n    this.selectedAction = null;\n    console.log('Modal closed and state reset');\n  }\n  deleteAction(id) {\n    if (confirm(\"Es-tu sûr de vouloir supprimer cette action ?\")) {\n      this.actionService.deleteActionById(id).subscribe({\n        next: () => {\n          // After deleting, reload the list\n          this.actions = this.actions.filter(a => a.idAction !== id);\n          alert(\"Action supprimée avec succès !\");\n        },\n        error: err => {\n          console.error(\"Erreur lors de la suppression :\", err);\n          alert(\"Erreur lors de la suppression !\");\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function ActionsComponent_Factory(t) {\n      return new (t || ActionsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ActionService), i0.ɵɵdirectiveInject(i3.AuthenticationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionsComponent,\n      selectors: [[\"app-actions\"]],\n      viewQuery: function ActionsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 76,\n      vars: 9,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"rel\", \"stylesheet\", \"href\", \"https://fonts.googleapis.com/icon?family=Material+Icons\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [\"width\", \"900\", \"height\", \"380\", 2, \"background-color\", \"lightgrey\"], [\"myChart\", \"\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"custom-topbar\", \"flex-md-nowrap\", \"p-0\"], [1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\", \"d-flex\", \"align-items-center\"], [\"src\", \"assets/images/gti.jpg\", \"alt\", \"GTI Logo\", 1, \"logo-img\", \"mr-2\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"custom-sidebar\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"class\", \"nav-link\", \"href\", \"/adminDash\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/users\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/groups\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/transactions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actions\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/actionnaires\", 4, \"ngIf\"], [\"class\", \"nav-link\", \"href\", \"/port\", 4, \"ngIf\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"thead-dark\"], [4, \"ngFor\", \"ngForOf\"], [\"id\", \"myChart\", \"width\", \"900\", \"height\", \"380\", 1, \"my-4\"], [\"id\", \"actionModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"actionModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-dialog-centered\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"actionModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Fermer\", 1, \"btn-close\"], [\"class\", \"modal-body\", 4, \"ngIf\"], [\"href\", \"/adminDash\", 1, \"nav-link\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"grid\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"book\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"modal-body\"]],\n      template: function ActionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"link\", 5);\n          i0.ɵɵelementStart(7, \"title\");\n          i0.ɵɵtext(8, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"link\", 6)(10, \"link\", 5)(11, \"link\", 7)(12, \"link\", 8)(13, \"link\", 9)(14, \"link\", 10)(15, \"canvas\", 11, 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"body\")(18, \"nav\", 13)(19, \"a\", 14);\n          i0.ɵɵelement(20, \"img\", 15);\n          i0.ɵɵtext(21, \" GTI \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"ul\", 16)(23, \"li\", 17)(24, \"a\", 18);\n          i0.ɵɵlistener(\"click\", function ActionsComponent_Template_a_click_24_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(25, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 19)(27, \"div\", 20)(28, \"nav\", 21)(29, \"div\", 22)(30, \"ul\", 23)(31, \"li\", 24);\n          i0.ɵɵtemplate(32, ActionsComponent_a_32_Template, 5, 0, \"a\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"li\", 24);\n          i0.ɵɵtemplate(34, ActionsComponent_a_34_Template, 3, 0, \"a\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"li\", 24);\n          i0.ɵɵtemplate(36, ActionsComponent_a_36_Template, 3, 0, \"a\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"li\", 24);\n          i0.ɵɵtemplate(38, ActionsComponent_a_38_Template, 3, 0, \"a\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"li\", 24);\n          i0.ɵɵtemplate(40, ActionsComponent_a_40_Template, 3, 0, \"a\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"li\", 24);\n          i0.ɵɵtemplate(42, ActionsComponent_a_42_Template, 3, 0, \"a\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"li\", 24);\n          i0.ɵɵtemplate(44, ActionsComponent_a_44_Template, 3, 0, \"a\", 31);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(45, \"main\", 32)(46, \"div\", 33)(47, \"h1\", 34);\n          i0.ɵɵtext(48, \"Gestion des Actions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 35);\n          i0.ɵɵelement(50, \"div\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 37)(52, \"table\", 38)(53, \"thead\", 39)(54, \"tr\")(55, \"th\");\n          i0.ɵɵtext(56, \"ISIN\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"th\");\n          i0.ɵɵtext(58, \"Nom Soci\\u00E9t\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\");\n          i0.ɵɵtext(60, \"Prix\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"th\");\n          i0.ɵɵtext(62, \"Date de Cr\\u00E9ation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\");\n          i0.ɵɵtext(64, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"tbody\");\n          i0.ɵɵtemplate(66, ActionsComponent_tr_66_Template, 15, 7, \"tr\", 40);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(67, \"canvas\", 41);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(68, \"div\", 42)(69, \"div\", 43)(70, \"div\", 44)(71, \"div\", 45)(72, \"h5\", 46);\n          i0.ɵɵtext(73, \"D\\u00E9tails de l'Action\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(74, \"button\", 47);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(75, ActionsComponent_div_75_Template, 22, 8, \"div\", 48);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(32);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/adminDash\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/users\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/groups\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/transactions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actions\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/actionnaires\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isRouteAllowed(\"/port\"));\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngForOf\", ctx.actions);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedAction);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i4.DatePipe],\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "bootstrap", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ActionsComponent_tr_66_Template_button_click_11_listener", "restoredCtx", "ɵɵrestoreView", "_r12", "action_r10", "$implicit", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "viewActionDetails", "idAction", "ActionsComponent_tr_66_Template_button_click_13_listener", "ctx_r13", "deleteAction", "ɵɵadvance", "ɵɵtextInterpolate", "isinAction", "nomSociete", "prix", "ɵɵpipeBind2", "dateCreation", "ɵɵtextInterpolate1", "ctx_r9", "selectedAction", "ActionsComponent", "constructor", "router", "actionService", "authService", "actions", "ngOnInit", "loadActions", "ngAfterViewInit", "replace", "getAllActions", "subscribe", "next", "data", "console", "log", "error", "err", "logout", "localStorage", "removeItem", "navigate", "id", "getActionById", "action", "modal", "Modal", "document", "getElementById", "show", "onModalClose", "confirm", "deleteActionById", "filter", "a", "alert", "ɵɵdirectiveInject", "i1", "Router", "i2", "ActionService", "i3", "AuthenticationService", "selectors", "viewQuery", "ActionsComponent_Query", "rf", "ctx", "ActionsComponent_Template_a_click_24_listener", "ɵɵtemplate", "ActionsComponent_a_32_Template", "ActionsComponent_a_34_Template", "ActionsComponent_a_36_Template", "ActionsComponent_a_38_Template", "ActionsComponent_a_40_Template", "ActionsComponent_a_42_Template", "ActionsComponent_a_44_Template", "ActionsComponent_tr_66_Template", "ActionsComponent_div_75_Template", "ɵɵproperty", "isRouteAllowed"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\actions\\actions.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\actions\\actions.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, ViewChild, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\nimport { Action } from '../model/action.model';\nimport { ActionService } from '../services/action.service';\nimport * as bootstrap from 'bootstrap';\nimport { AuthenticationService } from '../auth/authentication.service';\n\n@Component({\n  selector: 'app-actions',\n  templateUrl: './actions.component.html',\n  styleUrls: ['./actions.component.css']\n})\nexport class ActionsComponent implements AfterViewInit, OnInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n  actions: Action[] = [];\n  selectedAction: Action | null = null;\n\n  constructor(\n    private router: Router,\n    private actionService: ActionService,\n    public authService: AuthenticationService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadActions();\n  }\n\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  loadActions(): void {\n    this.actionService.getAllActions().subscribe({\n      next: (data) => {\n        this.actions = data;\n        console.log('Actions loaded:', this.actions);\n      },\n      error: (err) => {\n        console.error('Erreur lors du chargement des actions :', err);\n      }\n    });\n  }\n\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    this.router.navigate(['/login']);\n  }\n\n\n  viewActionDetails(id: number): void {\n    this.actionService.getActionById(id).subscribe({\n      next: (action) => {\n        this.selectedAction = action;\n        const modal = new bootstrap.Modal(document.getElementById('actionModal')!);\n        modal.show();\n      },\n      error: (err) => {\n        console.error('Erreur récupération action:', err);\n      }\n    });\n  }\n\n  onModalClose(): void {\n    // Reset the selected action or any temp variables if needed\n    this.selectedAction = null;\n    console.log('Modal closed and state reset');\n  }\n\n\n  deleteAction(id: number) {\n    if (confirm(\"Es-tu sûr de vouloir supprimer cette action ?\")) {\n      this.actionService.deleteActionById(id).subscribe({\n        next: () => {\n          // After deleting, reload the list\n          this.actions = this.actions.filter(a => a.idAction !== id);\n          alert(\"Action supprimée avec succès !\");\n        },\n        error: err => {\n          console.error(\"Erreur lors de la suppression :\", err);\n          alert(\"Erreur lors de la suppression !\");\n        }\n      });\n    }\n  }\n  \n  \n}\n", "\n<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/css?family=Poppins:300,400,500&display=swap\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <link rel=\"stylesheet\" href=\"https://fonts.googleapis.com/icon?family=Material+Icons\">\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n\n    <!-- Custom styles for this template -->\n    <link href=\"./actions.component.css\" rel=\"stylesheet\">\n    <canvas #myChart width=\"900\" height=\"380\" style=\"background-color: lightgrey;\"></canvas>\n\n  </head>\n\n  <body>\n    \n     <!-- Navbar -->\n     <nav class=\"navbar navbar-dark sticky-top custom-topbar flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0 d-flex align-items-center\">\n        <img src=\"assets/images/gti.jpg\" alt=\"GTI Logo\" class=\"logo-img mr-2\" />\n        GTI\n      </a>\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block custom-sidebar sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/adminDash\" *ngIf=\"authService.isRouteAllowed('/adminDash')\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\" *ngIf=\"authService.isRouteAllowed('/users')\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\" *ngIf=\"authService.isRouteAllowed('/groups')\">\n                  <span data-feather=\"grid\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            \n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/transactions\" *ngIf=\"authService.isRouteAllowed('/transactions')\">\n                <span data-feather=\"dollar-sign\"></span>\n                Gestion des transactions\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/actions\" *ngIf=\"authService.isRouteAllowed('/actions')\">\n                <span data-feather=\"trending-up\"></span>\n               Gestion des actions\n              </a>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\" *ngIf=\"authService.isRouteAllowed('/actionnaires')\">\n                  <span data-feather=\"users\"></span>\n                 Gestion des actionnaires\n                </a>\n                     <li class=\"nav-item\">\n              <a class=\"nav-link\" href=\"/port\" *ngIf=\"authService.isRouteAllowed('/port')\">\n                <span data-feather=\"book\"></span>\n               Gestion des Portefeuilles\n              </a>\n            </ul>\n          </div>\n        </nav>\n\n        \n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Gestion des Actions</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n\n              </div>\n            </div>\n          </div>\n   <div class=\"table-responsive\">\n  <table class=\"table table-hover\">\n    <thead class=\"thead-dark\">\n      <tr>\n        <th>ISIN</th>\n        <th>Nom Société</th>\n        <th>Prix</th>\n        <th>Date de Création</th>\n        <th>Actions</th>\n      </tr>\n    </thead>\n    <tbody>\n      <tr *ngFor=\"let action of actions\">\n        <td>{{ action.isinAction }}</td>\n        <td>{{ action.nomSociete }}</td>\n        <td>{{ action.prix }}</td>\n        <td>{{ action.dateCreation | date: 'short' }}</td>\n        <td>\n          <button class=\"btn btn-sm btn-primary\" (click)=\"viewActionDetails(action.idAction)\">\n            Voir Détails\n          </button>\n          <button class=\"btn btn-danger btn-sm\" (click)=\"deleteAction(action.idAction)\">\n            Supprimer\n          </button>\n        </td>\n      </tr>\n    </tbody>\n  </table>\n</div>\n\n\n          <canvas class=\"my-4\" id=\"myChart\" width=\"900\" height=\"380\"></canvas>\n          \n        \n        </main>\n      </div>\n    </div>\n\n\n    <!-- Modal -->\n<div class=\"modal fade\" id=\"actionModal\" tabindex=\"-1\" aria-labelledby=\"actionModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-dialog-centered\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"actionModalLabel\">Détails de l'Action</h5>\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\n      </div>\n      <div class=\"modal-body\" *ngIf=\"selectedAction\">\n        <p><strong>ID:</strong> {{ selectedAction.idAction }}</p>\n        <p><strong>ISIN:</strong> {{ selectedAction.isinAction }}</p>\n        <p><strong>Nom Société:</strong> {{ selectedAction.nomSociete }}</p>\n        <p><strong>Prix:</strong> {{ selectedAction.prix }}</p>\n        <p><strong>Date de Création:</strong> {{ selectedAction.dateCreation | date: 'short' }}</p>\n      </div>\n    </div>\n  </div>\n</div>\n    <!-- Bootstrap core JavaScript\n    ================================================== -->\n    <!-- Placed at the end of the document so the pages load faster -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>    \n\n    </script>\n  </body>\n</html>\n"], "mappings": "AAEA,OAAO,KAAKA,OAAO,MAAM,eAAe;AAGxC,OAAO,KAAKC,SAAS,MAAM,WAAW;;;;;;;;;ICyCtBC,EAAA,CAAAC,cAAA,YAAuF;IACrFD,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAIlDJ,EAAA,CAAAC,cAAA,YAA+E;IAC7ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAiF;IAC/ED,EAAA,CAAAE,SAAA,eAAiC;IACjCF,EAAA,CAAAG,MAAA,4BACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAINJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAwC;IACxCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAGJJ,EAAA,CAAAC,cAAA,YAAmF;IACjFD,EAAA,CAAAE,SAAA,eAAwC;IACzCF,EAAA,CAAAG,MAAA,4BACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAEFJ,EAAA,CAAAC,cAAA,YAA6F;IAC3FD,EAAA,CAAAE,SAAA,eAAkC;IACnCF,EAAA,CAAAG,MAAA,iCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAENJ,EAAA,CAAAC,cAAA,YAA6E;IAC3ED,EAAA,CAAAE,SAAA,eAAiC;IAClCF,EAAA,CAAAG,MAAA,kCACD;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IA2BZJ,EAAA,CAAAC,cAAA,SAAmC;IAC7BD,EAAA,CAAAG,MAAA,GAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAyC;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClDJ,EAAA,CAAAC,cAAA,UAAI;IACqCD,EAAA,CAAAK,UAAA,mBAAAC,yDAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,IAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,OAAA,CAAAG,iBAAA,CAAAL,UAAA,CAAAM,QAAA,CAAkC;IAAA,EAAC;IACjFhB,EAAA,CAAAG,MAAA,2BACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA8E;IAAxCD,EAAA,CAAAK,UAAA,mBAAAY,yDAAA;MAAA,MAAAV,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,IAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAO,OAAA,GAAAlB,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAI,OAAA,CAAAC,YAAA,CAAAT,UAAA,CAAAM,QAAA,CAA6B;IAAA,EAAC;IAC3EhB,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAVPJ,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,iBAAA,CAAAX,UAAA,CAAAY,UAAA,CAAuB;IACvBtB,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,iBAAA,CAAAX,UAAA,CAAAa,UAAA,CAAuB;IACvBvB,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAqB,iBAAA,CAAAX,UAAA,CAAAc,IAAA,CAAiB;IACjBxB,EAAA,CAAAoB,SAAA,GAAyC;IAAzCpB,EAAA,CAAAqB,iBAAA,CAAArB,EAAA,CAAAyB,WAAA,OAAAf,UAAA,CAAAgB,YAAA,WAAyC;;;;;IA+B/C1B,EAAA,CAAAC,cAAA,cAA+C;IAClCD,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,GAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACzDJ,EAAA,CAAAC,cAAA,QAAG;IAAQD,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,GAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC7DJ,EAAA,CAAAC,cAAA,QAAG;IAAQD,EAAA,CAAAG,MAAA,8BAAY;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,IAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACpEJ,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,IAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACvDJ,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAG,MAAA,8BAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAACJ,EAAA,CAAAG,MAAA,IAAiD;;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAJnEJ,EAAA,CAAAoB,SAAA,GAA6B;IAA7BpB,EAAA,CAAA2B,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAb,QAAA,KAA6B;IAC3BhB,EAAA,CAAAoB,SAAA,GAA+B;IAA/BpB,EAAA,CAAA2B,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAP,UAAA,KAA+B;IACxBtB,EAAA,CAAAoB,SAAA,GAA+B;IAA/BpB,EAAA,CAAA2B,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAN,UAAA,KAA+B;IACtCvB,EAAA,CAAAoB,SAAA,GAAyB;IAAzBpB,EAAA,CAAA2B,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAL,IAAA,KAAyB;IACbxB,EAAA,CAAAoB,SAAA,GAAiD;IAAjDpB,EAAA,CAAA2B,kBAAA,MAAA3B,EAAA,CAAAyB,WAAA,QAAAG,MAAA,CAAAC,cAAA,CAAAH,YAAA,eAAiD;;;;AD1I/F,OAAM,MAAOI,gBAAgB;EAK3BC,YACUC,MAAc,EACdC,aAA4B,EAC7BC,WAAkC;IAFjC,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACd,KAAAC,WAAW,GAAXA,WAAW;IANpB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAN,cAAc,GAAkB,IAAI;EAMjC;EAEHO,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,eAAeA,CAAA;IACbxC,OAAO,CAACyC,OAAO,EAAE,CAAC,CAAC;EACrB;;EAEAF,WAAWA,CAAA;IACT,IAAI,CAACJ,aAAa,CAACO,aAAa,EAAE,CAACC,SAAS,CAAC;MAC3CC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACR,OAAO,GAAGQ,IAAI;QACnBC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACV,OAAO,CAAC;MAC9C,CAAC;MACDW,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEC,GAAG,CAAC;MAC/D;KACD,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChC,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAGApC,iBAAiBA,CAACqC,EAAU;IAC1B,IAAI,CAACnB,aAAa,CAACoB,aAAa,CAACD,EAAE,CAAC,CAACX,SAAS,CAAC;MAC7CC,IAAI,EAAGY,MAAM,IAAI;QACf,IAAI,CAACzB,cAAc,GAAGyB,MAAM;QAC5B,MAAMC,KAAK,GAAG,IAAIxD,SAAS,CAACyD,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAE,CAAC;QAC1EH,KAAK,CAACI,IAAI,EAAE;MACd,CAAC;MACDb,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;MACnD;KACD,CAAC;EACJ;EAEAa,YAAYA,CAAA;IACV;IACA,IAAI,CAAC/B,cAAc,GAAG,IAAI;IAC1Be,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;EAC7C;EAGA1B,YAAYA,CAACiC,EAAU;IACrB,IAAIS,OAAO,CAAC,+CAA+C,CAAC,EAAE;MAC5D,IAAI,CAAC5B,aAAa,CAAC6B,gBAAgB,CAACV,EAAE,CAAC,CAACX,SAAS,CAAC;QAChDC,IAAI,EAAEA,CAAA,KAAK;UACT;UACA,IAAI,CAACP,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC4B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChD,QAAQ,KAAKoC,EAAE,CAAC;UAC1Da,KAAK,CAAC,gCAAgC,CAAC;QACzC,CAAC;QACDnB,KAAK,EAAEC,GAAG,IAAG;UACXH,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEC,GAAG,CAAC;UACrDkB,KAAK,CAAC,iCAAiC,CAAC;QAC1C;OACD,CAAC;;EAEN;;;uBAzEWnC,gBAAgB,EAAA9B,EAAA,CAAAkE,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAApE,EAAA,CAAAkE,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAtE,EAAA,CAAAkE,iBAAA,CAAAK,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAAhB1C,gBAAgB;MAAA2C,SAAA;MAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCX7B5E,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAE,SAAA,cAAsB;UAMtBF,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAG,MAAA,uCAAgC;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAE/CJ,EAAA,CAAAE,SAAA,cAAmF;UAYrFF,EAAA,CAAAI,YAAA,EAAO;UAEPJ,EAAA,CAAAC,cAAA,YAAM;UAKAD,EAAA,CAAAE,SAAA,eAAwE;UACxEF,EAAA,CAAAG,MAAA,aACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAK,UAAA,mBAAAyE,8CAAA;YAAA,OAASD,GAAA,CAAA7B,MAAA,EAAQ;UAAA,EAAC;UAAChD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAIzDJ,EAAA,CAAAC,cAAA,eAA6B;UAMjBD,EAAA,CAAA+E,UAAA,KAAAC,8BAAA,gBAGI;UACNhF,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA+E,UAAA,KAAAE,8BAAA,gBAGI;UACNjF,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA+E,UAAA,KAAAG,8BAAA,gBAGI;UACNlF,EAAA,CAAAI,YAAA,EAAK;UAEPJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA+E,UAAA,KAAAI,8BAAA,gBAGI;UACNnF,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA+E,UAAA,KAAAK,8BAAA,gBAGI;UAJNpF,EAAA,CAAAI,YAAA,EAAqB;UAKnBJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAA+E,UAAA,KAAAM,8BAAA,gBAGI;UAJNrF,EAAA,CAAAI,YAAA,EAAqB;UAKdJ,EAAA,CAAAC,cAAA,cAAqB;UAC5BD,EAAA,CAAA+E,UAAA,KAAAO,8BAAA,gBAGI;UAJGtF,EAAA,CAAAI,YAAA,EAAqB;UAUlCJ,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAG,MAAA,2BAAmB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvCJ,EAAA,CAAAC,cAAA,eAAsC;UACpCD,EAAA,CAAAE,SAAA,eAEM;UACRF,EAAA,CAAAI,YAAA,EAAM;UAEfJ,EAAA,CAAAC,cAAA,eAA8B;UAIrBD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACbJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,6BAAW;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACbJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,6BAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGpBJ,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAA+E,UAAA,KAAAQ,+BAAA,kBAaK;UACPvF,EAAA,CAAAI,YAAA,EAAQ;UAKFJ,EAAA,CAAAE,SAAA,kBAAoE;UAGtEF,EAAA,CAAAI,YAAA,EAAO;UAMfJ,EAAA,CAAAC,cAAA,eAA6G;UAIvDD,EAAA,CAAAG,MAAA,gCAAmB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACtEJ,EAAA,CAAAE,SAAA,kBAA6F;UAC/FF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAA+E,UAAA,KAAAS,gCAAA,mBAMM;UACRxF,EAAA,CAAAI,YAAA,EAAM;;;UA3G6CJ,EAAA,CAAAoB,SAAA,IAA8C;UAA9CpB,EAAA,CAAAyF,UAAA,SAAAZ,GAAA,CAAA3C,WAAA,CAAAwD,cAAA,eAA8C;UAMlD1F,EAAA,CAAAoB,SAAA,GAA0C;UAA1CpB,EAAA,CAAAyF,UAAA,SAAAZ,GAAA,CAAA3C,WAAA,CAAAwD,cAAA,WAA0C;UAMzC1F,EAAA,CAAAoB,SAAA,GAA2C;UAA3CpB,EAAA,CAAAyF,UAAA,SAAAZ,GAAA,CAAA3C,WAAA,CAAAwD,cAAA,YAA2C;UAOvC1F,EAAA,CAAAoB,SAAA,GAAiD;UAAjDpB,EAAA,CAAAyF,UAAA,SAAAZ,GAAA,CAAA3C,WAAA,CAAAwD,cAAA,kBAAiD;UAMtD1F,EAAA,CAAAoB,SAAA,GAA4C;UAA5CpB,EAAA,CAAAyF,UAAA,SAAAZ,GAAA,CAAA3C,WAAA,CAAAwD,cAAA,aAA4C;UAKrC1F,EAAA,CAAAoB,SAAA,GAAiD;UAAjDpB,EAAA,CAAAyF,UAAA,SAAAZ,GAAA,CAAA3C,WAAA,CAAAwD,cAAA,kBAAiD;UAK3D1F,EAAA,CAAAoB,SAAA,GAAyC;UAAzCpB,EAAA,CAAAyF,UAAA,SAAAZ,GAAA,CAAA3C,WAAA,CAAAwD,cAAA,UAAyC;UA8B5D1F,EAAA,CAAAoB,SAAA,IAAU;UAAVpB,EAAA,CAAAyF,UAAA,YAAAZ,GAAA,CAAA1C,OAAA,CAAU;UAmCRnC,EAAA,CAAAoB,SAAA,GAAoB;UAApBpB,EAAA,CAAAyF,UAAA,SAAAZ,GAAA,CAAAhD,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}