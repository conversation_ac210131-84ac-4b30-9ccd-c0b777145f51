{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let ForgotPasswordComponent = class ForgotPasswordComponent {\n  constructor(http) {\n    this.http = http;\n    this.message = '';\n    this.error = '';\n  }\n  submitRequest() {\n    const request = {\n      matricule: this.matricule,\n      email: this.email\n    };\n    this.http.post('http://localhost:8000/api/v1/auth/resetPassword/send_request_to_Admin', request).subscribe({\n      next: res => {\n        this.message = \"Votre demande a été envoyée à l'administrateur.\";\n        this.error = '';\n      },\n      error: err => {\n        console.error(err);\n        this.error = \"Erreur lors de l'envoi. Veuillez vérifier vos informations.\";\n        this.message = '';\n      }\n    });\n  }\n};\nForgotPasswordComponent = __decorate([Component({\n  selector: 'app-forgot-password',\n  templateUrl: './forgot-password.component.html'\n})], ForgotPasswordComponent);", "map": {"version": 3, "names": ["Component", "ForgotPasswordComponent", "constructor", "http", "message", "error", "submitRequest", "request", "matricule", "email", "post", "subscribe", "next", "res", "err", "console", "__decorate", "selector", "templateUrl"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\forgot-password\\forgot-password.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\n\n@Component({\n  selector: 'app-forgot-password',\n  templateUrl: './forgot-password.component.html'\n})\nexport class ForgotPasswordComponent {\n  matricule!: number;\n  email!: string;\n  message = '';\n  error = '';\n\n  constructor(private http: HttpClient) {}\n\n  submitRequest() {\n    const request = {\n      matricule: this.matricule,\n      email: this.email\n    };\n\n    this.http.post('http://localhost:8000/api/v1/auth/resetPassword/send_request_to_Admin', request)\n      .subscribe({\n        next: (res: any) => {\n          this.message = \"Votre demande a été envoyée à l'administrateur.\";\n          this.error = '';\n        },\n        error: (err) => {\n          console.error(err);\n          this.error = \"Erreur lors de l'envoi. Veuillez vérifier vos informations.\";\n          this.message = '';\n        }\n      });\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAOlC,WAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAMlCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHxB,KAAAC,OAAO,GAAG,EAAE;IACZ,KAAAC,KAAK,GAAG,EAAE;EAE6B;EAEvCC,aAAaA,CAAA;IACX,MAAMC,OAAO,GAAG;MACdC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,KAAK,EAAE,IAAI,CAACA;KACb;IAED,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,uEAAuE,EAAEH,OAAO,CAAC,CAC7FI,SAAS,CAAC;MACTC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACT,OAAO,GAAG,iDAAiD;QAChE,IAAI,CAACC,KAAK,GAAG,EAAE;MACjB,CAAC;MACDA,KAAK,EAAGS,GAAG,IAAI;QACbC,OAAO,CAACV,KAAK,CAACS,GAAG,CAAC;QAClB,IAAI,CAACT,KAAK,GAAG,6DAA6D;QAC1E,IAAI,CAACD,OAAO,GAAG,EAAE;MACnB;KACD,CAAC;EACN;CACD;AA3BYH,uBAAuB,GAAAe,UAAA,EAJnChB,SAAS,CAAC;EACTiB,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE;CACd,CAAC,C,EACWjB,uBAAuB,CA2BnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}