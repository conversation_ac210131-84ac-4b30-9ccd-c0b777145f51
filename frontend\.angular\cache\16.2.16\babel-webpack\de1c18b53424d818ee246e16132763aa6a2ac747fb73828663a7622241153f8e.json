{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction ForgotPasswordComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.message);\n  }\n}\nexport class ForgotPasswordComponent {\n  static {\n    this.ɵfac = function ForgotPasswordComponent_Factory(t) {\n      return new (t || ForgotPasswordComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ForgotPasswordComponent,\n      selectors: [[\"app-forgot-password\"]],\n      decls: 19,\n      vars: 3,\n      consts: [[1, \"container\", \"d-flex\", \"justify-content-center\", \"align-items-center\", \"vh-100\"], [1, \"card\", \"p-4\", \"shadow-lg\", \"rounded\", 2, \"width\", \"400px\"], [1, \"text-center\"], [1, \"text-muted\"], [3, \"ngSubmit\"], [1, \"mb-3\"], [1, \"form-label\"], [\"type\", \"text\", \"name\", \"matricule\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"email\", \"name\", \"email\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\"], [\"class\", \"alert alert-info text-center mt-3\", 4, \"ngIf\"], [1, \"alert\", \"alert-info\", \"text-center\", \"mt-3\"]],\n      template: function ForgotPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h3\");\n          i0.ɵɵtext(4, \"Mot de passe oubli\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 3);\n          i0.ɵɵtext(6, \"Entrez votre matricule et email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function ForgotPasswordComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.submitRequest();\n          });\n          i0.ɵɵelementStart(8, \"div\", 5)(9, \"label\", 6);\n          i0.ɵɵtext(10, \"Matricule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"input\", 7);\n          i0.ɵɵlistener(\"ngModelChange\", function ForgotPasswordComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.matricule = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 5)(13, \"label\", 6);\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 8);\n          i0.ɵɵlistener(\"ngModelChange\", function ForgotPasswordComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"button\", 9);\n          i0.ɵɵtext(17, \"Envoyer la requ\\u00EAte\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, ForgotPasswordComponent_div_18_Template, 2, 1, \"div\", 10);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngModel\", ctx.matricule);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.email);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.message);\n        }\n      },\n      dependencies: [i1.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.NgModel, i2.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "message", "ForgotPasswordComponent", "selectors", "decls", "vars", "consts", "template", "ForgotPasswordComponent_Template", "rf", "ctx", "ɵɵlistener", "ForgotPasswordComponent_Template_form_ngSubmit_7_listener", "submitRequest", "ForgotPasswordComponent_Template_input_ngModelChange_11_listener", "$event", "matricule", "ForgotPasswordComponent_Template_input_ngModelChange_15_listener", "email", "ɵɵtemplate", "ForgotPasswordComponent_div_18_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\forgot-password\\forgot-password.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\forgot-password\\forgot-password.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-forgot-password',\n  templateUrl: './forgot-password.component.html',\n  styleUrls: ['./forgot-password.component.css']\n})\nexport class ForgotPasswordComponent {\n\n}\n", "<div class=\"container d-flex justify-content-center align-items-center vh-100\">\n    <div class=\"card p-4 shadow-lg rounded\" style=\"width: 400px;\">\n      <div class=\"text-center\">\n        <h3>Mot de passe oublié</h3>\n        <p class=\"text-muted\">Entrez votre matricule et email</p>\n      </div>\n      <form (ngSubmit)=\"submitRequest()\">\n        <div class=\"mb-3\">\n          <label class=\"form-label\">Matricule</label>\n          <input type=\"text\" [(ngModel)]=\"matricule\" name=\"matricule\" required class=\"form-control\">\n        </div>\n        <div class=\"mb-3\">\n          <label class=\"form-label\">Email</label>\n          <input type=\"email\" [(ngModel)]=\"email\" name=\"email\" required class=\"form-control\">\n        </div>\n        <button type=\"submit\" class=\"btn btn-primary w-100\">Envoyer la requête</button>\n        <div *ngIf=\"message\" class=\"alert alert-info text-center mt-3\">{{ message }}</div>\n      </form>\n    </div>\n  </div>\n  "], "mappings": ";;;;;ICgBQA,EAAA,CAAAC,cAAA,cAA+D;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAnBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAa;;;ADTpF,OAAM,MAAOC,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPpCf,EAAA,CAAAC,cAAA,aAA+E;UAGnED,EAAA,CAAAE,MAAA,+BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,WAAsB;UAAAD,EAAA,CAAAE,MAAA,sCAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3DH,EAAA,CAAAC,cAAA,cAAmC;UAA7BD,EAAA,CAAAiB,UAAA,sBAAAC,0DAAA;YAAA,OAAYF,GAAA,CAAAG,aAAA,EAAe;UAAA,EAAC;UAChCnB,EAAA,CAAAC,cAAA,aAAkB;UACUD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,gBAA0F;UAAvED,EAAA,CAAAiB,UAAA,2BAAAG,iEAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAM,SAAA,GAAAD,MAAA;UAAA,EAAuB;UAA1CrB,EAAA,CAAAG,YAAA,EAA0F;UAE5FH,EAAA,CAAAC,cAAA,cAAkB;UACUD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvCH,EAAA,CAAAC,cAAA,gBAAmF;UAA/DD,EAAA,CAAAiB,UAAA,2BAAAM,iEAAAF,MAAA;YAAA,OAAAL,GAAA,CAAAQ,KAAA,GAAAH,MAAA;UAAA,EAAmB;UAAvCrB,EAAA,CAAAG,YAAA,EAAmF;UAErFH,EAAA,CAAAC,cAAA,iBAAoD;UAAAD,EAAA,CAAAE,MAAA,+BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/EH,EAAA,CAAAyB,UAAA,KAAAC,uCAAA,kBAAkF;UACpF1B,EAAA,CAAAG,YAAA,EAAO;;;UARgBH,EAAA,CAAAI,SAAA,IAAuB;UAAvBJ,EAAA,CAAA2B,UAAA,YAAAX,GAAA,CAAAM,SAAA,CAAuB;UAItBtB,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAA2B,UAAA,YAAAX,GAAA,CAAAQ,KAAA,CAAmB;UAGnCxB,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAA2B,UAAA,SAAAX,GAAA,CAAAT,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}