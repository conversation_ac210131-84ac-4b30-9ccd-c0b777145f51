package com.Aziz.Administratif.ClientRelation.Entity;


import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransactionData {


    private String nomActionnaire;
    private String prenomActionnaire;
    private String ISINAction;
    private Double prixUnitaire;
    private String type;
    private Integer quantite;
    private Long idActionnaire;
    private Double montatnt;
    private String observations;


    @CreationTimestamp
    private LocalDateTime DateCreation;

    private LocalDateTime DateTransaction;


}
