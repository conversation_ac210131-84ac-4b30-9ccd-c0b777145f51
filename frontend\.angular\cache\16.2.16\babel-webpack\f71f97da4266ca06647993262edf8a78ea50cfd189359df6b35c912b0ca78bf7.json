{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ActionService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth/Action/All_Actions';\n  }\n  getAllActions() {\n    return this.http.get(this.apiUrl);\n  }\n  getActionById(id) {\n    return this.http.get(`http://localhost:8000/api/v1/auth/Action/${id}`);\n  }\n  deleteActionById(id) {\n    return this.http.delete(`http://localhost:8000/api/v1/auth/Action/${id}`);\n  }\n  addAction(action) {\n    const headers = new HttpHeaders().set('Content-Type', 'application/json');\n    return this.http.post('http://localhost:8000/api/v1/auth/Action/create', action, {\n      headers,\n      responseType: 'json' // Ensure the response is parsed as J<PERSON><PERSON>, if the backend returns JSON\n    });\n  }\n\n  static {\n    this.ɵfac = function ActionService_Factory(t) {\n      return new (t || ActionService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ActionService,\n      factory: ActionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "ActionService", "constructor", "http", "apiUrl", "getAllActions", "get", "getActionById", "id", "deleteActionById", "delete", "addAction", "action", "headers", "set", "post", "responseType", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\services\\action.service.ts"], "sourcesContent": ["// src/app/services/action.service.ts\nimport { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { Action } from '../model/action.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ActionService {\n  private apiUrl = 'http://localhost:8000/api/v1/auth/Action/All_Actions';\n\n  constructor(private http: HttpClient) {}\n\n  getAllActions(): Observable<Action[]> {\n    return this.http.get<Action[]>(this.apiUrl);\n  }\n\n  getActionById(id: number): Observable<Action> {\n    return this.http.get<Action>(`http://localhost:8000/api/v1/auth/Action/${id}`);\n  }\n  \n  deleteActionById(id: number): Observable<any> {\n    return this.http.delete(`http://localhost:8000/api/v1/auth/Action/${id}`);\n  }\n\naddAction(action: Partial<Action>): Observable<any> {\n  const headers = new HttpHeaders().set('Content-Type', 'application/json');\n  \n  return this.http.post('http://localhost:8000/api/v1/auth/Action/create', action, {\n    headers,\n    responseType: 'json' // Ensure the response is parsed as JSON, if the backend returns JSON\n  });\n}\n\n\n  \n}\n"], "mappings": "AAEA,SAAqBA,WAAW,QAAQ,sBAAsB;;;AAO9D,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,sDAAsD;EAEhC;EAEvCC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACF,IAAI,CAACG,GAAG,CAAW,IAAI,CAACF,MAAM,CAAC;EAC7C;EAEAG,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACL,IAAI,CAACG,GAAG,CAAS,4CAA4CE,EAAE,EAAE,CAAC;EAChF;EAEAC,gBAAgBA,CAACD,EAAU;IACzB,OAAO,IAAI,CAACL,IAAI,CAACO,MAAM,CAAC,4CAA4CF,EAAE,EAAE,CAAC;EAC3E;EAEFG,SAASA,CAACC,MAAuB;IAC/B,MAAMC,OAAO,GAAG,IAAIb,WAAW,EAAE,CAACc,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAEzE,OAAO,IAAI,CAACX,IAAI,CAACY,IAAI,CAAC,iDAAiD,EAAEH,MAAM,EAAE;MAC/EC,OAAO;MACPG,YAAY,EAAE,MAAM,CAAC;KACtB,CAAC;EACJ;;;;uBAxBaf,aAAa,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAbnB,aAAa;MAAAoB,OAAA,EAAbpB,aAAa,CAAAqB,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}