package com.Rayen.Actionnaire;


import com.Rayen.Actionnaire.Entity.Actionnaire;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("GRA/Actionnaire")
@RequiredArgsConstructor
public class ActionnaireController {

    private final ActionnaireService service;

    @PostMapping("ajout")
    @ResponseStatus(HttpStatus.CREATED)
    public void save(
            @RequestBody Actionnaire actionnaire
    ){
        service.saveUser(actionnaire);
    }

    @GetMapping
    public ResponseEntity<List<Actionnaire>>findAllUsers(){
        return ResponseEntity.ok(service.findAllUsers());
    }








    //relation avec User
    @GetMapping("/user/{user-id}")
    public ResponseEntity<List<Actionnaire>>findAllUsers(@PathVariable("user-id")Long userId){
        return ResponseEntity.ok(service.findActionnairesbyUser(userId));
    }

}
