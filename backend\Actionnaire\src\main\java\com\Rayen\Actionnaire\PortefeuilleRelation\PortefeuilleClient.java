package com.Rayen.Actionnaire.PortefeuilleRelation;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(name="Portefeuille-Service",url="${application.config.portefeuille-url}")
public interface PortefeuilleClient {

    @GetMapping("/actionnaire/{actionnaire-id}")
    List<Portefeuille>findPortefeuillesbyActionnaire(@PathVariable("actionnaire-id") Long actionnareId);
}
