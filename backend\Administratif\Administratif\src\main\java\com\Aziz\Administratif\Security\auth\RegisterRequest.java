package com.Aziz.Administratif.Security.auth;

import com.Aziz.Administratif.Entity.Groupe;
import com.Aziz.Administratif.Enum.Role;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegisterRequest {

  private String firstName;
  private String lastName;
  private String email;
  private Integer telephone;
  private Role role;
  private Long groupeId;  // Group ID will be selected, used to fetch the group and habilitation
  private Groupe groupe;  // This will be fetched from the database, no need for user input
  private Integer matricule;
  private Boolean state;

  // Permissions
  private Boolean canCreate;
  private Boolean canRead;
  private Boolean canUpdate;
  private Boolean canDelete;

  private Boolean flag;
}
