package com.Aziz.Client.Controllers;


import com.Aziz.Client.Entity.Actionnaire;
import com.Aziz.Client.Entity.Action;
import com.Aziz.Client.Repositories.ActionnaireRepository;
import com.Aziz.Client.Entity.Portefeuille;
import com.Aziz.Client.Services.ActionnaireService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("GRA/Client/Actionnaire")
@RequiredArgsConstructor
public class ActionnaireController {


    @Autowired
    private final ActionnaireRepository ActionnaireRepository;

    @Autowired
    private final ActionnaireService actionnaireService;

    @GetMapping("/All_Actionnaires")
    public ResponseEntity<List<Actionnaire>> getAllActionnaires() {
        return ResponseEntity.ok(ActionnaireRepository.findAll());
    }

    @GetMapping("/{idActionnaire}")
    public ResponseEntity<Actionnaire> getActionnaireById(@PathVariable Long idActionnaire) {
        Optional<Actionnaire> actionnaire = actionnaireService.findById(idActionnaire);
        return actionnaire.map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/create")
    public ResponseEntity<String> createActionnaire(@RequestBody Actionnaire actionnaire) {
        try {
            actionnaireService.saveActionnaire(actionnaire);
            return ResponseEntity.ok("Actionnaire saved successfully");
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/update/{idActionnaire}")
    public ResponseEntity<Actionnaire> updateActionnaire(@PathVariable Long idActionnaire, @RequestBody Actionnaire updatedActionnaire) {
        try {
            Actionnaire result = actionnaireService.updateActionnaire(idActionnaire, updatedActionnaire);
            return ResponseEntity.ok(result);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/delete/{idActionnaire}")
    public ResponseEntity<String> deleteActionnaire(@PathVariable Long idActionnaire) {
        try {
            actionnaireService.deleteActionnaire(idActionnaire);
            return ResponseEntity.ok("Actionnaire deleted successfully");
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }





    @GetMapping("/{idPortefeuille}/portefeuilles")
    public ResponseEntity<List<Portefeuille>> getPortefeuilles(@PathVariable Long idPortefeuille) {
        try {
            return ResponseEntity.ok(actionnaireService.getPortefeuillesByActionnaireId(idPortefeuille));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/{idAction}/actions")
    public ResponseEntity<List<Action>> getActions(@PathVariable Long idAction) {
        try {
            return ResponseEntity.ok(actionnaireService.getActionsByActionnaireId(idAction));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }


}
