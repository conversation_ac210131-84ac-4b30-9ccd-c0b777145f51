{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthenticationService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.apiUrl = 'http://localhost:8060/api/v1/auth';\n  } // ✅ Inject Router\n  // Register User\n  register(firstName, lastName, email, password) {\n    return this.http.post(`${this.apiUrl}/register`, {\n      firstName,\n      lastName,\n      email,\n      password\n    });\n  }\n  // Authenticate User\n  login(email, password) {\n    return this.http.post(`${this.apiUrl}/authenticate`, {\n      email,\n      password\n    });\n  }\n  // Store JWT Token\n  storeToken(token) {\n    localStorage.setItem('jwt_token', token);\n  }\n  // Get Token\n  getToken() {\n    return localStorage.getItem('jwt_token');\n  }\n  // Remove Token (Logout)\n  logout() {\n    localStorage.removeItem('token');\n    sessionStorage.removeItem('token');\n    // Redirect to login and clear history state\n    this.router.navigate(['/login']).then(() => {\n      window.location.href = '/login'; // 🔥 Force reload to clear cache\n    });\n  }\n  // Check if the user is logged in\n  isAuthenticated() {\n    return !!this.getToken();\n  }\n  // Decode JWT and extract role and group\n  getUserDetailsFromToken(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT\n      return {\n        role: payload?.role || '',\n        groupe: payload?.groupe || ''\n      };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return {\n        role: '',\n        groupe: ''\n      };\n    }\n  }\n  static {\n    this.ɵfac = function AuthenticationService_Factory(t) {\n      return new (t || AuthenticationService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthenticationService,\n      factory: AuthenticationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AuthenticationService", "constructor", "http", "router", "apiUrl", "register", "firstName", "lastName", "email", "password", "post", "login", "storeToken", "token", "localStorage", "setItem", "getToken", "getItem", "logout", "removeItem", "sessionStorage", "navigate", "then", "window", "location", "href", "isAuthenticated", "getUserDetailsFromToken", "payload", "JSON", "parse", "atob", "split", "role", "groupe", "e", "console", "error", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\authentication.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Router } from '@angular/router';  // ✅ Import Router\nimport { Observable } from 'rxjs';\n\ninterface AuthResponse {\n  token: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthenticationService {\n  private apiUrl = 'http://localhost:8060/api/v1/auth';\n\n  constructor(private http: HttpClient, private router: Router) {}  // ✅ Inject Router\n\n  // Register User\n  register(firstName: string, lastName: string, email: string, password: string): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/register`, { firstName, lastName, email, password });\n  }\n\n  // Authenticate User\n  login(email: string, password: string): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.apiUrl}/authenticate`, { email, password });\n  }\n\n  // Store JWT Token\n  storeToken(token: string) {\n    localStorage.setItem('jwt_token', token);\n  }\n\n  // Get Token\n  getToken(): string | null {\n    return localStorage.getItem('jwt_token');\n  }\n\n  // Remove Token (Logout)\n  logout() {\n    localStorage.removeItem('token');\n    sessionStorage.removeItem('token');\n  \n    // Redirect to login and clear history state\n    this.router.navigate(['/login']).then(() => {\n      window.location.href = '/login';  // 🔥 Force reload to clear cache\n    });\n  }\n  \n  \n\n  // Check if the user is logged in\n  isAuthenticated(): boolean {\n    return !!this.getToken();\n  }\n\n  // Decode JWT and extract role and group\n  getUserDetailsFromToken(token: string): { role: string, groupe: string } {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT\n      return { role: payload?.role || '', groupe: payload?.groupe || '' };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return { role: '', groupe: '' };\n    }\n  }\n}\n"], "mappings": ";;;AAYA,OAAM,MAAOA,qBAAqB;EAGhCC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAF5C,KAAAC,MAAM,GAAG,mCAAmC;EAEW,CAAC,CAAE;EAElE;EACAC,QAAQA,CAACC,SAAiB,EAAEC,QAAgB,EAAEC,KAAa,EAAEC,QAAgB;IAC3E,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAe,GAAG,IAAI,CAACN,MAAM,WAAW,EAAE;MAAEE,SAAS;MAAEC,QAAQ;MAAEC,KAAK;MAAEC;IAAQ,CAAE,CAAC;EAC1G;EAEA;EACAE,KAAKA,CAACH,KAAa,EAAEC,QAAgB;IACnC,OAAO,IAAI,CAACP,IAAI,CAACQ,IAAI,CAAe,GAAG,IAAI,CAACN,MAAM,eAAe,EAAE;MAAEI,KAAK;MAAEC;IAAQ,CAAE,CAAC;EACzF;EAEA;EACAG,UAAUA,CAACC,KAAa;IACtBC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEF,KAAK,CAAC;EAC1C;EAEA;EACAG,QAAQA,CAAA;IACN,OAAOF,YAAY,CAACG,OAAO,CAAC,WAAW,CAAC;EAC1C;EAEA;EACAC,MAAMA,CAAA;IACJJ,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;IAChCC,cAAc,CAACD,UAAU,CAAC,OAAO,CAAC;IAElC;IACA,IAAI,CAAChB,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,MAAK;MACzCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ,CAAC,CAAE;IACpC,CAAC,CAAC;EACJ;EAIA;EACAC,eAAeA,CAAA;IACb,OAAO,CAAC,CAAC,IAAI,CAACV,QAAQ,EAAE;EAC1B;EAEA;EACAW,uBAAuBA,CAACd,KAAa;IACnC,IAAI;MACF,MAAMe,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAAClB,KAAK,CAACmB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,OAAO;QAAEC,IAAI,EAAEL,OAAO,EAAEK,IAAI,IAAI,EAAE;QAAEC,MAAM,EAAEN,OAAO,EAAEM,MAAM,IAAI;MAAE,CAAE;KACpE,CAAC,OAAOC,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,CAAC,CAAC;MACzC,OAAO;QAAEF,IAAI,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAE;;EAEnC;;;uBApDWlC,qBAAqB,EAAAsC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAArB3C,qBAAqB;MAAA4C,OAAA,EAArB5C,qBAAqB,CAAA6C,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}