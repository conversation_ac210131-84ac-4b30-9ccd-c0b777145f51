package com.example.User.user;

import java.util.Collection;
import java.util.List;

import jakarta.persistence.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name="Users")
public class User implements UserDetails {
@Id
@GeneratedValue(strategy = GenerationType.IDENTITY)
private Long id;

@Column(name = "First_name")  // Explicitly specifying the column name
private String firstName;
@Column(name = "Last_name")
private String lastName;
private String password;
private String email;
private Integer telephone;
@Enumerated(EnumType.STRING)
private Role role;


public Long getId() {
	return id;
}
public void setId(Long id) {
	this.id = id;
}
public String getFirstName() {
	return firstName;
}
public void setFirstName(String firstName) {
	this.firstName = firstName;
}
public String getLastName() {
	return lastName;
}
public void setLastName(String lastName) {
	this.lastName = lastName;
}
public String getPassword() {
	return password;
}
public void setPassword(String password) {
	this.password = password;
}
public String getEmail() {
	return email;
}
public void setEmail(String email) {
	this.email = email;
}
public Integer getTelephone() {
	return telephone;
}
public void setTelephone(Integer telephone) {
	this.telephone = telephone;
}
@Override
public Collection<? extends GrantedAuthority> getAuthorities() {
	return List.of(new SimpleGrantedAuthority(role.name()));
}
@Override
public String getUsername() {
	return email;
}
@Override
public boolean isAccountNonExpired()
{
	return true;
}
@Override
public boolean isAccountNonLocked()
{
	return true;
}
@Override
public boolean isCredentialsNonExpired()
{
	return true;
}
@Override
public boolean isEnabled()
{
	return true;
}

}
