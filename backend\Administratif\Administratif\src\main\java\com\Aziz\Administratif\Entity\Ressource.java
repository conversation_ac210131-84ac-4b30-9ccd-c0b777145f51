package com.Aziz.Administratif.Entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.util.List;


@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="Ressource")
public class Ressource {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idRessource;

    private String link_path;

    private String nomRessource;


    @OneToMany(mappedBy="ressource")
    @JsonIgnore
    private List<Habilitation> habilitation ;



}
