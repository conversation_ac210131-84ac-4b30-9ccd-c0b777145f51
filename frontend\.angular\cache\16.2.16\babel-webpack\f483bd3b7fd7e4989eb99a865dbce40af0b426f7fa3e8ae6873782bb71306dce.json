{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./authentication.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(next, state) {\n    const token = this.authService.getToken();\n    if (token) {\n      const {\n        role,\n        groupe\n      } = this.getUserDetailsFromToken(token);\n      console.log(\"User role from token:\", role, \"Group:\", groupe);\n      if (role === 'RESPONSABLE') {\n        const allowedGroups = {\n          '/transactions': 'TRANSACTION',\n          '/actions': 'ACTION',\n          '/actionnaires': 'ACTIONNAIRE',\n          '/reports': 'NOTIFICATION',\n          '/port': 'PORTEFEUILLE',\n          '/ResDash': groupe // Always allow responsible users to access their dashboard\n        };\n\n        const expectedGroup = allowedGroups[state.url];\n        if (expectedGroup && groupe !== expectedGroup) {\n          console.warn(\"Access denied for group:\", groupe);\n          this.router.navigate(['/not-authorized']);\n          return false;\n        }\n      } else if (next.data['role'] && next.data['role'].toLowerCase() !== role.toLowerCase()) {\n        this.router.navigate(['/not-authorized']);\n        return false;\n      }\n      return true;\n    }\n    this.router.navigate(['/login']);\n    return false;\n  }\n  getUserDetailsFromToken(token) {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT\n      return {\n        role: payload?.role || '',\n        groupe: payload?.groupe || ''\n      };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return {\n        role: '',\n        groupe: ''\n      };\n    }\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthenticationService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "next", "state", "token", "getToken", "role", "groupe", "getUserDetailsFromToken", "console", "log", "allowedGroups", "expectedGroup", "url", "warn", "navigate", "data", "toLowerCase", "payload", "JSON", "parse", "atob", "split", "e", "error", "i0", "ɵɵinject", "i1", "AuthenticationService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\auth\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { AuthenticationService } from './authentication.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(private authService: AuthenticationService, private router: Router) {}\n\n  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {\n    const token = this.authService.getToken();\n\n    if (token) {\n      const { role, groupe } = this.getUserDetailsFromToken(token);\n      console.log(\"User role from token:\", role, \"Group:\", groupe);\n\n      if (role === 'RESPONSABLE') {\n        const allowedGroups: Record<string, string> = {\n          '/transactions': 'TRANSACTION',\n          '/actions': 'ACTION',\n          '/actionnaires': 'ACTIONNAIRE',\n          '/reports': 'NOTIFICATION',\n          '/port': 'PORTEFEUILLE',\n          '/ResDash': groupe  // Always allow responsible users to access their dashboard\n        };\n\n        const expectedGroup = allowedGroups[state.url];\n\n        if (expectedGroup && groupe !== expectedGroup) {\n          console.warn(\"Access denied for group:\", groupe);\n          this.router.navigate(['/not-authorized']);\n          return false;\n        }\n      } else if (next.data['role'] && next.data['role'].toLowerCase() !== role.toLowerCase()) {\n        this.router.navigate(['/not-authorized']);\n        return false;\n      }\n\n      return true;\n    }\n\n    this.router.navigate(['/login']);\n    return false;\n  }\n\n  private getUserDetailsFromToken(token: string): { role: string, groupe: string } {\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1])); // Decode JWT\n      return { role: payload?.role || '', groupe: payload?.groupe || '' };\n    } catch (e) {\n      console.error('Error decoding token:', e);\n      return { role: '', groupe: '' };\n    }\n  }\n}\n"], "mappings": ";;;AAOA,OAAM,MAAOA,SAAS;EAEpBC,YAAoBC,WAAkC,EAAUC,MAAc;IAA1D,KAAAD,WAAW,GAAXA,WAAW;IAAiC,KAAAC,MAAM,GAANA,MAAM;EAAW;EAEjFC,WAAWA,CAACC,IAA4B,EAAEC,KAA0B;IAClE,MAAMC,KAAK,GAAG,IAAI,CAACL,WAAW,CAACM,QAAQ,EAAE;IAEzC,IAAID,KAAK,EAAE;MACT,MAAM;QAAEE,IAAI;QAAEC;MAAM,CAAE,GAAG,IAAI,CAACC,uBAAuB,CAACJ,KAAK,CAAC;MAC5DK,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEJ,IAAI,EAAE,QAAQ,EAAEC,MAAM,CAAC;MAE5D,IAAID,IAAI,KAAK,aAAa,EAAE;QAC1B,MAAMK,aAAa,GAA2B;UAC5C,eAAe,EAAE,aAAa;UAC9B,UAAU,EAAE,QAAQ;UACpB,eAAe,EAAE,aAAa;UAC9B,UAAU,EAAE,cAAc;UAC1B,OAAO,EAAE,cAAc;UACvB,UAAU,EAAEJ,MAAM,CAAE;SACrB;;QAED,MAAMK,aAAa,GAAGD,aAAa,CAACR,KAAK,CAACU,GAAG,CAAC;QAE9C,IAAID,aAAa,IAAIL,MAAM,KAAKK,aAAa,EAAE;UAC7CH,OAAO,CAACK,IAAI,CAAC,0BAA0B,EAAEP,MAAM,CAAC;UAChD,IAAI,CAACP,MAAM,CAACe,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;UACzC,OAAO,KAAK;;OAEf,MAAM,IAAIb,IAAI,CAACc,IAAI,CAAC,MAAM,CAAC,IAAId,IAAI,CAACc,IAAI,CAAC,MAAM,CAAC,CAACC,WAAW,EAAE,KAAKX,IAAI,CAACW,WAAW,EAAE,EAAE;QACtF,IAAI,CAACjB,MAAM,CAACe,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;QACzC,OAAO,KAAK;;MAGd,OAAO,IAAI;;IAGb,IAAI,CAACf,MAAM,CAACe,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAChC,OAAO,KAAK;EACd;EAEQP,uBAAuBA,CAACJ,KAAa;IAC3C,IAAI;MACF,MAAMc,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACjB,KAAK,CAACkB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,OAAO;QAAEhB,IAAI,EAAEY,OAAO,EAAEZ,IAAI,IAAI,EAAE;QAAEC,MAAM,EAAEW,OAAO,EAAEX,MAAM,IAAI;MAAE,CAAE;KACpE,CAAC,OAAOgB,CAAC,EAAE;MACVd,OAAO,CAACe,KAAK,CAAC,uBAAuB,EAAED,CAAC,CAAC;MACzC,OAAO;QAAEjB,IAAI,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAE;;EAEnC;;;uBAhDWV,SAAS,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATjC,SAAS;MAAAkC,OAAA,EAATlC,SAAS,CAAAmC,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}