{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/groupe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction GroupsComponent_tr_71_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_tr_71_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const groupe_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.deleteGroupe(groupe_r2.idGroupe));\n    });\n    i0.ɵɵtext(1, \"Supprimer\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a1) {\n  return [\"/edit-groupe\", a1];\n};\nfunction GroupsComponent_tr_71_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵtext(1, \"Modifier\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(1, _c0, groupe_r2.idGroupe));\n  }\n}\nfunction GroupsComponent_tr_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtemplate(6, GroupsComponent_tr_71_button_6_Template, 2, 0, \"button\", 49);\n    i0.ɵɵtemplate(7, GroupsComponent_tr_71_button_7_Template, 2, 3, \"button\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const groupe_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r2.idGroupe || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(groupe_r2.nomGroupe);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", groupe_r2.idGroupe);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", groupe_r2.idGroupe);\n  }\n}\nexport class GroupsComponent {\n  constructor(groupeService, router) {\n    this.groupeService = groupeService;\n    this.router = router;\n    this.groupes = []; // List of groups\n    this.newGroupe = {\n      nomGroupe: ''\n    }; // New group model\n    this.editGroupe = null; // Group being edited\n  }\n\n  ngOnInit() {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n  // Load all groups\n  loadGroupes() {\n    this.groupeService.getAllGroupes().subscribe(data => {\n      this.groupes = data;\n    }, err => {\n      console.error('Error loading groups', err);\n    });\n  }\n  // Add a new group\n  addGroupe() {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(() => {\n        this.newGroupe = {\n          nomGroupe: ''\n        }; // Reset input\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error adding group', err);\n      });\n    }\n  }\n  // Set group to edit mode\n  setEditGroupe(groupe) {\n    this.editGroupe = {\n      ...groupe\n    }; // Clone object\n  }\n  // Update a group\n  updateGroupe() {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(() => {\n        this.editGroupe = null; // Reset edit mode\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error updating group', err);\n      });\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n  // Delete a group\n  deleteGroupe(id) {\n    if (id) {\n      this.groupeService.deleteGroupe(id).subscribe(() => {\n        this.loadGroupes(); // Refresh list after deletion\n      }, err => {\n        console.error('Error deleting group', err);\n      });\n    } else {\n      console.error('Invalid group ID');\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function GroupsComponent_Factory(t) {\n      return new (t || GroupsComponent)(i0.ɵɵdirectiveInject(i1.GroupeService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupsComponent,\n      selectors: [[\"app-groups\"]],\n      decls: 81,\n      vars: 2,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"http-equiv\", \"Cache-Control\", \"content\", \"no-cache, no-store, must-revalidate\"], [\"http-equiv\", \"Pragma\", \"content\", \"no-cache\"], [\"http-equiv\", \"Expires\", \"content\", \"0\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"stylesheet\", \"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\"], [\"width\", \"900\", \"height\", \"380\", 2, \"background-color\", \"white\", \"min-height\", \"380px\"], [\"myChart\", \"\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"form-control-dark\", \"w-100\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-light\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/adminDash\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"/users\", 1, \"nav-link\"], [\"data-feather\", \"user\"], [\"href\", \"/groups\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"dropdown-toggle\"], [\"data-feather\", \"calendar\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-sm\"], [4, \"ngFor\", \"ngForOf\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"nomGroupe\"], [\"type\", \"text\", \"id\", \"nomGroupe\", \"name\", \"nomGroupe\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [\"class\", \"btn btn-danger\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-warning\", 3, \"routerLink\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"btn\", \"btn-warning\", 3, \"routerLink\"]],\n      template: function GroupsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"meta\", 5)(7, \"meta\", 6)(8, \"meta\", 7)(9, \"link\", 8)(10, \"link\", 9)(11, \"canvas\", 10, 11);\n          i0.ɵɵelementStart(13, \"title\");\n          i0.ɵɵtext(14, \"Gestion des Groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"link\", 12)(16, \"link\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"body\")(18, \"nav\", 14)(19, \"a\", 15);\n          i0.ɵɵtext(20, \"GTI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"input\", 16);\n          i0.ɵɵelementStart(22, \"ul\", 17)(23, \"li\", 18)(24, \"a\", 19);\n          i0.ɵɵlistener(\"click\", function GroupsComponent_Template_a_click_24_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(25, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21)(28, \"nav\", 22)(29, \"div\", 23)(30, \"ul\", 24)(31, \"li\", 25)(32, \"a\", 26);\n          i0.ɵɵelement(33, \"span\", 27);\n          i0.ɵɵtext(34, \" Dashboard \");\n          i0.ɵɵelementStart(35, \"span\", 28);\n          i0.ɵɵtext(36, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"li\", 25)(38, \"a\", 29);\n          i0.ɵɵelement(39, \"span\", 30);\n          i0.ɵɵtext(40, \" Gestion des utilisateurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\", 25)(42, \"a\", 31);\n          i0.ɵɵelement(43, \"span\", 32);\n          i0.ɵɵtext(44, \" Gestion des groupes \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(45, \"main\", 33)(46, \"div\", 34)(47, \"h1\", 35);\n          i0.ɵɵtext(48, \"Gestion des groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 36)(50, \"div\", 37)(51, \"button\", 38);\n          i0.ɵɵtext(52, \"Import\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"button\", 38);\n          i0.ɵɵtext(54, \"Export\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"button\", 39);\n          i0.ɵɵelement(56, \"span\", 40);\n          i0.ɵɵtext(57, \" This week \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"h2\");\n          i0.ɵɵtext(59, \"Liste des groupes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 41)(61, \"table\", 42)(62, \"thead\")(63, \"tr\")(64, \"th\");\n          i0.ɵɵtext(65, \"ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"th\");\n          i0.ɵɵtext(67, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"th\");\n          i0.ɵɵtext(69, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(70, \"tbody\");\n          i0.ɵɵtemplate(71, GroupsComponent_tr_71_Template, 8, 4, \"tr\", 43);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(72, \"h2\");\n          i0.ɵɵtext(73, \"Ajouter un groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"form\", 44);\n          i0.ɵɵlistener(\"ngSubmit\", function GroupsComponent_Template_form_ngSubmit_74_listener() {\n            return ctx.addGroupe();\n          });\n          i0.ɵɵelementStart(75, \"div\", 45)(76, \"label\", 46);\n          i0.ɵɵtext(77, \"Nom du groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"input\", 47);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_78_listener($event) {\n            return ctx.newGroupe.nomGroupe = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"button\", 48);\n          i0.ɵɵtext(80, \"Ajouter\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(71);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groupes);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.newGroupe.nomGroupe);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm, i2.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "GroupsComponent_tr_71_button_6_Template_button_click_0_listener", "ɵɵrestoreView", "_r7", "groupe_r2", "ɵɵnextContext", "$implicit", "ctx_r5", "ɵɵresetView", "deleteGroupe", "idGroupe", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ɵɵtemplate", "GroupsComponent_tr_71_button_6_Template", "GroupsComponent_tr_71_button_7_Template", "ɵɵadvance", "ɵɵtextInterpolate", "nomGroupe", "GroupsComponent", "constructor", "groupeService", "router", "groupes", "newGroupe", "editGroupe", "ngOnInit", "loadGroupes", "getAllGroupes", "subscribe", "data", "err", "console", "error", "addGroupe", "trim", "setEditGroupe", "groupe", "updateGroupe", "id", "logout", "localStorage", "removeItem", "log", "getItem", "navigate", "ɵɵdirectiveInject", "i1", "GroupeService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "GroupsComponent_Template", "rf", "ctx", "ɵɵelement", "GroupsComponent_Template_a_click_24_listener", "GroupsComponent_tr_71_Template", "GroupsComponent_Template_form_ngSubmit_74_listener", "GroupsComponent_Template_input_ngModelChange_78_listener", "$event"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\groups\\groups.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\groups\\groups.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-groups',\n  templateUrl: './groups.component.html',\n  styleUrls: ['./groups.component.css']\n})\nexport class GroupsComponent implements OnInit {\n  groupes: Groupe[] = []; // List of groups\n  newGroupe: Groupe = { nomGroupe: '' }; // New group model\n  editGroupe: Groupe | null = null; // Group being edited\n\n  constructor(private groupeService: GroupeService,\n    private router: Router) {}\n\n  ngOnInit(): void {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n\n  // Load all groups\n  loadGroupes(): void {\n    this.groupeService.getAllGroupes().subscribe(\n      (data) => {\n        this.groupes = data;\n      },\n      (err) => {\n        console.error('Error loading groups', err);\n      }\n    );\n  }\n\n  // Add a new group\n  addGroupe(): void {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(\n        () => {\n          this.newGroupe = { nomGroupe: '' }; // Reset input\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error adding group', err);\n        }\n      );\n    }\n  }\n\n  // Set group to edit mode\n  setEditGroupe(groupe: Groupe): void {\n    this.editGroupe = { ...groupe }; // Clone object\n  }\n\n  // Update a group\n  updateGroupe(): void {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(\n        () => {\n          this.editGroupe = null; // Reset edit mode\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error updating group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n\n  // Delete a group\n  deleteGroupe(id: number): void {\n    if (id) {\n      this.groupeService.deleteGroupe(id).subscribe(\n        () => {\n          this.loadGroupes(); // Refresh list after deletion\n        },\n        (err) => {\n          console.error('Error deleting group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group ID');\n    }\n  }\n\n  logout(): void {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n}\n", "<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <meta http-equiv=\"Cache-Control\" content=\"no-cache, no-store, must-revalidate\">\n    <meta http-equiv=\"Pragma\" content=\"no-cache\">\n    <meta http-equiv=\"Expires\" content=\"0\">\n\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n    <canvas #myChart width=\"900\" height=\"380\" style=\"background-color: white; min-height: 380px;\"></canvas>\n\n    <title>Gestion des Groupes</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n\n    <!-- Custom styles for this template -->\n  </head>\n\n  <body>\n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"adminDash\">GTI</a>\n      <input class=\"form-control form-control-dark w-100\" type=\"text\" placeholder=\"Search\" aria-label=\"Search\">\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block bg-light sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link active\" href=\"/adminDash\">\n                  <span data-feather=\"home\"></span>\n                  Dashboard <span class=\"sr-only\">(current)</span>\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/users\">\n                  <span data-feather=\"user\"></span>\n                  Gestion des utilisateurs\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/groups\">\n                  <span data-feather=\"users\"></span>\n                  Gestion des groupes\n                </a>\n              </li>\n            </ul>\n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Gestion des groupes</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n                <button class=\"btn btn-sm btn-outline-secondary\">Import</button>\n                <button class=\"btn btn-sm btn-outline-secondary\">Export</button>\n              </div>\n              <button class=\"btn btn-sm btn-outline-secondary dropdown-toggle\">\n                <span data-feather=\"calendar\"></span>\n                This week\n              </button>\n            </div>\n          </div>\n\n          <h2>Liste des groupes</h2>\n          <div class=\"table-responsive\">\n            <table class=\"table table-striped table-sm\">\n              <thead>\n                <tr>\n                  <th>ID</th>\n                  <th>Nom du groupe</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let groupe of groupes\">\n                  <td>{{ groupe.idGroupe || 'N/A' }}</td>\n                  <td>{{ groupe.nomGroupe }}</td>\n                  <td>\n                    <!-- Check if idGroupe is defined before performing actions -->\n                    <button class=\"btn btn-danger\" *ngIf=\"groupe.idGroupe\" (click)=\"deleteGroupe(groupe.idGroupe)\">Supprimer</button>\n                    <button class=\"btn btn-warning\" [routerLink]=\"['/edit-groupe', groupe.idGroupe]\" *ngIf=\"groupe.idGroupe\">Modifier</button>\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n\n          <h2>Ajouter un groupe</h2>\n          <form (ngSubmit)=\"addGroupe()\">\n            <div class=\"form-group\">\n              <label for=\"nomGroupe\">Nom du groupe</label>\n              <input type=\"text\" id=\"nomGroupe\" [(ngModel)]=\"newGroupe.nomGroupe\" name=\"nomGroupe\" class=\"form-control\" required>\n            </div>\n            <button type=\"submit\" class=\"btn btn-primary\">Ajouter</button>\n          </form>\n        </main>\n      </div>\n    </div>\n\n    <!-- Bootstrap core JavaScript -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>feather.replace()</script>\n\n    <!-- Chart.js -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.min.js\"></script>\n  </body>\n</html>\n"], "mappings": ";;;;;;;;IC8FoBA,EAAA,CAAAC,cAAA,iBAA+F;IAAxCD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAN,EAAA,CAAAO,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAU,WAAA,CAAAD,MAAA,CAAAE,YAAA,CAAAL,SAAA,CAAAM,QAAA,CAA6B;IAAA,EAAC;IAACZ,EAAA,CAAAa,MAAA,gBAAS;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;;;IACjHd,EAAA,CAAAC,cAAA,iBAAyG;IAAAD,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAA1Fd,EAAA,CAAAe,UAAA,eAAAf,EAAA,CAAAgB,eAAA,IAAAC,GAAA,EAAAX,SAAA,CAAAM,QAAA,EAAgD;;;;;IANpFZ,EAAA,CAAAC,cAAA,SAAmC;IAC7BD,EAAA,CAAAa,MAAA,GAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAsB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC/Bd,EAAA,CAAAC,cAAA,SAAI;IAEFD,EAAA,CAAAkB,UAAA,IAAAC,uCAAA,qBAAiH;IACjHnB,EAAA,CAAAkB,UAAA,IAAAE,uCAAA,qBAA0H;IAC5HpB,EAAA,CAAAc,YAAA,EAAK;;;;IANDd,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAsB,iBAAA,CAAAhB,SAAA,CAAAM,QAAA,UAA8B;IAC9BZ,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAAsB,iBAAA,CAAAhB,SAAA,CAAAiB,SAAA,CAAsB;IAGQvB,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAAe,UAAA,SAAAT,SAAA,CAAAM,QAAA,CAAqB;IAC6BZ,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAAe,UAAA,SAAAT,SAAA,CAAAM,QAAA,CAAqB;;;ADrF3H,OAAM,MAAOY,eAAe;EAK1BC,YAAoBC,aAA4B,EACtCC,MAAc;IADJ,KAAAD,aAAa,GAAbA,aAAa;IACvB,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,OAAO,GAAa,EAAE,CAAC,CAAC;IACxB,KAAAC,SAAS,GAAW;MAAEN,SAAS,EAAE;IAAE,CAAE,CAAC,CAAC;IACvC,KAAAO,UAAU,GAAkB,IAAI,CAAC,CAAC;EAGP;;EAE3BC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;EACtB;EAEA;EACAA,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,aAAa,EAAE,CAACC,SAAS,CACzCC,IAAI,IAAI;MACP,IAAI,CAACP,OAAO,GAAGO,IAAI;IACrB,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;IAC5C,CAAC,CACF;EACH;EAEA;EACAG,SAASA,CAAA;IACP,IAAI,IAAI,CAACV,SAAS,CAACN,SAAS,CAACiB,IAAI,EAAE,EAAE;MACnC,IAAI,CAACd,aAAa,CAACa,SAAS,CAAC,IAAI,CAACV,SAAS,CAAC,CAACK,SAAS,CACpD,MAAK;QACH,IAAI,CAACL,SAAS,GAAG;UAAEN,SAAS,EAAE;QAAE,CAAE,CAAC,CAAC;QACpC,IAAI,CAACS,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAI,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;MAC1C,CAAC,CACF;;EAEL;EAEA;EACAK,aAAaA,CAACC,MAAc;IAC1B,IAAI,CAACZ,UAAU,GAAG;MAAE,GAAGY;IAAM,CAAE,CAAC,CAAC;EACnC;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACb,UAAU,IAAI,IAAI,CAACA,UAAU,CAAClB,QAAQ,EAAE;MAC/C,IAAI,CAACc,aAAa,CAACiB,YAAY,CAAC,IAAI,CAACb,UAAU,CAAClB,QAAQ,EAAE,IAAI,CAACkB,UAAU,CAAC,CAACI,SAAS,CAClF,MAAK;QACH,IAAI,CAACJ,UAAU,GAAG,IAAI,CAAC,CAAC;QACxB,IAAI,CAACE,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAI,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;;EAEtD;EAEA;EACA3B,YAAYA,CAACiC,EAAU;IACrB,IAAIA,EAAE,EAAE;MACN,IAAI,CAAClB,aAAa,CAACf,YAAY,CAACiC,EAAE,CAAC,CAACV,SAAS,CAC3C,MAAK;QACH,IAAI,CAACF,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAI,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,kBAAkB,CAAC;;EAErC;EAEAO,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCV,OAAO,CAACW,GAAG,CAACF,YAAY,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAnFW1B,eAAe,EAAAxB,EAAA,CAAAmD,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAArD,EAAA,CAAAmD,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAf/B,eAAe;MAAAgC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT5B9D,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAgE,SAAA,cAAsB;UAYtBhE,EAAA,CAAAC,cAAA,aAAO;UAAAD,EAAA,CAAAa,MAAA,2BAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAElCd,EAAA,CAAAgE,SAAA,gBAAmF;UAMrFhE,EAAA,CAAAc,YAAA,EAAO;UAEPd,EAAA,CAAAC,cAAA,YAAM;UAE8DD,EAAA,CAAAa,MAAA,WAAG;UAAAb,EAAA,CAAAc,YAAA,EAAI;UACvEd,EAAA,CAAAgE,SAAA,iBAAyG;UACzGhE,EAAA,CAAAC,cAAA,cAA4B;UAEJD,EAAA,CAAAE,UAAA,mBAAA+D,6CAAA;YAAA,OAASF,GAAA,CAAAlB,MAAA,EAAQ;UAAA,EAAC;UAAC7C,EAAA,CAAAa,MAAA,gBAAQ;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAKzDd,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAAgE,SAAA,gBAAiC;UACjChE,EAAA,CAAAa,MAAA,mBAAU;UAAAb,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAGpDd,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAgE,SAAA,gBAAiC;UACjChE,EAAA,CAAAa,MAAA,kCACF;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAENd,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAgE,SAAA,gBAAkC;UAClChE,EAAA,CAAAa,MAAA,6BACF;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAMZd,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAa,MAAA,2BAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvCd,EAAA,CAAAC,cAAA,eAAsC;UAEeD,EAAA,CAAAa,MAAA,cAAM;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAChEd,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAa,MAAA,cAAM;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAElEd,EAAA,CAAAC,cAAA,kBAAiE;UAC/DD,EAAA,CAAAgE,SAAA,gBAAqC;UACrChE,EAAA,CAAAa,MAAA,mBACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAIbd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,yBAAiB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC1Bd,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAa,MAAA,UAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACXd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,qBAAa;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACtBd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAGpBd,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAkB,UAAA,KAAAgD,8BAAA,iBAQK;UACPlE,EAAA,CAAAc,YAAA,EAAQ;UAIZd,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAa,MAAA,yBAAiB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC1Bd,EAAA,CAAAC,cAAA,gBAA+B;UAAzBD,EAAA,CAAAE,UAAA,sBAAAiE,mDAAA;YAAA,OAAYJ,GAAA,CAAAxB,SAAA,EAAW;UAAA,EAAC;UAC5BvC,EAAA,CAAAC,cAAA,eAAwB;UACCD,EAAA,CAAAa,MAAA,qBAAa;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAC5Cd,EAAA,CAAAC,cAAA,iBAAmH;UAAjFD,EAAA,CAAAE,UAAA,2BAAAkE,yDAAAC,MAAA;YAAA,OAAAN,GAAA,CAAAlC,SAAA,CAAAN,SAAA,GAAA8C,MAAA;UAAA,EAAiC;UAAnErE,EAAA,CAAAc,YAAA,EAAmH;UAErHd,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAa,MAAA,eAAO;UAAAb,EAAA,CAAAc,YAAA,EAAS;;;UAnBnCd,EAAA,CAAAqB,SAAA,IAAU;UAAVrB,EAAA,CAAAe,UAAA,YAAAgD,GAAA,CAAAnC,OAAA,CAAU;UAiBD5B,EAAA,CAAAqB,SAAA,GAAiC;UAAjCrB,EAAA,CAAAe,UAAA,YAAAgD,GAAA,CAAAlC,SAAA,CAAAN,SAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}