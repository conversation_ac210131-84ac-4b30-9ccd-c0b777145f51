package com.Aziz.Client.Entity;


import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name="Action")
public class Action {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idAction;
    private String isinAction;
    private String nomSociete;
    private Double prix;
   // private String proprietaire;
   @CreationTimestamp
   private LocalDateTime DateCreation;



//    @LastModifiedDate
//    private LocalDateTime DateModification;



    @OneToMany(mappedBy = "action",cascade = CascadeType.ALL)
    @JsonBackReference
    @JsonIgnore
    private List<Portefeuille> portefeuilles;

    @OneToMany(mappedBy = "action",cascade = CascadeType.ALL)
    @JsonBackReference
    private List<Cotation> cotations;


    @JsonIgnore
    public List<Actionnaire> getActionnaires() {
        return portefeuilles.stream()
                .map(Portefeuille::getActionnaire)
                .distinct()
                .collect(Collectors.toList());
    }



}
