package com.Aziz.Administratif.Services;

import com.Aziz.Administratif.Entity.Ressource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RessourceService {
    private final com.Aziz.Administratif.Repositories.RessourceRepository RessourceRepository;

//Ajout Ressource
    public void saveRessource(Ressource Ressource){
        RessourceRepository.save(Ressource);
    }




    public List<Ressource> findAllRessources(){
        return RessourceRepository.findAll();
    }

    public Ressource FindRessourceById(Long idRessource) {
        return RessourceRepository.findById(idRessource)
                .orElseThrow(() -> new RuntimeException("Ressource not found"));
    }
    public Ressource FindRessourceByNom(String NomRessource){
        return RessourceRepository.findByNomRessource(NomRessource);
               // .orElseThrow(() -> new RuntimeException("Ressource not found"));
    }






    public void deleteRessource(Long idRessource) {
        RessourceRepository.deleteById(idRessource);
    }




    public Ressource updateRessource(Long idRessource, Ressource updatedRessource) {
        return RessourceRepository.findById(idRessource).map(existingRessource -> {
            // Update user details
            existingRessource.setNomRessource(updatedRessource.getNomRessource());

             // Update the group

            // Save the updated user and return
            return RessourceRepository.save(existingRessource);
        }).orElseThrow(() -> new RuntimeException("Groupe not found"));
    }




}
