{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/habilitation.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction HabilitationComponent_option_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r3.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(user_r3.nom);\n  }\n}\nfunction HabilitationComponent_option_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r4.idGroupe);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(group_r4.nomGroupe);\n  }\n}\nfunction HabilitationComponent_option_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const resource_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", resource_r5.idRessource);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(resource_r5.nomRessource);\n  }\n}\nexport class HabilitationComponent {\n  constructor(habilitationService) {\n    this.habilitationService = habilitationService;\n    this.users = [];\n    this.groups = [];\n    this.resources = [];\n    this.selectedUserId = null;\n    this.selectedGroupId = null;\n    this.selectedResourceId = null;\n  }\n  ngOnInit() {\n    this.loadUsers();\n    this.loadGroups();\n    this.loadResources();\n  }\n  loadUsers() {\n    this.habilitationService.getAllUsers().subscribe(data => {\n      this.users = data;\n    });\n  }\n  loadGroups() {\n    this.habilitationService.getAllGroups().subscribe(data => {\n      this.groups = data;\n    });\n  }\n  loadResources() {\n    this.habilitationService.getAllResources().subscribe(data => {\n      this.resources = data;\n    });\n  }\n  assignHabilitation() {\n    if (this.selectedUserId && this.selectedGroupId && this.selectedResourceId) {\n      this.habilitationService.assignHabilitation(this.selectedUserId, this.selectedGroupId, this.selectedResourceId).subscribe(response => {\n        alert('Habilitation assigned successfully!');\n        // Optionally reload data or clear selections\n        this.loadUsers(); // To update user data after habilitation assignment\n      }, error => {\n        console.error('Error assigning habilitation:', error);\n        alert('Error assigning habilitation.');\n      });\n    } else {\n      alert('Please select a user, group, and resource.');\n    }\n  }\n  static {\n    this.ɵfac = function HabilitationComponent_Factory(t) {\n      return new (t || HabilitationComponent)(i0.ɵɵdirectiveInject(i1.HabilitationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HabilitationComponent,\n      selectors: [[\"app-habilitation\"]],\n      decls: 20,\n      vars: 6,\n      consts: [[1, \"habilitation-container\"], [1, \"form-group\"], [\"for\", \"userSelect\"], [\"id\", \"userSelect\", 3, \"ngModel\", \"ngModelChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"groupSelect\"], [\"id\", \"groupSelect\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"resourceSelect\"], [\"id\", \"resourceSelect\", 3, \"ngModel\", \"ngModelChange\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [3, \"value\"]],\n      template: function HabilitationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Assign Habilitation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"label\", 2);\n          i0.ɵɵtext(5, \"Select User:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"select\", 3);\n          i0.ɵɵlistener(\"ngModelChange\", function HabilitationComponent_Template_select_ngModelChange_6_listener($event) {\n            return ctx.selectedUserId = $event;\n          });\n          i0.ɵɵtemplate(7, HabilitationComponent_option_7_Template, 2, 2, \"option\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 1)(9, \"label\", 5);\n          i0.ɵɵtext(10, \"Select Group:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"select\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function HabilitationComponent_Template_select_ngModelChange_11_listener($event) {\n            return ctx.selectedGroupId = $event;\n          });\n          i0.ɵɵtemplate(12, HabilitationComponent_option_12_Template, 2, 2, \"option\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 1)(14, \"label\", 7);\n          i0.ɵɵtext(15, \"Select Resource:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"select\", 8);\n          i0.ɵɵlistener(\"ngModelChange\", function HabilitationComponent_Template_select_ngModelChange_16_listener($event) {\n            return ctx.selectedResourceId = $event;\n          });\n          i0.ɵɵtemplate(17, HabilitationComponent_option_17_Template, 2, 2, \"option\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function HabilitationComponent_Template_button_click_18_listener() {\n            return ctx.assignHabilitation();\n          });\n          i0.ɵɵtext(19, \"Assign Habilitation\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedUserId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.users);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedGroupId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groups);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedResourceId);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.resources);\n        }\n      },\n      dependencies: [i2.NgForOf, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "user_r3", "id", "ɵɵadvance", "ɵɵtextInterpolate", "nom", "group_r4", "idGroupe", "nomGroupe", "resource_r5", "idRessource", "nomRessource", "HabilitationComponent", "constructor", "habilitationService", "users", "groups", "resources", "selectedUserId", "selectedGroupId", "selectedResourceId", "ngOnInit", "loadUsers", "loadGroups", "loadResources", "getAllUsers", "subscribe", "data", "getAllGroups", "getAllResources", "assignHabilitation", "response", "alert", "error", "console", "ɵɵdirectiveInject", "i1", "HabilitationService", "selectors", "decls", "vars", "consts", "template", "HabilitationComponent_Template", "rf", "ctx", "ɵɵlistener", "HabilitationComponent_Template_select_ngModelChange_6_listener", "$event", "ɵɵtemplate", "HabilitationComponent_option_7_Template", "HabilitationComponent_Template_select_ngModelChange_11_listener", "HabilitationComponent_option_12_Template", "HabilitationComponent_Template_select_ngModelChange_16_listener", "HabilitationComponent_option_17_Template", "HabilitationComponent_Template_button_click_18_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\habilitation\\habilitation.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\habilitation\\habilitation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { HabilitationService } from '../services/habilitation.service';  // Import the service\nimport { User } from '../model/user.model'; // Import User model\nimport { Groupe } from '../model/groupe.model'; // Import Group model\nimport { Ressource } from '../model/ressource.model'; // Import Ressource model\n\n@Component({\n  selector: 'app-habilitation',\n  templateUrl: './habilitation.component.html',\n  styleUrls: ['./habilitation.component.css'],\n})\nexport class HabilitationComponent implements OnInit {\n  users: User[] = [];\n  groups: Groupe[] = [];\n  resources: Ressource[] = [];\n  selectedUserId: number | null = null;\n  selectedGroupId: number | null = null;\n  selectedResourceId: number | null = null;\n\n  constructor(private habilitationService: HabilitationService) {}\n\n  ngOnInit(): void {\n    this.loadUsers();\n    this.loadGroups();\n    this.loadResources();\n  }\n\n  loadUsers() {\n    this.habilitationService.getAllUsers().subscribe((data) => {\n      this.users = data;\n    });\n  }\n\n  loadGroups() {\n    this.habilitationService.getAllGroups().subscribe((data) => {\n      this.groups = data;\n    });\n  }\n\n  loadResources() {\n    this.habilitationService.getAllResources().subscribe((data) => {\n      this.resources = data;\n    });\n  }\n\n  assignHabilitation() {\n    if (this.selectedUserId && this.selectedGroupId && this.selectedResourceId) {\n      this.habilitationService\n        .assignHabilitation(this.selectedUserId, this.selectedGroupId, this.selectedResourceId)\n        .subscribe(\n          (response) => {\n            alert('Habilitation assigned successfully!');\n            // Optionally reload data or clear selections\n            this.loadUsers(); // To update user data after habilitation assignment\n          },\n          (error) => {\n            console.error('Error assigning habilitation:', error);\n            alert('Error assigning habilitation.');\n          }\n        );\n    } else {\n      alert('Please select a user, group, and resource.');\n    }\n  }\n}\n", "<div class=\"habilitation-container\">\n    <h2>Assign Habilitation</h2>\n  \n    <div class=\"form-group\">\n      <label for=\"userSelect\">Select User:</label>\n      <select id=\"userSelect\" [(ngModel)]=\"selectedUserId\">\n        <option *ngFor=\"let user of users\" [value]=\"user.id\">{{ user.nom }}</option>\n      </select>\n    </div>\n  \n    <div class=\"form-group\">\n      <label for=\"groupSelect\">Select Group:</label>\n      <select id=\"groupSelect\" [(ngModel)]=\"selectedGroupId\">\n        <option *ngFor=\"let group of groups\" [value]=\"group.idGroupe\">{{ group.nomGroupe }}</option>\n      </select>\n    </div>\n  \n    <div class=\"form-group\">\n      <label for=\"resourceSelect\">Select Resource:</label>\n      <select id=\"resourceSelect\" [(ngModel)]=\"selectedResourceId\">\n        <option *ngFor=\"let resource of resources\" [value]=\"resource.idRessource\">{{ resource.nomRessource }}</option>\n      </select>\n    </div>\n  \n    <button (click)=\"assignHabilitation()\" class=\"btn btn-primary\">Assign Habilitation</button>\n  </div>\n  "], "mappings": ";;;;;;ICMQA,EAAA,CAAAC,cAAA,iBAAqD;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAzCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,EAAA,CAAiB;IAACN,EAAA,CAAAO,SAAA,GAAc;IAAdP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAI,GAAA,CAAc;;;;;IAOnET,EAAA,CAAAC,cAAA,iBAA8D;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAvDH,EAAA,CAAAI,UAAA,UAAAM,QAAA,CAAAC,QAAA,CAAwB;IAACX,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,iBAAA,CAAAE,QAAA,CAAAE,SAAA,CAAqB;;;;;IAOnFZ,EAAA,CAAAC,cAAA,iBAA0E;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAnEH,EAAA,CAAAI,UAAA,UAAAS,WAAA,CAAAC,WAAA,CAA8B;IAACd,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAQ,iBAAA,CAAAK,WAAA,CAAAE,YAAA,CAA2B;;;ADT7G,OAAM,MAAOC,qBAAqB;EAQhCC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;IAPvC,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,MAAM,GAAa,EAAE;IACrB,KAAAC,SAAS,GAAgB,EAAE;IAC3B,KAAAC,cAAc,GAAkB,IAAI;IACpC,KAAAC,eAAe,GAAkB,IAAI;IACrC,KAAAC,kBAAkB,GAAkB,IAAI;EAEuB;EAE/DC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAF,SAASA,CAAA;IACP,IAAI,CAACR,mBAAmB,CAACW,WAAW,EAAE,CAACC,SAAS,CAAEC,IAAI,IAAI;MACxD,IAAI,CAACZ,KAAK,GAAGY,IAAI;IACnB,CAAC,CAAC;EACJ;EAEAJ,UAAUA,CAAA;IACR,IAAI,CAACT,mBAAmB,CAACc,YAAY,EAAE,CAACF,SAAS,CAAEC,IAAI,IAAI;MACzD,IAAI,CAACX,MAAM,GAAGW,IAAI;IACpB,CAAC,CAAC;EACJ;EAEAH,aAAaA,CAAA;IACX,IAAI,CAACV,mBAAmB,CAACe,eAAe,EAAE,CAACH,SAAS,CAAEC,IAAI,IAAI;MAC5D,IAAI,CAACV,SAAS,GAAGU,IAAI;IACvB,CAAC,CAAC;EACJ;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACZ,cAAc,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACC,kBAAkB,EAAE;MAC1E,IAAI,CAACN,mBAAmB,CACrBgB,kBAAkB,CAAC,IAAI,CAACZ,cAAc,EAAE,IAAI,CAACC,eAAe,EAAE,IAAI,CAACC,kBAAkB,CAAC,CACtFM,SAAS,CACPK,QAAQ,IAAI;QACXC,KAAK,CAAC,qCAAqC,CAAC;QAC5C;QACA,IAAI,CAACV,SAAS,EAAE,CAAC,CAAC;MACpB,CAAC,EACAW,KAAK,IAAI;QACRC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDD,KAAK,CAAC,+BAA+B,CAAC;MACxC,CAAC,CACF;KACJ,MAAM;MACLA,KAAK,CAAC,4CAA4C,CAAC;;EAEvD;;;uBApDWpB,qBAAqB,EAAAhB,EAAA,CAAAuC,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAArBzB,qBAAqB;MAAA0B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXlChD,EAAA,CAAAC,cAAA,aAAoC;UAC5BD,EAAA,CAAAE,MAAA,0BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5BH,EAAA,CAAAC,cAAA,aAAwB;UACED,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,gBAAqD;UAA7BD,EAAA,CAAAkD,UAAA,2BAAAC,+DAAAC,MAAA;YAAA,OAAAH,GAAA,CAAA3B,cAAA,GAAA8B,MAAA;UAAA,EAA4B;UAClDpD,EAAA,CAAAqD,UAAA,IAAAC,uCAAA,oBAA4E;UAC9EtD,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,aAAwB;UACGD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9CH,EAAA,CAAAC,cAAA,iBAAuD;UAA9BD,EAAA,CAAAkD,UAAA,2BAAAK,gEAAAH,MAAA;YAAA,OAAAH,GAAA,CAAA1B,eAAA,GAAA6B,MAAA;UAAA,EAA6B;UACpDpD,EAAA,CAAAqD,UAAA,KAAAG,wCAAA,oBAA4F;UAC9FxD,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,cAAwB;UACMD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,iBAA6D;UAAjCD,EAAA,CAAAkD,UAAA,2BAAAO,gEAAAL,MAAA;YAAA,OAAAH,GAAA,CAAAzB,kBAAA,GAAA4B,MAAA;UAAA,EAAgC;UAC1DpD,EAAA,CAAAqD,UAAA,KAAAK,wCAAA,oBAA8G;UAChH1D,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,iBAA+D;UAAvDD,EAAA,CAAAkD,UAAA,mBAAAS,wDAAA;YAAA,OAASV,GAAA,CAAAf,kBAAA,EAAoB;UAAA,EAAC;UAAyBlC,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UAnBjEH,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAI,UAAA,YAAA6C,GAAA,CAAA3B,cAAA,CAA4B;UACzBtB,EAAA,CAAAO,SAAA,GAAQ;UAARP,EAAA,CAAAI,UAAA,YAAA6C,GAAA,CAAA9B,KAAA,CAAQ;UAMVnB,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAI,UAAA,YAAA6C,GAAA,CAAA1B,eAAA,CAA6B;UAC1BvB,EAAA,CAAAO,SAAA,GAAS;UAATP,EAAA,CAAAI,UAAA,YAAA6C,GAAA,CAAA7B,MAAA,CAAS;UAMTpB,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAI,UAAA,YAAA6C,GAAA,CAAAzB,kBAAA,CAAgC;UAC7BxB,EAAA,CAAAO,SAAA,GAAY;UAAZP,EAAA,CAAAI,UAAA,YAAA6C,GAAA,CAAA5B,SAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}