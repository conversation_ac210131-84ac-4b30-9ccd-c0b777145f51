package com.Rayen.User;

import com.Rayen.User.Entity.User;
import com.Rayen.User.Enum.Role;
import com.stoyanr.evictor.queue.PriorityEvictionQueue;
import lombok.RequiredArgsConstructor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class UserService {
    private final UserRepository UserRepository;

    @Autowired
    private ActionnaireClient ActionnaireClient;


    public void saveUser(User User){
        UserRepository.save(User);
    }

    public List<User> findAllUsers(){
        return UserRepository.findAll();
    }

    public List<User> findAllByRole(Role role) {
        return UserRepository.findAllByRole(role);

    }

    public void deleteUser(Long userId) {
        UserRepository.deleteById(userId);
    }

    public User updateUser(Long userId, User updatedUser) {
        return UserRepository.findById(userId).map(existingUser -> {
            // Update user details
            existingUser.setNom(updatedUser.getNom());
            existingUser.setPrenom(updatedUser.getPrenom());
            existingUser.setEmail(updatedUser.getEmail());
            existingUser.setTelephone(updatedUser.getTelephone());
            existingUser.setGroupe(updatedUser.getGroupe()); // Update the group

            // Save the updated user and return
            return UserRepository.save(existingUser);
        }).orElseThrow(() -> new RuntimeException("User not found"));
    }








    //relation avec actionnaire
    public FullUserResponse findUserswithActionnaire(Long userId) {
        var user=UserRepository.findById(userId)
                .orElse(
                        User.builder()
                                .nom("Not_Found")
                                .prenom("Not_Found")
                                .email("Not_Found")
                                .build()
                );
        var actionnaire= ActionnaireClient.findActionnairesbyUser(userId);
        return FullUserResponse.builder()
                .nom(user.getNom())
                .prenom(user.getPrenom())
                .email(user.getEmail())
                .telephone(user.getTelephone())
                .Actionnaire(actionnaire)
                .build();
    }
}
