{"ast": null, "code": "import * as feather from 'feather-icons';\nimport { Chart } from 'chart.js';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"myChart\"];\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n    font-size: .875rem;\\n  }\\n  \\n  .feather[_ngcontent-%COMP%] {\\n    width: 16px;\\n    height: 16px;\\n    vertical-align: text-bottom;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 100; \\n\\n    padding: 0;\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);\\n  }\\n  \\n  .sidebar-sticky[_ngcontent-%COMP%] {\\n    position: sticky;\\n    top: 48px; \\n\\n    height: calc(100vh - 48px);\\n    padding-top: .5rem;\\n    overflow-x: hidden;\\n    overflow-y: auto; \\n\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n    font-weight: 500;\\n    color: #333;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    color: #999;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n    color: #007bff;\\n  }\\n  \\n  .sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n    color: inherit;\\n  }\\n  \\n  .sidebar-heading[_ngcontent-%COMP%] {\\n    font-size: .75rem;\\n    text-transform: uppercase;\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .navbar-brand[_ngcontent-%COMP%] {\\n    padding-top: .75rem;\\n    padding-bottom: .75rem;\\n    font-size: 1rem;\\n    background-color: rgba(0, 0, 0, .25);\\n    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);\\n  }\\n  \\n  .navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    padding: .75rem 1rem;\\n    border-width: 0;\\n    border-radius: 0;\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%] {\\n    color: #fff;\\n    background-color: rgba(255, 255, 255, .1);\\n    border-color: rgba(255, 255, 255, .1);\\n  }\\n  \\n  .form-control-dark[_ngcontent-%COMP%]:focus {\\n    border-color: transparent;\\n    box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n  }\\n  \\n  \\n\\n\\n\\n  \\n  .border-top[_ngcontent-%COMP%] { border-top: 1px solid #e5e5e5; }\\n  .border-bottom[_ngcontent-%COMP%] { border-bottom: 1px solid #e5e5e5; }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class ResponDashboardComponent {\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n    // Ensure chart element exists\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement;\n      new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday'],\n          datasets: [{\n            data: [15339, 21345, 18483],\n            backgroundColor: 'transparent',\n            borderColor: '#007bff',\n            borderWidth: 4,\n            pointBackgroundColor: '#007bff'\n          }]\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function ResponDashboardComponent_Factory(t) {\n      return new (t || ResponDashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResponDashboardComponent,\n      selectors: [[\"app-respon-dashboard\"]],\n      viewQuery: function ResponDashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 94,\n      vars: 0,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"href\", \"https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css\", \"rel\", \"stylesheet\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"#\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"form-control-dark\", \"w-100\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [\"href\", \"/signup\", 1, \"nav-link\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-light\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"#\", 1, \"nav-link\", \"active\"], [\"data-feather\", \"home\"], [1, \"sr-only\"], [\"href\", \"#\", 1, \"nav-link\"], [\"data-feather\", \"file\"], [\"data-feather\", \"shopping-cart\"], [\"data-feather\", \"users\"], [\"data-feather\", \"bar-chart-2\"], [\"data-feather\", \"layers\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [\"width\", \"900\", \"height\", \"380\", 1, \"my-4\"], [\"myChart\", \"\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\", \"table-sm\"]],\n      template: function ResponDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4);\n          i0.ɵɵelementStart(6, \"title\");\n          i0.ɵɵtext(7, \"Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"link\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"body\")(10, \"nav\", 6)(11, \"a\", 7);\n          i0.ɵɵtext(12, \"Company name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"input\", 8);\n          i0.ɵɵelementStart(14, \"ul\", 9)(15, \"li\", 10)(16, \"a\", 11);\n          i0.ɵɵtext(17, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(18, \"div\", 12)(19, \"div\", 13)(20, \"nav\", 14)(21, \"div\", 15)(22, \"ul\", 16)(23, \"li\", 17)(24, \"a\", 18);\n          i0.ɵɵelement(25, \"span\", 19);\n          i0.ɵɵtext(26, \" Dashboard \");\n          i0.ɵɵelementStart(27, \"span\", 20);\n          i0.ɵɵtext(28, \"(current)\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"li\", 17)(30, \"a\", 21);\n          i0.ɵɵelement(31, \"span\", 22);\n          i0.ɵɵtext(32, \" Orders \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"li\", 17)(34, \"a\", 21);\n          i0.ɵɵelement(35, \"span\", 23);\n          i0.ɵɵtext(36, \" Products \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"li\", 17)(38, \"a\", 21);\n          i0.ɵɵelement(39, \"span\", 24);\n          i0.ɵɵtext(40, \" Customers \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\", 17)(42, \"a\", 21);\n          i0.ɵɵelement(43, \"span\", 25);\n          i0.ɵɵtext(44, \" Reports \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"li\", 17)(46, \"a\", 21);\n          i0.ɵɵelement(47, \"span\", 26);\n          i0.ɵɵtext(48, \" Integrations \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(49, \"main\", 27)(50, \"div\", 28)(51, \"h1\", 29);\n          i0.ɵɵtext(52, \"Dashboard\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(53, \"canvas\", 30, 31);\n          i0.ɵɵelementStart(55, \"h2\");\n          i0.ɵɵtext(56, \"Section title\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 32)(58, \"table\", 33)(59, \"thead\")(60, \"tr\")(61, \"th\");\n          i0.ɵɵtext(62, \"#\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\");\n          i0.ɵɵtext(64, \"Header\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\");\n          i0.ɵɵtext(66, \"Header\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\");\n          i0.ɵɵtext(68, \"Header\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"th\");\n          i0.ɵɵtext(70, \"Header\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(71, \"tbody\")(72, \"tr\")(73, \"td\");\n          i0.ɵɵtext(74, \"1,001\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"td\");\n          i0.ɵɵtext(76, \"Lorem\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"td\");\n          i0.ɵɵtext(78, \"ipsum\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"td\");\n          i0.ɵɵtext(80, \"dolor\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"td\");\n          i0.ɵɵtext(82, \"sit\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"tr\")(84, \"td\");\n          i0.ɵɵtext(85, \"1,002\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"td\");\n          i0.ɵɵtext(87, \"amet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"td\");\n          i0.ɵɵtext(89, \"consectetur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"td\");\n          i0.ɵɵtext(91, \"adipiscing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"td\");\n          i0.ɵɵtext(93, \"elit\");\n          i0.ɵɵelementEnd()()()()()()()()()();\n        }\n      },\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "Chart", "ResponDashboardComponent", "ngAfterViewInit", "replace", "myChartRef", "ctx", "nativeElement", "type", "data", "labels", "datasets", "backgroundColor", "borderColor", "borderWidth", "pointBackgroundColor", "selectors", "viewQuery", "ResponDashboardComponent_Query", "rf", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\respon-dashboard\\respon-dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\respon-dashboard\\respon-dashboard.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, ViewChild } from '@angular/core';\nimport * as feather from 'feather-icons';\nimport { Chart } from 'chart.js';\n\n@Component({\n  selector: 'app-respon-dashboard',\n  templateUrl: './respon-dashboard.component.html',\n  styleUrls: ['../../dashboard.css']\n})\nexport class ResponDashboardComponent implements AfterViewInit { \n  @ViewChild('myChart') myChartRef!: ElementRef;\n\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n\n    // Ensure chart element exists\n    if (this.myChartRef) {\n      const ctx = this.myChartRef.nativeElement as HTMLCanvasElement;\n      new Chart(ctx, {\n        type: 'line',\n        data: {\n          labels: ['Sunday', 'Monday', 'Tuesday'],\n          datasets: [\n            {\n              data: [15339, 21345, 18483],\n              backgroundColor: 'transparent',\n              borderColor: '#007bff',\n              borderWidth: 4,\n              pointBackgroundColor: '#007bff',\n            },\n          ],\n        },\n      });\n    }\n  }\n}\n", "<!doctype html>\r\n<html lang=\"en\">\r\n  <head>\r\n    <meta charset=\"utf-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\r\n    <meta name=\"description\" content=\"\">\r\n    <meta name=\"author\" content=\"\">\r\n    <title>Dashboard</title>\r\n\r\n    <!-- Bootstrap CSS -->\r\n    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">\r\n    <link href=\"../../dashboard.css\" rel=\"stylesheet\">\r\n  </head>\r\n\r\n  <body>\r\n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\r\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"#\">Company name</a>\r\n      <input class=\"form-control form-control-dark w-100\" type=\"text\" placeholder=\"Search\" aria-label=\"Search\">\r\n      <ul class=\"navbar-nav px-3\">\r\n        <li class=\"nav-item text-nowrap\">\r\n          <a class=\"nav-link\" href=\"/signup\">Sign out</a>\r\n        </li>\r\n      </ul>\r\n    </nav>\r\n\r\n    <div class=\"container-fluid\">\r\n      <div class=\"row\">\r\n        <nav class=\"col-md-2 d-none d-md-block bg-light sidebar\">\r\n          <div class=\"sidebar-sticky\">\r\n            <ul class=\"nav flex-column\">\r\n              <li class=\"nav-item\">\r\n                <a class=\"nav-link active\" href=\"#\">\r\n                  <span data-feather=\"home\"></span>\r\n                  Dashboard <span class=\"sr-only\">(current)</span>\r\n                </a>\r\n              </li>\r\n              <li class=\"nav-item\">\r\n                <a class=\"nav-link\" href=\"#\">\r\n                  <span data-feather=\"file\"></span>\r\n                  Orders\r\n                </a>\r\n              </li>\r\n              <li class=\"nav-item\">\r\n                <a class=\"nav-link\" href=\"#\">\r\n                  <span data-feather=\"shopping-cart\"></span>\r\n                  Products\r\n                </a>\r\n              </li>\r\n              <li class=\"nav-item\">\r\n                <a class=\"nav-link\" href=\"#\">\r\n                  <span data-feather=\"users\"></span>\r\n                  Customers\r\n                </a>\r\n              </li>\r\n              <li class=\"nav-item\">\r\n                <a class=\"nav-link\" href=\"#\">\r\n                  <span data-feather=\"bar-chart-2\"></span>\r\n                  Reports\r\n                </a>\r\n              </li>\r\n              <li class=\"nav-item\">\r\n                <a class=\"nav-link\" href=\"#\">\r\n                  <span data-feather=\"layers\"></span>\r\n                  Integrations\r\n                </a>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n        </nav>\r\n\r\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\r\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\r\n            <h1 class=\"h2\">Dashboard</h1>\r\n          </div>\r\n\r\n          <!-- Chart Canvas -->\r\n          <canvas #myChart class=\"my-4\" width=\"900\" height=\"380\"></canvas>\r\n\r\n          <h2>Section title</h2>\r\n          <div class=\"table-responsive\">\r\n            <table class=\"table table-striped table-sm\">\r\n              <thead>\r\n                <tr>\r\n                  <th>#</th>\r\n                  <th>Header</th>\r\n                  <th>Header</th>\r\n                  <th>Header</th>\r\n                  <th>Header</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                <tr>\r\n                  <td>1,001</td>\r\n                  <td>Lorem</td>\r\n                  <td>ipsum</td>\r\n                  <td>dolor</td>\r\n                  <td>sit</td>\r\n                </tr>\r\n                <tr>\r\n                  <td>1,002</td>\r\n                  <td>amet</td>\r\n                  <td>consectetur</td>\r\n                  <td>adipiscing</td>\r\n                  <td>elit</td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Bootstrap and Dependencies -->\r\n    <script src=\"https://code.jquery.com/jquery-3.6.0.min.js\"></script>\r\n    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js\"></script>\r\n\r\n    <!-- Feather Icons -->\r\n    <script src=\"https://unpkg.com/feather-icons\"></script>\r\n    <script>\r\n      feather.replace();\r\n    </script>\r\n\r\n    <!-- Chart.js -->\r\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\r\n    <script>\r\n      document.addEventListener(\"DOMContentLoaded\", function() {\r\n        var ctx = document.querySelector(\"#myChart\").getContext('2d');\r\n        new Chart(ctx, {\r\n          type: 'line',\r\n          data: {\r\n            labels: [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"],\r\n            datasets: [{\r\n              data: [15339, 21345, 18483, 24003, 23489, 24092, 12034],\r\n              lineTension: 0,\r\n              backgroundColor: 'transparent',\r\n              borderColor: '#007bff',\r\n              borderWidth: 4,\r\n              pointBackgroundColor: '#007bff'\r\n            }]\r\n          },\r\n          options: {\r\n            scales: {\r\n              yAxes: [{\r\n                ticks: {\r\n                  beginAtZero: false\r\n                }\r\n              }]\r\n            },\r\n            legend: {\r\n              display: false\r\n            }\r\n          }\r\n        });\r\n      });\r\n    </script>\r\n  </body>\r\n</html>\r\n"], "mappings": "AACA,OAAO,KAAKA,OAAO,MAAM,eAAe;AACxC,SAASC,KAAK,QAAQ,UAAU;;;;AAOhC,OAAM,MAAOC,wBAAwB;EAGnCC,eAAeA,CAAA;IACbH,OAAO,CAACI,OAAO,EAAE,CAAC,CAAC;IAEnB;IACA,IAAI,IAAI,CAACC,UAAU,EAAE;MACnB,MAAMC,GAAG,GAAG,IAAI,CAACD,UAAU,CAACE,aAAkC;MAC9D,IAAIN,KAAK,CAACK,GAAG,EAAE;QACbE,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE;UACJC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;UACvCC,QAAQ,EAAE,CACR;YACEF,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;YAC3BG,eAAe,EAAE,aAAa;YAC9BC,WAAW,EAAE,SAAS;YACtBC,WAAW,EAAE,CAAC;YACdC,oBAAoB,EAAE;WACvB;;OAGN,CAAC;;EAEN;;;uBAzBWb,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAc,SAAA;MAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAb,GAAA;QAAA,IAAAa,EAAA;;;;;;;;;;;;;UCRrCC,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAE,SAAA,cAAsB;UAItBF,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAG,MAAA,gBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAGxBJ,EAAA,CAAAE,SAAA,cAAsG;UAExGF,EAAA,CAAAI,YAAA,EAAO;UAEPJ,EAAA,CAAAC,cAAA,WAAM;UAEsDD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACxEJ,EAAA,CAAAE,SAAA,gBAAyG;UACzGF,EAAA,CAAAC,cAAA,aAA4B;UAEWD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAKrDJ,EAAA,CAAAC,cAAA,eAA6B;UAOfD,EAAA,CAAAE,SAAA,gBAAiC;UACjCF,EAAA,CAAAG,MAAA,mBAAU;UAAAH,EAAA,CAAAC,cAAA,gBAAsB;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAGpDJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAiC;UACjCF,EAAA,CAAAG,MAAA,gBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAA0C;UAC1CF,EAAA,CAAAG,MAAA,kBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAkC;UAClCF,EAAA,CAAAG,MAAA,mBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAwC;UACxCF,EAAA,CAAAG,MAAA,iBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAENJ,EAAA,CAAAC,cAAA,cAAqB;UAEjBD,EAAA,CAAAE,SAAA,gBAAmC;UACnCF,EAAA,CAAAG,MAAA,sBACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAMZJ,EAAA,CAAAC,cAAA,gBAAkE;UAE/CD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAI/BJ,EAAA,CAAAE,SAAA,sBAAgE;UAEhEF,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACtBJ,EAAA,CAAAC,cAAA,eAA8B;UAIlBD,EAAA,CAAAG,MAAA,SAAC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACVJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACfJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACfJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACfJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGnBJ,EAAA,CAAAC,cAAA,aAAO;UAECD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,WAAG;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEdJ,EAAA,CAAAC,cAAA,UAAI;UACED,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACbJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,mBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnBJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}