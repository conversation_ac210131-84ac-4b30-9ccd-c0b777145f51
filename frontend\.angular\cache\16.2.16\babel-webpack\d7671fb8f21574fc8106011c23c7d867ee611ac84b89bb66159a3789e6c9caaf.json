{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction AddResponsableComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1, \" Nom is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1, \" Pr\\u00E9nom is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1, \" A valid email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_29_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_29_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone cannot be more than 8 digits.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, AddResponsableComponent_div_29_div_1_Template, 2, 0, \"div\", 28);\n    i0.ɵɵtemplate(2, AddResponsableComponent_div_29_div_2_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r7 = i0.ɵɵreference(27);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r7.errors == null ? null : _r7.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (_r7.value == null ? null : _r7.value.toString().length) > 8);\n  }\n}\nfunction AddResponsableComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1, \" Password is required (minimum 8 characters). \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddResponsableComponent_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r16.idGroupe);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", group_r16.nomGroupe, \" \");\n  }\n}\nfunction AddResponsableComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1, \" Group is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class AddResponsableComponent {\n  constructor(responsableService, router) {\n    this.responsableService = responsableService;\n    this.router = router;\n    this.newResponsable = {\n      id: 0,\n      firstName: '',\n      lastName: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupeId: 0 // Use groupeId instead of groupe object\n    };\n\n    this.groups = [];\n  }\n  ngOnInit() {\n    // Fetch the list of groups\n    this.responsableService.getGroups().subscribe(data => {\n      this.groups = data; // Store the groups to bind to the dropdown\n    });\n  }\n  // Method to add the new responsable\n  addResponsable() {\n    if (!this.newResponsable.groupeId) {\n      alert('Please select a valid group');\n      return;\n    }\n    if (!this.newResponsable.password) {\n      alert('Please enter a password');\n      return;\n    }\n    // Log the object to verify\n    console.log('Adding Responsable:', this.newResponsable);\n    // Send the request to the backend\n    this.responsableService.addResponsable(this.newResponsable).subscribe(response => {\n      console.log('Responsable added successfully:', response);\n      // Reset the form after successful creation\n      this.newResponsable = {\n        firstName: '',\n        lastName: '',\n        email: '',\n        telephone: '',\n        role: 'RESPONSABLE',\n        password: '',\n        groupeId: 0 // Reset selected group\n      };\n\n      this.router.navigate(['/users']);\n    }, error => {\n      console.error('Error adding responsable:', error);\n    });\n  }\n  cancelEdit() {\n    // Reset form data (optional, if you want to clear the form fields on cancel)\n    this.newResponsable = {\n      id: 0,\n      firstName: '',\n      lastName: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupeId: 0\n    };\n    // Navigate to the users list or a different page (replace '/users' with your desired route)\n    this.router.navigateByUrl('/users');\n  }\n  static {\n    this.ɵfac = function AddResponsableComponent_Factory(t) {\n      return new (t || AddResponsableComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddResponsableComponent,\n      selectors: [[\"app-add-responsable\"]],\n      decls: 48,\n      vars: 14,\n      consts: [[1, \"container\"], [3, \"ngSubmit\"], [\"responsableForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"nom\"], [\"type\", \"text\", \"id\", \"nom\", \"name\", \"nom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"nom\", \"ngModel\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"for\", \"prenom\"], [\"type\", \"text\", \"id\", \"prenom\", \"name\", \"prenom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"prenom\", \"ngModel\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"email\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"email\", \"ngModel\"], [\"for\", \"telephone\"], [\"type\", \"number\", \"id\", \"telephone\", \"name\", \"telephone\", \"required\", \"\", \"maxlength\", \"8\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"telephone\", \"ngModel\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"minlength\", \"8\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"password\", \"ngModel\"], [\"for\", \"groupe\"], [\"id\", \"groupe\", \"name\", \"groupeId\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"groupe\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-between\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"text-danger\"], [4, \"ngIf\"], [3, \"value\"]],\n      template: function AddResponsableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Ajouter un nouveau responsable\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"form\", 1, 2);\n          i0.ɵɵlistener(\"ngSubmit\", function AddResponsableComponent_Template_form_ngSubmit_3_listener() {\n            return ctx.addResponsable();\n          });\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"label\", 4);\n          i0.ɵɵtext(7, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"input\", 5, 6);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_8_listener($event) {\n            return ctx.newResponsable.firstName = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, AddResponsableComponent_div_10_Template, 2, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 3)(12, \"label\", 8);\n          i0.ɵɵtext(13, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"input\", 9, 10);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_14_listener($event) {\n            return ctx.newResponsable.lastName = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(16, AddResponsableComponent_div_16_Template, 2, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 3)(18, \"label\", 11);\n          i0.ɵɵtext(19, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"input\", 12, 13);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_20_listener($event) {\n            return ctx.newResponsable.email = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(22, AddResponsableComponent_div_22_Template, 2, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 3)(24, \"label\", 14);\n          i0.ɵɵtext(25, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"input\", 15, 16);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_26_listener($event) {\n            return ctx.newResponsable.telephone = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(28, \" /> \");\n          i0.ɵɵtemplate(29, AddResponsableComponent_div_29_Template, 3, 2, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 3)(31, \"label\", 17);\n          i0.ɵɵtext(32, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"input\", 18, 19);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_33_listener($event) {\n            return ctx.newResponsable.password = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, AddResponsableComponent_div_35_Template, 2, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 3)(37, \"label\", 20);\n          i0.ɵɵtext(38, \"Group\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"select\", 21, 22);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_select_ngModelChange_39_listener($event) {\n            return ctx.newResponsable.groupeId = $event;\n          });\n          i0.ɵɵtemplate(41, AddResponsableComponent_option_41_Template, 2, 2, \"option\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, AddResponsableComponent_div_42_Template, 2, 0, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 24)(44, \"button\", 25);\n          i0.ɵɵtext(45, \" Ajouter \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function AddResponsableComponent_Template_button_click_46_listener() {\n            return ctx.cancelEdit();\n          });\n          i0.ɵɵtext(47, \" Cancel \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(4);\n          const _r1 = i0.ɵɵreference(9);\n          const _r3 = i0.ɵɵreference(15);\n          const _r5 = i0.ɵɵreference(21);\n          const _r7 = i0.ɵɵreference(27);\n          const _r9 = i0.ɵɵreference(34);\n          const _r11 = i0.ɵɵreference(40);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.firstName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r1.invalid && _r1.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.lastName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r3.invalid && _r3.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r5.invalid && _r5.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.telephone);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", _r7.invalid && _r7.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r9.invalid && _r9.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.groupeId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groups);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r11.invalid && _r11.touched);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", _r0.invalid);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.ɵNgNoValidate, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.DefaultValueAccessor, i4.NumberValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.MinLengthValidator, i4.MaxLengthValidator, i4.EmailValidator, i4.NgModel, i4.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "AddResponsableComponent_div_29_div_1_Template", "AddResponsableComponent_div_29_div_2_Template", "ɵɵadvance", "ɵɵproperty", "_r7", "errors", "value", "toString", "length", "group_r16", "idGroupe", "ɵɵtextInterpolate1", "nomGroupe", "AddResponsableComponent", "constructor", "responsableService", "router", "newResponsable", "id", "firstName", "lastName", "email", "telephone", "role", "password", "groupeId", "groups", "ngOnInit", "getGroups", "subscribe", "data", "addResponsable", "alert", "console", "log", "response", "navigate", "error", "cancelEdit", "navigateByUrl", "ɵɵdirectiveInject", "i1", "ResponsableService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AddResponsableComponent_Template", "rf", "ctx", "ɵɵlistener", "AddResponsableComponent_Template_form_ngSubmit_3_listener", "AddResponsableComponent_Template_input_ngModelChange_8_listener", "$event", "AddResponsableComponent_div_10_Template", "AddResponsableComponent_Template_input_ngModelChange_14_listener", "AddResponsableComponent_div_16_Template", "AddResponsableComponent_Template_input_ngModelChange_20_listener", "AddResponsableComponent_div_22_Template", "AddResponsableComponent_Template_input_ngModelChange_26_listener", "AddResponsableComponent_div_29_Template", "AddResponsableComponent_Template_input_ngModelChange_33_listener", "AddResponsableComponent_div_35_Template", "AddResponsableComponent_Template_select_ngModelChange_39_listener", "AddResponsableComponent_option_41_Template", "AddResponsableComponent_div_42_Template", "AddResponsableComponent_Template_button_click_46_listener", "_r1", "invalid", "touched", "_r3", "_r5", "_r9", "_r11", "_r0"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ResponsableService } from '../responsable.service';\nimport { Responsable } from '../model/respons.model';  // Import Responsable model\nimport { Groupe } from '../model/groupe.model';\nimport { Router } from '@angular/router';  // Import Router\n\n\n@Component({\n  selector: 'app-add-responsable',\n  templateUrl: './add-responsable.component.html',\n  styleUrls: ['./add-responsable.component.css']\n})\nexport class AddResponsableComponent implements OnInit {\n  newResponsable: Responsable = {  \n    id: 0, \n    firstName: '', \n    lastName: '', \n    email: '', \n    telephone: '', \n    role: 'RESPONSABLE',  \n    password: '',\n    groupeId: 0,  // Use groupeId instead of groupe object\n  };\n\n  groups: Groupe[] = [];\n  console: any;\n\n  constructor(private responsableService: ResponsableService ,private router: Router) {}\n\n  ngOnInit(): void {\n    // Fetch the list of groups\n    this.responsableService.getGroups().subscribe((data) => {\n      this.groups = data;  // Store the groups to bind to the dropdown\n    });\n  }\n\n  // Method to add the new responsable\n  addResponsable(): void {\n    if (!this.newResponsable.groupeId) {\n      alert('Please select a valid group');\n      return;\n    }\n\n    if (!this.newResponsable.password) {\n      alert('Please enter a password');\n      return;\n    }\n\n    // Log the object to verify\n    console.log('Adding Responsable:', this.newResponsable);\n\n    // Send the request to the backend\n    this.responsableService.addResponsable(this.newResponsable).subscribe(\n      (response) => {\n        console.log('Responsable added successfully:', response);\n\n        // Reset the form after successful creation\n        this.newResponsable = {\n          firstName: '',\n          lastName: '',\n          email: '',\n          telephone: '',\n          role: 'RESPONSABLE',\n          password: '',\n          groupeId: 0,  // Reset selected group\n        };\n        this.router.navigate(['/users']);\n\n      },\n      (error) => {\n        console.error('Error adding responsable:', error);\n      }\n    );\n  }\n\n  cancelEdit() {\n    // Reset form data (optional, if you want to clear the form fields on cancel)\n    this.newResponsable = {\n      id: 0,\n      firstName: '',\n      lastName: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupeId: 0,\n    };\n\n    // Navigate to the users list or a different page (replace '/users' with your desired route)\n    this.router.navigateByUrl('/users');\n  }\n}\n", "<div class=\"container\">\n  <h2>Ajouter un nouveau responsable</h2>\n  <form (ngSubmit)=\"addResponsable()\" #responsableForm=\"ngForm\">\n    <div class=\"form-group\">\n      <label for=\"nom\">Nom</label>\n      <input\n        type=\"text\"\n        class=\"form-control\"\n        id=\"nom\"\n        [(ngModel)]=\"newResponsable.firstName\"\n        name=\"nom\"\n        required\n        #nom=\"ngModel\"\n      />\n      <!-- Error message for 'Nom' -->\n      <div *ngIf=\"nom.invalid && nom.touched\" class=\"text-danger\">\n        Nom is required.\n      </div>\n    </div>\n\n    <div class=\"form-group\">\n      <label for=\"prenom\">Prénom</label>\n      <input\n        type=\"text\"\n        class=\"form-control\"\n        id=\"prenom\"\n        [(ngModel)]=\"newResponsable.lastName\"\n        name=\"prenom\"\n        required\n        #prenom=\"ngModel\"\n      />\n      <!-- Error message for 'Prénom' -->\n      <div *ngIf=\"prenom.invalid && prenom.touched\" class=\"text-danger\">\n        Prénom is required.\n      </div>\n    </div>\n\n    <div class=\"form-group\">\n      <label for=\"email\">Email</label>\n      <input\n        type=\"email\"\n        class=\"form-control\"\n        id=\"email\"\n        [(ngModel)]=\"newResponsable.email\"\n        name=\"email\"\n        required\n        email\n        #email=\"ngModel\"\n      />\n      <!-- Error message for 'Email' -->\n      <div *ngIf=\"email.invalid && email.touched\" class=\"text-danger\">\n        A valid email is required.\n      </div>\n    </div>\n\n    <div class=\"form-group\">\n      <label for=\"telephone\">Téléphone</label>\n      <input\n        type=\"number\"\n        class=\"form-control\"\n        id=\"telephone\"\n        [(ngModel)]=\"newResponsable.telephone\"\n        name=\"telephone\"\n        required\n        #telephone=\"ngModel\"\n        maxlength=\"8\">\n      />\n      \n      <!-- Error message for 'Téléphone' -->\n      <div *ngIf=\"telephone.invalid && telephone.touched\" class=\"text-danger\">\n        <div *ngIf=\"telephone.errors?.['required']\">Téléphone is required.</div>\n        <div *ngIf=\"telephone.value?.toString().length > 8\">Téléphone cannot be more than 8 digits.</div>\n      </div>\n    </div>\n    \n\n    <div class=\"form-group\">\n      <label for=\"password\">Password</label>\n      <input\n        type=\"password\"\n        class=\"form-control\"\n        id=\"password\"\n        [(ngModel)]=\"newResponsable.password\"\n        name=\"password\"\n        required\n        minlength=\"8\"\n        #password=\"ngModel\"\n      />\n      <!-- Error message for 'Password' -->\n      <div *ngIf=\"password.invalid && password.touched\" class=\"text-danger\">\n        Password is required (minimum 8 characters).\n      </div>\n    </div>\n\n    <!-- Dropdown for selecting the group -->\n    <div class=\"form-group\">\n      <label for=\"groupe\">Group</label>\n      <select\n        id=\"groupe\"\n        class=\"form-control\"\n        [(ngModel)]=\"newResponsable.groupeId\"\n        name=\"groupeId\"\n        required\n        #groupe=\"ngModel\"\n      >\n        <option *ngFor=\"let group of groups\" [value]=\"group.idGroupe\">\n          {{ group.nomGroupe }}\n        </option>\n      </select>\n      <!-- Error message for 'Group' -->\n      <div *ngIf=\"groupe.invalid && groupe.touched\" class=\"text-danger\">\n        Group is required.\n      </div>\n    </div>\n\n    <div class=\"d-flex justify-content-between\">\n      <button\n        type=\"submit\"\n        class=\"btn btn-primary\"\n        [disabled]=\"responsableForm.invalid\"\n      >\n        Ajouter\n      </button>\n      <button\n        type=\"button\"\n        class=\"btn btn-secondary\"\n        (click)=\"cancelEdit()\"\n      >\n        Cancel\n      </button>\n    </div>\n  </form>\n</div>\n"], "mappings": ";;;;;;;ICeMA,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAeNH,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBNH,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAkBJH,EAAA,CAAAC,cAAA,UAA4C;IAAAD,EAAA,CAAAE,MAAA,uCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACxEH,EAAA,CAAAC,cAAA,UAAoD;IAAAD,EAAA,CAAAE,MAAA,wDAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFnGH,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAI,UAAA,IAAAC,6CAAA,kBAAwE;IACxEL,EAAA,CAAAI,UAAA,IAAAE,6CAAA,kBAAiG;IACnGN,EAAA,CAAAG,YAAA,EAAM;;;;;IAFEH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAoC;IACpCV,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAQ,UAAA,UAAAC,GAAA,CAAAE,KAAA,kBAAAF,GAAA,CAAAE,KAAA,CAAAC,QAAA,GAAAC,MAAA,MAA4C;;;;;IAkBpDb,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAcJH,EAAA,CAAAC,cAAA,iBAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF4BH,EAAA,CAAAQ,UAAA,UAAAM,SAAA,CAAAC,QAAA,CAAwB;IAC3Df,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAgB,kBAAA,MAAAF,SAAA,CAAAG,SAAA,MACF;;;;;IAGFjB,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;ADpGZ,OAAM,MAAOe,uBAAuB;EAelCC,YAAoBC,kBAAsC,EAAUC,MAAc;IAA9D,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAA8B,KAAAC,MAAM,GAANA,MAAM;IAd1E,KAAAC,cAAc,GAAgB;MAC5BC,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,CAAC,CAAG;KACf;;IAED,KAAAC,MAAM,GAAa,EAAE;EAGgE;EAErFC,QAAQA,CAAA;IACN;IACA,IAAI,CAACZ,kBAAkB,CAACa,SAAS,EAAE,CAACC,SAAS,CAAEC,IAAI,IAAI;MACrD,IAAI,CAACJ,MAAM,GAAGI,IAAI,CAAC,CAAE;IACvB,CAAC,CAAC;EACJ;EAEA;EACAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACd,cAAc,CAACQ,QAAQ,EAAE;MACjCO,KAAK,CAAC,6BAA6B,CAAC;MACpC;;IAGF,IAAI,CAAC,IAAI,CAACf,cAAc,CAACO,QAAQ,EAAE;MACjCQ,KAAK,CAAC,yBAAyB,CAAC;MAChC;;IAGF;IACAC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACjB,cAAc,CAAC;IAEvD;IACA,IAAI,CAACF,kBAAkB,CAACgB,cAAc,CAAC,IAAI,CAACd,cAAc,CAAC,CAACY,SAAS,CAClEM,QAAQ,IAAI;MACXF,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,QAAQ,CAAC;MAExD;MACA,IAAI,CAAClB,cAAc,GAAG;QACpBE,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,CAAC,CAAG;OACf;;MACD,IAAI,CAACT,MAAM,CAACoB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAElC,CAAC,EACAC,KAAK,IAAI;MACRJ,OAAO,CAACI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,CACF;EACH;EAEAC,UAAUA,CAAA;IACR;IACA,IAAI,CAACrB,cAAc,GAAG;MACpBC,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX;IAED;IACA,IAAI,CAACT,MAAM,CAACuB,aAAa,CAAC,QAAQ,CAAC;EACrC;;;uBA9EW1B,uBAAuB,EAAAlB,EAAA,CAAA6C,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA/C,EAAA,CAAA6C,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB/B,uBAAuB;MAAAgC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZpCxD,EAAA,CAAAC,cAAA,aAAuB;UACjBD,EAAA,CAAAE,MAAA,qCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,iBAA8D;UAAxDD,EAAA,CAAA0D,UAAA,sBAAAC,0DAAA;YAAA,OAAYF,GAAA,CAAArB,cAAA,EAAgB;UAAA,EAAC;UACjCpC,EAAA,CAAAC,cAAA,aAAwB;UACLD,EAAA,CAAAE,MAAA,UAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5BH,EAAA,CAAAC,cAAA,kBAQE;UAJAD,EAAA,CAAA0D,UAAA,2BAAAE,gEAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAAnC,cAAA,CAAAE,SAAA,GAAAqC,MAAA;UAAA,EAAsC;UAJxC7D,EAAA,CAAAG,YAAA,EAQE;UAEFH,EAAA,CAAAI,UAAA,KAAA0D,uCAAA,iBAEM;UACR9D,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAwB;UACFD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,oBAQE;UAJAD,EAAA,CAAA0D,UAAA,2BAAAK,iEAAAF,MAAA;YAAA,OAAAJ,GAAA,CAAAnC,cAAA,CAAAG,QAAA,GAAAoC,MAAA;UAAA,EAAqC;UAJvC7D,EAAA,CAAAG,YAAA,EAQE;UAEFH,EAAA,CAAAI,UAAA,KAAA4D,uCAAA,iBAEM;UACRhE,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAwB;UACHD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,qBASE;UALAD,EAAA,CAAA0D,UAAA,2BAAAO,iEAAAJ,MAAA;YAAA,OAAAJ,GAAA,CAAAnC,cAAA,CAAAI,KAAA,GAAAmC,MAAA;UAAA,EAAkC;UAJpC7D,EAAA,CAAAG,YAAA,EASE;UAEFH,EAAA,CAAAI,UAAA,KAAA8D,uCAAA,iBAEM;UACRlE,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAwB;UACCD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,qBAQgB;UAJdD,EAAA,CAAA0D,UAAA,2BAAAS,iEAAAN,MAAA;YAAA,OAAAJ,GAAA,CAAAnC,cAAA,CAAAK,SAAA,GAAAkC,MAAA;UAAA,EAAsC;UAJxC7D,EAAA,CAAAG,YAAA,EAQgB;UAChBH,EAAA,CAAAE,MAAA,YAEA;UACAF,EAAA,CAAAI,UAAA,KAAAgE,uCAAA,iBAGM;UACRpE,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAAwB;UACAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,qBASE;UALAD,EAAA,CAAA0D,UAAA,2BAAAW,iEAAAR,MAAA;YAAA,OAAAJ,GAAA,CAAAnC,cAAA,CAAAO,QAAA,GAAAgC,MAAA;UAAA,EAAqC;UAJvC7D,EAAA,CAAAG,YAAA,EASE;UAEFH,EAAA,CAAAI,UAAA,KAAAkE,uCAAA,iBAEM;UACRtE,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAAwB;UACFD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjCH,EAAA,CAAAC,cAAA,sBAOC;UAJCD,EAAA,CAAA0D,UAAA,2BAAAa,kEAAAV,MAAA;YAAA,OAAAJ,GAAA,CAAAnC,cAAA,CAAAQ,QAAA,GAAA+B,MAAA;UAAA,EAAqC;UAKrC7D,EAAA,CAAAI,UAAA,KAAAoE,0CAAA,qBAES;UACXxE,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAI,UAAA,KAAAqE,uCAAA,iBAEM;UACRzE,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA4C;UAMxCD,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAA0D,UAAA,mBAAAgB,0DAAA;YAAA,OAASjB,GAAA,CAAAd,UAAA,EAAY;UAAA,EAAC;UAEtB3C,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;UAxHPH,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAiD,GAAA,CAAAnC,cAAA,CAAAE,SAAA,CAAsC;UAMlCxB,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,UAAA,SAAAmE,GAAA,CAAAC,OAAA,IAAAD,GAAA,CAAAE,OAAA,CAAgC;UAWpC7E,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAAiD,GAAA,CAAAnC,cAAA,CAAAG,QAAA,CAAqC;UAMjCzB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,SAAAsE,GAAA,CAAAF,OAAA,IAAAE,GAAA,CAAAD,OAAA,CAAsC;UAW1C7E,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,YAAAiD,GAAA,CAAAnC,cAAA,CAAAI,KAAA,CAAkC;UAO9B1B,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,SAAAuE,GAAA,CAAAH,OAAA,IAAAG,GAAA,CAAAF,OAAA,CAAoC;UAWxC7E,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,YAAAiD,GAAA,CAAAnC,cAAA,CAAAK,SAAA,CAAsC;UAQlC3B,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAmE,OAAA,IAAAnE,GAAA,CAAAoE,OAAA,CAA4C;UAahD7E,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAAiD,GAAA,CAAAnC,cAAA,CAAAO,QAAA,CAAqC;UAOjC7B,EAAA,CAAAO,SAAA,GAA0C;UAA1CP,EAAA,CAAAQ,UAAA,SAAAwE,GAAA,CAAAJ,OAAA,IAAAI,GAAA,CAAAH,OAAA,CAA0C;UAW9C7E,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAAiD,GAAA,CAAAnC,cAAA,CAAAQ,QAAA,CAAqC;UAKX9B,EAAA,CAAAO,SAAA,GAAS;UAATP,EAAA,CAAAQ,UAAA,YAAAiD,GAAA,CAAA1B,MAAA,CAAS;UAK/B/B,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,SAAAyE,IAAA,CAAAL,OAAA,IAAAK,IAAA,CAAAJ,OAAA,CAAsC;UAS1C7E,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,aAAA0E,GAAA,CAAAN,OAAA,CAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}