{"ast": null, "code": "import { Role } from '../model/role.enum';\nimport { Groupe } from '../model/groupe.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../responsable.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction ResponsableEditComponent_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", groupe_r1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(groupe_r1);\n  }\n}\nexport class ResponsableEditComponent {\n  constructor(route, responsableService, router) {\n    this.route = route;\n    this.responsableService = responsableService;\n    this.router = router;\n    this.responsableId = 0;\n    this.responsable = {\n      id: 0,\n      nom: '',\n      prenom: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      groupe: 'ACTION'\n    };\n    this.roles = Object.values(Role); // Dynamically get roles from the Role enum\n    this.groupes = Object.values(Groupe); // Dynamically get groupes from the Groupe enum\n  }\n\n  ngOnInit() {\n    // Get the ID from the route\n    this.responsableId = Number(this.route.snapshot.paramMap.get('id'));\n    this.getResponsable(this.responsableId); // Fetch user data\n  }\n\n  getResponsable(id) {\n    this.responsableService.getResponsableById(id).subscribe(data => {\n      this.responsable = data; // Bind the fetched data to the responsable object\n    }, error => {\n      console.error('Error fetching responsable:', error);\n    });\n  }\n  updateResponsable() {\n    // Send updated data to the backend\n    this.responsableService.updateResponsable(this.responsableId, this.responsable).subscribe(response => {\n      console.log('Responsable updated successfully', response);\n      this.router.navigate(['/adminDash']); // Navigate after successful update\n    }, error => {\n      console.error('Error updating responsable:', error);\n    });\n  }\n  static {\n    this.ɵfac = function ResponsableEditComponent_Factory(t) {\n      return new (t || ResponsableEditComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ResponsableService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResponsableEditComponent,\n      selectors: [[\"app-responsable-edit\"]],\n      decls: 27,\n      vars: 6,\n      consts: [[1, \"container\", \"mt-4\"], [3, \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"nom\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"nom\", \"name\", \"nom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"prenom\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"prenom\", \"name\", \"prenom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"telephone\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"telephone\", \"name\", \"telephone\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"groupe\", 1, \"form-label\"], [\"id\", \"groupe\", \"name\", \"groupe\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [3, \"value\"]],\n      template: function ResponsableEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Edit Responsable\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"form\", 1);\n          i0.ɵɵlistener(\"ngSubmit\", function ResponsableEditComponent_Template_form_ngSubmit_3_listener() {\n            return ctx.updateResponsable();\n          });\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"input\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_7_listener($event) {\n            return ctx.responsable.nom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 2)(9, \"label\", 5);\n          i0.ɵɵtext(10, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"input\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.responsable.prenom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 2)(13, \"label\", 7);\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 8);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.responsable.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 2)(17, \"label\", 9);\n          i0.ɵɵtext(18, \"Telephone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"input\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_19_listener($event) {\n            return ctx.responsable.telephone = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 2)(21, \"label\", 11);\n          i0.ɵɵtext(22, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"select\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_select_ngModelChange_23_listener($event) {\n            return ctx.responsable.groupe = $event;\n          });\n          i0.ɵɵtemplate(24, ResponsableEditComponent_option_24_Template, 2, 2, \"option\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"button\", 14);\n          i0.ɵɵtext(26, \"Update Responsable\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.nom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.prenom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.telephone);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.groupe);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groupes);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.ɵNgNoValidate, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.DefaultValueAccessor, i4.NumberValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Role", "Groupe", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "groupe_r1", "ɵɵadvance", "ɵɵtextInterpolate", "ResponsableEditComponent", "constructor", "route", "responsableService", "router", "responsableId", "responsable", "id", "nom", "prenom", "email", "telephone", "role", "groupe", "roles", "Object", "values", "groupes", "ngOnInit", "Number", "snapshot", "paramMap", "get", "getResponsable", "getResponsableById", "subscribe", "data", "error", "console", "updateResponsable", "response", "log", "navigate", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ResponsableService", "Router", "selectors", "decls", "vars", "consts", "template", "ResponsableEditComponent_Template", "rf", "ctx", "ɵɵlistener", "ResponsableEditComponent_Template_form_ngSubmit_3_listener", "ResponsableEditComponent_Template_input_ngModelChange_7_listener", "$event", "ResponsableEditComponent_Template_input_ngModelChange_11_listener", "ResponsableEditComponent_Template_input_ngModelChange_15_listener", "ResponsableEditComponent_Template_input_ngModelChange_19_listener", "ResponsableEditComponent_Template_select_ngModelChange_23_listener", "ɵɵtemplate", "ResponsableEditComponent_option_24_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\responsable-edit\\responsable-edit.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\responsable-edit\\responsable-edit.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport { Role } from '../model/role.enum';\nimport { Groupe } from '../model/groupe.enum';\n\n@Component({\n  selector: 'app-responsable-edit',\n  templateUrl: './responsable-edit.component.html',\n  styleUrls: ['./responsable-edit.component.css']\n})\nexport class ResponsableEditComponent implements OnInit {\n  responsableId: number = 0;\n  responsable: User = { id: 0, nom: '', prenom: '', email: '', telephone: '', role: 'RESPONSABLE', groupe: 'ACTION' };\n  roles = Object.values(Role); // Dynamically get roles from the Role enum\n  groupes = Object.values(Groupe); // Dynamically get groupes from the Groupe enum\n\n  constructor(\n    private route: ActivatedRoute,\n    private responsableService: ResponsableService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    // Get the ID from the route\n    this.responsableId = Number(this.route.snapshot.paramMap.get('id'));\n    this.getResponsable(this.responsableId); // Fetch user data\n  }\n\n  getResponsable(id: number) {\n    this.responsableService.getResponsableById(id).subscribe(\n      (data) => {\n        this.responsable = data; // Bind the fetched data to the responsable object\n      },\n      (error) => {\n        console.error('Error fetching responsable:', error);\n      }\n    );\n  }\n\n  updateResponsable() {\n    // Send updated data to the backend\n    this.responsableService.updateResponsable(this.responsableId, this.responsable).subscribe(\n      (response) => {\n        console.log('Responsable updated successfully', response);\n        this.router.navigate(['/adminDash']); // Navigate after successful update\n      },\n      (error) => {\n        console.error('Error updating responsable:', error);\n      }\n    );\n  }\n}\n", "<div class=\"container mt-4\">\n  <h2>Edit Responsable</h2>\n  <form (ngSubmit)=\"updateResponsable()\">\n    <!-- First Name -->\n    <div class=\"mb-3\">\n      <label for=\"nom\" class=\"form-label\">First Name</label>\n      <input type=\"text\" id=\"nom\" [(ngModel)]=\"responsable.nom\" name=\"nom\" class=\"form-control\" required />\n    </div>\n\n    <!-- Last Name -->\n    <div class=\"mb-3\">\n      <label for=\"prenom\" class=\"form-label\">Last Name</label>\n      <input type=\"text\" id=\"prenom\" [(ngModel)]=\"responsable.prenom\" name=\"prenom\" class=\"form-control\" required />\n    </div>\n\n    <!-- Email -->\n    <div class=\"mb-3\">\n      <label for=\"email\" class=\"form-label\">Email</label>\n      <input type=\"email\" id=\"email\" [(ngModel)]=\"responsable.email\" name=\"email\" class=\"form-control\" required />\n    </div>\n\n    <!-- Telephone -->\n    <div class=\"mb-3\">\n      <label for=\"telephone\" class=\"form-label\">Telephone</label>\n      <input type=\"number\" id=\"telephone\" [(ngModel)]=\"responsable.telephone\" name=\"telephone\" class=\"form-control\" required />\n    </div>\n\n\n    <!-- Groupe -->\n    <div class=\"mb-3\">\n      <label for=\"groupe\" class=\"form-label\">Groupe</label>\n      <select id=\"groupe\" [(ngModel)]=\"responsable.groupe\" name=\"groupe\" class=\"form-select\">\n        <option *ngFor=\"let groupe of groupes\" [value]=\"groupe\">{{ groupe }}</option>\n      </select>\n    </div>\n\n    <!-- Submit Button -->\n    <button type=\"submit\" class=\"btn btn-primary\">Update Responsable</button>\n  </form>\n</div>\n"], "mappings": "AAIA,SAASA,IAAI,QAAQ,oBAAoB;AACzC,SAASC,MAAM,QAAQ,sBAAsB;;;;;;;;IC2BrCC,EAAA,CAAAC,cAAA,iBAAwD;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAtCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAgB;IAACL,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,iBAAA,CAAAF,SAAA,CAAY;;;ADpB5E,OAAM,MAAOG,wBAAwB;EAMnCC,YACUC,KAAqB,EACrBC,kBAAsC,EACtCC,MAAc;IAFd,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAC,aAAa,GAAW,CAAC;IACzB,KAAAC,WAAW,GAAS;MAAEC,EAAE,EAAE,CAAC;MAAEC,GAAG,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,IAAI,EAAE,aAAa;MAAEC,MAAM,EAAE;IAAQ,CAAE;IACnH,KAAAC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC1B,IAAI,CAAC,CAAC,CAAC;IAC7B,KAAA2B,OAAO,GAAGF,MAAM,CAACC,MAAM,CAACzB,MAAM,CAAC,CAAC,CAAC;EAM9B;;EAEH2B,QAAQA,CAAA;IACN;IACA,IAAI,CAACb,aAAa,GAAGc,MAAM,CAAC,IAAI,CAACjB,KAAK,CAACkB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnE,IAAI,CAACC,cAAc,CAAC,IAAI,CAAClB,aAAa,CAAC,CAAC,CAAC;EAC3C;;EAEAkB,cAAcA,CAAChB,EAAU;IACvB,IAAI,CAACJ,kBAAkB,CAACqB,kBAAkB,CAACjB,EAAE,CAAC,CAACkB,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAACpB,WAAW,GAAGoB,IAAI,CAAC,CAAC;IAC3B,CAAC,EACAC,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CACF;EACH;EAEAE,iBAAiBA,CAAA;IACf;IACA,IAAI,CAAC1B,kBAAkB,CAAC0B,iBAAiB,CAAC,IAAI,CAACxB,aAAa,EAAE,IAAI,CAACC,WAAW,CAAC,CAACmB,SAAS,CACtFK,QAAQ,IAAI;MACXF,OAAO,CAACG,GAAG,CAAC,kCAAkC,EAAED,QAAQ,CAAC;MACzD,IAAI,CAAC1B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,EACAL,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CACF;EACH;;;uBAxCW3B,wBAAwB,EAAAR,EAAA,CAAAyC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3C,EAAA,CAAAyC,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAA7C,EAAA,CAAAyC,iBAAA,CAAAC,EAAA,CAAAI,MAAA;IAAA;EAAA;;;YAAxBtC,wBAAwB;MAAAuC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZrCrD,EAAA,CAAAC,cAAA,aAA4B;UACtBD,EAAA,CAAAE,MAAA,uBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,cAAuC;UAAjCD,EAAA,CAAAuD,UAAA,sBAAAC,2DAAA;YAAA,OAAYF,GAAA,CAAAjB,iBAAA,EAAmB;UAAA,EAAC;UAEpCrC,EAAA,CAAAC,cAAA,aAAkB;UACoBD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtDH,EAAA,CAAAC,cAAA,eAAqG;UAAzED,EAAA,CAAAuD,UAAA,2BAAAE,iEAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAAxC,WAAA,CAAAE,GAAA,GAAA0C,MAAA;UAAA,EAA6B;UAAzD1D,EAAA,CAAAG,YAAA,EAAqG;UAIvGH,EAAA,CAAAC,cAAA,aAAkB;UACuBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,gBAA8G;UAA/ED,EAAA,CAAAuD,UAAA,2BAAAI,kEAAAD,MAAA;YAAA,OAAAJ,GAAA,CAAAxC,WAAA,CAAAG,MAAA,GAAAyC,MAAA;UAAA,EAAgC;UAA/D1D,EAAA,CAAAG,YAAA,EAA8G;UAIhHH,EAAA,CAAAC,cAAA,cAAkB;UACsBD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,gBAA4G;UAA7ED,EAAA,CAAAuD,UAAA,2BAAAK,kEAAAF,MAAA;YAAA,OAAAJ,GAAA,CAAAxC,WAAA,CAAAI,KAAA,GAAAwC,MAAA;UAAA,EAA+B;UAA9D1D,EAAA,CAAAG,YAAA,EAA4G;UAI9GH,EAAA,CAAAC,cAAA,cAAkB;UAC0BD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3DH,EAAA,CAAAC,cAAA,iBAAyH;UAArFD,EAAA,CAAAuD,UAAA,2BAAAM,kEAAAH,MAAA;YAAA,OAAAJ,GAAA,CAAAxC,WAAA,CAAAK,SAAA,GAAAuC,MAAA;UAAA,EAAmC;UAAvE1D,EAAA,CAAAG,YAAA,EAAyH;UAK3HH,EAAA,CAAAC,cAAA,cAAkB;UACuBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,kBAAuF;UAAnED,EAAA,CAAAuD,UAAA,2BAAAO,mEAAAJ,MAAA;YAAA,OAAAJ,GAAA,CAAAxC,WAAA,CAAAO,MAAA,GAAAqC,MAAA;UAAA,EAAgC;UAClD1D,EAAA,CAAA+D,UAAA,KAAAC,2CAAA,qBAA6E;UAC/EhE,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UA/B3CH,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,YAAAkD,GAAA,CAAAxC,WAAA,CAAAE,GAAA,CAA6B;UAM1BhB,EAAA,CAAAM,SAAA,GAAgC;UAAhCN,EAAA,CAAAI,UAAA,YAAAkD,GAAA,CAAAxC,WAAA,CAAAG,MAAA,CAAgC;UAMhCjB,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAI,UAAA,YAAAkD,GAAA,CAAAxC,WAAA,CAAAI,KAAA,CAA+B;UAM1BlB,EAAA,CAAAM,SAAA,GAAmC;UAAnCN,EAAA,CAAAI,UAAA,YAAAkD,GAAA,CAAAxC,WAAA,CAAAK,SAAA,CAAmC;UAOnDnB,EAAA,CAAAM,SAAA,GAAgC;UAAhCN,EAAA,CAAAI,UAAA,YAAAkD,GAAA,CAAAxC,WAAA,CAAAO,MAAA,CAAgC;UACvBrB,EAAA,CAAAM,SAAA,GAAU;UAAVN,EAAA,CAAAI,UAAA,YAAAkD,GAAA,CAAA7B,OAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}