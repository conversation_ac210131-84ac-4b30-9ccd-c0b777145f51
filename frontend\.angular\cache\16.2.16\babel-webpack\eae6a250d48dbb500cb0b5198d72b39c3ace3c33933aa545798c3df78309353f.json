{"ast": null, "code": "import { AsyncScheduler } from './AsyncScheduler';\nexport class AnimationFrameScheduler extends AsyncScheduler {\n  flush(action) {\n    this._active = true;\n    let flushId;\n    if (action) {\n      flushId = action.id;\n    } else {\n      flushId = this._scheduled;\n      this._scheduled = undefined;\n    }\n    const {\n      actions\n    } = this;\n    let error;\n    action = action || actions.shift();\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while ((action = actions[0]) && action.id === flushId && actions.shift());\n    this._active = false;\n    if (error) {\n      while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}", "map": {"version": 3, "names": ["AsyncScheduler", "AnimationFrameScheduler", "flush", "action", "_active", "flushId", "id", "_scheduled", "undefined", "actions", "error", "shift", "execute", "state", "delay", "unsubscribe"], "sources": ["C:/Users/<USER>/Desktop/GRAgithubbackup/GRA/frontend/node_modules/rxjs/dist/esm/internal/scheduler/AnimationFrameScheduler.js"], "sourcesContent": ["import { AsyncScheduler } from './AsyncScheduler';\nexport class AnimationFrameScheduler extends AsyncScheduler {\n    flush(action) {\n        this._active = true;\n        let flushId;\n        if (action) {\n            flushId = action.id;\n        }\n        else {\n            flushId = this._scheduled;\n            this._scheduled = undefined;\n        }\n        const { actions } = this;\n        let error;\n        action = action || actions.shift();\n        do {\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        } while ((action = actions[0]) && action.id === flushId && actions.shift());\n        this._active = false;\n        if (error) {\n            while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAO,MAAMC,uBAAuB,SAASD,cAAc,CAAC;EACxDE,KAAKA,CAACC,MAAM,EAAE;IACV,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAIC,OAAO;IACX,IAAIF,MAAM,EAAE;MACRE,OAAO,GAAGF,MAAM,CAACG,EAAE;IACvB,CAAC,MACI;MACDD,OAAO,GAAG,IAAI,CAACE,UAAU;MACzB,IAAI,CAACA,UAAU,GAAGC,SAAS;IAC/B;IACA,MAAM;MAAEC;IAAQ,CAAC,GAAG,IAAI;IACxB,IAAIC,KAAK;IACTP,MAAM,GAAGA,MAAM,IAAIM,OAAO,CAACE,KAAK,CAAC,CAAC;IAClC,GAAG;MACC,IAAKD,KAAK,GAAGP,MAAM,CAACS,OAAO,CAACT,MAAM,CAACU,KAAK,EAAEV,MAAM,CAACW,KAAK,CAAC,EAAG;QACtD;MACJ;IACJ,CAAC,QAAQ,CAACX,MAAM,GAAGM,OAAO,CAAC,CAAC,CAAC,KAAKN,MAAM,CAACG,EAAE,KAAKD,OAAO,IAAII,OAAO,CAACE,KAAK,CAAC,CAAC;IAC1E,IAAI,CAACP,OAAO,GAAG,KAAK;IACpB,IAAIM,KAAK,EAAE;MACP,OAAO,CAACP,MAAM,GAAGM,OAAO,CAAC,CAAC,CAAC,KAAKN,MAAM,CAACG,EAAE,KAAKD,OAAO,IAAII,OAAO,CAACE,KAAK,CAAC,CAAC,EAAE;QACtER,MAAM,CAACY,WAAW,CAAC,CAAC;MACxB;MACA,MAAML,KAAK;IACf;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}