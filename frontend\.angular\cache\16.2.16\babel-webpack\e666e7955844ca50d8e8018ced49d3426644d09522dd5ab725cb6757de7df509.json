{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class HabilitationService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth';\n  }\n  // Fetch groupe by ID — FIXED\n  getGroupeById(id) {\n    return this.http.get(`${this.apiUrl}/Groupes/${id}`);\n  }\n  // Fetch all ressources\n  getAllRessources() {\n    return this.http.get(`${this.apiUrl}/Ressources/all`);\n  }\n  // Submit habilitation\n  updateGroupe(id, groupe) {\n    return this.http.put(`${this.apiUrl}/edit/${id}`, groupe, {\n      headers: this.getHeaders()\n    });\n  }\n  getHeaders() {\n    const token = localStorage.getItem('token');\n    console.log('Token being sent in headers:', token); // Log token for debugging\n    if (!token) {\n      console.error('No token found!');\n      // Redirect to login page\n      window.location.href = '/login';\n      throw new Error('No token found!');\n    }\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`\n    });\n  }\n  static {\n    this.ɵfac = function HabilitationService_Factory(t) {\n      return new (t || HabilitationService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: HabilitationService,\n      factory: HabilitationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "HabilitationService", "constructor", "http", "apiUrl", "getGroupeById", "id", "get", "getAllRessources", "updateGroupe", "groupe", "put", "headers", "getHeaders", "token", "localStorage", "getItem", "console", "log", "error", "window", "location", "href", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\services\\habilitation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { Habilitation } from '../model/Habilitation.model';\nimport { User } from '../model/user.model';  // Assuming you have a User model\nimport { Groupe } from '../model/groupe.model';\nimport { Ressource } from '../model/ressource.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class HabilitationService {\n\n  private apiUrl = 'http://localhost:8000/api/v1/auth';\n\n  constructor(private http: HttpClient) {}\n\n// Fetch groupe by ID — FIXED\ngetGroupeById(id: number): Observable<Groupe> {\n  return this.http.get<Groupe>(`${this.apiUrl}/Groupes/${id}`);\n}\n\n\n  // Fetch all ressources\n  getAllRessources(): Observable<Ressource[]> {\n    return this.http.get<Ressource[]>(`${this.apiUrl}/Ressources/all`);\n  }\n\n\n// Submit habilitation\nupdateGroupe(id: number, groupe: Groupe): Observable<Groupe> {\n  return this.http.put<Groupe>(`${this.apiUrl}/edit/${id}`, groupe, {\n    headers: this.getHeaders()\n  });\n}\n private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('token');\n    console.log('Token being sent in headers:', token); // Log token for debugging\n    if (!token) {\n      console.error('No token found!');\n      // Redirect to login page\n      window.location.href = '/login';\n      throw new Error('No token found!');\n    }\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n    });\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;;;AAU9D,OAAM,MAAOC,mBAAmB;EAI9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,mCAAmC;EAEb;EAEzC;EACAC,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAS,GAAG,IAAI,CAACH,MAAM,YAAYE,EAAE,EAAE,CAAC;EAC9D;EAGE;EACAE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACL,IAAI,CAACI,GAAG,CAAc,GAAG,IAAI,CAACH,MAAM,iBAAiB,CAAC;EACpE;EAGF;EACAK,YAAYA,CAACH,EAAU,EAAEI,MAAc;IACrC,OAAO,IAAI,CAACP,IAAI,CAACQ,GAAG,CAAS,GAAG,IAAI,CAACP,MAAM,SAASE,EAAE,EAAE,EAAEI,MAAM,EAAE;MAChEE,OAAO,EAAE,IAAI,CAACC,UAAU;KACzB,CAAC;EACJ;EACSA,UAAUA,CAAA;IACf,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3CC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEJ,KAAK,CAAC,CAAC,CAAC;IACpD,IAAI,CAACA,KAAK,EAAE;MACVG,OAAO,CAACE,KAAK,CAAC,iBAAiB,CAAC;MAChC;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MAC/B,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;;IAEpC,OAAO,IAAIvB,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUc,KAAK;KACjC,CAAC;EACJ;;;uBApCWb,mBAAmB,EAAAuB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnB1B,mBAAmB;MAAA2B,OAAA,EAAnB3B,mBAAmB,CAAA4B,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}