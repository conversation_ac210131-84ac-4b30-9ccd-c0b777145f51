{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { catchError, throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ResponsableService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'http://localhost:8000/api/v1/auth/Users_Responsables'; // Your API URL\n  }\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId) {\n    return this.http.get(`${this.apiUrl}/${responsableId}`); // Fetching a single user\n  }\n  // Get all responsibles\n  getResponsables() {\n    const headers = new HttpHeaders().set('Authorization', `Bearer ${localStorage.getItem('token')}`);\n    return this.http.get(this.apiUrl, {\n      headers\n    });\n  }\n  // Delete a responsable\n  deleteResponsable(id) {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, {\n      responseType: 'text'\n    }) // Force response as text\n    .pipe(catchError(error => {\n      console.error('Delete error:', error);\n      return throwError(error); // Re-throw the error so we can handle it in the component\n    }));\n  }\n  // Update a responsable\n  updateResponsable(id, responsable) {\n    const headers = new HttpHeaders().set('Authorization', `Bearer ${localStorage.getItem('token')}`);\n    // Ensure the role and groupe are included in the request body\n    const updatedUser = {\n      ...responsable,\n      role: responsable.role || 'RESPONSABLE' // Set role if not present\n    };\n\n    return this.http.put(`${this.apiUrl}/edit/${id}`, updatedUser, {\n      headers\n    });\n  }\n  static {\n    this.ɵfac = function ResponsableService_Factory(t) {\n      return new (t || ResponsableService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ResponsableService,\n      factory: ResponsableService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "catchError", "throwError", "ResponsableService", "constructor", "http", "apiUrl", "getResponsableById", "responsableId", "get", "getResponsables", "headers", "set", "localStorage", "getItem", "deleteResponsable", "id", "delete", "responseType", "pipe", "error", "console", "updateResponsable", "responsable", "updatedUser", "role", "put", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\responsable.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { catchError, Observable, throwError } from 'rxjs';\nimport { User } from './model/user.model'; // Import the User model\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ResponsableService {\n  private apiUrl = 'http://localhost:8000/api/v1/auth/Users_Responsables'; // Your API URL\n\n  constructor(private http: HttpClient) {}\n\n  // Implement the method to get a responsable by id\n  getResponsableById(responsableId: number): Observable<User> {\n    return this.http.get<User>(`${this.apiUrl}/${responsableId}`); // Fetching a single user\n  }\n  \n  // Get all responsibles\n  getResponsables(): Observable<User[]> {\n    const headers = new HttpHeaders().set(\n      'Authorization',\n      `Bearer ${localStorage.getItem('token')}`\n    );\n\n    return this.http.get<User[]>(this.apiUrl, { headers });\n  }\n\n  // Delete a responsable\n  deleteResponsable(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/delete/${id}`, { responseType: 'text' })  // Force response as text\n      .pipe(\n        catchError((error) => {\n          console.error('Delete error:', error);\n          return throwError(error); // Re-throw the error so we can handle it in the component\n        })\n      );\n  }\n  \n\n  // Update a responsable\nupdateResponsable(id: number, responsable: User): Observable<any> {\n  const headers = new HttpHeaders().set('Authorization', `Bearer ${localStorage.getItem('token')}`);\n  \n  // Ensure the role and groupe are included in the request body\n  const updatedUser = {\n    ...responsable,\n    role: responsable.role || 'RESPONSABLE', // Set role if not present\n  };\n\n  return this.http.put(`${this.apiUrl}/edit/${id}`, updatedUser, { headers });\n}\n}"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,EAAcC,UAAU,QAAQ,MAAM;;;AAMzD,OAAM,MAAOC,kBAAkB;EAG7BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,sDAAsD,CAAC,CAAC;EAElC;EAEvC;EACAC,kBAAkBA,CAACC,aAAqB;IACtC,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAO,GAAG,IAAI,CAACH,MAAM,IAAIE,aAAa,EAAE,CAAC,CAAC,CAAC;EACjE;EAEA;EACAE,eAAeA,CAAA;IACb,MAAMC,OAAO,GAAG,IAAIX,WAAW,EAAE,CAACY,GAAG,CACnC,eAAe,EACf,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE,CAC1C;IAED,OAAO,IAAI,CAACT,IAAI,CAACI,GAAG,CAAS,IAAI,CAACH,MAAM,EAAE;MAAEK;IAAO,CAAE,CAAC;EACxD;EAEA;EACAI,iBAAiBA,CAACC,EAAU;IAC1B,OAAO,IAAI,CAACX,IAAI,CAACY,MAAM,CAAC,GAAG,IAAI,CAACX,MAAM,WAAWU,EAAE,EAAE,EAAE;MAAEE,YAAY,EAAE;IAAM,CAAE,CAAC,CAAE;IAAA,CAC/EC,IAAI,CACHlB,UAAU,CAAEmB,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAOlB,UAAU,CAACkB,KAAK,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,CACH;EACL;EAGA;EACFE,iBAAiBA,CAACN,EAAU,EAAEO,WAAiB;IAC7C,MAAMZ,OAAO,GAAG,IAAIX,WAAW,EAAE,CAACY,GAAG,CAAC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;IAEjG;IACA,MAAMU,WAAW,GAAG;MAClB,GAAGD,WAAW;MACdE,IAAI,EAAEF,WAAW,CAACE,IAAI,IAAI,aAAa,CAAE;KAC1C;;IAED,OAAO,IAAI,CAACpB,IAAI,CAACqB,GAAG,CAAC,GAAG,IAAI,CAACpB,MAAM,SAASU,EAAE,EAAE,EAAEQ,WAAW,EAAE;MAAEb;IAAO,CAAE,CAAC;EAC7E;;;uBA3CaR,kBAAkB,EAAAwB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlB3B,kBAAkB;MAAA4B,OAAA,EAAlB5B,kBAAkB,CAAA6B,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}