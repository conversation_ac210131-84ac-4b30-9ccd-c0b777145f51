{"ast": null, "code": "/*!\n * express\n * Copyright(c) 2009-2013 <PERSON><PERSON>\n * Copyright(c) 2013 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module dependencies.\n * @private\n */\nvar accepts = require('accepts');\nvar deprecate = require('depd')('express');\nvar isIP = require('net').isIP;\nvar typeis = require('type-is');\nvar http = require('http');\nvar fresh = require('fresh');\nvar parseRange = require('range-parser');\nvar parse = require('parseurl');\nvar proxyaddr = require('proxy-addr');\n\n/**\n * Request prototype.\n * @public\n */\n\nvar req = Object.create(http.IncomingMessage.prototype);\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = req;\n\n/**\n * Return request header.\n *\n * The `Referrer` header field is special-cased,\n * both `Referrer` and `Referer` are interchangeable.\n *\n * Examples:\n *\n *     req.get('Content-Type');\n *     // => \"text/plain\"\n *\n *     req.get('content-type');\n *     // => \"text/plain\"\n *\n *     req.get('Something');\n *     // => undefined\n *\n * Aliased as `req.header()`.\n *\n * @param {String} name\n * @return {String}\n * @public\n */\n\nreq.get = req.header = function header(name) {\n  if (!name) {\n    throw new TypeError('name argument is required to req.get');\n  }\n  if (typeof name !== 'string') {\n    throw new TypeError('name must be a string to req.get');\n  }\n  var lc = name.toLowerCase();\n  switch (lc) {\n    case 'referer':\n    case 'referrer':\n      return this.headers.referrer || this.headers.referer;\n    default:\n      return this.headers[lc];\n  }\n};\n\n/**\n * To do: update docs.\n *\n * Check if the given `type(s)` is acceptable, returning\n * the best match when true, otherwise `undefined`, in which\n * case you should respond with 406 \"Not Acceptable\".\n *\n * The `type` value may be a single MIME type string\n * such as \"application/json\", an extension name\n * such as \"json\", a comma-delimited list such as \"json, html, text/plain\",\n * an argument list such as `\"json\", \"html\", \"text/plain\"`,\n * or an array `[\"json\", \"html\", \"text/plain\"]`. When a list\n * or array is given, the _best_ match, if any is returned.\n *\n * Examples:\n *\n *     // Accept: text/html\n *     req.accepts('html');\n *     // => \"html\"\n *\n *     // Accept: text/*, application/json\n *     req.accepts('html');\n *     // => \"html\"\n *     req.accepts('text/html');\n *     // => \"text/html\"\n *     req.accepts('json, text');\n *     // => \"json\"\n *     req.accepts('application/json');\n *     // => \"application/json\"\n *\n *     // Accept: text/*, application/json\n *     req.accepts('image/png');\n *     req.accepts('png');\n *     // => undefined\n *\n *     // Accept: text/*;q=.5, application/json\n *     req.accepts(['html', 'json']);\n *     req.accepts('html', 'json');\n *     req.accepts('html, json');\n *     // => \"json\"\n *\n * @param {String|Array} type(s)\n * @return {String|Array|Boolean}\n * @public\n */\n\nreq.accepts = function () {\n  var accept = accepts(this);\n  return accept.types.apply(accept, arguments);\n};\n\n/**\n * Check if the given `encoding`s are accepted.\n *\n * @param {String} ...encoding\n * @return {String|Array}\n * @public\n */\n\nreq.acceptsEncodings = function () {\n  var accept = accepts(this);\n  return accept.encodings.apply(accept, arguments);\n};\nreq.acceptsEncoding = deprecate.function(req.acceptsEncodings, 'req.acceptsEncoding: Use acceptsEncodings instead');\n\n/**\n * Check if the given `charset`s are acceptable,\n * otherwise you should respond with 406 \"Not Acceptable\".\n *\n * @param {String} ...charset\n * @return {String|Array}\n * @public\n */\n\nreq.acceptsCharsets = function () {\n  var accept = accepts(this);\n  return accept.charsets.apply(accept, arguments);\n};\nreq.acceptsCharset = deprecate.function(req.acceptsCharsets, 'req.acceptsCharset: Use acceptsCharsets instead');\n\n/**\n * Check if the given `lang`s are acceptable,\n * otherwise you should respond with 406 \"Not Acceptable\".\n *\n * @param {String} ...lang\n * @return {String|Array}\n * @public\n */\n\nreq.acceptsLanguages = function () {\n  var accept = accepts(this);\n  return accept.languages.apply(accept, arguments);\n};\nreq.acceptsLanguage = deprecate.function(req.acceptsLanguages, 'req.acceptsLanguage: Use acceptsLanguages instead');\n\n/**\n * Parse Range header field, capping to the given `size`.\n *\n * Unspecified ranges such as \"0-\" require knowledge of your resource length. In\n * the case of a byte range this is of course the total number of bytes. If the\n * Range header field is not given `undefined` is returned, `-1` when unsatisfiable,\n * and `-2` when syntactically invalid.\n *\n * When ranges are returned, the array has a \"type\" property which is the type of\n * range that is required (most commonly, \"bytes\"). Each array element is an object\n * with a \"start\" and \"end\" property for the portion of the range.\n *\n * The \"combine\" option can be set to `true` and overlapping & adjacent ranges\n * will be combined into a single range.\n *\n * NOTE: remember that ranges are inclusive, so for example \"Range: users=0-3\"\n * should respond with 4 users when available, not 3.\n *\n * @param {number} size\n * @param {object} [options]\n * @param {boolean} [options.combine=false]\n * @return {number|array}\n * @public\n */\n\nreq.range = function range(size, options) {\n  var range = this.get('Range');\n  if (!range) return;\n  return parseRange(size, range, options);\n};\n\n/**\n * Return the value of param `name` when present or `defaultValue`.\n *\n *  - Checks route placeholders, ex: _/user/:id_\n *  - Checks body params, ex: id=12, {\"id\":12}\n *  - Checks query string params, ex: ?id=12\n *\n * To utilize request bodies, `req.body`\n * should be an object. This can be done by using\n * the `bodyParser()` middleware.\n *\n * @param {String} name\n * @param {Mixed} [defaultValue]\n * @return {String}\n * @public\n */\n\nreq.param = function param(name, defaultValue) {\n  var params = this.params || {};\n  var body = this.body || {};\n  var query = this.query || {};\n  var args = arguments.length === 1 ? 'name' : 'name, default';\n  deprecate('req.param(' + args + '): Use req.params, req.body, or req.query instead');\n  if (null != params[name] && params.hasOwnProperty(name)) return params[name];\n  if (null != body[name]) return body[name];\n  if (null != query[name]) return query[name];\n  return defaultValue;\n};\n\n/**\n * Check if the incoming request contains the \"Content-Type\"\n * header field, and it contains the given mime `type`.\n *\n * Examples:\n *\n *      // With Content-Type: text/html; charset=utf-8\n *      req.is('html');\n *      req.is('text/html');\n *      req.is('text/*');\n *      // => true\n *\n *      // When Content-Type is application/json\n *      req.is('json');\n *      req.is('application/json');\n *      req.is('application/*');\n *      // => true\n *\n *      req.is('html');\n *      // => false\n *\n * @param {String|Array} types...\n * @return {String|false|null}\n * @public\n */\n\nreq.is = function is(types) {\n  var arr = types;\n\n  // support flattened arguments\n  if (!Array.isArray(types)) {\n    arr = new Array(arguments.length);\n    for (var i = 0; i < arr.length; i++) {\n      arr[i] = arguments[i];\n    }\n  }\n  return typeis(this, arr);\n};\n\n/**\n * Return the protocol string \"http\" or \"https\"\n * when requested with TLS. When the \"trust proxy\"\n * setting trusts the socket address, the\n * \"X-Forwarded-Proto\" header field will be trusted\n * and used if present.\n *\n * If you're running behind a reverse proxy that\n * supplies https for you this may be enabled.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'protocol', function protocol() {\n  var proto = this.connection.encrypted ? 'https' : 'http';\n  var trust = this.app.get('trust proxy fn');\n  if (!trust(this.connection.remoteAddress, 0)) {\n    return proto;\n  }\n\n  // Note: X-Forwarded-Proto is normally only ever a\n  //       single value, but this is to be safe.\n  var header = this.get('X-Forwarded-Proto') || proto;\n  var index = header.indexOf(',');\n  return index !== -1 ? header.substring(0, index).trim() : header.trim();\n});\n\n/**\n * Short-hand for:\n *\n *    req.protocol === 'https'\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'secure', function secure() {\n  return this.protocol === 'https';\n});\n\n/**\n * Return the remote address from the trusted proxy.\n *\n * The is the remote address on the socket unless\n * \"trust proxy\" is set.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'ip', function ip() {\n  var trust = this.app.get('trust proxy fn');\n  return proxyaddr(this, trust);\n});\n\n/**\n * When \"trust proxy\" is set, trusted proxy addresses + client.\n *\n * For example if the value were \"client, proxy1, proxy2\"\n * you would receive the array `[\"client\", \"proxy1\", \"proxy2\"]`\n * where \"proxy2\" is the furthest down-stream and \"proxy1\" and\n * \"proxy2\" were trusted.\n *\n * @return {Array}\n * @public\n */\n\ndefineGetter(req, 'ips', function ips() {\n  var trust = this.app.get('trust proxy fn');\n  var addrs = proxyaddr.all(this, trust);\n\n  // reverse the order (to farthest -> closest)\n  // and remove socket address\n  addrs.reverse().pop();\n  return addrs;\n});\n\n/**\n * Return subdomains as an array.\n *\n * Subdomains are the dot-separated parts of the host before the main domain of\n * the app. By default, the domain of the app is assumed to be the last two\n * parts of the host. This can be changed by setting \"subdomain offset\".\n *\n * For example, if the domain is \"tobi.ferrets.example.com\":\n * If \"subdomain offset\" is not set, req.subdomains is `[\"ferrets\", \"tobi\"]`.\n * If \"subdomain offset\" is 3, req.subdomains is `[\"tobi\"]`.\n *\n * @return {Array}\n * @public\n */\n\ndefineGetter(req, 'subdomains', function subdomains() {\n  var hostname = this.hostname;\n  if (!hostname) return [];\n  var offset = this.app.get('subdomain offset');\n  var subdomains = !isIP(hostname) ? hostname.split('.').reverse() : [hostname];\n  return subdomains.slice(offset);\n});\n\n/**\n * Short-hand for `url.parse(req.url).pathname`.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'path', function path() {\n  return parse(this).pathname;\n});\n\n/**\n * Parse the \"Host\" header field to a hostname.\n *\n * When the \"trust proxy\" setting trusts the socket\n * address, the \"X-Forwarded-Host\" header field will\n * be trusted.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'hostname', function hostname() {\n  var trust = this.app.get('trust proxy fn');\n  var host = this.get('X-Forwarded-Host');\n  if (!host || !trust(this.connection.remoteAddress, 0)) {\n    host = this.get('Host');\n  } else if (host.indexOf(',') !== -1) {\n    // Note: X-Forwarded-Host is normally only ever a\n    //       single value, but this is to be safe.\n    host = host.substring(0, host.indexOf(',')).trimRight();\n  }\n  if (!host) return;\n\n  // IPv6 literal support\n  var offset = host[0] === '[' ? host.indexOf(']') + 1 : 0;\n  var index = host.indexOf(':', offset);\n  return index !== -1 ? host.substring(0, index) : host;\n});\n\n// TODO: change req.host to return host in next major\n\ndefineGetter(req, 'host', deprecate.function(function host() {\n  return this.hostname;\n}, 'req.host: Use req.hostname instead'));\n\n/**\n * Check if the request is fresh, aka\n * Last-Modified and/or the ETag\n * still match.\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'fresh', function () {\n  var method = this.method;\n  var res = this.res;\n  var status = res.statusCode;\n\n  // GET or HEAD for weak freshness validation only\n  if ('GET' !== method && 'HEAD' !== method) return false;\n\n  // 2xx or 304 as per rfc2616 14.26\n  if (status >= 200 && status < 300 || 304 === status) {\n    return fresh(this.headers, {\n      'etag': res.get('ETag'),\n      'last-modified': res.get('Last-Modified')\n    });\n  }\n  return false;\n});\n\n/**\n * Check if the request is stale, aka\n * \"Last-Modified\" and / or the \"ETag\" for the\n * resource has changed.\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'stale', function stale() {\n  return !this.fresh;\n});\n\n/**\n * Check if the request was an _XMLHttpRequest_.\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'xhr', function xhr() {\n  var val = this.get('X-Requested-With') || '';\n  return val.toLowerCase() === 'xmlhttprequest';\n});\n\n/**\n * Helper function for creating a getter on an object.\n *\n * @param {Object} obj\n * @param {String} name\n * @param {Function} getter\n * @private\n */\nfunction defineGetter(obj, name, getter) {\n  Object.defineProperty(obj, name, {\n    configurable: true,\n    enumerable: true,\n    get: getter\n  });\n}", "map": {"version": 3, "names": ["accepts", "require", "deprecate", "isIP", "typeis", "http", "fresh", "parseRange", "parse", "proxyaddr", "req", "Object", "create", "IncomingMessage", "prototype", "module", "exports", "get", "header", "name", "TypeError", "lc", "toLowerCase", "headers", "referrer", "referer", "accept", "types", "apply", "arguments", "acceptsEncodings", "encodings", "acceptsEncoding", "function", "acceptsCharsets", "charsets", "acceptsCharset", "acceptsLanguages", "languages", "acceptsLanguage", "range", "size", "options", "param", "defaultValue", "params", "body", "query", "args", "length", "hasOwnProperty", "is", "arr", "Array", "isArray", "i", "defineGetter", "protocol", "proto", "connection", "encrypted", "trust", "app", "remoteAddress", "index", "indexOf", "substring", "trim", "secure", "ip", "ips", "addrs", "all", "reverse", "pop", "subdomains", "hostname", "offset", "split", "slice", "path", "pathname", "host", "trimRight", "method", "res", "status", "statusCode", "stale", "xhr", "val", "obj", "getter", "defineProperty", "configurable", "enumerable"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/express/lib/request.js"], "sourcesContent": ["/*!\n * express\n * Copyright(c) 2009-2013 <PERSON><PERSON>\n * Copyright(c) 2013 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar accepts = require('accepts');\nvar deprecate = require('depd')('express');\nvar isIP = require('net').isIP;\nvar typeis = require('type-is');\nvar http = require('http');\nvar fresh = require('fresh');\nvar parseRange = require('range-parser');\nvar parse = require('parseurl');\nvar proxyaddr = require('proxy-addr');\n\n/**\n * Request prototype.\n * @public\n */\n\nvar req = Object.create(http.IncomingMessage.prototype)\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = req\n\n/**\n * Return request header.\n *\n * The `Referrer` header field is special-cased,\n * both `Referrer` and `Referer` are interchangeable.\n *\n * Examples:\n *\n *     req.get('Content-Type');\n *     // => \"text/plain\"\n *\n *     req.get('content-type');\n *     // => \"text/plain\"\n *\n *     req.get('Something');\n *     // => undefined\n *\n * Aliased as `req.header()`.\n *\n * @param {String} name\n * @return {String}\n * @public\n */\n\nreq.get =\nreq.header = function header(name) {\n  if (!name) {\n    throw new TypeError('name argument is required to req.get');\n  }\n\n  if (typeof name !== 'string') {\n    throw new TypeError('name must be a string to req.get');\n  }\n\n  var lc = name.toLowerCase();\n\n  switch (lc) {\n    case 'referer':\n    case 'referrer':\n      return this.headers.referrer\n        || this.headers.referer;\n    default:\n      return this.headers[lc];\n  }\n};\n\n/**\n * To do: update docs.\n *\n * Check if the given `type(s)` is acceptable, returning\n * the best match when true, otherwise `undefined`, in which\n * case you should respond with 406 \"Not Acceptable\".\n *\n * The `type` value may be a single MIME type string\n * such as \"application/json\", an extension name\n * such as \"json\", a comma-delimited list such as \"json, html, text/plain\",\n * an argument list such as `\"json\", \"html\", \"text/plain\"`,\n * or an array `[\"json\", \"html\", \"text/plain\"]`. When a list\n * or array is given, the _best_ match, if any is returned.\n *\n * Examples:\n *\n *     // Accept: text/html\n *     req.accepts('html');\n *     // => \"html\"\n *\n *     // Accept: text/*, application/json\n *     req.accepts('html');\n *     // => \"html\"\n *     req.accepts('text/html');\n *     // => \"text/html\"\n *     req.accepts('json, text');\n *     // => \"json\"\n *     req.accepts('application/json');\n *     // => \"application/json\"\n *\n *     // Accept: text/*, application/json\n *     req.accepts('image/png');\n *     req.accepts('png');\n *     // => undefined\n *\n *     // Accept: text/*;q=.5, application/json\n *     req.accepts(['html', 'json']);\n *     req.accepts('html', 'json');\n *     req.accepts('html, json');\n *     // => \"json\"\n *\n * @param {String|Array} type(s)\n * @return {String|Array|Boolean}\n * @public\n */\n\nreq.accepts = function(){\n  var accept = accepts(this);\n  return accept.types.apply(accept, arguments);\n};\n\n/**\n * Check if the given `encoding`s are accepted.\n *\n * @param {String} ...encoding\n * @return {String|Array}\n * @public\n */\n\nreq.acceptsEncodings = function(){\n  var accept = accepts(this);\n  return accept.encodings.apply(accept, arguments);\n};\n\nreq.acceptsEncoding = deprecate.function(req.acceptsEncodings,\n  'req.acceptsEncoding: Use acceptsEncodings instead');\n\n/**\n * Check if the given `charset`s are acceptable,\n * otherwise you should respond with 406 \"Not Acceptable\".\n *\n * @param {String} ...charset\n * @return {String|Array}\n * @public\n */\n\nreq.acceptsCharsets = function(){\n  var accept = accepts(this);\n  return accept.charsets.apply(accept, arguments);\n};\n\nreq.acceptsCharset = deprecate.function(req.acceptsCharsets,\n  'req.acceptsCharset: Use acceptsCharsets instead');\n\n/**\n * Check if the given `lang`s are acceptable,\n * otherwise you should respond with 406 \"Not Acceptable\".\n *\n * @param {String} ...lang\n * @return {String|Array}\n * @public\n */\n\nreq.acceptsLanguages = function(){\n  var accept = accepts(this);\n  return accept.languages.apply(accept, arguments);\n};\n\nreq.acceptsLanguage = deprecate.function(req.acceptsLanguages,\n  'req.acceptsLanguage: Use acceptsLanguages instead');\n\n/**\n * Parse Range header field, capping to the given `size`.\n *\n * Unspecified ranges such as \"0-\" require knowledge of your resource length. In\n * the case of a byte range this is of course the total number of bytes. If the\n * Range header field is not given `undefined` is returned, `-1` when unsatisfiable,\n * and `-2` when syntactically invalid.\n *\n * When ranges are returned, the array has a \"type\" property which is the type of\n * range that is required (most commonly, \"bytes\"). Each array element is an object\n * with a \"start\" and \"end\" property for the portion of the range.\n *\n * The \"combine\" option can be set to `true` and overlapping & adjacent ranges\n * will be combined into a single range.\n *\n * NOTE: remember that ranges are inclusive, so for example \"Range: users=0-3\"\n * should respond with 4 users when available, not 3.\n *\n * @param {number} size\n * @param {object} [options]\n * @param {boolean} [options.combine=false]\n * @return {number|array}\n * @public\n */\n\nreq.range = function range(size, options) {\n  var range = this.get('Range');\n  if (!range) return;\n  return parseRange(size, range, options);\n};\n\n/**\n * Return the value of param `name` when present or `defaultValue`.\n *\n *  - Checks route placeholders, ex: _/user/:id_\n *  - Checks body params, ex: id=12, {\"id\":12}\n *  - Checks query string params, ex: ?id=12\n *\n * To utilize request bodies, `req.body`\n * should be an object. This can be done by using\n * the `bodyParser()` middleware.\n *\n * @param {String} name\n * @param {Mixed} [defaultValue]\n * @return {String}\n * @public\n */\n\nreq.param = function param(name, defaultValue) {\n  var params = this.params || {};\n  var body = this.body || {};\n  var query = this.query || {};\n\n  var args = arguments.length === 1\n    ? 'name'\n    : 'name, default';\n  deprecate('req.param(' + args + '): Use req.params, req.body, or req.query instead');\n\n  if (null != params[name] && params.hasOwnProperty(name)) return params[name];\n  if (null != body[name]) return body[name];\n  if (null != query[name]) return query[name];\n\n  return defaultValue;\n};\n\n/**\n * Check if the incoming request contains the \"Content-Type\"\n * header field, and it contains the given mime `type`.\n *\n * Examples:\n *\n *      // With Content-Type: text/html; charset=utf-8\n *      req.is('html');\n *      req.is('text/html');\n *      req.is('text/*');\n *      // => true\n *\n *      // When Content-Type is application/json\n *      req.is('json');\n *      req.is('application/json');\n *      req.is('application/*');\n *      // => true\n *\n *      req.is('html');\n *      // => false\n *\n * @param {String|Array} types...\n * @return {String|false|null}\n * @public\n */\n\nreq.is = function is(types) {\n  var arr = types;\n\n  // support flattened arguments\n  if (!Array.isArray(types)) {\n    arr = new Array(arguments.length);\n    for (var i = 0; i < arr.length; i++) {\n      arr[i] = arguments[i];\n    }\n  }\n\n  return typeis(this, arr);\n};\n\n/**\n * Return the protocol string \"http\" or \"https\"\n * when requested with TLS. When the \"trust proxy\"\n * setting trusts the socket address, the\n * \"X-Forwarded-Proto\" header field will be trusted\n * and used if present.\n *\n * If you're running behind a reverse proxy that\n * supplies https for you this may be enabled.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'protocol', function protocol(){\n  var proto = this.connection.encrypted\n    ? 'https'\n    : 'http';\n  var trust = this.app.get('trust proxy fn');\n\n  if (!trust(this.connection.remoteAddress, 0)) {\n    return proto;\n  }\n\n  // Note: X-Forwarded-Proto is normally only ever a\n  //       single value, but this is to be safe.\n  var header = this.get('X-Forwarded-Proto') || proto\n  var index = header.indexOf(',')\n\n  return index !== -1\n    ? header.substring(0, index).trim()\n    : header.trim()\n});\n\n/**\n * Short-hand for:\n *\n *    req.protocol === 'https'\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'secure', function secure(){\n  return this.protocol === 'https';\n});\n\n/**\n * Return the remote address from the trusted proxy.\n *\n * The is the remote address on the socket unless\n * \"trust proxy\" is set.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'ip', function ip(){\n  var trust = this.app.get('trust proxy fn');\n  return proxyaddr(this, trust);\n});\n\n/**\n * When \"trust proxy\" is set, trusted proxy addresses + client.\n *\n * For example if the value were \"client, proxy1, proxy2\"\n * you would receive the array `[\"client\", \"proxy1\", \"proxy2\"]`\n * where \"proxy2\" is the furthest down-stream and \"proxy1\" and\n * \"proxy2\" were trusted.\n *\n * @return {Array}\n * @public\n */\n\ndefineGetter(req, 'ips', function ips() {\n  var trust = this.app.get('trust proxy fn');\n  var addrs = proxyaddr.all(this, trust);\n\n  // reverse the order (to farthest -> closest)\n  // and remove socket address\n  addrs.reverse().pop()\n\n  return addrs\n});\n\n/**\n * Return subdomains as an array.\n *\n * Subdomains are the dot-separated parts of the host before the main domain of\n * the app. By default, the domain of the app is assumed to be the last two\n * parts of the host. This can be changed by setting \"subdomain offset\".\n *\n * For example, if the domain is \"tobi.ferrets.example.com\":\n * If \"subdomain offset\" is not set, req.subdomains is `[\"ferrets\", \"tobi\"]`.\n * If \"subdomain offset\" is 3, req.subdomains is `[\"tobi\"]`.\n *\n * @return {Array}\n * @public\n */\n\ndefineGetter(req, 'subdomains', function subdomains() {\n  var hostname = this.hostname;\n\n  if (!hostname) return [];\n\n  var offset = this.app.get('subdomain offset');\n  var subdomains = !isIP(hostname)\n    ? hostname.split('.').reverse()\n    : [hostname];\n\n  return subdomains.slice(offset);\n});\n\n/**\n * Short-hand for `url.parse(req.url).pathname`.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'path', function path() {\n  return parse(this).pathname;\n});\n\n/**\n * Parse the \"Host\" header field to a hostname.\n *\n * When the \"trust proxy\" setting trusts the socket\n * address, the \"X-Forwarded-Host\" header field will\n * be trusted.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'hostname', function hostname(){\n  var trust = this.app.get('trust proxy fn');\n  var host = this.get('X-Forwarded-Host');\n\n  if (!host || !trust(this.connection.remoteAddress, 0)) {\n    host = this.get('Host');\n  } else if (host.indexOf(',') !== -1) {\n    // Note: X-Forwarded-Host is normally only ever a\n    //       single value, but this is to be safe.\n    host = host.substring(0, host.indexOf(',')).trimRight()\n  }\n\n  if (!host) return;\n\n  // IPv6 literal support\n  var offset = host[0] === '['\n    ? host.indexOf(']') + 1\n    : 0;\n  var index = host.indexOf(':', offset);\n\n  return index !== -1\n    ? host.substring(0, index)\n    : host;\n});\n\n// TODO: change req.host to return host in next major\n\ndefineGetter(req, 'host', deprecate.function(function host(){\n  return this.hostname;\n}, 'req.host: Use req.hostname instead'));\n\n/**\n * Check if the request is fresh, aka\n * Last-Modified and/or the ETag\n * still match.\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'fresh', function(){\n  var method = this.method;\n  var res = this.res\n  var status = res.statusCode\n\n  // GET or HEAD for weak freshness validation only\n  if ('GET' !== method && 'HEAD' !== method) return false;\n\n  // 2xx or 304 as per rfc2616 14.26\n  if ((status >= 200 && status < 300) || 304 === status) {\n    return fresh(this.headers, {\n      'etag': res.get('ETag'),\n      'last-modified': res.get('Last-Modified')\n    })\n  }\n\n  return false;\n});\n\n/**\n * Check if the request is stale, aka\n * \"Last-Modified\" and / or the \"ETag\" for the\n * resource has changed.\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'stale', function stale(){\n  return !this.fresh;\n});\n\n/**\n * Check if the request was an _XMLHttpRequest_.\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'xhr', function xhr(){\n  var val = this.get('X-Requested-With') || '';\n  return val.toLowerCase() === 'xmlhttprequest';\n});\n\n/**\n * Helper function for creating a getter on an object.\n *\n * @param {Object} obj\n * @param {String} name\n * @param {Function} getter\n * @private\n */\nfunction defineGetter(obj, name, getter) {\n  Object.defineProperty(obj, name, {\n    configurable: true,\n    enumerable: true,\n    get: getter\n  });\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AAEA,IAAIA,OAAO,GAAGC,OAAO,CAAC,SAAS,CAAC;AAChC,IAAIC,SAAS,GAAGD,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC;AAC1C,IAAIE,IAAI,GAAGF,OAAO,CAAC,KAAK,CAAC,CAACE,IAAI;AAC9B,IAAIC,MAAM,GAAGH,OAAO,CAAC,SAAS,CAAC;AAC/B,IAAII,IAAI,GAAGJ,OAAO,CAAC,MAAM,CAAC;AAC1B,IAAIK,KAAK,GAAGL,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIM,UAAU,GAAGN,OAAO,CAAC,cAAc,CAAC;AACxC,IAAIO,KAAK,GAAGP,OAAO,CAAC,UAAU,CAAC;AAC/B,IAAIQ,SAAS,GAAGR,OAAO,CAAC,YAAY,CAAC;;AAErC;AACA;AACA;AACA;;AAEA,IAAIS,GAAG,GAAGC,MAAM,CAACC,MAAM,CAACP,IAAI,CAACQ,eAAe,CAACC,SAAS,CAAC;;AAEvD;AACA;AACA;AACA;;AAEAC,MAAM,CAACC,OAAO,GAAGN,GAAG;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAA,GAAG,CAACO,GAAG,GACPP,GAAG,CAACQ,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACjC,IAAI,CAACA,IAAI,EAAE;IACT,MAAM,IAAIC,SAAS,CAAC,sCAAsC,CAAC;EAC7D;EAEA,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAIC,SAAS,CAAC,kCAAkC,CAAC;EACzD;EAEA,IAAIC,EAAE,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;EAE3B,QAAQD,EAAE;IACR,KAAK,SAAS;IACd,KAAK,UAAU;MACb,OAAO,IAAI,CAACE,OAAO,CAACC,QAAQ,IACvB,IAAI,CAACD,OAAO,CAACE,OAAO;IAC3B;MACE,OAAO,IAAI,CAACF,OAAO,CAACF,EAAE,CAAC;EAC3B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAX,GAAG,CAACV,OAAO,GAAG,YAAU;EACtB,IAAI0B,MAAM,GAAG1B,OAAO,CAAC,IAAI,CAAC;EAC1B,OAAO0B,MAAM,CAACC,KAAK,CAACC,KAAK,CAACF,MAAM,EAAEG,SAAS,CAAC;AAC9C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAnB,GAAG,CAACoB,gBAAgB,GAAG,YAAU;EAC/B,IAAIJ,MAAM,GAAG1B,OAAO,CAAC,IAAI,CAAC;EAC1B,OAAO0B,MAAM,CAACK,SAAS,CAACH,KAAK,CAACF,MAAM,EAAEG,SAAS,CAAC;AAClD,CAAC;AAEDnB,GAAG,CAACsB,eAAe,GAAG9B,SAAS,CAAC+B,QAAQ,CAACvB,GAAG,CAACoB,gBAAgB,EAC3D,mDAAmD,CAAC;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEApB,GAAG,CAACwB,eAAe,GAAG,YAAU;EAC9B,IAAIR,MAAM,GAAG1B,OAAO,CAAC,IAAI,CAAC;EAC1B,OAAO0B,MAAM,CAACS,QAAQ,CAACP,KAAK,CAACF,MAAM,EAAEG,SAAS,CAAC;AACjD,CAAC;AAEDnB,GAAG,CAAC0B,cAAc,GAAGlC,SAAS,CAAC+B,QAAQ,CAACvB,GAAG,CAACwB,eAAe,EACzD,iDAAiD,CAAC;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAxB,GAAG,CAAC2B,gBAAgB,GAAG,YAAU;EAC/B,IAAIX,MAAM,GAAG1B,OAAO,CAAC,IAAI,CAAC;EAC1B,OAAO0B,MAAM,CAACY,SAAS,CAACV,KAAK,CAACF,MAAM,EAAEG,SAAS,CAAC;AAClD,CAAC;AAEDnB,GAAG,CAAC6B,eAAe,GAAGrC,SAAS,CAAC+B,QAAQ,CAACvB,GAAG,CAAC2B,gBAAgB,EAC3D,mDAAmD,CAAC;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA3B,GAAG,CAAC8B,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,IAAIF,KAAK,GAAG,IAAI,CAACvB,GAAG,CAAC,OAAO,CAAC;EAC7B,IAAI,CAACuB,KAAK,EAAE;EACZ,OAAOjC,UAAU,CAACkC,IAAI,EAAED,KAAK,EAAEE,OAAO,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAhC,GAAG,CAACiC,KAAK,GAAG,SAASA,KAAKA,CAACxB,IAAI,EAAEyB,YAAY,EAAE;EAC7C,IAAIC,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,CAAC,CAAC;EAC9B,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI,IAAI,CAAC,CAAC;EAC1B,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC;EAE5B,IAAIC,IAAI,GAAGnB,SAAS,CAACoB,MAAM,KAAK,CAAC,GAC7B,MAAM,GACN,eAAe;EACnB/C,SAAS,CAAC,YAAY,GAAG8C,IAAI,GAAG,mDAAmD,CAAC;EAEpF,IAAI,IAAI,IAAIH,MAAM,CAAC1B,IAAI,CAAC,IAAI0B,MAAM,CAACK,cAAc,CAAC/B,IAAI,CAAC,EAAE,OAAO0B,MAAM,CAAC1B,IAAI,CAAC;EAC5E,IAAI,IAAI,IAAI2B,IAAI,CAAC3B,IAAI,CAAC,EAAE,OAAO2B,IAAI,CAAC3B,IAAI,CAAC;EACzC,IAAI,IAAI,IAAI4B,KAAK,CAAC5B,IAAI,CAAC,EAAE,OAAO4B,KAAK,CAAC5B,IAAI,CAAC;EAE3C,OAAOyB,YAAY;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAlC,GAAG,CAACyC,EAAE,GAAG,SAASA,EAAEA,CAACxB,KAAK,EAAE;EAC1B,IAAIyB,GAAG,GAAGzB,KAAK;;EAEf;EACA,IAAI,CAAC0B,KAAK,CAACC,OAAO,CAAC3B,KAAK,CAAC,EAAE;IACzByB,GAAG,GAAG,IAAIC,KAAK,CAACxB,SAAS,CAACoB,MAAM,CAAC;IACjC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACH,MAAM,EAAEM,CAAC,EAAE,EAAE;MACnCH,GAAG,CAACG,CAAC,CAAC,GAAG1B,SAAS,CAAC0B,CAAC,CAAC;IACvB;EACF;EAEA,OAAOnD,MAAM,CAAC,IAAI,EAAEgD,GAAG,CAAC;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAI,YAAY,CAAC9C,GAAG,EAAE,UAAU,EAAE,SAAS+C,QAAQA,CAAA,EAAE;EAC/C,IAAIC,KAAK,GAAG,IAAI,CAACC,UAAU,CAACC,SAAS,GACjC,OAAO,GACP,MAAM;EACV,IAAIC,KAAK,GAAG,IAAI,CAACC,GAAG,CAAC7C,GAAG,CAAC,gBAAgB,CAAC;EAE1C,IAAI,CAAC4C,KAAK,CAAC,IAAI,CAACF,UAAU,CAACI,aAAa,EAAE,CAAC,CAAC,EAAE;IAC5C,OAAOL,KAAK;EACd;;EAEA;EACA;EACA,IAAIxC,MAAM,GAAG,IAAI,CAACD,GAAG,CAAC,mBAAmB,CAAC,IAAIyC,KAAK;EACnD,IAAIM,KAAK,GAAG9C,MAAM,CAAC+C,OAAO,CAAC,GAAG,CAAC;EAE/B,OAAOD,KAAK,KAAK,CAAC,CAAC,GACf9C,MAAM,CAACgD,SAAS,CAAC,CAAC,EAAEF,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC,GACjCjD,MAAM,CAACiD,IAAI,CAAC,CAAC;AACnB,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAX,YAAY,CAAC9C,GAAG,EAAE,QAAQ,EAAE,SAAS0D,MAAMA,CAAA,EAAE;EAC3C,OAAO,IAAI,CAACX,QAAQ,KAAK,OAAO;AAClC,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAD,YAAY,CAAC9C,GAAG,EAAE,IAAI,EAAE,SAAS2D,EAAEA,CAAA,EAAE;EACnC,IAAIR,KAAK,GAAG,IAAI,CAACC,GAAG,CAAC7C,GAAG,CAAC,gBAAgB,CAAC;EAC1C,OAAOR,SAAS,CAAC,IAAI,EAAEoD,KAAK,CAAC;AAC/B,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAL,YAAY,CAAC9C,GAAG,EAAE,KAAK,EAAE,SAAS4D,GAAGA,CAAA,EAAG;EACtC,IAAIT,KAAK,GAAG,IAAI,CAACC,GAAG,CAAC7C,GAAG,CAAC,gBAAgB,CAAC;EAC1C,IAAIsD,KAAK,GAAG9D,SAAS,CAAC+D,GAAG,CAAC,IAAI,EAAEX,KAAK,CAAC;;EAEtC;EACA;EACAU,KAAK,CAACE,OAAO,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC;EAErB,OAAOH,KAAK;AACd,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAf,YAAY,CAAC9C,GAAG,EAAE,YAAY,EAAE,SAASiE,UAAUA,CAAA,EAAG;EACpD,IAAIC,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAE5B,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;EAExB,IAAIC,MAAM,GAAG,IAAI,CAACf,GAAG,CAAC7C,GAAG,CAAC,kBAAkB,CAAC;EAC7C,IAAI0D,UAAU,GAAG,CAACxE,IAAI,CAACyE,QAAQ,CAAC,GAC5BA,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAACL,OAAO,CAAC,CAAC,GAC7B,CAACG,QAAQ,CAAC;EAEd,OAAOD,UAAU,CAACI,KAAK,CAACF,MAAM,CAAC;AACjC,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;;AAEArB,YAAY,CAAC9C,GAAG,EAAE,MAAM,EAAE,SAASsE,IAAIA,CAAA,EAAG;EACxC,OAAOxE,KAAK,CAAC,IAAI,CAAC,CAACyE,QAAQ;AAC7B,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAzB,YAAY,CAAC9C,GAAG,EAAE,UAAU,EAAE,SAASkE,QAAQA,CAAA,EAAE;EAC/C,IAAIf,KAAK,GAAG,IAAI,CAACC,GAAG,CAAC7C,GAAG,CAAC,gBAAgB,CAAC;EAC1C,IAAIiE,IAAI,GAAG,IAAI,CAACjE,GAAG,CAAC,kBAAkB,CAAC;EAEvC,IAAI,CAACiE,IAAI,IAAI,CAACrB,KAAK,CAAC,IAAI,CAACF,UAAU,CAACI,aAAa,EAAE,CAAC,CAAC,EAAE;IACrDmB,IAAI,GAAG,IAAI,CAACjE,GAAG,CAAC,MAAM,CAAC;EACzB,CAAC,MAAM,IAAIiE,IAAI,CAACjB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IACnC;IACA;IACAiB,IAAI,GAAGA,IAAI,CAAChB,SAAS,CAAC,CAAC,EAAEgB,IAAI,CAACjB,OAAO,CAAC,GAAG,CAAC,CAAC,CAACkB,SAAS,CAAC,CAAC;EACzD;EAEA,IAAI,CAACD,IAAI,EAAE;;EAEX;EACA,IAAIL,MAAM,GAAGK,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,GACxBA,IAAI,CAACjB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GACrB,CAAC;EACL,IAAID,KAAK,GAAGkB,IAAI,CAACjB,OAAO,CAAC,GAAG,EAAEY,MAAM,CAAC;EAErC,OAAOb,KAAK,KAAK,CAAC,CAAC,GACfkB,IAAI,CAAChB,SAAS,CAAC,CAAC,EAAEF,KAAK,CAAC,GACxBkB,IAAI;AACV,CAAC,CAAC;;AAEF;;AAEA1B,YAAY,CAAC9C,GAAG,EAAE,MAAM,EAAER,SAAS,CAAC+B,QAAQ,CAAC,SAASiD,IAAIA,CAAA,EAAE;EAC1D,OAAO,IAAI,CAACN,QAAQ;AACtB,CAAC,EAAE,oCAAoC,CAAC,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEApB,YAAY,CAAC9C,GAAG,EAAE,OAAO,EAAE,YAAU;EACnC,IAAI0E,MAAM,GAAG,IAAI,CAACA,MAAM;EACxB,IAAIC,GAAG,GAAG,IAAI,CAACA,GAAG;EAClB,IAAIC,MAAM,GAAGD,GAAG,CAACE,UAAU;;EAE3B;EACA,IAAI,KAAK,KAAKH,MAAM,IAAI,MAAM,KAAKA,MAAM,EAAE,OAAO,KAAK;;EAEvD;EACA,IAAKE,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,IAAK,GAAG,KAAKA,MAAM,EAAE;IACrD,OAAOhF,KAAK,CAAC,IAAI,CAACiB,OAAO,EAAE;MACzB,MAAM,EAAE8D,GAAG,CAACpE,GAAG,CAAC,MAAM,CAAC;MACvB,eAAe,EAAEoE,GAAG,CAACpE,GAAG,CAAC,eAAe;IAC1C,CAAC,CAAC;EACJ;EAEA,OAAO,KAAK;AACd,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAuC,YAAY,CAAC9C,GAAG,EAAE,OAAO,EAAE,SAAS8E,KAAKA,CAAA,EAAE;EACzC,OAAO,CAAC,IAAI,CAAClF,KAAK;AACpB,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;;AAEAkD,YAAY,CAAC9C,GAAG,EAAE,KAAK,EAAE,SAAS+E,GAAGA,CAAA,EAAE;EACrC,IAAIC,GAAG,GAAG,IAAI,CAACzE,GAAG,CAAC,kBAAkB,CAAC,IAAI,EAAE;EAC5C,OAAOyE,GAAG,CAACpE,WAAW,CAAC,CAAC,KAAK,gBAAgB;AAC/C,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkC,YAAYA,CAACmC,GAAG,EAAExE,IAAI,EAAEyE,MAAM,EAAE;EACvCjF,MAAM,CAACkF,cAAc,CAACF,GAAG,EAAExE,IAAI,EAAE;IAC/B2E,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAI;IAChB9E,GAAG,EAAE2E;EACP,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}