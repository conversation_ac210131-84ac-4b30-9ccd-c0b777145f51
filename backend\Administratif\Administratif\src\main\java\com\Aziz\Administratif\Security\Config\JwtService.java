package com.Aziz.Administratif.Security.Config;

import com.Aziz.Administratif.Entity.Ressource;
import com.Aziz.Administratif.Entity.User;
import com.Aziz.Administratif.Enum.Role;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.security.Key;
import java.util.*;
import java.util.function.Function;

@Service
public class JwtService {

	private static final String SECRETKEY = "6c262377794f202e586a7137696331627776512b26242b565f247d705f";

	// Générer un token pour un User
	public String generateToken(User user) {
		return generateToken(new HashMap<>(), user);
	}

	// Générer un token avec des claims supplémentaires
	public String generateToken(Map<String, Object> extraClaims, User user) {
		Role role = user.getRole();
		List<String> allowedPaths = getAllowedPathsForRole(role, user);

		// Ajouter les informations au token
		extraClaims.put("role", role.name());
		extraClaims.put("groupe", user.getGroupe());  // Add the groupe claim
		extraClaims.put("allowedPaths", allowedPaths);  // Add dynamically calculated allowed paths
		extraClaims.put("state", user.getState());  // Add the groupe claim

		return Jwts.builder()
				.setClaims(extraClaims)
				.setSubject(user.getUsername()) // Nom d'utilisateur comme subject
				.setIssuedAt(new Date(System.currentTimeMillis()))
				.setExpiration(new Date(System.currentTimeMillis() + 1000 * 60 * 60 * 24)) // Expiration : 1 jour
				.signWith(getSignInKey(), SignatureAlgorithm.HS256)
				.compact();
	}

	// Extraire le nom d'utilisateur du token
	public String extractUsername(String token) {
		return extractClaim(token, Claims::getSubject);
	}

	// Extraire une claim spécifique
	public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
		final Claims claims = extractClaims(token);
		return claimsResolver.apply(claims);
	}

	// Vérifier si un token est valide
	public boolean isTokenValid(String token, UserDetails userDetails) {
		final String username = extractUsername(token);
		return (username.equals(userDetails.getUsername())) && !isTokenExpired(token);
	}

	// Vérifier si le token est expiré
	private boolean isTokenExpired(String token) {
		return extractExpiration(token).before(new Date());
	}

	// Extraire la date d'expiration du token
	private Date extractExpiration(String token) {
		return extractClaim(token, Claims::getExpiration);
	}

	// Extraire les claims du token
	private Claims extractClaims(String token) {
		return Jwts.parserBuilder()
				.setSigningKey(getSignInKey())
				.build()
				.parseClaimsJws(token)
				.getBody();
	}

	// Clé secrète pour signer le JWT
	private Key getSignInKey() {
		byte[] keyBytes = Decoders.BASE64.decode(SECRETKEY);
		return Keys.hmacShaKeyFor(keyBytes);
	}
	private List<String> getAllowedPathsForRole(Role role, User user) {
		List<String> allowedPaths = new ArrayList<>();

		if (role == Role.RESPONSABLE) {
			// Retrieve group resources dynamically based on the user's group
			for (Ressource ressource : user.getGroupe().getRessources()) {
				String linkPath = ressource.getLink_path();

				// Ensure the link path is relative, e.g., '/transactions' instead of 'http://localhost:4200/transactions'
				if (linkPath.startsWith("http://localhost:4200")) {
					linkPath = linkPath.substring("http://localhost:4200".length()); // Remove the base URL part
				}

				// If the path does not start with '/', prepend it
				if (!linkPath.startsWith("/")) {
					linkPath = "/" + linkPath;
				}

				allowedPaths.add(linkPath); // Dynamically add each resource path
			}
		} else if (role == Role.ADMIN) {
			// For Admins, return the relative URL paths
			allowedPaths.add("/admin-dashboard");
			allowedPaths.add("/user-management");
		}

		return allowedPaths;
	}


}