{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { AuthGuard } from './auth/auth.guard';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\nimport { AdminDashComponent } from './admin-dash/admin-dash.component';\nimport { ResponsableEditComponent } from './responsable-edit/responsable-edit.component';\nimport { UsersComponent } from './users/users.component';\nimport { GroupsComponent } from './groups/groups.component';\nimport { ModifyGroupComponent } from './modify-group/modify-group.component';\nimport { AddResponsableComponent } from './add-responsable/add-responsable.component'; // Import AddResponsableComponent\nimport { HabilitationComponent } from './habilitation/habilitation.component'; // Import the component\nimport { ForgotPasswordComponent } from './forgot-password/forgot-password.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'signup',\n  component: SignupComponent\n}, {\n  path: 'edit-responsable/:id',\n  component: ResponsableEditComponent\n}, {\n  path: 'adminDash',\n  component: AdminDashComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'transactions',\n  component: TransactionsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'actions',\n  component: ActionsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'actionnaires',\n  component: ActionnairesComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'reports',\n  component: ReportsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'port',\n  component: PortefeuillesComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'ResDash',\n  component: ResponDashboardComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'users',\n  component: UsersComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'groups',\n  component: GroupsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'edit-groupe/:id',\n  component: ModifyGroupComponent\n}, {\n  path: 'add-responsable',\n  component: AddResponsableComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'not-authorized',\n  component: NotAuthorizedComponent\n}, {\n  path: 'habilitation/:id',\n  component: HabilitationComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'forgot-password',\n  component: ForgotPasswordComponent\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "SignupComponent", "ResponDashboardComponent", "NotAuthorizedComponent", "<PERSON><PERSON><PERSON><PERSON>", "TransactionsComponent", "ActionsComponent", "ActionnairesComponent", "ReportsComponent", "PortefeuillesComponent", "AdminDashComponent", "ResponsableEditComponent", "UsersComponent", "GroupsComponent", "ModifyGroupComponent", "AddResponsableComponent", "HabilitationComponent", "ForgotPasswordComponent", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { LoginComponent } from './auth/login/login.component';\nimport { SignupComponent } from './auth/signup/signup.component';\nimport { ResponDashboardComponent } from './respon-dashboard/respon-dashboard.component';\nimport { NotAuthorizedComponent } from './not-authorized/not-authorized.component';\nimport { AuthGuard } from './auth/auth.guard';\nimport { TransactionsComponent } from './transactions/transactions.component';\nimport { ActionsComponent } from './actions/actions.component';\nimport { ActionnairesComponent } from './actionnaires/actionnaires.component';\nimport { ReportsComponent } from './reports/reports.component';\nimport { PortefeuillesComponent } from './portefeuilles/portefeuilles.component';\nimport { AdminDashComponent } from './admin-dash/admin-dash.component';\nimport { ResponsableEditComponent } from './responsable-edit/responsable-edit.component';\nimport { UsersComponent } from './users/users.component';\nimport { GroupsComponent } from './groups/groups.component';\nimport { ModifyGroupComponent } from './modify-group/modify-group.component';\nimport { AddResponsableComponent } from './add-responsable/add-responsable.component';  // Import AddResponsableComponent\nimport { HabilitationComponent } from './habilitation/habilitation.component';  // Import the component\nimport { ForgotPasswordComponent } from './forgot-password/forgot-password.component';\n\nconst routes: Routes = [\n  { path: '', redirectTo: 'login', pathMatch: 'full' },\n  { path: 'login', component: LoginComponent },\n  { path: 'signup', component: SignupComponent },\n  { path: 'edit-responsable/:id', component: ResponsableEditComponent },\n  { path: 'adminDash', component: AdminDashComponent, canActivate: [AuthGuard] },\n  { path: 'transactions', component: TransactionsComponent, canActivate: [AuthGuard] },\n  { path: 'actions', component: ActionsComponent, canActivate: [AuthGuard] },\n  { path: 'actionnaires', component: ActionnairesComponent, canActivate: [AuthGuard] },\n  { path: 'reports', component: ReportsComponent, canActivate: [AuthGuard] },\n  { path: 'port', component: PortefeuillesComponent, canActivate: [AuthGuard] },\n  { path: 'ResDash', component: ResponDashboardComponent, canActivate: [AuthGuard] },\n  { path: 'users', component: UsersComponent, canActivate: [AuthGuard] }, // Admin only\n  { path: 'groups', component: GroupsComponent, canActivate: [AuthGuard] }, // Admin only\n  { path: 'edit-groupe/:id', component: ModifyGroupComponent }, // Added route for editing a group\n  { path: 'add-responsable', component: AddResponsableComponent, canActivate: [AuthGuard] },  // Added route for AddResponsableComponent\n  { path: 'not-authorized', component: NotAuthorizedComponent },\n  { path: 'habilitation/:id', component: HabilitationComponent, canActivate: [AuthGuard] },  // Route with the dynamic id\n  { path: 'forgot-password', component: ForgotPasswordComponent },\n\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,sBAAsB,QAAQ,yCAAyC;AAChF,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,uBAAuB,QAAQ,6CAA6C,CAAC,CAAE;AACxF,SAASC,qBAAqB,QAAQ,uCAAuC,CAAC,CAAE;AAChF,SAASC,uBAAuB,QAAQ,6CAA6C;;;AAErF,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,OAAO;EAAEC,SAAS,EAAE;AAAM,CAAE,EACpD;EAAEF,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEtB;AAAc,CAAE,EAC5C;EAAEmB,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAErB;AAAe,CAAE,EAC9C;EAAEkB,IAAI,EAAE,sBAAsB;EAAEG,SAAS,EAAEX;AAAwB,CAAE,EACrE;EAAEQ,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAEZ,kBAAkB;EAAEa,WAAW,EAAE,CAACnB,SAAS;AAAC,CAAE,EAC9E;EAAEe,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAEjB,qBAAqB;EAAEkB,WAAW,EAAE,CAACnB,SAAS;AAAC,CAAE,EACpF;EAAEe,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEhB,gBAAgB;EAAEiB,WAAW,EAAE,CAACnB,SAAS;AAAC,CAAE,EAC1E;EAAEe,IAAI,EAAE,cAAc;EAAEG,SAAS,EAAEf,qBAAqB;EAAEgB,WAAW,EAAE,CAACnB,SAAS;AAAC,CAAE,EACpF;EAAEe,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEd,gBAAgB;EAAEe,WAAW,EAAE,CAACnB,SAAS;AAAC,CAAE,EAC1E;EAAEe,IAAI,EAAE,MAAM;EAAEG,SAAS,EAAEb,sBAAsB;EAAEc,WAAW,EAAE,CAACnB,SAAS;AAAC,CAAE,EAC7E;EAAEe,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEpB,wBAAwB;EAAEqB,WAAW,EAAE,CAACnB,SAAS;AAAC,CAAE,EAClF;EAAEe,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEV,cAAc;EAAEW,WAAW,EAAE,CAACnB,SAAS;AAAC,CAAE,EACtE;EAAEe,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAET,eAAe;EAAEU,WAAW,EAAE,CAACnB,SAAS;AAAC,CAAE,EACxE;EAAEe,IAAI,EAAE,iBAAiB;EAAEG,SAAS,EAAER;AAAoB,CAAE,EAC5D;EAAEK,IAAI,EAAE,iBAAiB;EAAEG,SAAS,EAAEP,uBAAuB;EAAEQ,WAAW,EAAE,CAACnB,SAAS;AAAC,CAAE,EACzF;EAAEe,IAAI,EAAE,gBAAgB;EAAEG,SAAS,EAAEnB;AAAsB,CAAE,EAC7D;EAAEgB,IAAI,EAAE,kBAAkB;EAAEG,SAAS,EAAEN,qBAAqB;EAAEO,WAAW,EAAE,CAACnB,SAAS;AAAC,CAAE,EACxF;EAAEe,IAAI,EAAE,iBAAiB;EAAEG,SAAS,EAAEL;AAAuB,CAAE,CAEhE;AAMD,OAAM,MAAOO,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBzB,YAAY,CAAC0B,OAAO,CAACP,MAAM,CAAC,EAC5BnB,YAAY;IAAA;EAAA;;;2EAEXyB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAA5B,YAAA;IAAA6B,OAAA,GAFjB7B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}