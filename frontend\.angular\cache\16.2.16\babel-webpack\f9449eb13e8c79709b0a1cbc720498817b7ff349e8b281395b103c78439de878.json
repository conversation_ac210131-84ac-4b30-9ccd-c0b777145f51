{"ast": null, "code": "import * as feather from 'feather-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = [\"myChart\"];\nconst _c1 = \"body[_ngcontent-%COMP%] {\\n  font-size: .875rem;\\n}\\n\\n.feather[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  vertical-align: text-bottom;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 100; \\n\\n  padding: 0;\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);\\n}\\n\\n.sidebar-sticky[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 48px; \\n\\n  height: calc(100vh - 48px);\\n  padding-top: .5rem;\\n  overflow-x: hidden;\\n  overflow-y: auto; \\n\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  color: #999;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover   .feather[_ngcontent-%COMP%], .sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%]   .feather[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.sidebar-heading[_ngcontent-%COMP%] {\\n  font-size: .75rem;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n\\n.navbar-brand[_ngcontent-%COMP%] {\\n  padding-top: .75rem;\\n  padding-bottom: .75rem;\\n  font-size: 1rem;\\n  background-color: rgba(0, 0, 0, .25);\\n  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);\\n}\\n\\n.navbar[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  padding: .75rem 1rem;\\n  border-width: 0;\\n  border-radius: 0;\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: rgba(255, 255, 255, .1);\\n  border-color: rgba(255, 255, 255, .1);\\n}\\n\\n.form-control-dark[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, .25);\\n}\\n\\n\\n\\n\\n\\n\\n.border-top[_ngcontent-%COMP%] { border-top: 1px solid #e5e5e5; }\\n.border-bottom[_ngcontent-%COMP%] { border-bottom: 1px solid #e5e5e5; }\\n\\n\\n.btn[_ngcontent-%COMP%] {\\npadding: 8px 16px;\\nfont-size: 14px;\\nborder: none;\\ncursor: pointer;\\nborder-radius: 4px;\\nfont-weight: bold;\\ntransition: all 0.3s ease;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\nbackground-color: #ff4d4d; \\n\\ncolor: white;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%]:hover {\\nbackground-color: #ff1a1a;\\n}\\n\\n.modify-btn[_ngcontent-%COMP%] {\\nbackground-color: #4d94ff; \\n\\ncolor: white;\\n}\\n\\n.modify-btn[_ngcontent-%COMP%]:hover {\\nbackground-color: #0066cc;\\n}\\n\\n\\n\\n\\n\\n.edit-icon[_ngcontent-%COMP%] {\\n  color: #007bff;  \\n\\n}\\n\\n\\n\\n.delete-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;  \\n\\n}\\n\\n\\n\\n.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;  \\n\\n}\\n\\n.delete-icon[_ngcontent-%COMP%]:hover {\\n  color: #c82333;  \\n\\n}\\n\\n.modal[_ngcontent-%COMP%]   .modal-dialog[_ngcontent-%COMP%] {\\nmax-width: 400px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%], .modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%], .modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\\npadding: 20px 30px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\nborder-radius: 3px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\\nbackground: #ecf0f1;\\nborder-radius: 0 0 3px 3px;\\n}\\n.modal[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\\n    display: inline-block;\\n}\\n.modal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\nborder-radius: 2px;\\nbox-shadow: none;\\nborder-color: #dddddd;\\n}\\n.modal[_ngcontent-%COMP%]   textarea.form-control[_ngcontent-%COMP%] {\\nresize: vertical;\\n}\\n.modal[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\nborder-radius: 2px;\\nmin-width: 100px;\\n}\\t\\n.modal[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\nfont-weight: normal;\\n}\\t\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class ActionsComponent {\n  constructor(router // Inject Router for page navigation\n  ) {\n    this.router = router;\n  }\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  }\n\n  logout() {\n    // Clear token and other data\n    localStorage.removeItem('jwt_token'); // Fix: Remove 'jwt_token'\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    // Optionally, check that these items are removed:\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    // Navigate to login page\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function ActionsComponent_Factory(t) {\n      return new (t || ActionsComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionsComponent,\n      selectors: [[\"app-actions\"]],\n      viewQuery: function ActionsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myChartRef = _t.first);\n        }\n      },\n      decls: 62,\n      vars: 0,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1, shrink-to-fit=no\"], [\"name\", \"description\", \"content\", \"\"], [\"name\", \"author\", \"content\", \"\"], [\"rel\", \"icon\", \"href\", \"/docs/4.0/assets/img/favicons/favicon.ico\"], [\"rel\", \"canonical\", \"href\", \"https://getbootstrap.com/docs/4.0/examples/dashboard/\"], [\"href\", \"../../../dist/css/bootstrap.min.css\"], [\"width\", \"900\", \"height\", \"380\", 2, \"background-color\", \"lightgrey\"], [\"myChart\", \"\"], [1, \"navbar\", \"navbar-dark\", \"sticky-top\", \"bg-dark\", \"flex-md-nowrap\", \"p-0\"], [\"href\", \"adminDash\", 1, \"navbar-brand\", \"col-sm-3\", \"col-md-2\", \"mr-0\"], [\"type\", \"text\", \"placeholder\", \"Search\", \"aria-label\", \"Search\", 1, \"form-control\", \"form-control-dark\", \"w-100\"], [1, \"navbar-nav\", \"px-3\"], [1, \"nav-item\", \"text-nowrap\"], [1, \"nav-link\", 3, \"click\"], [1, \"container-fluid\"], [1, \"row\"], [1, \"col-md-2\", \"d-none\", \"d-md-block\", \"bg-light\", \"sidebar\"], [1, \"sidebar-sticky\"], [1, \"nav\", \"flex-column\"], [1, \"nav-item\"], [\"href\", \"/transactions\", 1, \"nav-link\"], [\"data-feather\", \"dollar-sign\"], [\"href\", \"/actions\", 1, \"nav-link\"], [\"data-feather\", \"trending-up\"], [\"href\", \"/actionnaires\", 1, \"nav-link\"], [\"data-feather\", \"users\"], [\"href\", \"/reports\", 1, \"nav-link\"], [\"data-feather\", \"bar-chart-2\"], [\"href\", \"/port\", 1, \"nav-link\"], [\"data-feather\", \"layers\"], [\"role\", \"main\", 1, \"col-md-9\", \"ml-sm-auto\", \"col-lg-10\", \"pt-3\", \"px-4\"], [1, \"d-flex\", \"justify-content-between\", \"flex-wrap\", \"flex-md-nowrap\", \"align-items-center\", \"pb-2\", \"mb-3\", \"border-bottom\"], [1, \"h2\"], [1, \"btn-toolbar\", \"mb-2\", \"mb-md-0\"], [1, \"btn-group\", \"mr-2\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\"], [1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", \"dropdown-toggle\"], [\"data-feather\", \"calendar\"], [\"id\", \"myChart\", \"width\", \"900\", \"height\", \"380\", 1, \"my-4\"]],\n      template: function ActionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3)(5, \"meta\", 4)(6, \"link\", 5);\n          i0.ɵɵelementStart(7, \"title\");\n          i0.ɵɵtext(8, \"Dashboard Template for Bootstrap\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"link\", 6)(10, \"link\", 7)(11, \"canvas\", 8, 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"body\")(14, \"nav\", 10)(15, \"a\", 11);\n          i0.ɵɵtext(16, \"GTI\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 12);\n          i0.ɵɵelementStart(18, \"ul\", 13)(19, \"li\", 14)(20, \"a\", 15);\n          i0.ɵɵlistener(\"click\", function ActionsComponent_Template_a_click_20_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵtext(21, \"Sign out\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"nav\", 18)(25, \"div\", 19)(26, \"ul\", 20);\n          i0.ɵɵelement(27, \"li\", 21);\n          i0.ɵɵelementStart(28, \"li\", 21)(29, \"a\", 22);\n          i0.ɵɵelement(30, \"span\", 23);\n          i0.ɵɵtext(31, \" Transactions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"li\", 21)(33, \"a\", 24);\n          i0.ɵɵelement(34, \"span\", 25);\n          i0.ɵɵtext(35, \" Actions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"li\", 21)(37, \"a\", 26);\n          i0.ɵɵelement(38, \"span\", 27);\n          i0.ɵɵtext(39, \" Actionnaires \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"li\", 21)(41, \"a\", 28);\n          i0.ɵɵelement(42, \"span\", 29);\n          i0.ɵɵtext(43, \" Reports \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 21)(45, \"a\", 30);\n          i0.ɵɵelement(46, \"span\", 31);\n          i0.ɵɵtext(47, \" Portefeuilles \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(48, \"main\", 32)(49, \"div\", 33)(50, \"h1\", 34);\n          i0.ɵɵtext(51, \"Actions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\", 35)(53, \"div\", 36)(54, \"button\", 37);\n          i0.ɵɵtext(55, \"Share\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"button\", 37);\n          i0.ɵɵtext(57, \"Export\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"button\", 38);\n          i0.ɵɵelement(59, \"span\", 39);\n          i0.ɵɵtext(60, \" This week \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(61, \"canvas\", 40);\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      styles: [_c1, _c1]\n    });\n  }\n}", "map": {"version": 3, "names": ["feather", "ActionsComponent", "constructor", "router", "ngAfterViewInit", "replace", "logout", "localStorage", "removeItem", "console", "log", "getItem", "navigate", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "viewQuery", "ActionsComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ActionsComponent_Template_a_click_20_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\actions\\actions.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\actions\\actions.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, ViewChild } from '@angular/core';\nimport { Chart } from 'chart.js';\nimport { Router } from '@angular/router';\nimport * as feather from 'feather-icons';\n\n\n\n@Component({\n  selector: 'app-actions',\n  templateUrl: './actions.component.html',\n  styleUrls: ['../../dashboard.css']\n})\n\nexport class ActionsComponent implements AfterViewInit {\n  @ViewChild('myChart') myChartRef!: ElementRef;\n   constructor(\n      private router: Router // Inject Router for page navigation\n    ) {}\n\n  ngAfterViewInit() {\n    feather.replace(); // Initialize feather icons\n  \n  }\n  logout() {\n    // Clear token and other data\n    localStorage.removeItem('jwt_token');  // Fix: Remove 'jwt_token'\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    \n    // Optionally, check that these items are removed:\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n  \n    // Navigate to login page\n    this.router.navigate(['/login']);\n  }\n}", "\n<!doctype html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, shrink-to-fit=no\">\n    <meta name=\"description\" content=\"\">\n    <meta name=\"author\" content=\"\">\n    <link rel=\"icon\" href=\"/docs/4.0/assets/img/favicons/favicon.ico\">\n\n    <title>Dashboard Template for Bootstrap</title>\n\n    <link rel=\"canonical\" href=\"https://getbootstrap.com/docs/4.0/examples/dashboard/\">\n\n    <!-- Bootstrap core CSS -->\n    <link href=\"../../../dist/css/bootstrap.min.css\" >\n\n    <!-- Custom styles for this template -->\n    <link href=\"../../dashboard.css\" rel=\"stylesheet\">\n    <canvas #myChart width=\"900\" height=\"380\" style=\"background-color: lightgrey;\"></canvas>\n\n  </head>\n\n  <body>\n    \n    <nav class=\"navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0\">\n      <a class=\"navbar-brand col-sm-3 col-md-2 mr-0\" href=\"adminDash\">GTI</a>\n\n      <input class=\"form-control form-control-dark w-100\" type=\"text\" placeholder=\"Search\" aria-label=\"Search\">\n      <ul class=\"navbar-nav px-3\">\n        <li class=\"nav-item text-nowrap\">\n          <a class=\"nav-link\" (click)=\"logout()\">Sign out</a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <nav class=\"col-md-2 d-none d-md-block bg-light sidebar\">\n          <div class=\"sidebar-sticky\">\n            <ul class=\"nav flex-column\">\n              <li class=\"nav-item\">\n              \n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\"  href=\"/transactions\">\n                  <span data-feather=\"dollar-sign\"></span>\n                  Transactions\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actions\">\n                  <span data-feather=\"trending-up\"></span>\n                  Actions\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/actionnaires\">\n                  <span data-feather=\"users\"></span>\n                  Actionnaires\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/reports\">\n                  <span data-feather=\"bar-chart-2\"></span>\n                  Reports\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" href=\"/port\">\n                  <span data-feather=\"layers\"></span>\n                  Portefeuilles\n                </a>\n              </li>\n            </ul>\n\n        \n          </div>\n        </nav>\n\n        <main role=\"main\" class=\"col-md-9 ml-sm-auto col-lg-10 pt-3 px-4\">\n          <div class=\"d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom\">\n            <h1 class=\"h2\">Actions</h1>\n            <div class=\"btn-toolbar mb-2 mb-md-0\">\n              <div class=\"btn-group mr-2\">\n                <button class=\"btn btn-sm btn-outline-secondary\">Share</button>\n                <button class=\"btn btn-sm btn-outline-secondary\">Export</button>\n              </div>\n              <button class=\"btn btn-sm btn-outline-secondary dropdown-toggle\">\n                <span data-feather=\"calendar\"></span>\n                This week\n              </button>\n            </div>\n          </div>\n\n          <canvas class=\"my-4\" id=\"myChart\" width=\"900\" height=\"380\"></canvas>\n          \n        \n        </main>\n      </div>\n    </div>\n\n    <!-- Bootstrap core JavaScript\n    ================================================== -->\n    <!-- Placed at the end of the document so the pages load faster -->\n    <script src=\"https://code.jquery.com/jquery-3.2.1.slim.min.js\" integrity=\"sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN\" crossorigin=\"anonymous\"></script>\n    <script>window.jQuery || document.write('<script src=\"../../assets/js/vendor/jquery-slim.min.js\"><\\/script>')</script>\n    <script src=\"../../assets/js/vendor/popper.min.js\"></script>\n    <script src=\"../../dist/js/bootstrap.min.js\"></script>\n\n    <!-- Icons -->\n    <script src=\"https://unpkg.com/feather-icons/dist/feather.min.js\"></script>\n    <script>    \n\n    </script>\n  </body>\n</html>\n"], "mappings": "AAGA,OAAO,KAAKA,OAAO,MAAM,eAAe;;;;;AAUxC,OAAM,MAAOC,gBAAgB;EAE1BC,YACWC,MAAc,CAAC;EAAA,E;IAAf,KAAAA,MAAM,GAANA,MAAM;EACb;EAELC,eAAeA,CAAA;IACbJ,OAAO,CAACK,OAAO,EAAE,CAAC,CAAC;EAErB;;EACAC,MAAMA,CAAA;IACJ;IACAC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAE;IACvCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAEhC;IACAC,OAAO,CAACC,GAAG,CAACH,YAAY,CAACI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAEhD;IACA,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBArBWX,gBAAgB,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBf,gBAAgB;MAAAgB,SAAA;MAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCX7BP,EAAA,CAAAS,cAAA,cAAgB;UAEZT,EAAA,CAAAU,SAAA,cAAsB;UAMtBV,EAAA,CAAAS,cAAA,YAAO;UAAAT,EAAA,CAAAW,MAAA,uCAAgC;UAAAX,EAAA,CAAAY,YAAA,EAAQ;UAE/CZ,EAAA,CAAAU,SAAA,cAAmF;UASrFV,EAAA,CAAAY,YAAA,EAAO;UAEPZ,EAAA,CAAAS,cAAA,YAAM;UAG8DT,EAAA,CAAAW,MAAA,WAAG;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAEvEZ,EAAA,CAAAU,SAAA,iBAAyG;UACzGV,EAAA,CAAAS,cAAA,cAA4B;UAEJT,EAAA,CAAAa,UAAA,mBAAAC,8CAAA;YAAA,OAASN,GAAA,CAAAf,MAAA,EAAQ;UAAA,EAAC;UAACO,EAAA,CAAAW,MAAA,gBAAQ;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAKzDZ,EAAA,CAAAS,cAAA,eAA6B;UAKnBT,EAAA,CAAAU,SAAA,cAEK;UACLV,EAAA,CAAAS,cAAA,cAAqB;UAEjBT,EAAA,CAAAU,SAAA,gBAAwC;UACxCV,EAAA,CAAAW,MAAA,sBACF;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAENZ,EAAA,CAAAS,cAAA,cAAqB;UAEjBT,EAAA,CAAAU,SAAA,gBAAwC;UACxCV,EAAA,CAAAW,MAAA,iBACF;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAENZ,EAAA,CAAAS,cAAA,cAAqB;UAEjBT,EAAA,CAAAU,SAAA,gBAAkC;UAClCV,EAAA,CAAAW,MAAA,sBACF;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAENZ,EAAA,CAAAS,cAAA,cAAqB;UAEjBT,EAAA,CAAAU,SAAA,gBAAwC;UACxCV,EAAA,CAAAW,MAAA,iBACF;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAENZ,EAAA,CAAAS,cAAA,cAAqB;UAEjBT,EAAA,CAAAU,SAAA,gBAAmC;UACnCV,EAAA,CAAAW,MAAA,uBACF;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAQZZ,EAAA,CAAAS,cAAA,gBAAkE;UAE/CT,EAAA,CAAAW,MAAA,eAAO;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAC3BZ,EAAA,CAAAS,cAAA,eAAsC;UAEeT,EAAA,CAAAW,MAAA,aAAK;UAAAX,EAAA,CAAAY,YAAA,EAAS;UAC/DZ,EAAA,CAAAS,cAAA,kBAAiD;UAAAT,EAAA,CAAAW,MAAA,cAAM;UAAAX,EAAA,CAAAY,YAAA,EAAS;UAElEZ,EAAA,CAAAS,cAAA,kBAAiE;UAC/DT,EAAA,CAAAU,SAAA,gBAAqC;UACrCV,EAAA,CAAAW,MAAA,mBACF;UAAAX,EAAA,CAAAY,YAAA,EAAS;UAIbZ,EAAA,CAAAU,SAAA,kBAAoE;UAGtEV,EAAA,CAAAY,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}