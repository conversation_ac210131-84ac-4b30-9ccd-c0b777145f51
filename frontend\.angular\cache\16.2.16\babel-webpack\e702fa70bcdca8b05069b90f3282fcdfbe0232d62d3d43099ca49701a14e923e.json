{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../responsable.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction AddResponsableComponent_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", group_r1.idGroupe);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(group_r1.nomGroupe);\n  }\n}\nexport class AddResponsableComponent {\n  constructor(responsableService, router) {\n    this.responsableService = responsableService;\n    this.router = router;\n    this.newResponsable = {\n      id: 0,\n      nom: '',\n      prenom: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      groupe: {\n        idGroupe: 0,\n        nomGroupe: ''\n      } // Default value for groupe\n    };\n\n    this.groups = []; // Array to store groups\n  }\n\n  ngOnInit() {\n    // Fetch the groups from the backend when the component initializes\n    this.responsableService.getGroups().subscribe(data => {\n      this.groups = data; // Store the list of groups\n    });\n  }\n  // Method to add the new responsable\n  addResponsable() {\n    this.responsableService.addResponsable(this.newResponsable).subscribe(response => {\n      console.log('Responsable added successfully:', response);\n      this.router.navigate(['/users']); // Redirect after successful addition\n    }, error => {\n      console.error('Error adding responsable:', error);\n    });\n  }\n  static {\n    this.ɵfac = function AddResponsableComponent_Factory(t) {\n      return new (t || AddResponsableComponent)(i0.ɵɵdirectiveInject(i1.ResponsableService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AddResponsableComponent,\n      selectors: [[\"app-add-responsable\"]],\n      decls: 27,\n      vars: 6,\n      consts: [[1, \"container\"], [3, \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"nom\"], [\"type\", \"text\", \"id\", \"nom\", \"name\", \"nom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"prenom\"], [\"type\", \"text\", \"id\", \"prenom\", \"name\", \"prenom\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"telephone\"], [\"type\", \"text\", \"id\", \"telephone\", \"name\", \"telephone\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"groupe\"], [\"id\", \"groupe\", \"name\", \"groupe\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\"], [3, \"value\"]],\n      template: function AddResponsableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Add New Responsable\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"form\", 1);\n          i0.ɵɵlistener(\"ngSubmit\", function AddResponsableComponent_Template_form_ngSubmit_3_listener() {\n            return ctx.addResponsable();\n          });\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"input\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_7_listener($event) {\n            return ctx.newResponsable.nom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 2)(9, \"label\", 5);\n          i0.ɵɵtext(10, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"input\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.newResponsable.prenom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 2)(13, \"label\", 7);\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"input\", 8);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.newResponsable.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 2)(17, \"label\", 9);\n          i0.ɵɵtext(18, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"input\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_input_ngModelChange_19_listener($event) {\n            return ctx.newResponsable.telephone = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 2)(21, \"label\", 11);\n          i0.ɵɵtext(22, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"select\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function AddResponsableComponent_Template_select_ngModelChange_23_listener($event) {\n            return ctx.newResponsable.groupe.idGroupe = $event;\n          });\n          i0.ɵɵtemplate(24, AddResponsableComponent_option_24_Template, 2, 2, \"option\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"button\", 14);\n          i0.ɵɵtext(26, \"Add Responsable\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.nom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.prenom);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.telephone);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newResponsable.groupe.idGroupe);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groups);\n        }\n      },\n      dependencies: [i3.NgForOf, i4.ɵNgNoValidate, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.DefaultValueAccessor, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "group_r1", "idGroupe", "ɵɵadvance", "ɵɵtextInterpolate", "nomGroupe", "AddResponsableComponent", "constructor", "responsableService", "router", "newResponsable", "id", "nom", "prenom", "email", "telephone", "role", "groupe", "groups", "ngOnInit", "getGroups", "subscribe", "data", "addResponsable", "response", "console", "log", "navigate", "error", "ɵɵdirectiveInject", "i1", "ResponsableService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AddResponsableComponent_Template", "rf", "ctx", "ɵɵlistener", "AddResponsableComponent_Template_form_ngSubmit_3_listener", "AddResponsableComponent_Template_input_ngModelChange_7_listener", "$event", "AddResponsableComponent_Template_input_ngModelChange_11_listener", "AddResponsableComponent_Template_input_ngModelChange_15_listener", "AddResponsableComponent_Template_input_ngModelChange_19_listener", "AddResponsableComponent_Template_select_ngModelChange_23_listener", "ɵɵtemplate", "AddResponsableComponent_option_24_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\add-responsable\\add-responsable.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport { Groupe } from '../model/groupe.model';\n\n@Component({\n  selector: 'app-add-responsable',\n  templateUrl: './add-responsable.component.html',\n  styleUrls: ['./add-responsable.component.css']\n})\nexport class AddResponsableComponent implements OnInit {\n  newResponsable: User = {\n    id: 0,\n    nom: '',\n    prenom: '',\n    email: '',\n    telephone: '',\n    role: 'RESPONSABLE',\n    groupe: { idGroupe: 0, nomGroupe: '' }  // Default value for groupe\n  };\n\n  groups: Groupe[] = [];  // Array to store groups\n\n  constructor(\n    private responsableService: ResponsableService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Fetch the groups from the backend when the component initializes\n    this.responsableService.getGroups().subscribe((data) => {\n      this.groups = data;  // Store the list of groups\n    });\n  }\n\n  // Method to add the new responsable\n  addResponsable(): void {\n    this.responsableService.addResponsable(this.newResponsable).subscribe(\n      (response) => {\n        console.log('Responsable added successfully:', response);\n        this.router.navigate(['/users']);  // Redirect after successful addition\n      },\n      (error) => {\n        console.error('Error adding responsable:', error);\n      }\n    );\n  }\n}\n", "<div class=\"container\">\n    <h2>Add New Responsable</h2>\n    <form (ngSubmit)=\"addResponsable()\">\n      <div class=\"form-group\">\n        <label for=\"nom\">Nom</label>\n        <input type=\"text\" class=\"form-control\" id=\"nom\" [(ngModel)]=\"newResponsable.nom\" name=\"nom\" required />\n      </div>\n      <div class=\"form-group\">\n        <label for=\"prenom\">Prénom</label>\n        <input type=\"text\" class=\"form-control\" id=\"prenom\" [(ngModel)]=\"newResponsable.prenom\" name=\"prenom\" required />\n      </div>\n      <div class=\"form-group\">\n        <label for=\"email\">Email</label>\n        <input type=\"email\" class=\"form-control\" id=\"email\" [(ngModel)]=\"newResponsable.email\" name=\"email\" required />\n      </div>\n      <div class=\"form-group\">\n        <label for=\"telephone\">Téléphone</label>\n        <input type=\"text\" class=\"form-control\" id=\"telephone\" [(ngModel)]=\"newResponsable.telephone\" name=\"telephone\" required />\n      </div>\n  \n      <!-- Dropdown for selecting the group -->\n      <div class=\"form-group\">\n        <label for=\"groupe\">Groupe</label>\n        <select id=\"groupe\" class=\"form-control\" [(ngModel)]=\"newResponsable.groupe.idGroupe\" name=\"groupe\" required>\n          <option *ngFor=\"let group of groups\" [value]=\"group.idGroupe\">{{ group.nomGroupe }}</option>\n        </select>\n      </div>\n  \n      <button type=\"submit\" class=\"btn btn-success\">Add Responsable</button>\n    </form>\n  </div>\n  "], "mappings": ";;;;;;;ICwBUA,EAAA,CAAAC,cAAA,iBAA8D;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAvDH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAAC,QAAA,CAAwB;IAACN,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,iBAAA,CAAAH,QAAA,CAAAI,SAAA,CAAqB;;;ADb7F,OAAM,MAAOC,uBAAuB;EAalCC,YACUC,kBAAsC,EACtCC,MAAc;IADd,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IAdhB,KAAAC,cAAc,GAAS;MACrBC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa;MACnBC,MAAM,EAAE;QAAEf,QAAQ,EAAE,CAAC;QAAEG,SAAS,EAAE;MAAE,CAAE,CAAE;KACzC;;IAED,KAAAa,MAAM,GAAa,EAAE,CAAC,CAAE;EAKrB;;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACX,kBAAkB,CAACY,SAAS,EAAE,CAACC,SAAS,CAAEC,IAAI,IAAI;MACrD,IAAI,CAACJ,MAAM,GAAGI,IAAI,CAAC,CAAE;IACvB,CAAC,CAAC;EACJ;EAEA;EACAC,cAAcA,CAAA;IACZ,IAAI,CAACf,kBAAkB,CAACe,cAAc,CAAC,IAAI,CAACb,cAAc,CAAC,CAACW,SAAS,CAClEG,QAAQ,IAAI;MACXC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,QAAQ,CAAC;MACxD,IAAI,CAACf,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAE;IACrC,CAAC,EACAC,KAAK,IAAI;MACRH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,CACF;EACH;;;uBApCWtB,uBAAuB,EAAAV,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAnC,EAAA,CAAAiC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB3B,uBAAuB;MAAA4B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXpC5C,EAAA,CAAAC,cAAA,aAAuB;UACfD,EAAA,CAAAE,MAAA,0BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,cAAoC;UAA9BD,EAAA,CAAA8C,UAAA,sBAAAC,0DAAA;YAAA,OAAYF,GAAA,CAAAlB,cAAA,EAAgB;UAAA,EAAC;UACjC3B,EAAA,CAAAC,cAAA,aAAwB;UACLD,EAAA,CAAAE,MAAA,UAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5BH,EAAA,CAAAC,cAAA,eAAwG;UAAvDD,EAAA,CAAA8C,UAAA,2BAAAE,gEAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAA/B,cAAA,CAAAE,GAAA,GAAAiC,MAAA;UAAA,EAAgC;UAAjFjD,EAAA,CAAAG,YAAA,EAAwG;UAE1GH,EAAA,CAAAC,cAAA,aAAwB;UACFD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,gBAAiH;UAA7DD,EAAA,CAAA8C,UAAA,2BAAAI,iEAAAD,MAAA;YAAA,OAAAJ,GAAA,CAAA/B,cAAA,CAAAG,MAAA,GAAAgC,MAAA;UAAA,EAAmC;UAAvFjD,EAAA,CAAAG,YAAA,EAAiH;UAEnHH,EAAA,CAAAC,cAAA,cAAwB;UACHD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,gBAA+G;UAA3DD,EAAA,CAAA8C,UAAA,2BAAAK,iEAAAF,MAAA;YAAA,OAAAJ,GAAA,CAAA/B,cAAA,CAAAI,KAAA,GAAA+B,MAAA;UAAA,EAAkC;UAAtFjD,EAAA,CAAAG,YAAA,EAA+G;UAEjHH,EAAA,CAAAC,cAAA,cAAwB;UACCD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,iBAA0H;UAAnED,EAAA,CAAA8C,UAAA,2BAAAM,iEAAAH,MAAA;YAAA,OAAAJ,GAAA,CAAA/B,cAAA,CAAAK,SAAA,GAAA8B,MAAA;UAAA,EAAsC;UAA7FjD,EAAA,CAAAG,YAAA,EAA0H;UAI5HH,EAAA,CAAAC,cAAA,cAAwB;UACFD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,kBAA6G;UAApED,EAAA,CAAA8C,UAAA,2BAAAO,kEAAAJ,MAAA;YAAA,OAAAJ,GAAA,CAAA/B,cAAA,CAAAO,MAAA,CAAAf,QAAA,GAAA2C,MAAA;UAAA,EAA4C;UACnFjD,EAAA,CAAAsD,UAAA,KAAAC,0CAAA,qBAA4F;UAC9FvD,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,kBAA8C;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UAvBnBH,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAI,UAAA,YAAAyC,GAAA,CAAA/B,cAAA,CAAAE,GAAA,CAAgC;UAI7BhB,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAI,UAAA,YAAAyC,GAAA,CAAA/B,cAAA,CAAAG,MAAA,CAAmC;UAInCjB,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAI,UAAA,YAAAyC,GAAA,CAAA/B,cAAA,CAAAI,KAAA,CAAkC;UAI/BlB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAI,UAAA,YAAAyC,GAAA,CAAA/B,cAAA,CAAAK,SAAA,CAAsC;UAMpDnB,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAI,UAAA,YAAAyC,GAAA,CAAA/B,cAAA,CAAAO,MAAA,CAAAf,QAAA,CAA4C;UACzDN,EAAA,CAAAO,SAAA,GAAS;UAATP,EAAA,CAAAI,UAAA,YAAAyC,GAAA,CAAAvB,MAAA,CAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}