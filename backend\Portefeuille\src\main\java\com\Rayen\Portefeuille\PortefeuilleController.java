package com.Rayen.Portefeuille;



import com.Rayen.Portefeuille.Entity.Portefeuille;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("GRA/Portefeuille")
@RequiredArgsConstructor
public class PortefeuilleController {

    private final PortefeuilleService service;

    @PostMapping("ajout")
    @ResponseStatus(HttpStatus.CREATED)
    public void save(
            @RequestBody Portefeuille Portefeuille
    ){
        service.savePortefeuille(Portefeuille);
    }

    @GetMapping
    public ResponseEntity<List<Portefeuille>>findAllPortefeuilles(){
        return ResponseEntity.ok(service.findAllPortefeuilles());
    }








    //relation avec actionnaire
    @GetMapping("/actionnaire/{actionnaire-id}")
    public ResponseEntity<List<Portefeuille>>findAllUsers(@PathVariable("actionnaire-id")Long actionnaireId){
        return ResponseEntity.ok(service.findPortefeuillebyActionnaire(actionnaireId));
    }

}
