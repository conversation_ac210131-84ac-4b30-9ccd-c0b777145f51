package com.Aziz.Client.Repositories;

import com.Aziz.Client.Entity.Portefeuille;
import feign.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PortefeuilleRepository extends JpaRepository<Portefeuille, Long> {

     // Trouver tous les portefeuilles d’un actionnaire par son ID
     List<Portefeuille> findAllByActionnaire_IdActionnaire(Long idActionnaire);

     // Trouver un portefeuille par le code ISIN de l'action
     List<Portefeuille> findByAction_isinAction(String ISINAction);

     Optional<Portefeuille> findByAction_IdActionAndActionnaire_IdActionnaire(Long idAction, Long idActionnaire);

     @Query("SELECT p FROM Portefeuille p WHERE p.action.isinAction = :isinAction AND p.actionnaire.idActionnaire = :idActionnaire")
     Optional<Portefeuille> findByIsinActionAndIdActionnaire(@Param("isinAction") String isinAction, @Param("idActionnaire") Long idActionnaire);

     Optional<Portefeuille> findByAction_IsinActionAndActionnaire_IdActionnaire(String isinAction, Long idActionnaire);

     // 👉 La nouvelle méthode que tu voulais ajouter :
     Optional<Portefeuille> findByAction_IdAction(Long idAction);
}
