{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/groupe.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction GroupsComponent_li_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_li_8_Template_button_click_2_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const groupe_r2 = restoredCtx.$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.setEditGroupe(groupe_r2));\n    });\n    i0.ɵɵtext(3, \"Edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_li_8_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r4);\n      const groupe_r2 = restoredCtx.$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.deleteGroupe(groupe_r2.idGroupe));\n    });\n    i0.ɵɵtext(5, \"Delete\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const groupe_r2 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", groupe_r2.nomGroupe, \" \");\n  }\n}\nfunction GroupsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"input\", 5);\n    i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_div_9_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.editGroupe.nomGroupe = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_div_9_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.updateGroupe());\n    });\n    i0.ɵɵtext(3, \"Update Group\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function GroupsComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.editGroupe = null);\n    });\n    i0.ɵɵtext(5, \"Cancel\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.editGroupe.nomGroupe);\n  }\n}\nexport class GroupsComponent {\n  constructor(groupeService, router) {\n    this.groupeService = groupeService;\n    this.router = router;\n    this.groupes = []; // List of groups\n    this.newGroupe = {\n      nomGroupe: ''\n    }; // New group model\n    this.editGroupe = null; // Group being edited\n  }\n\n  ngOnInit() {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n  // Load all groups\n  loadGroupes() {\n    this.groupeService.getAllGroupes().subscribe(data => {\n      this.groupes = data;\n    }, err => {\n      console.error('Error loading groups', err);\n    });\n  }\n  // Add a new group\n  addGroupe() {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(() => {\n        this.newGroupe = {\n          nomGroupe: ''\n        }; // Reset input\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error adding group', err);\n      });\n    }\n  }\n  // Set group to edit mode\n  setEditGroupe(groupe) {\n    this.editGroupe = {\n      ...groupe\n    }; // Clone object\n  }\n  // Update a group\n  updateGroupe() {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(() => {\n        this.editGroupe = null; // Reset edit mode\n        this.loadGroupes(); // Refresh list\n      }, err => {\n        console.error('Error updating group', err);\n      });\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n  // Delete a group\n  deleteGroupe(id) {\n    if (id) {\n      this.groupeService.deleteGroupe(id).subscribe(() => {\n        this.loadGroupes(); // Refresh list after deletion\n      }, err => {\n        console.error('Error deleting group', err);\n      });\n    } else {\n      console.error('Invalid group ID');\n    }\n  }\n  logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function GroupsComponent_Factory(t) {\n      return new (t || GroupsComponent)(i0.ɵɵdirectiveInject(i1.GroupeService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GroupsComponent,\n      selectors: [[\"app-groups\"]],\n      decls: 10,\n      vars: 3,\n      consts: [[1, \"container\"], [\"type\", \"text\", \"placeholder\", \"Group name\", 3, \"ngModel\", \"ngModelChange\"], [3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"placeholder\", \"Edit group name\", 3, \"ngModel\", \"ngModelChange\"]],\n      template: function GroupsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Groups\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\")(4, \"input\", 1);\n          i0.ɵɵlistener(\"ngModelChange\", function GroupsComponent_Template_input_ngModelChange_4_listener($event) {\n            return ctx.newGroupe.nomGroupe = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function GroupsComponent_Template_button_click_5_listener() {\n            return ctx.addGroupe();\n          });\n          i0.ɵɵtext(6, \"Add Group\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"ul\");\n          i0.ɵɵtemplate(8, GroupsComponent_li_8_Template, 6, 1, \"li\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, GroupsComponent_div_9_Template, 6, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newGroupe.nomGroupe);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groupes);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.editGroupe);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵlistener", "GroupsComponent_li_8_Template_button_click_2_listener", "restoredCtx", "ɵɵrestoreView", "_r4", "groupe_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "setEditGroupe", "ɵɵelementEnd", "GroupsComponent_li_8_Template_button_click_4_listener", "ctx_r5", "deleteGroupe", "idGroupe", "ɵɵadvance", "ɵɵtextInterpolate1", "nomGroupe", "GroupsComponent_div_9_Template_input_ngModelChange_1_listener", "$event", "_r7", "ctx_r6", "editGroupe", "GroupsComponent_div_9_Template_button_click_2_listener", "ctx_r8", "updateGroupe", "GroupsComponent_div_9_Template_button_click_4_listener", "ctx_r9", "ɵɵproperty", "ctx_r1", "GroupsComponent", "constructor", "groupeService", "router", "groupes", "newGroupe", "ngOnInit", "loadGroupes", "getAllGroupes", "subscribe", "data", "err", "console", "error", "addGroupe", "trim", "groupe", "id", "logout", "localStorage", "removeItem", "log", "getItem", "navigate", "ɵɵdirectiveInject", "i1", "GroupeService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "GroupsComponent_Template", "rf", "ctx", "GroupsComponent_Template_input_ngModelChange_4_listener", "GroupsComponent_Template_button_click_5_listener", "ɵɵtemplate", "GroupsComponent_li_8_Template", "GroupsComponent_div_9_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\groups\\groups.component.ts", "C:\\Users\\<USER>\\Desktop\\GRA\\GRA\\frontend\\src\\app\\groups\\groups.component.html"], "sourcesContent": ["// groups/groups.component.ts\nimport { Component, OnInit } from '@angular/core';\nimport { GroupeService } from '../services/groupe.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-groups',\n  templateUrl: './groups.component.html',\n  styleUrls: ['./groups.component.css']\n})\nexport class GroupsComponent implements OnInit {\n  groupes: Groupe[] = []; // List of groups\n  newGroupe: Groupe = { nomGroupe: '' }; // New group model\n  editGroupe: Groupe | null = null; // Group being edited\n\n  constructor(private groupeService: GroupeService,\n    private router: Router \n\n  ) {\n    \n  }\n\n  ngOnInit(): void {\n    this.loadGroupes(); // Load groups when component initializes\n  }\n\n  // Load all groups\n  loadGroupes(): void {\n    this.groupeService.getAllGroupes().subscribe(\n      (data) => {\n        this.groupes = data;\n      },\n      (err) => {\n        console.error('Error loading groups', err);\n      }\n    );\n  }\n\n  // Add a new group\n  addGroupe(): void {\n    if (this.newGroupe.nomGroupe.trim()) {\n      this.groupeService.addGroupe(this.newGroupe).subscribe(\n        () => {\n          this.newGroupe = { nomGroupe: '' }; // Reset input\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error adding group', err);\n        }\n      );\n    }\n  }\n\n  // Set group to edit mode\n  setEditGroupe(groupe: Groupe): void {\n    this.editGroupe = { ...groupe }; // Clone object\n  }\n\n  // Update a group\n  updateGroupe(): void {\n    if (this.editGroupe && this.editGroupe.idGroupe) {\n      this.groupeService.updateGroupe(this.editGroupe.idGroupe, this.editGroupe).subscribe(\n        () => {\n          this.editGroupe = null; // Reset edit mode\n          this.loadGroupes(); // Refresh list\n        },\n        (err) => {\n          console.error('Error updating group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group or missing group ID');\n    }\n  }\n\n  // Delete a group\n  deleteGroupe(id: number): void {\n    if (id) {\n      this.groupeService.deleteGroupe(id).subscribe(\n        () => {\n          this.loadGroupes(); // Refresh list after deletion\n        },\n        (err) => {\n          console.error('Error deleting group', err);\n        }\n      );\n    } else {\n      console.error('Invalid group ID');\n    }\n  }\n   logout() {\n    localStorage.removeItem('jwt_token');\n    localStorage.removeItem('role');\n    localStorage.removeItem('group');\n    console.log(localStorage.getItem('jwt_token')); // should be null after logout\n    this.router.navigate(['/login']);\n  }\n}\n", "<!-- groups.component.html -->\n<div class=\"container\">\n    <h2>Groups</h2>\n  \n    <!-- Add New Group -->\n    <div>\n      <input type=\"text\" [(ngModel)]=\"newGroupe.nomGroupe\" placeholder=\"Group name\">\n      <button (click)=\"addGroupe()\">Add Group</button>\n    </div>\n  \n    <!-- List of Groups -->\n    <ul>\n      <li *ngFor=\"let groupe of groupes\">\n        {{ groupe.nomGroupe }}\n        <button (click)=\"setEditGroupe(groupe)\">Edit</button>\n        <button (click)=\"deleteGroupe(groupe.idGroupe!)\">Delete</button>\n      </li>\n    </ul>\n  \n    <!-- Edit Group -->\n    <div *ngIf=\"editGroupe\">\n      <input [(ngModel)]=\"editGroupe.nomGroupe\" placeholder=\"Edit group name\">\n      <button (click)=\"updateGroupe()\">Update Group</button>\n      <button (click)=\"editGroupe = null\">Cancel</button>\n    </div>\n  </div>\n  "], "mappings": ";;;;;;;;ICYMA,EAAA,CAAAC,cAAA,SAAmC;IACjCD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,gBAAwC;IAAhCD,EAAA,CAAAG,UAAA,mBAAAC,sDAAA;MAAA,MAAAC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAL,SAAA,CAAqB;IAAA,EAAC;IAACR,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACrDd,EAAA,CAAAC,cAAA,gBAAiD;IAAzCD,EAAA,CAAAG,UAAA,mBAAAY,sDAAA;MAAA,MAAAV,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAO,MAAA,GAAAhB,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAI,MAAA,CAAAC,YAAA,CAAAT,SAAA,CAAAU,QAAA,CAA8B;IAAA,EAAC;IAAClB,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;IAFhEd,EAAA,CAAAmB,SAAA,GACA;IADAnB,EAAA,CAAAoB,kBAAA,MAAAZ,SAAA,CAAAa,SAAA,MACA;;;;;;IAMJrB,EAAA,CAAAC,cAAA,UAAwB;IACfD,EAAA,CAAAG,UAAA,2BAAAmB,8DAAAC,MAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAC,MAAA,GAAAzB,EAAA,CAAAW,aAAA;MAAA,OAAaX,EAAA,CAAAY,WAAA,CAAAa,MAAA,CAAAC,UAAA,CAAAL,SAAA,GAAAE,MAAA,CAA4B;IAAA,EAAP;IAAzCvB,EAAA,CAAAc,YAAA,EAAwE;IACxEd,EAAA,CAAAC,cAAA,gBAAiC;IAAzBD,EAAA,CAAAG,UAAA,mBAAAwB,uDAAA;MAAA3B,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAI,MAAA,GAAA5B,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAgB,MAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAAC7B,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAc,YAAA,EAAS;IACtDd,EAAA,CAAAC,cAAA,gBAAoC;IAA5BD,EAAA,CAAAG,UAAA,mBAAA2B,uDAAA;MAAA9B,EAAA,CAAAM,aAAA,CAAAkB,GAAA;MAAA,MAAAO,MAAA,GAAA/B,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAAmB,MAAA,CAAAL,UAAA,GAAsB,IAAI;IAAA,EAAC;IAAC1B,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAc,YAAA,EAAS;;;;IAF5Cd,EAAA,CAAAmB,SAAA,GAAkC;IAAlCnB,EAAA,CAAAgC,UAAA,YAAAC,MAAA,CAAAP,UAAA,CAAAL,SAAA,CAAkC;;;ADV/C,OAAM,MAAOa,eAAe;EAK1BC,YAAoBC,aAA4B,EACtCC,MAAc;IADJ,KAAAD,aAAa,GAAbA,aAAa;IACvB,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,OAAO,GAAa,EAAE,CAAC,CAAC;IACxB,KAAAC,SAAS,GAAW;MAAElB,SAAS,EAAE;IAAE,CAAE,CAAC,CAAC;IACvC,KAAAK,UAAU,GAAkB,IAAI,CAAC,CAAC;EAOlC;;EAEAc,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;EACtB;EAEA;EACAA,WAAWA,CAAA;IACT,IAAI,CAACL,aAAa,CAACM,aAAa,EAAE,CAACC,SAAS,CACzCC,IAAI,IAAI;MACP,IAAI,CAACN,OAAO,GAAGM,IAAI;IACrB,CAAC,EACAC,GAAG,IAAI;MACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;IAC5C,CAAC,CACF;EACH;EAEA;EACAG,SAASA,CAAA;IACP,IAAI,IAAI,CAACT,SAAS,CAAClB,SAAS,CAAC4B,IAAI,EAAE,EAAE;MACnC,IAAI,CAACb,aAAa,CAACY,SAAS,CAAC,IAAI,CAACT,SAAS,CAAC,CAACI,SAAS,CACpD,MAAK;QACH,IAAI,CAACJ,SAAS,GAAG;UAAElB,SAAS,EAAE;QAAE,CAAE,CAAC,CAAC;QACpC,IAAI,CAACoB,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAI,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAEF,GAAG,CAAC;MAC1C,CAAC,CACF;;EAEL;EAEA;EACAhC,aAAaA,CAACqC,MAAc;IAC1B,IAAI,CAACxB,UAAU,GAAG;MAAE,GAAGwB;IAAM,CAAE,CAAC,CAAC;EACnC;EAEA;EACArB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACH,UAAU,IAAI,IAAI,CAACA,UAAU,CAACR,QAAQ,EAAE;MAC/C,IAAI,CAACkB,aAAa,CAACP,YAAY,CAAC,IAAI,CAACH,UAAU,CAACR,QAAQ,EAAE,IAAI,CAACQ,UAAU,CAAC,CAACiB,SAAS,CAClF,MAAK;QACH,IAAI,CAACjB,UAAU,GAAG,IAAI,CAAC,CAAC;QACxB,IAAI,CAACe,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAI,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,mCAAmC,CAAC;;EAEtD;EAEA;EACA9B,YAAYA,CAACkC,EAAU;IACrB,IAAIA,EAAE,EAAE;MACN,IAAI,CAACf,aAAa,CAACnB,YAAY,CAACkC,EAAE,CAAC,CAACR,SAAS,CAC3C,MAAK;QACH,IAAI,CAACF,WAAW,EAAE,CAAC,CAAC;MACtB,CAAC,EACAI,GAAG,IAAI;QACNC,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEF,GAAG,CAAC;MAC5C,CAAC,CACF;KACF,MAAM;MACLC,OAAO,CAACC,KAAK,CAAC,kBAAkB,CAAC;;EAErC;EACCK,MAAMA,CAAA;IACLC,YAAY,CAACC,UAAU,CAAC,WAAW,CAAC;IACpCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;IAC/BD,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCR,OAAO,CAACS,GAAG,CAACF,YAAY,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAtFWvB,eAAe,EAAAlC,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA5D,EAAA,CAAA0D,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAf5B,eAAe;MAAA6B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5BrE,EAAA,CAAAC,cAAA,aAAuB;UACfD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAc,YAAA,EAAK;UAGfd,EAAA,CAAAC,cAAA,UAAK;UACgBD,EAAA,CAAAG,UAAA,2BAAAoE,wDAAAhD,MAAA;YAAA,OAAA+C,GAAA,CAAA/B,SAAA,CAAAlB,SAAA,GAAAE,MAAA;UAAA,EAAiC;UAApDvB,EAAA,CAAAc,YAAA,EAA8E;UAC9Ed,EAAA,CAAAC,cAAA,gBAA8B;UAAtBD,EAAA,CAAAG,UAAA,mBAAAqE,iDAAA;YAAA,OAASF,GAAA,CAAAtB,SAAA,EAAW;UAAA,EAAC;UAAChD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAc,YAAA,EAAS;UAIlDd,EAAA,CAAAC,cAAA,SAAI;UACFD,EAAA,CAAAyE,UAAA,IAAAC,6BAAA,gBAIK;UACP1E,EAAA,CAAAc,YAAA,EAAK;UAGLd,EAAA,CAAAyE,UAAA,IAAAE,8BAAA,iBAIM;UACR3E,EAAA,CAAAc,YAAA,EAAM;;;UAnBiBd,EAAA,CAAAmB,SAAA,GAAiC;UAAjCnB,EAAA,CAAAgC,UAAA,YAAAsC,GAAA,CAAA/B,SAAA,CAAAlB,SAAA,CAAiC;UAM7BrB,EAAA,CAAAmB,SAAA,GAAU;UAAVnB,EAAA,CAAAgC,UAAA,YAAAsC,GAAA,CAAAhC,OAAA,CAAU;UAQ7BtC,EAAA,CAAAmB,SAAA,GAAgB;UAAhBnB,EAAA,CAAAgC,UAAA,SAAAsC,GAAA,CAAA5C,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}