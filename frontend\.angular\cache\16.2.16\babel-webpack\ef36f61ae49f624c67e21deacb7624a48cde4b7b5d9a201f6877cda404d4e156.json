{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function map(project, thisArg) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      subscriber.next(project.call(thisArg, value, index++));\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "map", "project", "thisArg", "source", "subscriber", "index", "subscribe", "value", "next", "call"], "sources": ["C:/Users/<USER>/Desktop/GRA/GRA/frontend/node_modules/rxjs/dist/esm/internal/operators/map.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function map(project, thisArg) {\n    return operate((source, subscriber) => {\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            subscriber.next(project.call(thisArg, value, index++));\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,GAAGA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAClC,OAAOJ,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,KAAK,GAAG,CAAC;IACbF,MAAM,CAACG,SAAS,CAACP,wBAAwB,CAACK,UAAU,EAAGG,KAAK,IAAK;MAC7DH,UAAU,CAACI,IAAI,CAACP,OAAO,CAACQ,IAAI,CAACP,OAAO,EAAEK,KAAK,EAAEF,KAAK,EAAE,CAAC,CAAC;IAC1D,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}