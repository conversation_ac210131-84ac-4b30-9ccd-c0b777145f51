{"ast": null, "code": "import { Role } from '../model/role.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../responsable.service\";\nimport * as i3 from \"../services/ressource.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction ResponsableEditComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Nom is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResponsableEditComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Pr\\u00E9nom is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResponsableEditComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" A valid email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResponsableEditComponent_div_58_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResponsableEditComponent_div_58_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone must be a number and up to 8 digits.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResponsableEditComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtemplate(1, ResponsableEditComponent_div_58_div_1_Template, 2, 0, \"div\", 60);\n    i0.ɵɵtemplate(2, ResponsableEditComponent_div_58_div_2_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r7 = i0.ɵɵreference(57);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r7.errors == null ? null : _r7.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r7.errors == null ? null : _r7.errors[\"pattern\"]);\n  }\n}\nfunction ResponsableEditComponent_option_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const groupe_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", groupe_r17.idGroupe);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", groupe_r17.nomGroupe, \" \");\n  }\n}\nfunction ResponsableEditComponent_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Groupe is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResponsableEditComponent_option_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ressource_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", ressource_r18.idRessource);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ressource_r18.nomRessource, \" \");\n  }\n}\nfunction ResponsableEditComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1, \" Ressource is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ResponsableEditComponent {\n  constructor(route, responsableService, router, ressourceService) {\n    this.route = route;\n    this.responsableService = responsableService;\n    this.router = router;\n    this.ressourceService = ressourceService;\n    this.responsableId = 0;\n    this.responsable = {\n      id: 0,\n      nom: '',\n      prenom: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupe: {\n        idGroupe: 0,\n        nomGroupe: ''\n      },\n      habilitation: {\n        id: 0,\n        idGroupe: 0,\n        ressource: {\n          idRessource: 0,\n          nomRessource: '',\n          link_path: ''\n        }\n      },\n      canCreate: false,\n      canRead: false,\n      canUpdate: false,\n      canDelete: false,\n      ressourceId: 0 // Add ressourceId property\n    };\n\n    this.roles = Object.values(Role);\n    this.groupes = [];\n    this.ressources = []; // Array to store ressources\n  }\n\n  ngOnInit() {\n    this.responsableId = Number(this.route.snapshot.paramMap.get('id'));\n    this.getResponsable(this.responsableId);\n    this.getGroups();\n    this.getRessources(); // Fetch available ressources\n  }\n\n  getResponsable(id) {\n    this.responsableService.getResponsableById(id).subscribe(data => {\n      this.responsable = data;\n      // Assign ressourceId for dropdown binding\n      if (data.habilitation && data.habilitation.ressource) {\n        this.responsable.ressourceId = data.habilitation.ressource.idRessource;\n      }\n      // Also ensure habilitation group is linked properly\n      if (this.responsable.groupe && this.responsable.groupe.idGroupe !== undefined) {\n        this.responsable.habilitation.idGroupe = this.responsable.groupe.idGroupe;\n      }\n    }, error => {\n      console.error('Error fetching responsable:', error);\n    });\n  }\n  getGroups() {\n    this.responsableService.getGroups().subscribe(data => {\n      this.groupes = data;\n    });\n  }\n  getRessources() {\n    this.ressourceService.getAllRessources().subscribe(data => {\n      this.ressources = data; // Store fetched ressources\n    });\n  }\n\n  updateResponsable() {\n    if (this.responsable.habilitation) {\n      if (this.responsable.groupe && this.responsable.groupe.idGroupe !== undefined) {\n        this.responsable.habilitation.idGroupe = this.responsable.groupe.idGroupe;\n      } else {\n        console.error('Groupe ID is not defined');\n        return;\n      }\n    } else {\n      console.error('Habilitation is not defined');\n      return;\n    }\n    // Sync selected ressource\n    if (this.responsable.ressourceId) {\n      const selectedRessource = this.ressources.find(r => r.idRessource === this.responsable.ressourceId);\n      if (selectedRessource) {\n        this.responsable.habilitation.ressource = selectedRessource;\n      } else {\n        console.error('Selected ressource not found!');\n        return;\n      }\n    }\n    this.responsableService.updateResponsable(this.responsableId, this.responsable).subscribe(response => {\n      console.log('Responsable updated successfully:', response);\n      this.router.navigateByUrl('/users');\n    }, error => {\n      console.error('Error updating responsable:', error);\n    });\n  }\n  cancelEdit() {\n    this.responsable = {\n      id: 0,\n      nom: '',\n      prenom: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupe: {\n        idGroupe: 0,\n        nomGroupe: ''\n      },\n      habilitation: {\n        id: 0,\n        idGroupe: 0,\n        ressource: {\n          idRessource: 0,\n          nomRessource: '',\n          link_path: ''\n        }\n      },\n      canCreate: false,\n      canRead: false,\n      canUpdate: false,\n      canDelete: false,\n      ressourceId: 0 // Reset ressourceId\n    };\n\n    this.router.navigateByUrl('/users');\n  }\n  static {\n    this.ɵfac = function ResponsableEditComponent_Factory(t) {\n      return new (t || ResponsableEditComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ResponsableService), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i3.RessourceService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResponsableEditComponent,\n      selectors: [[\"app-responsable-edit\"]],\n      decls: 106,\n      vars: 20,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"UTF-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1.0\"], [\"http-equiv\", \"X-UA-Compatible\", \"content\", \"ie=edge\"], [\"href\", \"https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css\", \"rel\", \"stylesheet\"], [\"href\", \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css\", \"rel\", \"stylesheet\"], [1, \"background-image\"], [1, \"container\", \"mt-5\"], [1, \"form-container\", \"mx-auto\", 2, \"max-width\", \"900px\"], [1, \"text-center\", \"mb-4\"], [1, \"form-group\", 3, \"ngSubmit\"], [\"responsableForm\", \"ngForm\"], [1, \"form-row\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"nom\"], [1, \"input-group\"], [1, \"input-group-prepend\"], [1, \"input-group-text\"], [1, \"fa\", \"fa-user\"], [\"type\", \"text\", \"id\", \"nom\", \"name\", \"nom\", \"required\", \"\", \"placeholder\", \"Entrez le nom\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"nom\", \"ngModel\"], [\"class\", \"text-danger small\", 4, \"ngIf\"], [\"for\", \"prenom\"], [\"type\", \"text\", \"id\", \"prenom\", \"name\", \"prenom\", \"required\", \"\", \"placeholder\", \"Entrez le pr\\u00E9nom\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"prenom\", \"ngModel\"], [\"for\", \"email\"], [1, \"fa\", \"fa-envelope\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"Entrez l'email\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"email\", \"ngModel\"], [\"for\", \"telephone\"], [1, \"fa\", \"fa-phone\"], [\"type\", \"tel\", \"id\", \"telephone\", \"name\", \"telephone\", \"required\", \"\", \"pattern\", \"^[0-9]{1,8}$\", \"placeholder\", \"Entrez le t\\u00E9l\\u00E9phone\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"telephone\", \"ngModel\"], [\"for\", \"groupe\"], [1, \"fa\", \"fa-users\"], [\"id\", \"groupe\", \"name\", \"groupe\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"groupe\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\"], [\"for\", \"ressource\"], [\"id\", \"ressource\", \"name\", \"ressource\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"ressource\", \"ngModel\"], [\"disabled\", \"\", 3, \"ngValue\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-md-12\", \"mb-3\"], [2, \"margin-bottom\", \"15px\", \"display\", \"block\"], [1, \"form-check\", \"form-check-inline\"], [\"type\", \"checkbox\", \"id\", \"canCreate\", \"name\", \"canCreate\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canCreate\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"canRead\", \"name\", \"canRead\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canRead\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"canUpdate\", \"name\", \"canUpdate\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canUpdate\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"canDelete\", \"name\", \"canDelete\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"canDelete\", 1, \"form-check-label\"], [1, \"form-row\", \"mt-4\"], [1, \"col-md-12\", \"text-center\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"ml-3\", 3, \"click\"], [1, \"text-danger\", \"small\"], [4, \"ngIf\"], [3, \"value\"], [3, \"ngValue\"]],\n      template: function ResponsableEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2)(4, \"meta\", 3);\n          i0.ɵɵelementStart(5, \"title\");\n          i0.ɵɵtext(6, \"Modifier l'Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"link\", 4)(8, \"link\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"body\")(10, \"div\", 6)(11, \"div\", 7)(12, \"div\", 8)(13, \"h2\", 9);\n          i0.ɵɵtext(14, \"Modifier l'Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"form\", 10, 11);\n          i0.ɵɵlistener(\"ngSubmit\", function ResponsableEditComponent_Template_form_ngSubmit_15_listener() {\n            return ctx.updateResponsable();\n          });\n          i0.ɵɵelementStart(17, \"div\", 12)(18, \"div\", 13)(19, \"label\", 14);\n          i0.ɵɵtext(20, \"Nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16)(23, \"span\", 17);\n          i0.ɵɵelement(24, \"i\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"input\", 19, 20);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_25_listener($event) {\n            return ctx.responsable.nom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(27, ResponsableEditComponent_div_27_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 13)(29, \"label\", 22);\n          i0.ɵɵtext(30, \"Pr\\u00E9nom\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 15)(32, \"div\", 16)(33, \"span\", 17);\n          i0.ɵɵelement(34, \"i\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"input\", 23, 24);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_35_listener($event) {\n            return ctx.responsable.prenom = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(37, ResponsableEditComponent_div_37_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 12)(39, \"div\", 13)(40, \"label\", 25);\n          i0.ɵɵtext(41, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 15)(43, \"div\", 16)(44, \"span\", 17);\n          i0.ɵɵelement(45, \"i\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"input\", 27, 28);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_46_listener($event) {\n            return ctx.responsable.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(48, ResponsableEditComponent_div_48_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 13)(50, \"label\", 29);\n          i0.ɵɵtext(51, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\", 15)(53, \"div\", 16)(54, \"span\", 17);\n          i0.ɵɵelement(55, \"i\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"input\", 31, 32);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_56_listener($event) {\n            return ctx.responsable.telephone = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(58, ResponsableEditComponent_div_58_Template, 3, 2, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"div\", 12)(60, \"div\", 13)(61, \"label\", 33);\n          i0.ɵɵtext(62, \"Groupe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 15)(64, \"div\", 16)(65, \"span\", 17);\n          i0.ɵɵelement(66, \"i\", 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"select\", 35, 36);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_select_ngModelChange_67_listener($event) {\n            return ctx.responsable.groupe.idGroupe = $event;\n          });\n          i0.ɵɵtemplate(69, ResponsableEditComponent_option_69_Template, 2, 2, \"option\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(70, ResponsableEditComponent_div_70_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 38)(72, \"label\", 39);\n          i0.ɵɵtext(73, \"Ressource\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"select\", 40, 41);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_select_ngModelChange_74_listener($event) {\n            return ctx.responsable.ressourceId = $event;\n          });\n          i0.ɵɵelementStart(76, \"option\", 42);\n          i0.ɵɵtext(77, \"-- Select Ressource --\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(78, ResponsableEditComponent_option_78_Template, 2, 2, \"option\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(79, ResponsableEditComponent_div_79_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"div\", 12)(81, \"div\", 44)(82, \"label\", 45);\n          i0.ɵɵtext(83, \"Droits\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"div\", 46)(85, \"input\", 47);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_85_listener($event) {\n            return ctx.responsable.canCreate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"label\", 48);\n          i0.ɵɵtext(87, \"Cr\\u00E9er\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(88, \"div\", 46)(89, \"input\", 49);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_89_listener($event) {\n            return ctx.responsable.canRead = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"label\", 50);\n          i0.ɵɵtext(91, \"Lire\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(92, \"div\", 46)(93, \"input\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_93_listener($event) {\n            return ctx.responsable.canUpdate = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"label\", 52);\n          i0.ɵɵtext(95, \"Mettre \\u00E0 jour\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(96, \"div\", 46)(97, \"input\", 53);\n          i0.ɵɵlistener(\"ngModelChange\", function ResponsableEditComponent_Template_input_ngModelChange_97_listener($event) {\n            return ctx.responsable.canDelete = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"label\", 54);\n          i0.ɵɵtext(99, \"Supprimer\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(100, \"div\", 55)(101, \"div\", 56)(102, \"button\", 57);\n          i0.ɵɵtext(103, \" Update Responsable \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"button\", 58);\n          i0.ɵɵlistener(\"click\", function ResponsableEditComponent_Template_button_click_104_listener() {\n            return ctx.cancelEdit();\n          });\n          i0.ɵɵtext(105, \"Cancel\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(16);\n          const _r1 = i0.ɵɵreference(26);\n          const _r3 = i0.ɵɵreference(36);\n          const _r5 = i0.ɵɵreference(47);\n          const _r7 = i0.ɵɵreference(57);\n          const _r9 = i0.ɵɵreference(68);\n          const _r12 = i0.ɵɵreference(75);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.nom);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r1.invalid && _r1.touched);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.prenom);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r3.invalid && _r3.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r5.invalid && _r5.touched);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.telephone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r7.invalid && _r7.touched);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.groupe.idGroupe);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.groupes);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r9.invalid && _r9.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.ressourceId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngValue\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.ressources);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r12.invalid && _r12.touched);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.canCreate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.canRead);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.canUpdate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.responsable.canDelete);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", _r0.invalid);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.ɵNgNoValidate, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.CheckboxControlValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.PatternValidator, i5.EmailValidator, i5.NgModel, i5.NgForm],\n      styles: [\"body[_ngcontent-%COMP%] {\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n  background: linear-gradient(135deg, #1e3c72, #0c2147); \\n\\n  color: #333; \\n\\n  height: 100vh;\\n  margin: 0;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n\\n\\n.form-container[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95); \\n\\n  padding: 60px;\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  max-width: 1000px; \\n\\n  width: 100%;\\n}\\n\\n\\n\\nh2[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 600;\\n  text-align: center;\\n  color: #333;\\n  margin-bottom: 30px;\\n}\\n\\n\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n}\\n\\n.input-group[_ngcontent-%COMP%] {\\n  border-radius: 25px; \\n\\n}\\n\\n.input-group-text[_ngcontent-%COMP%] {\\n  background-color: #1e3c72;\\n  color: white;\\n  border-radius: 10px 0 0 10px; \\n\\n}\\n\\n.input-group-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  border: 1px solid #ddd;\\n  padding: 9px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff; \\n\\n  box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);\\n}\\n\\n\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #e74c3c;\\n}\\n.form-check-inline[_ngcontent-%COMP%] {\\n  margin-right: 15px; \\n\\n}\\n\\n\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border: none;\\n  padding: 12px 30px;\\n  font-size: 1rem;\\n  border-radius: 25px;\\n  transition: background-color 0.3s;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #0056b3;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  border: none;\\n  padding: 12px 30px;\\n  font-size: 1rem;\\n  border-radius: 25px;\\n  transition: background-color 0.3s;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Role", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ResponsableEditComponent_div_58_div_1_Template", "ResponsableEditComponent_div_58_div_2_Template", "ɵɵadvance", "ɵɵproperty", "_r7", "errors", "groupe_r17", "idGroupe", "ɵɵtextInterpolate1", "nomGroupe", "ressource_r18", "idRessource", "nomRessource", "ResponsableEditComponent", "constructor", "route", "responsableService", "router", "ressourceService", "responsableId", "responsable", "id", "nom", "prenom", "email", "telephone", "role", "password", "groupe", "habilitation", "ressource", "link_path", "canCreate", "canRead", "canUpdate", "canDelete", "ressourceId", "roles", "Object", "values", "groupes", "ressources", "ngOnInit", "Number", "snapshot", "paramMap", "get", "getResponsable", "getGroups", "getRessources", "getResponsableById", "subscribe", "data", "undefined", "error", "console", "getAllRessources", "updateResponsable", "selectedRessource", "find", "r", "response", "log", "navigateByUrl", "cancelEdit", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ResponsableService", "Router", "i3", "RessourceService", "selectors", "decls", "vars", "consts", "template", "ResponsableEditComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "ResponsableEditComponent_Template_form_ngSubmit_15_listener", "ResponsableEditComponent_Template_input_ngModelChange_25_listener", "$event", "ResponsableEditComponent_div_27_Template", "ResponsableEditComponent_Template_input_ngModelChange_35_listener", "ResponsableEditComponent_div_37_Template", "ResponsableEditComponent_Template_input_ngModelChange_46_listener", "ResponsableEditComponent_div_48_Template", "ResponsableEditComponent_Template_input_ngModelChange_56_listener", "ResponsableEditComponent_div_58_Template", "ResponsableEditComponent_Template_select_ngModelChange_67_listener", "ResponsableEditComponent_option_69_Template", "ResponsableEditComponent_div_70_Template", "ResponsableEditComponent_Template_select_ngModelChange_74_listener", "ResponsableEditComponent_option_78_Template", "ResponsableEditComponent_div_79_Template", "ResponsableEditComponent_Template_input_ngModelChange_85_listener", "ResponsableEditComponent_Template_input_ngModelChange_89_listener", "ResponsableEditComponent_Template_input_ngModelChange_93_listener", "ResponsableEditComponent_Template_input_ngModelChange_97_listener", "ResponsableEditComponent_Template_button_click_104_listener", "_r1", "invalid", "touched", "_r3", "_r5", "_r9", "_r12", "_r0"], "sources": ["C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\responsable-edit\\responsable-edit.component.ts", "C:\\Users\\<USER>\\Desktop\\GRAgithubbackup\\GRA\\frontend\\src\\app\\responsable-edit\\responsable-edit.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ResponsableService } from '../responsable.service';\nimport { User } from '../model/user.model';\nimport { Role } from '../model/role.enum';\nimport { Groupe } from '../model/groupe.model';\nimport { Ressource } from '../model/ressource.model'; \nimport { RessourceService } from '../services/ressource.service';  // Import RessourceService\n\n@Component({\n  selector: 'app-responsable-edit',\n  templateUrl: './responsable-edit.component.html',\n  styleUrls: ['./responsable-edit.component.css']\n})\nexport class ResponsableEditComponent implements OnInit {\n  responsableId: number = 0;\n  responsable: User = {\n    id: 0,\n    nom: '',\n    prenom: '',\n    email: '',\n    telephone: '',\n    role: 'RESPONSABLE',\n    password: '',\n    groupe: { idGroupe: 0, nomGroupe: '' },\n    habilitation: {\n      id: 0,\n      idGroupe: 0,\n      ressource: { idRessource: 0, nomRessource: '', link_path: '' }\n    },\n    canCreate: false,\n    canRead: false,\n    canUpdate: false,\n    canDelete: false,\n    ressourceId: 0,  // Add ressourceId property\n  };\n  roles = Object.values(Role); \n  groupes: Groupe[] = [];\n  ressources: Ressource[] = [];  // Array to store ressources\n\n  constructor(\n    private route: ActivatedRoute,\n    private responsableService: ResponsableService,\n    private router: Router,\n    private ressourceService: RessourceService,  // Inject RessourceService\n  ) {}\n\n  ngOnInit() {\n    this.responsableId = Number(this.route.snapshot.paramMap.get('id'));\n    this.getResponsable(this.responsableId);\n    this.getGroups();\n    this.getRessources();  // Fetch available ressources\n  }\n\n  getResponsable(id: number) {\n    this.responsableService.getResponsableById(id).subscribe(\n      (data) => {\n        this.responsable = data;\n  \n        // Assign ressourceId for dropdown binding\n        if (data.habilitation && data.habilitation.ressource) {\n          this.responsable.ressourceId = data.habilitation.ressource.idRessource;\n        }\n  \n        // Also ensure habilitation group is linked properly\n        if (this.responsable.groupe && this.responsable.groupe.idGroupe !== undefined) {\n          this.responsable.habilitation.idGroupe = this.responsable.groupe.idGroupe;\n        }\n      },\n      (error) => {\n        console.error('Error fetching responsable:', error);\n      }\n    );\n  }\n  \n  getGroups() {\n    this.responsableService.getGroups().subscribe((data) => {\n      this.groupes = data;\n    });\n  }\n\n  getRessources() {\n    this.ressourceService.getAllRessources().subscribe((data) => {\n      this.ressources = data;  // Store fetched ressources\n    });\n  }\n\n  updateResponsable() {\n    if (this.responsable.habilitation) {\n      if (this.responsable.groupe && this.responsable.groupe.idGroupe !== undefined) {\n        this.responsable.habilitation.idGroupe = this.responsable.groupe.idGroupe;\n      } else {\n        console.error('Groupe ID is not defined');\n        return;\n      }\n    } else {\n      console.error('Habilitation is not defined');\n      return;\n    }\n  \n    // Sync selected ressource\n    if (this.responsable.ressourceId) {\n      const selectedRessource = this.ressources.find(r => r.idRessource === this.responsable.ressourceId);\n      if (selectedRessource) {\n        this.responsable.habilitation.ressource = selectedRessource;\n      } else {\n        console.error('Selected ressource not found!');\n        return;\n      }\n    }\n  \n    this.responsableService.updateResponsable(this.responsableId, this.responsable).subscribe(\n      (response) => {\n        console.log('Responsable updated successfully:', response);\n        this.router.navigateByUrl('/users');\n      },\n      (error) => {\n        console.error('Error updating responsable:', error);\n      }\n    );\n  }\n  \n\n  cancelEdit() {\n    this.responsable = {\n      id: 0,\n      nom: '',\n      prenom: '',\n      email: '',\n      telephone: '',\n      role: 'RESPONSABLE',\n      password: '',\n      groupe: { idGroupe: 0, nomGroupe: '' },\n      habilitation: {\n        id: 0,\n        idGroupe: 0,\n        ressource: { idRessource: 0, nomRessource: '', link_path: '' }\n      },\n      canCreate: false,\n      canRead: false,\n      canUpdate: false,\n      canDelete: false,\n      ressourceId: 0, // Reset ressourceId\n    };\n    this.router.navigateByUrl('/users');\n  }\n}\n", "<!DOCTYPE html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\" />\n    <title>Modifier l'Utilisateur</title>\n    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\" />\n    <link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css\" rel=\"stylesheet\" />\n  </head>\n\n  <body>\n    <div class=\"background-image\">\n      <div class=\"container mt-5\">\n        <div class=\"form-container mx-auto\" style=\"max-width: 900px;\">\n          <h2 class=\"text-center mb-4\">Modifier l'Utilisateur</h2>\n          <form (ngSubmit)=\"updateResponsable()\" #responsableForm=\"ngForm\" class=\"form-group\">\n            <!-- Nom + Prénom -->\n            <div class=\"form-row\">\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"nom\">Nom</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-user\"></i></span>\n                  </div>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"nom\"\n                    [(ngModel)]=\"responsable.nom\"\n                    name=\"nom\"\n                    required\n                    #nom=\"ngModel\"\n                    placeholder=\"Entrez le nom\"\n                  />\n                </div>\n                <div *ngIf=\"nom.invalid && nom.touched\" class=\"text-danger small\">\n                  Nom is required.\n                </div>\n              </div>\n\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"prenom\">Prénom</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-user\"></i></span>\n                  </div>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"prenom\"\n                    [(ngModel)]=\"responsable.prenom\"\n                    name=\"prenom\"\n                    required\n                    #prenom=\"ngModel\"\n                    placeholder=\"Entrez le prénom\"\n                  />\n                </div>\n                <div *ngIf=\"prenom.invalid && prenom.touched\" class=\"text-danger small\">\n                  Prénom is required.\n                </div>\n              </div>\n            </div>\n\n            <!-- Email + Téléphone -->\n            <div class=\"form-row\">\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"email\">Email</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-envelope\"></i></span>\n                  </div>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"email\"\n                    [(ngModel)]=\"responsable.email\"\n                    name=\"email\"\n                    required\n                    email\n                    #email=\"ngModel\"\n                    placeholder=\"Entrez l'email\"\n                  />\n                </div>\n                <div *ngIf=\"email.invalid && email.touched\" class=\"text-danger small\">\n                  A valid email is required.\n                </div>\n              </div>\n\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"telephone\">Téléphone</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-phone\"></i></span>\n                  </div>\n                  <input\n                    type=\"tel\"\n                    class=\"form-control\"\n                    id=\"telephone\"\n                    [(ngModel)]=\"responsable.telephone\"\n                    name=\"telephone\"\n                    required\n                    pattern=\"^[0-9]{1,8}$\"\n                    #telephone=\"ngModel\"\n                    placeholder=\"Entrez le téléphone\"\n                  />\n                </div>\n                <div *ngIf=\"telephone.invalid && telephone.touched\" class=\"text-danger small\">\n                  <div *ngIf=\"telephone.errors?.['required']\">Téléphone is required.</div>\n                  <div *ngIf=\"telephone.errors?.['pattern']\">Téléphone must be a number and up to 8 digits.</div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Groupe -->\n            <div class=\"form-row\">\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"groupe\">Groupe</label>\n                <div class=\"input-group\">\n                  <div class=\"input-group-prepend\">\n                    <span class=\"input-group-text\"><i class=\"fa fa-users\"></i></span>\n                  </div>\n                  <select\n                    id=\"groupe\"\n                    class=\"form-control\"\n                    [(ngModel)]=\"responsable.groupe.idGroupe\"\n                    name=\"groupe\"\n                    required\n                    #groupe=\"ngModel\"\n                  >\n                    <option *ngFor=\"let groupe of groupes\" [value]=\"groupe.idGroupe\">\n                      {{ groupe.nomGroupe }}\n                    </option>\n                  </select>\n                </div>\n                <div *ngIf=\"groupe.invalid && groupe.touched\" class=\"text-danger small\">\n                  Groupe is required.\n                </div>\n              </div>\n            </div>\n\n            <!-- Ressource Dropdown -->\n            <div class=\"form-group\">\n              <label for=\"ressource\">Ressource</label>\n              <select\n                class=\"form-control\"\n                id=\"ressource\"\n                name=\"ressource\"\n                [(ngModel)]=\"responsable.ressourceId\"\n                required\n                #ressource=\"ngModel\"\n              >\n                <option [ngValue]=\"null\" disabled>-- Select Ressource --</option>\n                <option *ngFor=\"let ressource of ressources\" [ngValue]=\"ressource.idRessource\">\n                  {{ ressource.nomRessource }}\n                </option>\n              </select>\n              <div *ngIf=\"ressource.invalid && ressource.touched\" class=\"text-danger small\">\n                Ressource is required.\n              </div>\n            </div>\n            \n            \n\n            <!-- Droit Checkboxes -->\n            <div class=\"form-row\">\n              <div class=\"col-md-12 mb-3\">\n                <label style=\"margin-bottom: 15px; display: block;\">Droits</label> <!-- Added margin-bottom and display:block -->\n                <div class=\"form-check form-check-inline\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"canCreate\"\n                    class=\"form-check-input\"\n                    [(ngModel)]=\"responsable.canCreate\"\n                    name=\"canCreate\"\n                  />\n                  <label class=\"form-check-label\" for=\"canCreate\">Créer</label>\n                </div>\n                <div class=\"form-check form-check-inline\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"canRead\"\n                    class=\"form-check-input\"\n                    [(ngModel)]=\"responsable.canRead\"\n                    name=\"canRead\"\n                  />\n                  <label class=\"form-check-label\" for=\"canRead\">Lire</label>\n                </div>\n                <div class=\"form-check form-check-inline\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"canUpdate\"\n                    class=\"form-check-input\"\n                    [(ngModel)]=\"responsable.canUpdate\"\n                    name=\"canUpdate\"\n                  />\n                  <label class=\"form-check-label\" for=\"canUpdate\">Mettre à jour</label>\n                </div>\n                <div class=\"form-check form-check-inline\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"canDelete\"\n                    class=\"form-check-input\"\n                    [(ngModel)]=\"responsable.canDelete\"\n                    name=\"canDelete\"\n                  />\n                  <label class=\"form-check-label\" for=\"canDelete\">Supprimer</label>\n                </div>\n              </div>\n            </div>\n            \n            <!-- Buttons -->\n            <div class=\"form-row mt-4\">\n              <div class=\"col-md-12 text-center\">\n                <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"responsableForm.invalid\">\n                  Update Responsable\n                </button>\n                <button type=\"button\" class=\"btn btn-secondary ml-3\" (click)=\"cancelEdit()\">Cancel</button>\n              </div>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n\n    <script src=\"https://code.jquery.com/jquery-3.5.1.slim.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@4.5.0/dist/js/bootstrap.bundle.min.js\"></script>\n  </body>\n</html>\n"], "mappings": "AAIA,SAASA,IAAI,QAAQ,oBAAoB;;;;;;;;;ICgCzBC,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBNH,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAwBNH,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAsBJH,EAAA,CAAAC,cAAA,UAA4C;IAAAD,EAAA,CAAAE,MAAA,uCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACxEH,EAAA,CAAAC,cAAA,UAA2C;IAAAD,EAAA,CAAAE,MAAA,+DAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFjGH,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAI,UAAA,IAAAC,8CAAA,kBAAwE;IACxEL,EAAA,CAAAI,UAAA,IAAAE,8CAAA,kBAA+F;IACjGN,EAAA,CAAAG,YAAA,EAAM;;;;;IAFEH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAoC;IACpCV,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,YAAmC;;;;;IAqBvCV,EAAA,CAAAC,cAAA,iBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF8BH,EAAA,CAAAQ,UAAA,UAAAG,UAAA,CAAAC,QAAA,CAAyB;IAC9DZ,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAa,kBAAA,MAAAF,UAAA,CAAAG,SAAA,MACF;;;;;IAGJd,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBNH,EAAA,CAAAC,cAAA,iBAA+E;IAC7ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFoCH,EAAA,CAAAQ,UAAA,YAAAO,aAAA,CAAAC,WAAA,CAAiC;IAC5EhB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAa,kBAAA,MAAAE,aAAA,CAAAE,YAAA,MACF;;;;;IAEFjB,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;ADjJpB,OAAM,MAAOe,wBAAwB;EA0BnCC,YACUC,KAAqB,EACrBC,kBAAsC,EACtCC,MAAc,EACdC,gBAAkC;IAHlC,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IA7B1B,KAAAC,aAAa,GAAW,CAAC;IACzB,KAAAC,WAAW,GAAS;MAClBC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE;QAAErB,QAAQ,EAAE,CAAC;QAAEE,SAAS,EAAE;MAAE,CAAE;MACtCoB,YAAY,EAAE;QACZR,EAAE,EAAE,CAAC;QACLd,QAAQ,EAAE,CAAC;QACXuB,SAAS,EAAE;UAAEnB,WAAW,EAAE,CAAC;UAAEC,YAAY,EAAE,EAAE;UAAEmB,SAAS,EAAE;QAAE;OAC7D;MACDC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,CAAC,CAAG;KAClB;;IACD,KAAAC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC7C,IAAI,CAAC;IAC3B,KAAA8C,OAAO,GAAa,EAAE;IACtB,KAAAC,UAAU,GAAgB,EAAE,CAAC,CAAE;EAO5B;;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACvB,aAAa,GAAGwB,MAAM,CAAC,IAAI,CAAC5B,KAAK,CAAC6B,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnE,IAAI,CAACC,cAAc,CAAC,IAAI,CAAC5B,aAAa,CAAC;IACvC,IAAI,CAAC6B,SAAS,EAAE;IAChB,IAAI,CAACC,aAAa,EAAE,CAAC,CAAE;EACzB;;EAEAF,cAAcA,CAAC1B,EAAU;IACvB,IAAI,CAACL,kBAAkB,CAACkC,kBAAkB,CAAC7B,EAAE,CAAC,CAAC8B,SAAS,CACrDC,IAAI,IAAI;MACP,IAAI,CAAChC,WAAW,GAAGgC,IAAI;MAEvB;MACA,IAAIA,IAAI,CAACvB,YAAY,IAAIuB,IAAI,CAACvB,YAAY,CAACC,SAAS,EAAE;QACpD,IAAI,CAACV,WAAW,CAACgB,WAAW,GAAGgB,IAAI,CAACvB,YAAY,CAACC,SAAS,CAACnB,WAAW;;MAGxE;MACA,IAAI,IAAI,CAACS,WAAW,CAACQ,MAAM,IAAI,IAAI,CAACR,WAAW,CAACQ,MAAM,CAACrB,QAAQ,KAAK8C,SAAS,EAAE;QAC7E,IAAI,CAACjC,WAAW,CAACS,YAAY,CAACtB,QAAQ,GAAG,IAAI,CAACa,WAAW,CAACQ,MAAM,CAACrB,QAAQ;;IAE7E,CAAC,EACA+C,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CACF;EACH;EAEAN,SAASA,CAAA;IACP,IAAI,CAAChC,kBAAkB,CAACgC,SAAS,EAAE,CAACG,SAAS,CAAEC,IAAI,IAAI;MACrD,IAAI,CAACZ,OAAO,GAAGY,IAAI;IACrB,CAAC,CAAC;EACJ;EAEAH,aAAaA,CAAA;IACX,IAAI,CAAC/B,gBAAgB,CAACsC,gBAAgB,EAAE,CAACL,SAAS,CAAEC,IAAI,IAAI;MAC1D,IAAI,CAACX,UAAU,GAAGW,IAAI,CAAC,CAAE;IAC3B,CAAC,CAAC;EACJ;;EAEAK,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACrC,WAAW,CAACS,YAAY,EAAE;MACjC,IAAI,IAAI,CAACT,WAAW,CAACQ,MAAM,IAAI,IAAI,CAACR,WAAW,CAACQ,MAAM,CAACrB,QAAQ,KAAK8C,SAAS,EAAE;QAC7E,IAAI,CAACjC,WAAW,CAACS,YAAY,CAACtB,QAAQ,GAAG,IAAI,CAACa,WAAW,CAACQ,MAAM,CAACrB,QAAQ;OAC1E,MAAM;QACLgD,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAC;QACzC;;KAEH,MAAM;MACLC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAC;MAC5C;;IAGF;IACA,IAAI,IAAI,CAAClC,WAAW,CAACgB,WAAW,EAAE;MAChC,MAAMsB,iBAAiB,GAAG,IAAI,CAACjB,UAAU,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjD,WAAW,KAAK,IAAI,CAACS,WAAW,CAACgB,WAAW,CAAC;MACnG,IAAIsB,iBAAiB,EAAE;QACrB,IAAI,CAACtC,WAAW,CAACS,YAAY,CAACC,SAAS,GAAG4B,iBAAiB;OAC5D,MAAM;QACLH,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAC;QAC9C;;;IAIJ,IAAI,CAACtC,kBAAkB,CAACyC,iBAAiB,CAAC,IAAI,CAACtC,aAAa,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC+B,SAAS,CACtFU,QAAQ,IAAI;MACXN,OAAO,CAACO,GAAG,CAAC,mCAAmC,EAAED,QAAQ,CAAC;MAC1D,IAAI,CAAC5C,MAAM,CAAC8C,aAAa,CAAC,QAAQ,CAAC;IACrC,CAAC,EACAT,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,CACF;EACH;EAGAU,UAAUA,CAAA;IACR,IAAI,CAAC5C,WAAW,GAAG;MACjBC,EAAE,EAAE,CAAC;MACLC,GAAG,EAAE,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE;QAAErB,QAAQ,EAAE,CAAC;QAAEE,SAAS,EAAE;MAAE,CAAE;MACtCoB,YAAY,EAAE;QACZR,EAAE,EAAE,CAAC;QACLd,QAAQ,EAAE,CAAC;QACXuB,SAAS,EAAE;UAAEnB,WAAW,EAAE,CAAC;UAAEC,YAAY,EAAE,EAAE;UAAEmB,SAAS,EAAE;QAAE;OAC7D;MACDC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,CAAC,CAAE;KACjB;;IACD,IAAI,CAACnB,MAAM,CAAC8C,aAAa,CAAC,QAAQ,CAAC;EACrC;;;uBAnIWlD,wBAAwB,EAAAlB,EAAA,CAAAsE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxE,EAAA,CAAAsE,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAA1E,EAAA,CAAAsE,iBAAA,CAAAC,EAAA,CAAAI,MAAA,GAAA3E,EAAA,CAAAsE,iBAAA,CAAAM,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAxB3D,wBAAwB;MAAA4D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbrCpF,EAAA,CAAAC,cAAA,cAAgB;UAEZD,EAAA,CAAAsF,SAAA,cAAwB;UAGxBtF,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,6BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrCH,EAAA,CAAAsF,SAAA,cAAwG;UAE1GtF,EAAA,CAAAG,YAAA,EAAO;UAEPH,EAAA,CAAAC,cAAA,WAAM;UAI+BD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,oBAAoF;UAA9ED,EAAA,CAAAuF,UAAA,sBAAAC,4DAAA;YAAA,OAAYH,GAAA,CAAAvB,iBAAA,EAAmB;UAAA,EAAC;UAEpC9D,EAAA,CAAAC,cAAA,eAAsB;UAEDD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5BH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAsF,SAAA,aAA0B;UAAAtF,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,qBASE;UALAD,EAAA,CAAAuF,UAAA,2BAAAE,kEAAAC,MAAA;YAAA,OAAAL,GAAA,CAAA5D,WAAA,CAAAE,GAAA,GAAA+D,MAAA;UAAA,EAA6B;UAJ/B1F,EAAA,CAAAG,YAAA,EASE;UAEJH,EAAA,CAAAI,UAAA,KAAAuF,wCAAA,kBAEM;UACR3F,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA2B;UACLD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAsF,SAAA,aAA0B;UAAAtF,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,qBASE;UALAD,EAAA,CAAAuF,UAAA,2BAAAK,kEAAAF,MAAA;YAAA,OAAAL,GAAA,CAAA5D,WAAA,CAAAG,MAAA,GAAA8D,MAAA;UAAA,EAAgC;UAJlC1F,EAAA,CAAAG,YAAA,EASE;UAEJH,EAAA,CAAAI,UAAA,KAAAyF,wCAAA,kBAEM;UACR7F,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAsB;UAECD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAsF,SAAA,aAA8B;UAAAtF,EAAA,CAAAG,YAAA,EAAO;UAEtEH,EAAA,CAAAC,cAAA,qBAUE;UANAD,EAAA,CAAAuF,UAAA,2BAAAO,kEAAAJ,MAAA;YAAA,OAAAL,GAAA,CAAA5D,WAAA,CAAAI,KAAA,GAAA6D,MAAA;UAAA,EAA+B;UAJjC1F,EAAA,CAAAG,YAAA,EAUE;UAEJH,EAAA,CAAAI,UAAA,KAAA2F,wCAAA,kBAEM;UACR/F,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA2B;UACFD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAsF,SAAA,aAA2B;UAAAtF,EAAA,CAAAG,YAAA,EAAO;UAEnEH,EAAA,CAAAC,cAAA,qBAUE;UANAD,EAAA,CAAAuF,UAAA,2BAAAS,kEAAAN,MAAA;YAAA,OAAAL,GAAA,CAAA5D,WAAA,CAAAK,SAAA,GAAA4D,MAAA;UAAA,EAAmC;UAJrC1F,EAAA,CAAAG,YAAA,EAUE;UAEJH,EAAA,CAAAI,UAAA,KAAA6F,wCAAA,kBAGM;UACRjG,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAsB;UAEED,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClCH,EAAA,CAAAC,cAAA,eAAyB;UAEUD,EAAA,CAAAsF,SAAA,aAA2B;UAAAtF,EAAA,CAAAG,YAAA,EAAO;UAEnEH,EAAA,CAAAC,cAAA,sBAOC;UAJCD,EAAA,CAAAuF,UAAA,2BAAAW,mEAAAR,MAAA;YAAA,OAAAL,GAAA,CAAA5D,WAAA,CAAAQ,MAAA,CAAArB,QAAA,GAAA8E,MAAA;UAAA,EAAyC;UAKzC1F,EAAA,CAAAI,UAAA,KAAA+F,2CAAA,qBAES;UACXnG,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAI,UAAA,KAAAgG,wCAAA,kBAEM;UACRpG,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAwB;UACCD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,sBAOC;UAHCD,EAAA,CAAAuF,UAAA,2BAAAc,mEAAAX,MAAA;YAAA,OAAAL,GAAA,CAAA5D,WAAA,CAAAgB,WAAA,GAAAiD,MAAA;UAAA,EAAqC;UAIrC1F,EAAA,CAAAC,cAAA,kBAAkC;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACjEH,EAAA,CAAAI,UAAA,KAAAkG,2CAAA,qBAES;UACXtG,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAI,UAAA,KAAAmG,wCAAA,kBAEM;UACRvG,EAAA,CAAAG,YAAA,EAAM;UAKNH,EAAA,CAAAC,cAAA,eAAsB;UAEkCD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClEH,EAAA,CAAAC,cAAA,eAA0C;UAKtCD,EAAA,CAAAuF,UAAA,2BAAAiB,kEAAAd,MAAA;YAAA,OAAAL,GAAA,CAAA5D,WAAA,CAAAY,SAAA,GAAAqD,MAAA;UAAA,EAAmC;UAJrC1F,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,iBAAgD;UAAAD,EAAA,CAAAE,MAAA,kBAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE/DH,EAAA,CAAAC,cAAA,eAA0C;UAKtCD,EAAA,CAAAuF,UAAA,2BAAAkB,kEAAAf,MAAA;YAAA,OAAAL,GAAA,CAAA5D,WAAA,CAAAa,OAAA,GAAAoD,MAAA;UAAA,EAAiC;UAJnC1F,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,iBAA8C;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAE5DH,EAAA,CAAAC,cAAA,eAA0C;UAKtCD,EAAA,CAAAuF,UAAA,2BAAAmB,kEAAAhB,MAAA;YAAA,OAAAL,GAAA,CAAA5D,WAAA,CAAAc,SAAA,GAAAmD,MAAA;UAAA,EAAmC;UAJrC1F,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,iBAAgD;UAAAD,EAAA,CAAAE,MAAA,0BAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEvEH,EAAA,CAAAC,cAAA,eAA0C;UAKtCD,EAAA,CAAAuF,UAAA,2BAAAoB,kEAAAjB,MAAA;YAAA,OAAAL,GAAA,CAAA5D,WAAA,CAAAe,SAAA,GAAAkD,MAAA;UAAA,EAAmC;UAJrC1F,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,iBAAgD;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAMvEH,EAAA,CAAAC,cAAA,gBAA2B;UAGrBD,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAA4E;UAAvBD,EAAA,CAAAuF,UAAA,mBAAAqB,4DAAA;YAAA,OAASvB,GAAA,CAAAhB,UAAA,EAAY;UAAA,EAAC;UAACrE,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;UA5LvFH,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAA5D,WAAA,CAAAE,GAAA,CAA6B;UAO3B3B,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,UAAA,SAAAqG,GAAA,CAAAC,OAAA,IAAAD,GAAA,CAAAE,OAAA,CAAgC;UAelC/G,EAAA,CAAAO,SAAA,GAAgC;UAAhCP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAA5D,WAAA,CAAAG,MAAA,CAAgC;UAO9B5B,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,SAAAwG,GAAA,CAAAF,OAAA,IAAAE,GAAA,CAAAD,OAAA,CAAsC;UAkBxC/G,EAAA,CAAAO,SAAA,GAA+B;UAA/BP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAA5D,WAAA,CAAAI,KAAA,CAA+B;UAQ7B7B,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,SAAAyG,GAAA,CAAAH,OAAA,IAAAG,GAAA,CAAAF,OAAA,CAAoC;UAetC/G,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAA5D,WAAA,CAAAK,SAAA,CAAmC;UAQjC9B,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAqG,OAAA,IAAArG,GAAA,CAAAsG,OAAA,CAA4C;UAkB9C/G,EAAA,CAAAO,SAAA,GAAyC;UAAzCP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAA5D,WAAA,CAAAQ,MAAA,CAAArB,QAAA,CAAyC;UAKdZ,EAAA,CAAAO,SAAA,GAAU;UAAVP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAAxC,OAAA,CAAU;UAKnC7C,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAJ,OAAA,IAAAI,GAAA,CAAAH,OAAA,CAAsC;UAa5C/G,EAAA,CAAAO,SAAA,GAAqC;UAArCP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAA5D,WAAA,CAAAgB,WAAA,CAAqC;UAI7BzC,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAQ,UAAA,iBAAgB;UACMR,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAAvC,UAAA,CAAa;UAIvC9C,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,UAAA,SAAA2G,IAAA,CAAAL,OAAA,IAAAK,IAAA,CAAAJ,OAAA,CAA4C;UAgB5C/G,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAA5D,WAAA,CAAAY,SAAA,CAAmC;UAUnCrC,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAA5D,WAAA,CAAAa,OAAA,CAAiC;UAUjCtC,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAA5D,WAAA,CAAAc,SAAA,CAAmC;UAUnCvC,EAAA,CAAAO,SAAA,GAAmC;UAAnCP,EAAA,CAAAQ,UAAA,YAAA6E,GAAA,CAAA5D,WAAA,CAAAe,SAAA,CAAmC;UAWOxC,EAAA,CAAAO,SAAA,GAAoC;UAApCP,EAAA,CAAAQ,UAAA,aAAA4G,GAAA,CAAAN,OAAA,CAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}