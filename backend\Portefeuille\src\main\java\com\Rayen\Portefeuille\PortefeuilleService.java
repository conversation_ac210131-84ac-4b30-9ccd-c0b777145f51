package com.Rayen.Portefeuille;

import com.Rayen.Portefeuille.Entity.Portefeuille;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class PortefeuilleService {
    private final com.Rayen.Portefeuille.PortefeuilleRepository PortefeuilleRepository;


    public void savePortefeuille(Portefeuille portefeuille){
        PortefeuilleRepository.save(portefeuille);
    }

    public List<Portefeuille> findAllPortefeuilles(){
        return PortefeuilleRepository.findAll();
    }





    //relation avec Actionnaire
    public List<Portefeuille> findPortefeuillebyActionnaire(Long actionnaireId) {
      return  PortefeuilleRepository.findAllByActionnaireId(actionnaireId);
    }
}
