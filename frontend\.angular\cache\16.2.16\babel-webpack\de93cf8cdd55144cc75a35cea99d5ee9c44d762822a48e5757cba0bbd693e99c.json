{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/habilitation.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction HabilitationComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Groupe:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.groupe.nomGroupe, \"\");\n  }\n}\nfunction HabilitationComponent_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ressource_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", ressource_r3.idRessource);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", ressource_r3.nomRessource, \" (\", ressource_r3.link_path, \") \");\n  }\n}\nfunction HabilitationComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1, \" Veuillez s\\u00E9lectionner une ressource avant de soumettre. \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class HabilitationComponent {\n  constructor(route, habilitationService) {\n    this.route = route;\n    this.habilitationService = habilitationService;\n    this.ressources = [];\n    this.habilitation = {\n      id: 0,\n      idGroupe: 0,\n      ressource: {\n        idRessource: 0,\n        nomRessource: '',\n        link_path: ''\n      }\n    };\n  }\n  ngOnInit() {\n    const idParam = this.route.snapshot.paramMap.get('id');\n    if (idParam) {\n      this.groupeId = Number(idParam);\n      this.getGroupeDetails(this.groupeId);\n      this.getRessources();\n    }\n  }\n  getGroupeDetails(id) {\n    this.habilitationService.getGroupeById(id).subscribe({\n      next: group => {\n        this.groupe = group;\n      },\n      error: err => {\n        console.error('Error fetching group:', err);\n      }\n    });\n  }\n  getRessources() {\n    this.habilitationService.getAllRessources().subscribe({\n      next: data => {\n        this.ressources = data;\n      },\n      error: err => {\n        console.error('Error fetching ressources:', err);\n      }\n    });\n  }\n  submitHabilitation() {\n    console.log('Selected Ressource ID:', this.habilitation.ressource.idRessource);\n    console.log('Full Ressource Object:', this.habilitation.ressource);\n    // Validate if a valid resource is selected\n    const selectedRessource = this.ressources.find(r => r.idRessource === this.habilitation.ressource.idRessource);\n    if (this.groupe && selectedRessource) {\n      this.habilitation.idGroupe = this.groupeId;\n      this.habilitation.ressource = selectedRessource;\n      this.habilitationService.submitHabilitation(this.habilitation).subscribe({\n        next: response => {\n          console.log('Habilitation saved successfully', response);\n        },\n        error: err => {\n          console.error('Error saving habilitation:', err);\n        }\n      });\n    } else {\n      console.warn('Please select a valid ressource before submitting.');\n    }\n  }\n  static {\n    this.ɵfac = function HabilitationComponent_Factory(t) {\n      return new (t || HabilitationComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.HabilitationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HabilitationComponent,\n      selectors: [[\"app-habilitation\"]],\n      decls: 14,\n      vars: 5,\n      consts: [[1, \"container\", \"mt-4\"], [4, \"ngIf\"], [1, \"mb-3\"], [\"for\", \"ressourceSelect\", 1, \"form-label\"], [\"id\", \"ressourceSelect\", \"name\", \"ressource\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"0\", \"disabled\", \"\", \"selected\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [\"class\", \"text-danger mt-2\", 4, \"ngIf\"], [3, \"value\"], [1, \"text-danger\", \"mt-2\"]],\n      template: function HabilitationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Cr\\u00E9er une Habilitation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, HabilitationComponent_div_3_Template, 5, 1, \"div\", 1);\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"label\", 3);\n          i0.ɵɵtext(6, \"S\\u00E9lectionner une Ressource:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"select\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function HabilitationComponent_Template_select_ngModelChange_7_listener($event) {\n            return ctx.habilitation.ressource.idRessource = $event;\n          });\n          i0.ɵɵelementStart(8, \"option\", 5);\n          i0.ɵɵtext(9, \"-- Choisissez une ressource --\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, HabilitationComponent_option_10_Template, 2, 3, \"option\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function HabilitationComponent_Template_button_click_11_listener() {\n            return ctx.submitHabilitation();\n          });\n          i0.ɵɵtext(12, \" Enregistrer \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, HabilitationComponent_div_13_Template, 2, 0, \"div\", 8);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupe);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.habilitation.ressource.idRessource);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.ressources);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.habilitation.ressource.idRessource === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.habilitation.ressource.idRessource === 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.NgSelectOption, i4.ɵNgSelectMultipleOption, i4.SelectControlValueAccessor, i4.NgControlStatus, i4.NgModel],\n      styles: [\".habilitation-container[_ngcontent-%COMP%] {\\n    max-width: 600px;\\n    margin: 20px auto;\\n    padding: 20px;\\n    background-color: #f9f9f9;\\n    border-radius: 8px;\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n  }\\n  \\n  h2[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 20px;\\n    color: #333;\\n    font-size: 1.5rem;\\n  }\\n  \\n  .form-group[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n  \\n  label[_ngcontent-%COMP%] {\\n    display: block;\\n    margin-bottom: 5px;\\n    font-weight: bold;\\n    color: #555;\\n  }\\n  \\n  select[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 10px;\\n    border: 1px solid #ddd;\\n    border-radius: 4px;\\n    font-size: 1rem;\\n    background-color: #fff;\\n    transition: border 0.3s ease;\\n  }\\n  \\n  select[_ngcontent-%COMP%]:focus {\\n    border-color: #007bff;\\n    outline: none;\\n  }\\n  \\n  button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 12px;\\n    background-color: #007bff;\\n    color: white;\\n    font-size: 1.1rem;\\n    border: none;\\n    border-radius: 4px;\\n    cursor: pointer;\\n    transition: background-color 0.3s ease;\\n  }\\n  \\n  button[_ngcontent-%COMP%]:hover {\\n    background-color: #0056b3;\\n  }\\n  \\n  button[_ngcontent-%COMP%]:focus {\\n    outline: none;\\n  }\\n  \\n  @media (max-width: 600px) {\\n    .habilitation-container[_ngcontent-%COMP%] {\\n      padding: 15px;\\n    }\\n  \\n    h2[_ngcontent-%COMP%] {\\n      font-size: 1.25rem;\\n    }\\n  \\n    select[_ngcontent-%COMP%] {\\n      font-size: 0.9rem;\\n    }\\n  \\n    button[_ngcontent-%COMP%] {\\n      font-size: 1rem;\\n    }\\n  }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "groupe", "nomGroupe", "ɵɵproperty", "ressource_r3", "idRessource", "ɵɵtextInterpolate2", "nomRessource", "link_path", "HabilitationComponent", "constructor", "route", "habilitationService", "ressources", "habilitation", "id", "idGroupe", "ressource", "ngOnInit", "idParam", "snapshot", "paramMap", "get", "groupeId", "Number", "getGroupeDetails", "getRessources", "getGroupeById", "subscribe", "next", "group", "error", "err", "console", "getAllRessources", "data", "submitHabilitation", "log", "selectedRessource", "find", "r", "response", "warn", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "HabilitationService", "selectors", "decls", "vars", "consts", "template", "HabilitationComponent_Template", "rf", "ctx", "ɵɵtemplate", "HabilitationComponent_div_3_Template", "ɵɵlistener", "HabilitationComponent_Template_select_ngModelChange_7_listener", "$event", "HabilitationComponent_option_10_Template", "HabilitationComponent_Template_button_click_11_listener", "HabilitationComponent_div_13_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\habilitation\\habilitation.component.ts", "C:\\Users\\<USER>\\Desktop\\Grafinalfront\\GRA\\frontend\\src\\app\\habilitation\\habilitation.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { HabilitationService } from '../services/habilitation.service';\nimport { Groupe } from '../model/groupe.model';\nimport { Ressource } from '../model/ressource.model';\nimport { Habilitation } from '../model/Habilitation.model';\n\n@Component({\n  selector: 'app-habilitation',\n  templateUrl: './habilitation.component.html',\n  styleUrls: ['./habilitation.component.css']\n})\nexport class HabilitationComponent implements OnInit {\n  groupeId!: number;\n  groupe?: Groupe;\n  ressources: Ressource[] = [];\n\n  habilitation: Habilitation = {\n    id: 0,\n    idGroupe: 0,\n    ressource: {\n      idRessource: 0, // Initially set to 0, should be updated after fetching resources\n      nomRessource: '',\n      link_path: ''\n    }\n  };\n\n  constructor(\n    private route: ActivatedRoute,\n    private habilitationService: HabilitationService\n  ) {}\n\n  ngOnInit(): void {\n    const idParam = this.route.snapshot.paramMap.get('id');\n    if (idParam) {\n      this.groupeId = Number(idParam);\n      this.getGroupeDetails(this.groupeId);\n      this.getRessources();\n    }\n  }\n\n  getGroupeDetails(id: number): void {\n    this.habilitationService.getGroupeById(id).subscribe({\n      next: (group: Groupe) => {\n        this.groupe = group;\n      },\n      error: (err) => {\n        console.error('Error fetching group:', err);\n      }\n    });\n  }\n\n  getRessources(): void {\n    this.habilitationService.getAllRessources().subscribe({\n      next: (data: Ressource[]) => {\n        this.ressources = data;\n      },\n      error: (err) => {\n        console.error('Error fetching ressources:', err);\n      }\n    });\n  }\n\n  submitHabilitation(): void {\n    console.log('Selected Ressource ID:', this.habilitation.ressource.idRessource);\n    console.log('Full Ressource Object:', this.habilitation.ressource);\n\n    // Validate if a valid resource is selected\n    const selectedRessource = this.ressources.find(\n      r => r.idRessource === this.habilitation.ressource.idRessource\n    );\n\n    if (this.groupe && selectedRessource) {\n      this.habilitation.idGroupe = this.groupeId;\n      this.habilitation.ressource = selectedRessource;\n\n      this.habilitationService.submitHabilitation(this.habilitation).subscribe({\n        next: (response) => {\n          console.log('Habilitation saved successfully', response);\n        },\n        error: (err) => {\n          console.error('Error saving habilitation:', err);\n        }\n      });\n    } else {\n      console.warn('Please select a valid ressource before submitting.');\n    }\n  }\n}\n", "<div class=\"container mt-4\">\n  <h2><PERSON><PERSON>er une Habilitation</h2>\n\n  <!-- Display group info -->\n  <div *ngIf=\"groupe\">\n    <p><strong>Groupe:</strong> {{ groupe.nomGroupe }}</p>\n  </div>\n\n  <!-- Dropdown for selecting a ressource -->\n  <div class=\"mb-3\">\n    <label for=\"ressourceSelect\" class=\"form-label\">Sélectionner une Ressource:</label>\n    <select\n      id=\"ressourceSelect\"\n      class=\"form-select\"\n      [(ngModel)]=\"habilitation.ressource.idRessource\"\n      name=\"ressource\"\n    >\n      <option value=\"0\" disabled selected>-- Choisissez une ressource --</option>\n      <option *ngFor=\"let ressource of ressources\" [value]=\"ressource.idRessource\">\n        {{ ressource.nomRessource }} ({{ ressource.link_path }})\n      </option>\n    </select>\n  </div>\n\n  <!-- Submit button -->\n  <button\n    class=\"btn btn-primary\"\n    (click)=\"submitHabilitation()\"\n    [disabled]=\"habilitation.ressource.idRessource === 0\"\n  >\n    Enregistrer\n  </button>\n\n  <!-- Optional: success or error message -->\n  <div *ngIf=\"habilitation.ressource.idRessource === 0\" class=\"text-danger mt-2\">\n    Veuillez sélectionner une ressource avant de soumettre.\n  </div>\n</div>"], "mappings": ";;;;;;;ICIEA,EAAA,CAAAC,cAAA,UAAoB;IACPD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA1BH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,MAAA,CAAAC,SAAA,KAAsB;;;;;IAahDR,EAAA,CAAAC,cAAA,gBAA6E;IAC3ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFoCH,EAAA,CAAAS,UAAA,UAAAC,YAAA,CAAAC,WAAA,CAA+B;IAC1EX,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAF,YAAA,CAAAG,YAAA,QAAAH,YAAA,CAAAI,SAAA,OACF;;;;;IAcJd,EAAA,CAAAC,cAAA,cAA+E;IAC7ED,EAAA,CAAAE,MAAA,qEACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;ADxBR,OAAM,MAAOY,qBAAqB;EAehCC,YACUC,KAAqB,EACrBC,mBAAwC;IADxC,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAd7B,KAAAC,UAAU,GAAgB,EAAE;IAE5B,KAAAC,YAAY,GAAiB;MAC3BC,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE;QACTZ,WAAW,EAAE,CAAC;QACdE,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;;KAEd;EAKE;EAEHU,QAAQA,CAAA;IACN,MAAMC,OAAO,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACtD,IAAIH,OAAO,EAAE;MACX,IAAI,CAACI,QAAQ,GAAGC,MAAM,CAACL,OAAO,CAAC;MAC/B,IAAI,CAACM,gBAAgB,CAAC,IAAI,CAACF,QAAQ,CAAC;MACpC,IAAI,CAACG,aAAa,EAAE;;EAExB;EAEAD,gBAAgBA,CAACV,EAAU;IACzB,IAAI,CAACH,mBAAmB,CAACe,aAAa,CAACZ,EAAE,CAAC,CAACa,SAAS,CAAC;MACnDC,IAAI,EAAGC,KAAa,IAAI;QACtB,IAAI,CAAC7B,MAAM,GAAG6B,KAAK;MACrB,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;MAC7C;KACD,CAAC;EACJ;EAEAN,aAAaA,CAAA;IACX,IAAI,CAACd,mBAAmB,CAACsB,gBAAgB,EAAE,CAACN,SAAS,CAAC;MACpDC,IAAI,EAAGM,IAAiB,IAAI;QAC1B,IAAI,CAACtB,UAAU,GAAGsB,IAAI;MACxB,CAAC;MACDJ,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEC,GAAG,CAAC;MAClD;KACD,CAAC;EACJ;EAEAI,kBAAkBA,CAAA;IAChBH,OAAO,CAACI,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACvB,YAAY,CAACG,SAAS,CAACZ,WAAW,CAAC;IAC9E4B,OAAO,CAACI,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACvB,YAAY,CAACG,SAAS,CAAC;IAElE;IACA,MAAMqB,iBAAiB,GAAG,IAAI,CAACzB,UAAU,CAAC0B,IAAI,CAC5CC,CAAC,IAAIA,CAAC,CAACnC,WAAW,KAAK,IAAI,CAACS,YAAY,CAACG,SAAS,CAACZ,WAAW,CAC/D;IAED,IAAI,IAAI,CAACJ,MAAM,IAAIqC,iBAAiB,EAAE;MACpC,IAAI,CAACxB,YAAY,CAACE,QAAQ,GAAG,IAAI,CAACO,QAAQ;MAC1C,IAAI,CAACT,YAAY,CAACG,SAAS,GAAGqB,iBAAiB;MAE/C,IAAI,CAAC1B,mBAAmB,CAACwB,kBAAkB,CAAC,IAAI,CAACtB,YAAY,CAAC,CAACc,SAAS,CAAC;QACvEC,IAAI,EAAGY,QAAQ,IAAI;UACjBR,OAAO,CAACI,GAAG,CAAC,iCAAiC,EAAEI,QAAQ,CAAC;QAC1D,CAAC;QACDV,KAAK,EAAGC,GAAG,IAAI;UACbC,OAAO,CAACF,KAAK,CAAC,4BAA4B,EAAEC,GAAG,CAAC;QAClD;OACD,CAAC;KACH,MAAM;MACLC,OAAO,CAACS,IAAI,CAAC,oDAAoD,CAAC;;EAEtE;;;uBA3EWjC,qBAAqB,EAAAf,EAAA,CAAAiD,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAnD,EAAA,CAAAiD,iBAAA,CAAAG,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAArBtC,qBAAqB;MAAAuC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlC5D,EAAA,CAAAC,cAAA,aAA4B;UACtBD,EAAA,CAAAE,MAAA,kCAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG/BH,EAAA,CAAA8D,UAAA,IAAAC,oCAAA,iBAEM;UAGN/D,EAAA,CAAAC,cAAA,aAAkB;UACgCD,EAAA,CAAAE,MAAA,uCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnFH,EAAA,CAAAC,cAAA,gBAKC;UAFCD,EAAA,CAAAgE,UAAA,2BAAAC,+DAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAzC,YAAA,CAAAG,SAAA,CAAAZ,WAAA,GAAAuD,MAAA;UAAA,EAAgD;UAGhDlE,EAAA,CAAAC,cAAA,gBAAoC;UAAAD,EAAA,CAAAE,MAAA,qCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC3EH,EAAA,CAAA8D,UAAA,KAAAK,wCAAA,oBAES;UACXnE,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,iBAIC;UAFCD,EAAA,CAAAgE,UAAA,mBAAAI,wDAAA;YAAA,OAASP,GAAA,CAAAnB,kBAAA,EAAoB;UAAA,EAAC;UAG9B1C,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAA8D,UAAA,KAAAO,qCAAA,iBAEM;UACRrE,EAAA,CAAAG,YAAA,EAAM;;;UAjCEH,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAS,UAAA,SAAAoD,GAAA,CAAAtD,MAAA,CAAY;UAUdP,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAS,UAAA,YAAAoD,GAAA,CAAAzC,YAAA,CAAAG,SAAA,CAAAZ,WAAA,CAAgD;UAIlBX,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAS,UAAA,YAAAoD,GAAA,CAAA1C,UAAA,CAAa;UAU7CnB,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAS,UAAA,aAAAoD,GAAA,CAAAzC,YAAA,CAAAG,SAAA,CAAAZ,WAAA,OAAqD;UAMjDX,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAAS,UAAA,SAAAoD,GAAA,CAAAzC,YAAA,CAAAG,SAAA,CAAAZ,WAAA,OAA8C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}